export APP_NAME="__APP_NAME__"
export DEBUG="false"
export APP_ENCODING="UTF-8"
export APP_DEFAULT_LOCALE="en_US"
export SECURITY_SALT="__SALT__"
export APP_ENV= 'dev'
#export API_URL= "https://api.universityrecrentals.com/"
export DOMAIN ='.universityrecrentals.com'
#AWS S3 configuration
export AWS_S3_ACCESS_KEY =""
export AWS_S3_ACCESS_SECRET =""
export AWS_S3_VERSION =""
export AWS_S3_REGION=""
export AWS_S3_PRODUCT_BUCKET="local.rentmy"  // local
export GOOGLE_APP_ID=""
export GOOGLE_APP_SECRET=""
export GOOGLE_APP_REDIRECT_URL=""

export SMS_gateway=""
export SMS_Twilio_sid=""
export SMS_Twilio_token=""
export SMS_Twilio_from="+"
export SMS_Twillo_messagingServiceSid=""

LEAPING_EMAIL_TRANSPORT_CLASS="Smtp"
export LEAPING_EMAIL_TRANSPORT_HOST="email-smtp.us-east-2.amazonaws.com"
export LEAPING_EMAIL_TRANSPORT_PORT=25
export LEAPING_EMAIL_TRANSPORT_USERNAME="AKIAXPBSGVZRSJ7CIANK"
export LEAPING_EMAIL_TRANSPORT_PASSWORD="BMIJMYkGDQtGO5/C057Ecj7XS26zMmdw0HUdZtxljd8g"
export LEAPING_EMAIL_TRANSPORT_TLS=true

#EMAIL Config /src/Lib/RentMy/RentMy.php
export EMAIL_FROM_NAME="RentMy"
export EMAIL_FROM_EMAIL="<EMAIL>"

# /config/environment/config-stage.php
export ADMIN_EMAIL_TO='<EMAIL>'
export ADMIN_EMAIL_BCC='<EMAIL>'
export ORDER_EMAILS='<EMAIL>,<EMAIL>'
export PARTNER_HOST='http://partner.rentmy.leaperdev.rocks'
export CLIENT_HOST='https://client.rentmystag1ng.com'
export SERVER_IP='************,**************'
export HOST='https://rentmy.co'
export GOOGLE_API_KEY='AIzaSyCNDGKLAho8CCg_y67mwLkY89zzjJkOcAA'
export GOOGLE_GEO_API_KEY='AIzaSyDbvPlnG6m-_3PcsSdfpZSAVs9-ZWtEaCA'
export STORE_HOST='https://,.rentmystag1ng.com'
export SHIP_ENGINE_API_TOKEN='Y3/g2dcz6bDBUpOz7oAl/J8Hvm3tzt4MZqVEPPX3McA'
export SHIP_ENGINE_API_ENDPOINT='https://api.shipengine.com/v1'
export CLIENT_API='https://api.rentmystag1ng.com'
export TIMEZONE='America/Chicago'
export TMP_IMG_UPLOAD_DIR='/var/www/tmp/'
export DIGEST_SENDING_TIME='3:00'
export APPLICATION_FEE_PERCENT=7

# /config/app-staging.php
export DATABASE_HOST="localhost"
export DATABASE_USER="rentmydev"
export DATABASE_PASSWORD="5SaM07q3LW8yX"
export DATABASE_NAME="rentmy_stage"
export DATABASE_ENCODING="utf8"
export DATABASE_TIMEZONE="UTC"

export DATABASE_HOST_SLAVE1="localhost"
export DATABASE_USER_SLAVE1="rentmydev"
export DATABASE_PASSWORD_SLAVE1="5SaM07q3LW8yX"
export DATABASE_NAME_SLAVE1="rentmy_stage"
export DATABASE_ENCODING_SLAVE1="utf8"
export DATABASE_TIMEZONE_SLAVE1="UTC"

export DATABASE_HOST_BOOT_LIVE="************"
export DATABASE_USER_BOOT_LIVE="boot_live"
export DATABASE_PASSWORD_BOOT_LIVE="pF5j^7h6"
export DATABASE_NAME_BOOT_LIVE="boot_live"
export DATABASE_ENCODING_BOOT_LIVE="utf8"
export DATABASE_TIMEZONE_BOOT_LIVE="UTC"

export DATABASE_HOST_TEST="localhost"
export DATABASE_USER_TEST="leaperdev"
export DATABASE_PASSWORD_TEST="Wpz70wjglcdMX7JO"
export DATABASE_NAME_TEST="checkdefense_dev"
export DATABASE_ENCODING_TEST="utf8"
export DATABASE_TIMEZONE_TEST="UTC"

# /config/environment/config-stage.php
export ADMIN_EMAIL_TO='<EMAIL>'
export ADMIN_EMAIL_BCC='<EMAIL>'
export ORDER_EMAILS='<EMAIL>,<EMAIL>'
export PARTNER_HOST='http://partner.rentmy.leaperdev.rocks'
export CLIENT_HOST='https://client.rentmystag1ng.com'
export SERVER_IP='************,**************'
export HOST='https://rentmy.co'
export GOOGLE_API_KEY='AIzaSyCNDGKLAho8CCg_y67mwLkY89zzjJkOcAA'
export GOOGLE_GEO_API_KEY='AIzaSyDbvPlnG6m-_3PcsSdfpZSAVs9-ZWtEaCA'
export STORE_HOST='https://,.rentmystag1ng.com'
export SHIP_ENGINE_API_TOKEN='Y3/g2dcz6bDBUpOz7oAl/J8Hvm3tzt4MZqVEPPX3McA'
export SHIP_ENGINE_API_ENDPOINT='https://api.shipengine.com/v1'
export CLIENT_API='https://api.rentmystag1ng.com'
export TIMEZONE='America/Chicago'
export TMP_IMG_UPLOAD_DIR='/var/www/tmp/'
export DIGEST_SENDING_TIME='3:00'
export APPLICATION_FEE_PERCENT=7