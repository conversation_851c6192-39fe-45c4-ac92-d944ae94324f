<?php

return [
    'admin_email' => ['to' => '<EMAIL>', 'bcc' => '<EMAIL>'],
    //'admin_email' => '<EMAIL>',
    'order_emails' => ['<EMAIL>', '<EMAIL>'],
    'PARTNER_HOST' => env('PARTNER_HOST', 'http://partner.rentmy.leaperdev.rocks'),
    'CLIENT_HOST' => env('CLIENT_HOST', 'https://client.rentmy.co'),
    'HOST' => 'https://rentmy.co',
    'SERVER_IP' => ['************', '*************'],
    //'HOST' => 'http://rentmy.local.com',
    //'GOOGLE_API_KEY' => 'AIzaSyDsqHrDAlX26IuF8ZmWQNeiSKKEuQcfO38',
    'GOOGLE_API_KEY' => 'AIzaSyCNDGKLAho8CCg_y67mwLkY89zzjJkOcAA',
    'GOOGLE_GEO_API_KEY' => 'AIzaSyDbvPlnG6m-_3PcsSdfpZSAVs9-ZWtEaCA',
    'STORE_HOST' => [
        'https://',
        '.rentmy.co'
    ],
    'ship_engine_api_token' => 'Y3/g2dcz6bDBUpOz7oAl/J8Hvm3tzt4MZqVEPPX3McA',
    'ship_engine_api_endPoint' => 'https://api.shipengine.com/v1',

    'CLIENT_API' => env('CLIENT_API', 'https://clientapi.rentmy.co'),
    'TIMEZONE' => 'America/Chicago',
    // 'tmpImgUploadDir' => "/var/www/vhosts/rentmy.co/tmp/image_upload/",
    'tmpImgUploadDir' => "/var/www/rentmy/tmp/image_upload/",
    'DIGEST_SENDING_TIME' => '3:00',
    'APPLICATION_FEE_PERCENT' => env('APPLICATION_FEE_PERCENT', 7),
    'OrderCostType' => [
        '1' => 'Product',
        '2' => 'Service',
        '3' => 'Delivery',
        '4' => 'Shipping',
    ],
    'assetCondition' => [
        '1' => ['label' => "In Stock", "color" => "#3498db"],
        '2' => ['label' => "Rented Out", "color" => "#e67e22"],
        '3' => ['label' => "Sold", "color" => "#2ecc71"],
        '4' => ['label' => "Damaged", "color" => "#c0392b"],
        '5' => ['label' => "In Service", "color" => "#e74c3c"],
        '6' => ['label' => "In Transit", "color" => "#e74D3c"],
        '7' => ['label' => "Deleted", "color" => "#e74c3D"],
        '8' => ['label' => "Retired", "color" => "#e74c3E"],
        '9' => ['label' => "Needs Refill", "color" => "#e7773f"],
        '10' => ['label' => "Inspect & Make Ready", "color" => "#e74c3c"],
    ],
    'assetStatus' => [
        '1' => ["label" => "Available", "color" => "#f39c12"],
        '2' => ["label" => "Unavailable", "color" => "#2980b9"],
        '3' => ["label" => "Deleted", "color" => "#c0392b"],
    ],
    'assetReturnStatus' => [
        '1' => ["label" => "New", "color" => "#3498db"],
        '2' => ["label" => "Good", "color" => "#e67e22"],
        '3' => ["label" => "Fair", "color" => "#2ecc71"],
        '4' => ["label" => "Poor", "color" => "#c0392b"],
        '5' => ["label" => "Need Service", "color" => "#e74c3c", 'update_asset' => ['current_condition' => 5, 'current_status' => 2]],
        '6' => ["label" => "Damaged", "color" => "#e74D3c", 'update_asset' => ['current_condition' => 4, 'current_status' => 2]],
        '7' => ["label" => "Missing", "color" => "#e74c3D", 'update_asset' => ['current_condition' => 7, 'current_status' => 3]],
    ],
    'layout' => [
        "colorSettings" => null,
        "layout_id" => 0,
        "section" => (object)["grid" => true,
            "featured_product" => true,
            "new_arrival" => false,
            "promotion" => true]
    ],
    'address_validate_status' => [
        1 => 'Verified',
        2 => 'Unverified',
        3 => 'Warning',
        4 => 'Error'
    ],
    'rentManageTime' => [
        'before' => 2,
        'after' => 2,
    ],
    'taxValue' => 0.00,
    'tax' => [
        'rent_tax' => 0.00,
        'buy_tax' => 0.00,
        'delivery_tax' => 0.00,
        'tax_apply_on' => 1,/** 0=None, 1=Both, 2=Buy, 3=Rent*/
    ],
    'subscription' => [
        'create_order' => false,
        'total_order' => 10
    ],
    'priceType' => [
        '1' => 'No Rental Charge',
        '2' => 'Fixed Rental Rate',
        '3' => 'Flat Rental Rate',
        '4' => 'Flex Rental Rate',
    ],
    'navigationContent' => [
        '1' => 'Tag',
        '2' => 'Category',
        '3' => 'Content',
        '4' => 'Custom',
        '5' => 'Pages',
        '6' => 'Home',
        '7' => 'About Us',
        '8' => 'Contact Us'
    ],
    /** @deprecated */
    'templates' => [
        'types' => [
            1 => [
                'name' => 'pdf',
                'content_type' => []
            ],
            2 => [
                'name' => 'email',
                'content_type' => [
                    1 => 'Order Email',
                    2 => 'Customer Registration',
                    3 => 'Customer Forget Password',
                    4 => 'User Forget Password',
                    5 => 'Order Remainder Email'
                ]
            ],
            3 => [
                'name' => 'mobile',
                'content_type' => [
                    1 => 'Order SMS'
                ]
            ]
        ]
    ],
    'notification_events' => [
        [
            'label' => 'Customer Registration',
            'event' => 'customer_register',
            'multipleAllow' => false,
            'default' => true,
            'email_template' => [
                'title' => 'Customer Registration',
                'type' => 2,
                'content_type' => 2,
                'content_body' => 'Thank you for registering with us <br/> In order to activate your account, please click the button below',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Customer Forgot Password',
            'event' => 'customer_forgot_password',
            'multipleAllow' => false,
            'default' => true,
            'template_layout' => 'customer_forgot_password',
            'email_template' => [
                'title' => 'Customer Forgot Password',
                'type' => 2,
                'content_type' => 2,
                'content_body' => 'Click the below button to change your password.',
                'old_content_type' => 3 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'User Forgot Password',
            'event' => 'user_forgot_password',
            'multipleAllow' => false,
            'default' => true,
            'email_template' => [
                'title' => 'User Forgot Password',
                'type' => 2,
                'content_type' => 2,
                'content_body' => 'Click the below button to change your password.',
                'old_content_type' => 4 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Rental Reminder Before Start date',
            'event' => 'order_rental_reminder_start',
            'multipleAllow' => true,
            'default' => false,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Rental {{start_end}} Reminder',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 5 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'before',
                'duration_type' => 'days',
                'duration' => 2
            ],
            'order_related' => true,
        ],
        [
            'label' => 'Rental Reminder Before End date',
            'event' => 'order_rental_reminder_end',
            'multipleAllow' => true,
            'default' => false,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Rental {{start_end}} Reminder',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 5 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'before',
                'duration_type' => 'days',
                'duration' => 2
            ],
            'order_related' => true,
        ],
        [
            'label' => 'Order Received',
            'event' => 'create_order',
            'multipleAllow' => false,
            'default' => true,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Thanks for your order',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 1 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => true,
                'content' => '{{order_quote}}. Please click the link below to download your new document from {{link}}'
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ],
            'order_related' => true,
        ],
        [
            'label' => 'Order Quote Accept',
            'event' => 'order_quote_accept',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => 'Your quote has been accepted',
                'type' => 2,
                'content_type' => 1,
            ],
            'sms_template' => [
                'enable' => true,
                'content' => 'Your order quote has been accepted. Please click the link below to download your new document from {{link}}'
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ],
            'order_related' => true,
        ],
        [
            'label' => 'Order Status (Canceled)',
            'event' => 'order_cancel',
            'multipleAllow' => true,
            'default' => false,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Order cancelled', 'type' => 2, 'content_type' => 1,
            ],
            'sms_template' => ['enable' => false],
            'trigger_on' => ['send' => 'immediately'],
            'order_related' => true,
        ],
        [
            'label' => 'Order Status (Completed)',
            'event' => 'order_complete',
            'multipleAllow' => true,
            'default' => false,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Order Completed', 'type' => 2, 'content_type' => 1,
            ],
            'sms_template' => ['enable' => false],
            'trigger_on' => ['send' => 'immediately'],
            'order_related' => true,
        ],
        [
            'label' => 'Order Status (With Customer)',
            'event' => 'order_with_customer',
            'multipleAllow' => true,
            'default' => false,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Order items with customer', 'type' => 2, 'content_type' => 1,
            ],
            'sms_template' => ['enable' => false],
            'trigger_on' => ['send' => 'immediately'],
            'order_related' => true,
        ],
        [
            'label' => 'Waiver Sharing',
            'event' => 'waiver_sharing',
            'multipleAllow' => false,
            'default' => true,
            'email_template' => [
                'title' => 'Waiver Sharing',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 1 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Collect Signature',
            'event' => 'collect_signature',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => 'Collect Signature',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 1 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Subscription created',
            'event' => 'subscription_created',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => 'Exchange Request',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 1 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Closet Exchange',
            'event' => 'closet_exchange',
            'multipleAllow' => false,
            'default' => true,
            'email_template' => [
                'title' => 'Exchange Request',
                'type' => 2,
                'content_type' => 1,
                'old_content_type' => 1 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Daily digest',
            'event' => 'daily_digest',
            'multipleAllow' => false,
            'default' => true,
            'email_template' => [
                'title' => 'Daily digest',
                'type' => 2,
                'content_type' => 3,
//                'old_content_type' => 1 //@adopt to old system,
                'content_body' =>  '',
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Order Status Changed',
            'event' => 'order_changed',
            'multipleAllow' => true,
            'default' => false,
            'product_specific' => true,
            'email_template' => [
                'title' => 'Order status changed', 'type' => 2, 'content_type' => 1,
            ],
            'sms_template' => ['enable' => false],
            'trigger_on' => ['send' => 'immediately'],
            'order_related' => true,
        ],
        [
            'label' => 'Shipment created',
            'event' => 'shipment_created',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => 'Shipment created', 'type' => 2, 'content_type' => 1,
            ],
            'sms_template' => ['enable' => false],
            'trigger_on' => ['send' => 'immediately'],
            'order_related' => true,
        ],
        [
            'label' => 'Partner Confirmation',
            'event' => 'partner_confirmation',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => 'Partner Confirmation',
                'type' => 2,
                'content_type' => 2,
                'content_body' => 'Thank you for registering with us <br/> In order to access your account, please click the link below',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Abandoned Cart',
            'event' => 'abandoned_cart',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => '',
                'type' => 2,
                'content_type' => 2,
                'content_body' => '',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Wishlist',
            'event' => 'wishlist',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => '',
                'type' => 2,
                'content_type' => 2,
                'content_body' => '',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Payment Request',
            'event' => 'payment_request',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => '',
                'type' => 2,
                'content_type' => 2,
                'content_body' => '',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Payment Received',
            'event' => 'payment_received',
            'multipleAllow' => true,
            'default' => false,
            'email_template' => [
                'title' => '',
                'type' => 2,
                'content_type' => 2,
                'content_body' => '',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ]
        ],
        [
            'label' => 'Send Quote',
            'event' => 'send_quote',
            'multipleAllow' => false,
            'default' => false,
            'email_template' => [
                'title' => '',
                'type' => 2,
                'content_type' => 2,
                'content_body' => '',
                'old_content_type' => 2 //@adopt to old system
            ],
            'sms_template' => [
                'enable' => false
            ],
            'trigger_on' => [
                'send' => 'immediately',
            ],
            'order_related' => true,
        ],
//        [
//            'label' => 'Order amount paid',
//            'event' => 'order_amount_paid',
//            'multipleAllow' => true,
//            'default' => false,
//            'email_template' => [
//                'title' => 'Order amount paid email', 'type' => 2, 'content_type' => 1,
//            ],
//            'sms_template' => ['enable' => false],
//            'trigger_on' => ['send' => 'immediately'],
//            'order_related' => true,
//        ],
//        [
//            'label' => 'Payment amount authorized',
//            'event' => 'order_amount_authorized',
//            'multipleAllow' => true,
//            'default' => false,
//            'email_template' => [
//                'title' => 'Order amount authorized', 'type' => 2, 'content_type' => 1,
//            ],
//            'sms_template' => ['enable' => false],
//            'trigger_on' => ['send' => 'immediately'],
//            'order_related' => true,
//        ],
//        [
//            'label' => 'Payment amount captured',
//            'event' => 'order_amount_captured',
//            'multipleAllow' => true,
//            'default' => false,
//            'email_template' => [
//                'title' => 'Order amount captured', 'type' => 2, 'content_type' => 1,
//            ],
//            'sms_template' => ['enable' => false],
//            'trigger_on' => ['send' => 'immediately'],
//            'order_related' => true,
//        ],
    ],
    /** @deprecated */
    'shippingMethod' => [
        ['shipping_location' => 'In Store Pickup', 'shipping_cost' => 0, 'id' => 1],
        ['shipping_location' => 'FedEx ( Overnight )', 'shipping_cost' => 20, 'id' => 2],
        ['shipping_location' => 'UPS Ground', 'shipping_cost' => 9.99, 'id' => 3],
    ],
    /** @deprecated */
    'fedex_service_list' => [
        1 => 'fedex_ground',
        2 => 'fedex_home_delivery',
        3 => 'fedex_2day',
        4 => 'fedex_2day_am',
        5 => 'fedex_express_saver',
        6 => 'fedex_standard_overnight',
        7 => 'fedex_priority_overnight',
        8 => 'fedex_first_overnight',
        9 => 'fedex_1_day_freight',
        10 => 'fedex_2_day_freight',
        11 => 'fedex_3_day_freight',
        12 => 'fedex_first_overnight_freight',
        13 => 'fedex_ground_international',
        14 => 'fedex_international_economy',
        15 => 'fedex_international_priority',
        16 => 'fedex_international_first',
        17 => 'fedex_international_economy_freight',
        18 => 'fedex_international_priority_freight'
    ],
    'delivery_method' => [
        1 => 'In-store',
        2 => 'Zone',
        3 => 'Location',
        4 => 'Fex',
        5 => 'UPS',
        6 => 'Standard'
    ],
    'delivery_flow_types' => [
        [
            'label' => 'Single address fulfillment',
            'value' => 1,
            'note' => 'drop-off & pickup from the same address.',
            'moves' => ['dropoff', 'pickup']
        ],
        [
            'label' => 'Dual address fulfilment',
            'value' => 2,
            'note' => 'Drop-off at one address & pickup from another address. Allows for the optional addition of distance charges for moves.',
            'moves' => ['dropoff', 'move', 'pickup']
        ],
        [
            'label' => 'Dual address fulfilment',
            'value' => 3,
            'note' => 'Drop-off at one address & pickup from another address. Allows for the addition of storage charges and/or optional move charges.',
            'moves' => ['dropoff', 'storage', 'move', 'pickup']
        ],
        [
            'label' => 'Dual address fulfilment',
            'value' => 4,
            'note' => 'Drop-off at one address & pickup from another address.',
            'moves' => ['dropoff', 'pickup']
        ]
    ],
    /** @deprecated */
    'paymentGatewayList' => [
        1 => 'CardConnect',
        2 => 'BoltCardConnect',
        3 => 'Stripe',
    ],
    'paymentGateways' => [
        'Stripe' => [
            1 => 'secret_key',
            2 => 'publishable_key',
        ],
        'Square' => [
            1 => 'access_key',
            2 => 'access_token',
            3 => 'location_id'
        ],
        'Authorize.Net' => [
            1 => 'apiLoginID',
            2 => 'transactionKey'
        ],
        'fitech' => [
            1 => 'merchantKey',
            2 => 'processorId'
        ],
        'goMerchant' => [
            1 => 'merchantKey',
            2 => 'processorId'
        ],
        'FreedomPay' => [
            1 => 'es_key',
            2 => 'store_id',
            3 => 'terminal_id',
            4 => 'is_live'
        ],
        'Atrium' => [
            1 => 'label',
            2 => 'client_name',
            3 => 'integration_name',
            4 => 'key',
            5 => 'authorization_tender',
            6 => 'charge_tender',
            7 => 'terminal_id'
        ],
//        'BoltCardConnect' => [
//            1 => 'ApiUrl',
//            2 => 'MerchantId',
//            3 => 'Username',
//            4 => 'Password',
//            5 => 'P2PE_ApiUrl',
//            6 => 'P2PE_AuthToken',
//        ],
//        'CardConnect' => [
//            1 => 'ApiUrl',
//            2 => 'MerchantId',
//            3 => 'Username',
//            4 => 'Password',
//        ],

//        'PayPal' => [
//            1 => 'clientId',
//            2 => 'secret'
//        ],
        'PAX' => [
            1 => 'IP_address'
        ]

    ],

    'shippingGatewayList' => [
        1 => 'Fedex',
        2 => 'UPS',
        3 => 'USPS'
    ],
    'shippingGatewaySettings' => [
        'Fedex' => [
            ['field_name' => 'api-key', 'label' => 'ShipEngine API Key', 'required' => true],
            ['field_name' => 'nickname', 'label' => 'Account Nickname', 'required' => true],
            ['field_name' => 'account_number', 'label' => 'FedEx Account No', 'required' => true],
            ['field_name' => 'first_name', 'label' => 'First Name', 'required' => true],
            ['field_name' => 'last_name', 'label' => 'Last Name', 'required' => true],
            ['field_name' => 'company', 'label' => 'Company', 'required' => false],
            ['field_name' => 'address1', 'label' => 'Address1', 'required' => true],
            ['field_name' => 'address2', 'label' => 'Address2', 'required' => false],
            ['field_name' => 'city', 'label' => 'City', 'required' => true],
            ['field_name' => 'state', 'label' => 'State', 'required' => true],
            ['field_name' => 'postal_code', 'label' => 'Zip', 'required' => true],
            ['field_name' => 'country_code', 'label' => 'Country Code', 'required' => true],
            ['field_name' => 'email', 'label' => 'Email', 'required' => true],
            ['field_name' => 'phone', 'label' => 'Phone', 'required' => true],
            ['field_name' => 'agree_to_eula', 'label' => 'FedEx EULA.', 'required' => true, 'type' => 'checkbox'],
        ],
        'UPS' => [
            ['field_name' => 'api-key', 'label' => 'ShipEngine API Key', 'required' => true],
            ['field_name' => 'nickname', 'label' => 'Account Nickname', 'required' => true],
            ['field_name' => 'account_number', 'label' => 'UPS Account No', 'required' => true],
            ['field_name' => 'account_country_code', 'label' => 'Account Country Code', 'required' => true],
            ['field_name' => 'account_postal_code', 'label' => 'Account Zip', 'required' => true],
            ['field_name' => 'title', 'label' => 'Title', 'required' => false],
            ['field_name' => 'first_name', 'label' => 'First Name', 'required' => true],
            ['field_name' => 'last_name', 'label' => 'Last Name', 'required' => true],
            ['field_name' => 'company', 'label' => 'Company', 'required' => false],
            ['field_name' => 'address1', 'label' => 'Address1', 'required' => true],
            ['field_name' => 'address2', 'label' => 'Address2', 'required' => false],
            ['field_name' => 'city', 'label' => 'City', 'required' => true],
            ['field_name' => 'state', 'label' => 'State', 'required' => true],
            ['field_name' => 'postal_code', 'label' => 'Zip', 'required' => true],
            ['field_name' => 'country_code', 'label' => 'Country Code', 'required' => true],
            ['field_name' => 'email', 'label' => 'Email', 'required' => true],
            ['field_name' => 'phone', 'label' => 'Phone', 'required' => true],
            ['field_name' => 'invoice', 'label' => 'I have received an invoice in the last 90 days', 'required' => true, 'type' => 'checkbox'],
            ['field_name' => 'agree_to_technology_agreement', 'label' => 'Agree.', 'required' => true, 'type' => 'checkbox'],
        ],
        'USPS' => [
            ['field_name' => 'api-key', 'label' => 'ShipEngine API Key', 'required' => true],
            ['field_name' => 'nickname', 'label' => 'Stamps.com Account Nickname', 'required' => true],
            ['field_name' => 'username', 'label' => 'Username', 'required' => true],
            ['field_name' => 'password', 'label' => 'Password', 'required' => true],
        ]
    ],

    'StripeTestMode' => false,
    'PayPalTestMode' => false,
    'AuthorizeDOTNETTestMode' => false,

    'availableStatus' => [
        0 => 'Return',
        1 => 'Reserve',
        2 => 'Pickup',
    ],

    'AttributeStatus' => [
        0 => 'Inactive',
        1 => 'Active',
        2 => 'Deleted'
    ],
    'denomination' => [
        'open' => [
            ['id' => 1, 'label' => '$100', 'value' => 100, 'type' => 1],
            ['id' => 2, 'label' => '$50', 'value' => 50, 'type' => 1],
            ['id' => 3, 'label' => '$20', 'value' => 20, 'type' => 1],
            ['id' => 4, 'label' => '$10', 'value' => 10, 'type' => 1],
            ['id' => 5, 'label' => '$5', 'value' => 5, 'type' => 1],
            ['id' => 6, 'label' => '$1', 'value' => 1, 'type' => 1],
            ['id' => 11, 'label' => '￠50', 'value' => 0.50, 'type' => 2],
            ['id' => 12, 'label' => '￠25', 'value' => 0.25, 'type' => 2],
            ['id' => 13, 'label' => '￠10', 'value' => 0.10, 'type' => 2],
            ['id' => 14, 'label' => '￠5', 'value' => 0.05, 'type' => 2],
            ['id' => 15, 'label' => '￠1', 'value' => 0.01, 'type' => 2],
        ],
        'close' => [
            ['id' => 1, 'label' => '$100', 'value' => 100, 'type' => 1],
            ['id' => 2, 'label' => '$50', 'value' => 50, 'type' => 1],
            ['id' => 3, 'label' => '$20', 'value' => 20, 'type' => 1],
            ['id' => 4, 'label' => '$10', 'value' => 10, 'type' => 1],
            ['id' => 5, 'label' => '$5', 'value' => 5, 'type' => 1],
            ['id' => 6, 'label' => '$1', 'value' => 1, 'type' => 1],
            ['id' => 11, 'label' => '￠50', 'value' => 0.50, 'type' => 2],
            ['id' => 12, 'label' => '￠25', 'value' => 0.25, 'type' => 2],
            ['id' => 13, 'label' => '￠10', 'value' => 0.10, 'type' => 2],
            ['id' => 14, 'label' => '￠5', 'value' => 0.05, 'type' => 2],
            ['id' => 15, 'label' => '￠1', 'value' => 0.01, 'type' => 2],
            ['id' => 8, 'label' => 'Checks', 'value' => 0, 'type' => 3],
        ]
    ],
    'permissions' => [
        'users' => ['none', 'read', 'write'],
        'inventory' => ['none', 'read', 'write', 'pricing_none', 'pricing_read', 'pricing_write'],
        'order' => ['none', 'read', 'write'],
        'order_checklist' => ['none', 'read', 'write', 'manage'],
        'pos' => ['access', 'enable_new_order_button',
            'payment_view' => ['view_all_gateways', 'view_promise_to_pay_gateways', 'view_real_gateways'], // 1
            'payment_add',// 2
            'payment_refund', // 3
            'order_details_order_status','order_details_add_item', 'order_details_add_charge', 'order_details_change_status',
            'order_details_edit_item', 'order_details_delete_item', 'order_details_rental_date_edit', // 5
            'tax_exempt', // 2
            'customer_display', 'customer_clear_window', // 3
            'pickup_change_status', // 4
            'sales_report',
            'disable_cancel_order'
        ],
        'business_settings' => ['store_configuration', 'order_options', 'fulfillment', 'hours_&_holidays', 'locations', 'payment_methods', 'paperwork', 'sales_tax', 'additional_charge_configuration', 'commissions'],
        'website_settings' => ['custom_URL', 'Design', 'Integrations'],
        'customers' => ['none', 'read', 'write'],
        'assets' => ['none', 'read', 'write', 'transfer', 'assign'],
        'show_report' => ['none', 'prep_report', 'view_all']
    ],
    'redirect_urls' => [
        'Dashboard' => 'admin/dashboard',
        'Inventory' => 'admin/inventory',
        'Asset Listing' => 'admin/inventory/product-asset',
        'Reservations' => 'admin/reservations/all',
        'Quotes' => 'admin/reservations/quote',
        'POS' => 'cash-register/dashboard',
        'Shipping' => 'admin/reservations/shipping-orders',
        'Delivery Jobs' => 'admin/reservations/shipping-orders/delivery-jobs',
        'Tours' => 'admin/reservations/events',
    ],
    'UserStatus' => [
        0 => 'Pending',
        1 => 'Active',
        2 => 'Inactive',
        3 => 'Delete',
        4 => 'Permanent Delete'
    ],
    'ProductStatus' => [
        1 => 'Active',
        6 => 'Hidden',
        2 => 'Inactive',
        3 => 'Out of Stock',
        4 => 'Faulty',
        5 => 'Deleted'
    ],
    'UserTypes' => [
        1 => 'suadmin',
        2 => 'company admin',
        3 => 'admin',
        4 => 'cash register',
    ],
    'AddressTypes' => [
        1 => 'Home Address',
        2 => 'Work Address'
    ],
    'ImageStatus' => [
        1 => 'Additional',
        2 => 'Featured',
        3 => 'Inactive'
    ],

    'quoteStatus' => [
        1 => 'Draft',
        2 => 'Archived',
        3 => 'Rejected',
        4 => 'Unconfirmed',
        5 => 'Confirmed',
    ],

//    'orderStatus' => [
//        1 => 'Archived',
//        2 => 'Pending',
//        3 => 'Paid',
//        4 => 'Rejected',
//        5 => 'With Customer',
//        6 => 'Returned',
//        7 => 'Reserved',
//        8 => 'Shipped',
//        9 => 'Unpaid',
//        //  10 => 'Quote',
//        11 => 'Completed',
//        12 => 'Paid Other',
//        13 => 'Processing'
//    ],

    'orderStatus' => [
        ['id' => 1, 'label' => 'Archived', 'serial_no' => 8],
        ['id' => 2, 'label' => 'Pending', 'serial_no' => 1,
            'child' => [
                ['id' => '2-1', 'label' => 'Attention'],
                ['id' => '2-2', 'label' => 'No Cap'],
                ['id' => '2-3', 'label' => 'No Regulator']
            ]
        ],
        ['id' => 3, 'label' => 'Processing', 'serial_no' => 2],
        ['id' => 4,
            'label' => 'Order prep',
            'child' => [
                ['id' => '4-1', 'label' => 'Delivery prep'],
                ['id' => '4-2', 'label' => 'Shipping prep'],
                ['id' => '4-3', 'label' => 'Pickup prep']
            ]
            , 'serial_no' => 3
        ],
        ['id' => 5, 'label' => 'With customer', 'serial_no' => 5],
        ['id' => 6, 'label' => 'Returned', 'serial_no' => 6],
        [
            'id' => 7,
            'label' => 'Fulfillment',
            'serial_no' => 4,
            'child' => [
                ['id' => "6-1", 'label' => 'Out for delivery'],
                ['id' => '6-2', 'label' => 'Shipped'],
                ['id' => '6-3', 'label' => 'Awaiting pickup']
            ]
        ],
        ['id' => 13, 'label' => 'Partially Shipped', 'serial_no' => 13],
        ['id' => 11, 'label' => 'Completed', 'serial_no' => 7],
        ['id' => 12, 'label' => 'Paid Other', 'serial_no' => 9],
    ],

    'paymentType' => [
        1 => 'Online',
        2 => 'Offline'
    ],
    'paymentStatus' => [
        1 => 'Paid',
        2 => 'Unpaid',
        3 => 'Partial Paid'
    ],
    'paymentContent' => [
        1 => 'Online store – Card entry',
        2 => 'In-store – Card entry',
        3 => 'In-store – Card Swipe',
        4 => 'In-store – Cash',
        5 => 'In-store – Additional',
        6 => 'Online store – Additional',
        7 => 'POS - Atrium'
    ],
    'orderSource' => [
        1 => 'Online',
        2 => 'Instore',
        3 => 'POS',
        4 => 'WP Plugin',
        5 => 'API'
    ],
    'onlinePaymentMethod' => [
        1 => 'Paypal',
        2 => 'Visa Card',
        3 => 'Master Card',
    ],
    'offlinePaymentMethod' => [
        1 => 'Unpaid',
        2 => 'Paid in Cash',
        3 => 'Paid in Credit',
        4 => 'Partial payment',
        5 => 'Bill Account',
        6 => 'Credit Card'
    ],
    'message' => [
        'missing_param' => 'Please give all required field',
        'request' => 'Bad Request',
        'save' => 'Save  failed',
        'method' => 'Method Not Allowed',
        'found' => 'Not found!',
        'wrong' => 'Something wrong!',
        'permission' => 'Access denied!',
        'success' => 'Successfully saved!',
        'deleted' => 'Successfully deleted!',
    ],
    'banner' => [
        (object)array('id' => 'banner-images', 'tag' => 'Homepage Banner Images', 'multi' => true, 'type' => 'banner', 'image_size' => '1950x650px recommended for default layout'),
        (object)array('id' => 'banner-background', 'tag' => 'Homepage Banner Background', 'multi' => false, 'type' => 'banner', 'image_size' => '1950x650px recommended '),
        (object)array('id' => 'product-list-banner', 'tag' => 'Product List Banner', 'multi' => false, 'type' => 'banner', 'image_size' => '1950x150px recommended '),
        (object)array('id' => 'cart-banner', 'tag' => 'Cart Banner', 'multi' => false, 'type' => 'banner', 'image_size' => '1950x150px recommended '),
        (object)array('id' => 'pages-banner', 'tag' => 'Pages Banner', 'multi' => false, 'type' => 'banner', 'image_size' => '1950x150px recommended '),
    ],
    'pages' => [
        array('slug' => 'terms-and-conditions', 'name' => 'Terms and Conditions', 'contents' => (object)array('heading' => '', 'content' => 'Customers must agree to your terms & conditions.'), 'status' => 1),
        array('slug' => 'about', 'name' => 'About Us', 'contents' => (object)array('heading' => '', 'content' => ''), 'status' => 1),
        array('slug' => 'contact', 'name' => 'Contact Us', 'contents' => (object)array('heading' => '', 'content' => '', 'map_location' => [(object)['lat' => '', 'lng' => '']]), 'status' => 1)

    ],
    'navigations' => [
        array('content_url' => 'about', 'label' => 'About Us', 'content_type' => 'Page', 'type' => 'header', 'status' => 1, 'sequence_no' => 2),
        array('content_url' => 'contact', 'label' => 'Contact Us', 'content_type' => 'Page', 'type' => 'header', 'status' => 1, 'sequence_no' => 3),
        array('content_url' => 'products-list', 'label' => 'Catalog', 'content_type' => 'Page', 'type' => 'header', 'status' => 1, 'sequence_no' => 1),
        array('content_url' => 'blog', 'label' => 'Blog', 'content_type' => 'Page', 'type' => 'header', 'status' => 1, 'sequence_no' => 4)


    ],
    'content_tags' => [
        'banner' => 'Banner',
        'banner_background' => 'Banner Background',
        'grid_card' => 'Grid Card',
        'home_page_content' => 'Home Page content',
        'link' => 'Links',
        'address' => 'Address'
    ],
    'content_type' => [
        'text' => 'Text',
        'image' => 'Image',
        'content' => 'Content',
        'link' => 'Link'
    ],
    'site_specific_contents' => [
        'general' => [
            'address' => 'Store Address',
            'business_name'=>'',
            'phone' => 'xxx-xxx-xxxx',
            'email' => '<EMAIL>',
            'contact_title' => 'Contact Information',
            'home_url' => '/',
            'continue_shopping_url' => 'products-list',
            'favicon' => 'https://via.placeholder.com/60',

            'lbl_change_avatar' => 'Change Avatar',
            'btn_send_message' => 'Send Message',
            'lbl_billing_payment' => 'Billing and Payment',
            'btn_logout' => 'Log out',
            'lbl_job' => 'Job',
            'lbl_jobs' => 'Jobs',
            'lbl_no_order_found' => 'No order found',
            'lbl_feedback' => 'Feedback',
            'btn_send' => 'Send',
            'btn_upload' => 'Upload',
            'btn_reset' => 'Reset',
            'lbl_upload_image' => 'Upload Image',
            'lbl_maximum_file_size' => 'Maximum file size',
            'txt_drop_files_here' => 'Drop files here or click to upload',
            'lbl_file_list' => 'File List',
        ],
        'product_details' => [
            'page_title' => 'Product View',
            'package_title' => 'Package View',
            'page_breadcrumb' => 'Product View',
            'title_product_list' => 'Product List',
            'quantity' => 'Quantity',
            'buy' => 'Buy',
            'rent' => 'Rent',
            'all' => 'All',
            'available' => 'Available',
            'start_date' => 'Pick Date',
            'end_date' => 'End Date',
            'rent_date' => 'Rental date range',
            'add_to_cart' => 'ADD TO CART',
            'product_id' => 'Product ID',
            'description_title' => 'PRODUCT DESCRIPTION',
            'related_title' => 'RELATED PRODUCT',
            'not_available_text' => 'This product is not available',
            'not_available_package_text' => 'This package is not available',
            'exact_select_duration' => 'Select Duration',
            'exact_select_duration_placeholder' => '-Select-',
            'exact_select_start_time' => 'Select Start time',
            'exact_select_start_time_placeholder' => '-Select-',
            'exact_due_date' => 'Due Date',
            'title_package_includes' => 'Package includes',
            'lbl_today' => 'Today',
            'lbl_tomorrow' => 'Tomorrow',
            'lbl_recurring_pricing' => 'Rental Payments',
            'billed_weekly_at' => 'Billed weekly at',
            'billed_monthly_at' => 'Billed monthly at',
            'billed_at_a_rate' => 'Billed at a rate of',
            'lbl_min' => 'Min',
            'lbl_max' => 'Max',
            'btn_clear' => 'Clear',
            'btn_submit' => 'Submit',
            'btn_product_view' => 'Product View',
            'btn_view_details' => 'View Details',
            'lbl_sort_by' => 'Sort By',
            'lbl_sort_default' => 'Default',
            'lbl_sort_product_name_asc' => 'Product name A-Z',
            'lbl_sort_product_name_desc' => 'Product name Z-A',
            'lbl_sort_product_rent_asc' => 'Rental price low to high',
            'lbl_sort_product_rent_desc' => 'Rental price high to low',
            'lbl_sort_product_price_asc' => 'Sale price low to high',
            'lbl_sort_product_price_desc' => 'Sale price high to low',
            'lbl_no_product' => '',
            'lbl_select_location' => 'This product not available for this location. Please change your location from menu',
            'txt_reduce_amount' => 'Now just %amount% (save %percent%)',
            'lbl_product_reduced' => 'Price Reduced',
            'txt_reduce_valid_date' => 'Hurry-sales ends 20 November!',
            'txt_rental_time_adjustment' => 'Rental time(s) were adjusted for store open hours',
            'lbl_rental_start_date' => 'Select rental start date',
            'lbl_select_end_time' => 'Select end time',
            'lbl_rental_end_date' => 'Select rental end date',
            'lbl_rental_date_range' => 'Rental date range',
            'lbl_delivery_option' => 'Select delivery option',
            'lbl_fulfillment_option' => 'Select Fulfillment option',
            'lbl_billing_at_a_rate_of' => 'Billing at a rate of',
            'txt_product_not_in_cart' => 'Following product is not added in cart',
            'btn_edit_date' => 'Edit Dates',
            'lbl_minimum_rental' => 'Minimum Rental',
            'txt_all_rental_subjected_to_rental_periods' => 'All rentals are subjected to a minimum rental period',
            'txt_select_product_addon_quantity' => 'Select product add-on quantities',
            'txt_product_not_added_to_cart' => 'Product has not been added to cart',
            'txt_required_options_not_selected' => 'Required options are not selected',
            'txt_finish_order_for' => 'Finish the order for',
            'txt_before_changing_locations' => 'before changing locations',
            'txt_band_price' => '%band_price% for %band_name%',
        ],
        'home_page_promotion' => [
            'title' => 'BUY STYLISH HAND',
            'text' => 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quo blanditiis odio. consectetur adipisicing elit. Quo blanditiis odio Lorem ipsum dolor sit amet, consectetur adipisicing elit.',
            'btn_text' => 'Start Shopping',
            'btn_link' => '#',
            'offer_text' => 'offer 10%',
            'image' => 'https://via.placeholder.com/600'
        ],
        'social' => [
            'social_title' => 'Follow Us On Social',
            'facebook' => '',
            'X' => '',
            'linkedin' => '',
            'googlePlus' => '',
            'instagram' => '',
            'pinterest' => ''
        ],
        'order' => [
            'lbl_order_list' => 'Order list',
            'lbl_total_price' => 'Total Price',
            'lbl_order_details' => 'Order Details',
            'lbl_summary' => 'Summary',
            'lbl_payment_status' => 'Payment Status',
            'lbl_order_date_time' => 'Order Date & Time',
            'lbl_order_created_from' => 'Order created from',
            'lbl_delivery_charge_total' => 'Delivery Charge Total',
            'lbl_item_discount' => 'Item discount',
            'lbl_coupon_discount' => 'Coupon discount',
            'lbl_total_discount' => 'Total Discount',
            'lbl_grand_total' => 'Grand Total',
            'lbl_billing_shipping_details' => 'Billing & Shipping details',
            'lbl_additional_info' => 'Additional Info',
            'btn_download_file' => 'Download File',
            'lbl_shipping_info' => 'Shipping Info',
            'lbl_inventory_items' => 'Inventory Items',
            'btn_add_items' => 'Add Item',
            'lbl_rental_dates' => 'Rental Dates',
            'lbl_exchange_items' => 'Exchange Items',
            'lbl_image' => 'Image',
            'lbl_description' => 'Description',
            'lbl_requested_exchange_items' => 'Requested Exchange items',
            'lbl_requested_items' => 'Requested items',
            'lbl_returned_items' => 'Returned items',
            'lbl_item_id' => 'Item id',
            'btn_pay_due_amount' => 'Pay Due Amount',
            'lbl_total_amount' => 'Total Amount',
            'lbl_due_amount' => 'Due Amount',
            'lbl_payment_type' => 'Payment Type',
            'lbl_paid_amount' => 'Paid Amount',
            'lbl_no_payment_found' => 'No Payment Found',
            'lbl_notes' => 'Notes',
            'btn_add_note' => 'Add Note',
            'lbl_shipping_address' => 'Shipping Address',
            'lbl_accept_quote' => 'Accept Quote',
            'lbl_payment_history' => 'Payment History',
            'btn_deposit_amount' => 'Deposit Amount',
            'lbl_pending' => 'Pending',
            'lbl_order_source' => 'Order Source',
            'lbl_total_quantity' => 'Total Quantity',
            'lbl_pickup_store' => 'Pickup Store',
            'lbl_delivery_address' => 'Delivery Address',
            'lbl_per_month' => 'Per month',
            'txt_view_payment' => 'View Payment',
            'txt_view_details' => 'View details',
            'txt_download_pdf' => 'Download PDF',
            'txt_cancel_order' => 'Cancel order',
            'txt_send_message' => 'Send Message',
            'txt_delete' => 'delete',
            'txt_enter_note' => 'Enter note',
            'txt_choose_file' => 'Choose file',
            'btn_choose_file' => 'Choose file',
            'txt_sales_tax_included' => '(Sales Tax Included)',
            'txt_calculated_in_next_step' => 'Calculated in the next step',
            'btn_input_amount' => 'Input Amount',
            'btn_qty' => 'Qty',
            'txt_accept_terms_condition' => 'Accept Terms & Conditions to continue',
            'txt_select_shipping_method' => 'Please select shipping method',
            'txt_get_delivery_cost' => 'please get delivery cost',
            'txt_provide_signature' => 'Please provide a signature first',
            'btn_proceed' => 'Proceed',
            'btn_close' => 'Close',
            'txt_related_product' => 'Related Product',
            'txt_send_order_message' => 'Send message for order',
            'txt_has_been_canceled' => 'has been canceled',
            'txt_something_wrong_with_order' => 'Something wrong! Order',
            'txt_not_canceled' => 'has not been canceled',
            'txt_order' => 'Order',
            'lbl_pick_date' => 'Pick Date',
            'btn_process_return' => 'Process Return',
            'lbl_item_or_asset' => 'Item/Asset',
            'lbl_return_quantity' => 'Return Quantity',
            'lbl_return_status' => 'Return Status',
            'lbl_return_request' => 'Return Request',
            'btn_return_items' => 'Return items',
            'txt_items_with_customer' => 'Items With Customer',
            'txt_return_qty_cannot_be_greater' => 'Return Qty can not be greater than Remain Qty!',
            'txt_return_qty_cannot_be_zero_or_negative' => 'Return Qty can not zero or negative value!',
            'txt_select_at_least_one_row' => 'Select at least one row!',
            'txt_product_has_been_deleted' => 'Product has been deleted',
            'txt_you_can_add_up_to' => 'You can add up to',
            'txt_items' => 'items',
            'txt_quantity_successfully_updated' => 'Quantity successfully updated',
            'txt_information_has_been_updated' => 'Information has been updated',
            'txt_payment_has_been_deleted' => 'Payment has been deleted',
            'txt_payment_has_not_been_deleted' => 'Something wrong! Payment has not been deleted',
            'lbl_shipment_id' => 'Shipment ID',
            'txt_on_hand' => 'On Hand',
            'txt_item_on_hand' => 'Item On Shipment',
            'btn_return_again' => 'Return Again',
            'btn_missing' => 'Missing',
            'lbl_return_status' => 'Return Status',
            'txt_returned_items_note' => 'Returned items note',
        ],
        'cart' => [
            'title' => 'Cart View',
            'page_breadcrumb' => 'Cart View',
            'th_product' => 'Product',
            'th_unit_price' => 'Unit Price',
            'th_price' => 'Price',
            'th_sales_tax' => 'Sales Tax',
            'th_quantity' => 'Quantity',
            'th_subtotal' => 'Subtotal', //customer portal dynamic
            'th_order_item' => 'Item #',
            'th_address' => 'Address',
            'th_order_id' => 'Order ID',
            'th_status' => 'Status',
            'th_action' => 'Action', //customer portal dynamic end
            'available' => 'Available',
            'txt_coupon' => 'Enter Coupon Code',
            'btn_coupon' => 'Apply Coupon',
            'select_shipping_lbl' => 'Shipping',
            'select_location_lbl' => 'Select Location',
            'btn_checkout' => 'Proceed to checkout',
            'btn_checkout_quote' => 'Make quote',
            'lbl_cart_total' => 'Cart Totals',
            'lbl_shipping' => 'Delivery',
            'lbl_delivery_tax' => 'Delivery Tax',
            'lbl_discount' => 'Discount',
            'lbl_tax' => 'Tax',
            'lbl_total_deposite' => 'Deposit Amount',
            'lbl_additional_charge' => 'Optional Services',
            'lbl_taxes_and_fees' => 'Taxes & Fees',
            'lbl_total' => 'Total',
            'rental_start' => 'Rental Start',
            'rental_end' => 'Rental End',
            'rent_date' => 'Rental Dates',
            'lbl_optional_add-on' => 'Add-on Products',
            'lbl_consider_optional_services' => 'Consider these optional services',
            'btn_continue' => 'Continue Shopping',
            'lbl_next_step' => 'Calculated in the next step',
            'lbl_empty_cart' => 'Your cart is empty',
            'lbl_from' => 'From',
            'lbl_to' => 'To',
            'lbl_my_cart' => 'My Cart',
            'btn_cancel' => 'Cancel',
            'coupon_applied' => 'Coupon applied',
            'vendor_discount' => 'Vendor Discount',
            'lbl_tax_includes_sub_total' => '(incl. tax)',
            'lbl_tax_includes_total' => 'includes',
            'add_to_cart_redirect_url' => '',
            'lbl_drop_off_address' => 'Drop off Address',
            'lbl_pickup_address' => 'Pickup Address',
            'lbl_drop_off_pickup' => 'Drop off, pickup',
            'lbl_drop_off_move_pickup' => 'Drop off, Move, Pickup',
            'lbl_drop_off_storage_pickup' => 'Drop off, Storage, Pickup',
            'max_distance_limit_error_msg' => '{{flow}} crosses max delivery distance',
            'delivery_limit_msg_warning' => 'Delivery may not be available on this day.',
            'delivery_limit_msg_error' => 'Our delivery slots for this day has been fulfilled, not taking new orders.',
            'lbl_automated_discount' => 'Automated Discount',
            'txt_order_testing' => 'Orders are for testing only. Order will not be fulfilled',
            'txt_something_went_wrong' => 'Something went wrong!!! Please try again',
            'txt_coupon_accepted' => 'Coupon Accepted',
            'txt_invalid_coupon' => 'Invalid Coupon',
            'txt_oops' => 'Oops!',
            'txt_no_product_found_for_search' => 'No Product found for your search.',
            'txt_quantity_updated' => 'Quantity updated',
            'txt_change_date' => 'Change Date',
            'txt_change_date_for_your_order' => 'The rental date is changed for your order.'
        ],

        'checkout_info' => [
            'step_one' => 'Your Info',
            'step_two' => 'Fulfillment',
            'step_three' => 'Payment',
            'step_four' => 'Complete Order',
            'title' => 'Checkout',
            'page_breadcrumb' => 'Checkout',
            'title_contact' => 'Contact Information',
            'btn_back_to_cart' => 'Back to Cart',
            'btn_back' => 'Back',
            'lbl_first_name' => 'First Name',
            'lbl_lastname' => 'Last Name',
            'lbl_mobile' => 'Mobile Number',
            'lbl_email' => 'Email Address',
            'title_billing' => 'Address',
            'lbl_address_line_1' => 'Address Line 1',
            'lbl_address_line_2' => 'Address Line 2',
            'lbl_company_name' => 'Company Name',
            'lbl_city' => 'City',
            'lbl_state' => 'State',
            'lbl_zipcode' => 'Zipcode',
            'lbl_country' => 'Country',
            'lbl_special_comments' => 'Special Instructions/Comments',
            'lbl_special_request' => 'Special Requests',
            'title_shipping' => 'Fulfillment',
            'title_pickup_option' => 'Pickup',
            'title_delivery_option' => 'Delivery',
            'title_shipping_option' => 'Shipping',
            'lbl_shipping_name' => 'Name',
            'lbl_shipping_first_name' => 'First Name',
            'lbl_shipping_last_name' => 'Last Name',
            'lbl_shipping_mobile' => 'Mobile Number',
            'lbl_shipping_address_line_1' => 'Address Line 1*',
            'lbl_shipping_address_line_2' => 'Address Line 2',
            'lbl_shipping_city' => 'City*',
            'lbl_shipping_zipcode' => 'Zipcode*',
            'lbl_same_as_billing' => 'Same As Billing Address',
            'title_additional' => 'Additional Information',
            'title_custom_checkout' => 'Custom checkout information ',
            'lbl_driving_license' => 'Driving License',
            'lbl_alt_mobile' => 'Alternative Phone Number',
            'lbl_select_shipping' => 'Shipping',
            'lbl_select_location' => 'Select Location',
            'lbl_signature' => 'Signature',
            'lbl_required' => 'is required',
            'lbl_clear' => 'Clear',
            'lbl_undo' => 'Undo',
            'terms_and_condition' => "I have read and agree with the",
            'terms_and_condition_link_label' => "terms & conditions",
            'btn_continue' => 'Continue',
            'btn_get_delivery_cost' => 'Get delivery cost',
            'btn_get_shipping_method' => 'Get shipping method',
            'title_order_summary' => 'Your Order Summary',
            'title_select_shipping_method' => 'Select shipping method',
            'btn_quote_accept' => 'Accept & Pay',
            'lbl_welcome_to_login' => 'Welcome to %storename% (Sign in)',
            'lbl_delivery_cost' => 'Delivery Cost',
            'lbl_google_pay_success' => "Please click on the <b>Place Order</b> button to complete the order.",
            'lbl_checkout_text' => '',
            'lbl_global_checkout_error_msg' => "Please correct the highlighted issues to complete your orders",
            'lbl_customer_name' => 'Customer Name',
            'lbl_billing_address' => 'Billing Address',
            'lbl_status' => 'Status',
            'btn_add' => 'Add',
            'btn_update' => 'Update',
            'lbl_order_quote' => 'Order Quote',
            'txt_welcome_to' => 'Welcome to',
            'txt_sign_in' => '(Sign in)',
            'txt_select_payment_gateway' => 'Please select one payment gateway',
            'txt_driving_license_required' => 'Driving License is Required',
            'txt_product_not_available' => 'Sorry, Product not available',
            'txt_address_out_of_delivery_area' => 'Your address is outside of our delivery area. Please contact us to make other arrangements',
        ],
        'checkout_payment' => [
            'title' => 'Checkout',
            'page_breadcrumb' => 'Checkout',
            'title_credit_card' => 'Credit Card',
            'lbl_card_details' => 'Card Details',
            'lbl_amount_to_pay' => 'Amount to pay',
            'lbl_card_number' => 'Card Number',
            'lbl_name' => 'Name on Card',
            'lbl_expiration_data' => 'Expiration Date',
            'lbl_cvv' => 'CVV Number',
            'btn_continue' => 'Submit',
            'btn_back' => 'Back',
            'btn_back_to_cart' => 'Back to Cart',
            'btn_place_order' => 'Place Order',
            'btn_place_quote' => 'Place Quote',
            'lbl_minimum_payment' => 'A %amount% down payment is required to secure your reservation. Please choose an option and pay to proceed.',
            'lbl_minimum_payment_placeholder' => '%amount% is the minimum payment.',
            'lbl_pay_full_amount' => 'Pay full amount due',
            'lbl_pay_min_amount' => 'Pay %amount% now',
            'lbl_payment_options' => 'Payment options',
            'lbl_thanks_confirm_order' => 'Thank you for your order',
            'lbl_thanks_confirm_quote' => 'Thank you for your quote',
            'lbl_success_confirm_order' => 'Thank you for your order',
            'lbl_cancel_confirm_order' => 'Your order has been placed and remains unpaid.  Payment will be required at pickup.',
            'lbl_note' => 'Note',
            'lbl_create_account_on_order' => 'Create an account to make ordering faster in the future',
            'btn_download_receipt' => 'Download Receipt',
            'btn_download_quote' => 'Download quote',
            'lbl_payments' => 'Payments',
            'lbl_total_paid' => 'Total paid',
            'lbl_amount_due' => 'Amount due',
            'btn_pay_in_full' => 'Pay in full',
            'btn_pay_down_payment' => 'Pay down payment',
            'add_to_calendar_button_lbl' => 'Add to calendar',
            'view_order_details_button_lbl' => 'View Details',
            'after_order_page_footer' => '',
            'lbl_payment_agreement' => 'I agree to make rental payments per the rental agreements',
            'lbl_customer_information' => 'Customer Information',
            'lbl_select_existing_customer' => 'Select Existing Customer',
            'lbl_first_name_required' => 'First name required',
            'lbl_email_required' => 'Email required',
            'lbl_enter_correct_email' => 'Please enter correct email',
            'lbl_address_required' => 'Address required',
        ],
        'billing_payment' => [
            'btn_upgrade' => 'Upgrade',
            'btn_get_the_plan' => 'Get the plan',
            'btn_selected' => 'Selected',
            'btn_cancel' => 'Cancel',
            'lbl_my_billing_summary' => 'My Billing Summary',
            'lbl_change_default_card' => 'Change Default Card',
            'lbl_date' => 'Date',
            'lbl_amount' => 'Amount',
        ],
        'meta_contents' => [
            'home_page_title' => 'RentMy – The #1 Equipment Rental Software',
            'home_page_description' => '',
            'home_page_keywords' => '',
        ],
        'footer' => [
            'contact_us_title' => 'Contact Us',
            'quick_links_title' => 'Quick Links',
            'more_links_title' => 'More Links',
            'newsletter_title' => 'Newsletter',
            //'newsletter_text' => 'Get news and receive 20% off for your next buy!',
            'newsletter_text' => '',
            'newsletter_btn' => 'Subscribe',
            'newsletter_textbox_placeholder' => 'Your Email Address',
            'copyright_text' => "&copy; 2025 RentMy | Developed by <a href='https://rentmy.co'>RentMy.co</a>",
            'chk_hide_footer' => true,
            'chk_hide_footer_contact_us' => true,
            'chk_hide_footer_quick_links' => true,
            'chk_hide_footer_more_link' => true,
            'chk_hide_footer_newsletter' => true,
            'title_social_links' => 'Social Links'
        ],
        'grid' => [
            'header' => '',
            'details' => ''
        ],
        'contactus' => [
            'contact_form_success_message' => 'Thank you for contacting us.  We will be in touch shortly.',
            'contact_form_fail_message' => 'The message may not have sent.  Please call us.',
            'first_name' => 'First Name',
            'last_name' => 'Last Name',
            'phone' => 'Phone',
            'email' => 'Email',
            'Message' => 'Message',
            'btn_submit' => 'Submit'
        ],
        'customer_portal' => [
            'login_intro' => '',
            'lbl_login' => 'Log in',
            'lbl_sso_login' => 'SSO Log in',
            'lbl_signup' => 'Sign up',
            'title_login' => 'Login',
            'title_register' => 'Register',
            'lbl_forgot_password' => 'Forgot password',
            'lbl_first_name' => 'First name',
            'lbl_last_name' => 'Last name',
            'lbl_email' => 'Email',
            'lbl_company_name' => 'Company name',
            'lbl_contact_no' => 'Contact number',
            'lbl_password' => 'Password',
            'lbl_confirm_password' => 'Confirm password',
            'lbl_address_line1' => 'Address line1',
            'lbl_address_line2' => 'Address line2',
            'lbl_city' => 'City',
            'lbl_state' => 'State',
            'lbl_zipcode' => 'Zip code',
            //customer portal dynamic
            'lbl_country' => 'Country',
            'lbl_profile_info' => 'Profile Information',
            'lbl_profile' => 'Profile',
            'btn_edit_profile' => 'Edit profile',
            'lbl_change_password' => 'Change Password',
            'lbl_order_history' => 'Order History',
            'lbl_address' => 'Address',
            'lbl_add_address_type' => "Add address type (eg: 'Primary/Office/Home')",
            "btn_add_address" => "Add Address",
            "lbl_add_address" => "Add new address",
            "btn_edit_address" => "Update Address",
            "lbl_edit_address" => "Update address",
            "lbl_old_password" => "Old password",
            "lbl_new_password" => "New password",
            "btn_submit" => "Submit",
            "btn_return" => "Return",
            "btn_update" => "Update",
            "title_reset_password" => "Reset Password",
            "subtitle_reset_password" => "Enter your email to reset your password",
            "title_change_password" => "Change Password",
            "subtitle_change_password" => "Enter your New Password",
            "lbl_ready_to_go" => "You're ready to go!",
            "lbl_reset_password" => "Reset password!",
            "btn_activate_account" => "Activate account",
            "btn_change_password" => "Change Password",
            'lbl_job_list' => 'Job List',
            'lbl_catalog' => 'Catalog',
            'lbl_last_name_required' => 'Last name is required',
            'lbl_email_address' => 'Email Address',
            'lbl_email_invalid' => 'Email is invalid',
            'lbl_company' => 'Company',
            'txt_old_password_is_required' => 'Old password is required.',
            'txt_new_password_is_required' => 'New Password is required.',
            'txt_preferred_subscription' => 'Preferred Subscription',
            // vendor
            "lbl_create_your_account" => "Create Your Account",
            "lbl_partner_signup" => "Partner Sign Up",
            "lbl_login_to_your_account" => "Login to Your Account",
            "lbl_partner_login" => "Partner Login",
            "lbl_thank_you" => "Thank You",
            "partner_registration_thank_you_message" => "for registering as a Partner for {{store_name}}! Your registration is confirmed. We look forward to receiving your full participation details and showcase information soon.",
            'txt_already_registered' => 'Already Registered?',
            'txt_login_here' => 'Login Here',
            'btn_back' => 'Back',
        ],
        'others' => [
            'about_us' => 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
            'product_list_buy_now_for' => 'Buy now for',
            'product_list_starting_at' => 'Starting at',
            'product_list_per' => 'per',
            'product_list_for' => 'for',
            'product_list_price_filter' => 'Price',
            'product_list_type_fileter' => 'Type',
            'product_list_filter_reset_btn' => 'Reset',
            'product_list_filter_label' => 'Filter',
            'product_list_category_label' => 'Category',
            'home_page_category_label' => 'Categories',
            'product_list_no_product' => 'No Product Found',
            'lbl_home' => 'Home',
            'lbl_products' => 'Products',
            'lbl_hour' => 'hour',
            'lbl_hours' => 'hours',
            'lbl_day' => 'day',
            'lbl_days' => 'days',
            'lbl_week' => 'week',
            'lbl_weeks' => 'weeks',
            'lbl_month' => 'month',
            'lbl_months' => 'months',
            'lbl_lease' => 'Lease',
            'lbl_cart_total' => 'Total',
            'btn_view_cart' => 'View Cart',
            'btn_checkout' => 'Checkout',
            'btn_download' => 'Download receipt',
            'chk_gdpr' => false,
            'gdpr_content' => "Cookies help us customize RentMy for you, and some are necessary to make our site work. Cookies also let us create a better online experience for you on our site. Of course, you‘re in control. <button class='btn btn-xs cookie-btn'>Accept </button> to dismiss the message.",
            'title_home_page_featured' => 'Featured Products',
            'btn_search' => 'Search',
            'lbl_about_us' => 'About Us',
            'lbl_contact_us' => 'Contact Us',
            'lbl_terms_conditions' => 'Terms & Conditions',
            'txt_placeholder_select' => '--Select--',
            'txt_displaying' => 'Displaying',
            'txt_records' => 'records',
            'txt_to' => 'to',
            'txt_of' => 'of',
            'txt_confirm_delete' => 'Are you sure you want to delete?',
            'lbl_no_item_found' => 'No Item Found',
            'lbl_no_plan_found' => 'No Plan Found',
            'lbl_per' => 'per',
            'lbl_yes' => 'Yes',
            'lbl_no' => 'No',
            'lbl_current_balance' => 'Current balance',
            'lbl_change_plan_confirmation' => 'Do you want to change this plan?',
            'lbl_cancel_plan_confirmation' => 'Are you sure cancel this plan?',
            'lbl_yesterday' => 'Yesterday',
            'lbl_enter_a_location' => 'Enter a location',
            'lbl_create_new' => 'Create New',
            'btn_browse' => 'Browse',
            'txt_no_file_chosen' => 'No file chosen',
            'txt_confirm_delete_address' => 'Do you want to delete this address?',
            'btn_subscribe' => 'Subscribe',
            'lbl_card_holder_name' => 'Card holder Name',
            'txt_card_holder_name' => 'Card holder Name ',
            'txt_create_account' => 'Create an account to make ordering faster in the future',
            'txt_stripe_payment_error_message' => 'Stripe payment intend update error',
            'txt_required' => 'required',
            'txt_first_name_is' => 'First Name is',
            'txt_last_name_is' => 'Last Name is',
            'txt_valid_email_error_message' => 'Please enter a valid email address',
            'txt_password_is' => 'Password is',
            'txt_password_must_have_message' => 'Password must have at least',
            'txt_six' => '6',
            'txt_character' => 'character',
            'txt_confirm_password_required' => 'Confirm Password is required',
            'txt_password_does_not_match' => 'Passwords does not match',
            'txt_address_one_is' => 'Address 1 is',
            'txt_city_is' => 'City is',
            'txt_first_name_required' => 'First name is required',
            'txt_last_name_required' => 'Last name is required',
            'txt_email_required' => 'Email is required',
            'txt_confirm_email_required' => 'Confirm Email is required',
            'txt_email_does_not_match' => 'Email addresses don’t match. Please correct and proceed',
            'txt_password_required' => 'Password is required',
            'txt_email_is' => 'Email is',
            'txt_it_happens_to_all' => 'It happens to all of us.',
            'txt_enter_email_to_reset' => 'Enter your email to reset',
            'txt_your_password' => 'your password',
            'txt_email_is_not_valid' => 'Email is not valid',
            'txt_enter_your_email' => 'Please enter your email address',
            'txt_finish_order_for' => 'Finish the order for',
            'lbl_banner_title' => 'Banner Title',
            'btn_click_here' => 'Click here',
            'txt_set_up_product_grids' => 'to set up product grids',
            'txt_show_all' => '(Show All)',
            'txt_due_date' => 'Due Date',
            'lbl_password_six_character' => 'Password must be 6 characters',
            'lbl_current_plan' => 'Current Plan',
            'lbl_file' => 'File',
            'txt_update_successfully' => 'Update successfully',
            'txt_customer_updated' => 'Customer has been updated',
            'txt_something_wrong' => 'Something wrong! Please try again',
            'txt_new_order_not_created' => 'Something wrong! New order has not been created',
            'txt_customer_not_updated' => 'Something wrong! Customer has not been updated',
            'txt_add_banners_text' => 'to add your banners & text',
            'txt_pass_not_matched_with_confirm_pass' => 'Password does not match with confirm password',
            'txt_enter_new_pass' => 'Enter New Password',
            'txt_pass_mismatched' => 'Mismatched Password',
            'txt_forgot_pass' => 'Forgot Password',
            'txt_sorry_failed_to_upload' => 'Sorry!!! Failed to upload. Please try again.',
            'lbl_promotion_code' => 'Promotion code',
            'txt_welcome_back' => 'Welcome back',
            'txt_cart_session_has_expired' => 'Sorry, Your cart session has expired',
        ],
        'message'=>[
            'error_store_close'=>'Rentals can not start when store is closed. Enter valid rental start date and time.',
            'error_something_wrong' => "Sorry - we're experiencing an issue with this function. Please contact us for resolution",
            'error_tour_unavailable' => 'This tour is not available on the date you selected. Please pick another date.',
        ],
        'POS' => [
            'exchange_asset_with' => 'Tank to be exchanged',
            'exchange_asset' => 'Assign full tank'
        ]
    ],
    'store_config' => [
        "timezone" => 'America/Chicago',
        "show_end_date" => true,
        "show_end_time" => true,
        "show_start_date" => true,
        "show_start_time" => true,
        "rental_price_option" => true,
        "show_checkout_additional_field" => false,
        "show_checkout_address_field" => true,
        "fb_tracking_id" => "",
        "currency_format" =>
            ["pre" => true, "post" => false, "symbol" => "$", "code" => "USD"],
        "checkout" => [
            'billing' => [
                'first_name' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'last_name' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'email' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'company' => [
                    'show' => true,
                    'is_required' => false,
                ],
                'mobile' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'address' => [
                    'show' => true,
                    'is_required' => true,
                ],
            ],
            'fulfillment' => [
                'first_name' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'last_name' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'email' => [
                    'show' => true,
                    'is_required' => true,
                ],
                'address' => [
                    'show' => true,
                    'is_required' => true,
                ],
            ],
        ],
    ],
    'ApiResponse' => [
        '100' => 'Continue',
        '101' => 'Switching Protocols',
        '200' => 'OK',
        '201' => 'Created',
        '202' => 'Accepted',
        '203' => 'Non-Authoritative Information',
        '204' => 'No Content',
        '205' => 'Reset Content',
        '206' => 'Partial Content',
        '300' => 'Multiple Choices',
        '301' => 'Moved Permanently',
        '302' => 'Moved Temporarily',
        '303' => 'See Other',
        '304' => 'Not Modified',
        '305' => 'Use Proxy',
        '400' => 'Bad Request',
        '401' => 'Unauthorized',
        '402' => 'Payment Required',
        '403' => 'Forbidden',
        '404' => 'Not Found',
        '405' => 'Method Not Allowed',
        '406' => 'Not Acceptable',
        '407' => 'Proxy Authentication Required',
        '408' => 'Request Time-out',
        '409' => 'Conflict',
        '410' => 'Gone',
        '411' => 'Length Required',
        '412' => 'Precondition Failed',
        '413' => 'Request Entity Too Large',
        '414' => 'Request-URI Too Large',
        '415' => 'Unsupported Media Type',
        '500' => 'Internal Server Error',
        '501' => 'Not Implemented',
        '502' => 'Bad Gateway',
        '503' => 'Service Unavailable',
        '504' => 'Gateway Time-out',
        '505' => 'HTTP Version not supported',
        '416' => 'Invalid activation link!',
        '417' => 'Invalid Activation Link! Please check your email for valid activation link.',
        '418' => 'Invalid activation code',
        '419' => 'activation code date is already expired.',
        '420' => 'Password could not be changed. Please try again.',
        '421' => 'No request data has found. Please provide password.',
    ],

    /* card connect credential sandbox */
    'CardConnect.OnlinePayment.MerchantId' => '496261774889', //'496160873888',
    'CardConnect.OnlinePayment.Username' => 'renterval1',
    'CardConnect.OnlinePayment.Password' => '68@oRu89NBr#0kL',
    'CardConnect.OnlinePayment.ApiUrl' => 'https://fts.cardconnect.com:8443/cardconnect/rest',

    'CardConnect.P2PE.merchantId' => '496261774889',
    'CardConnect.P2PE.authToken' => 'JgfxKUGIrvjbfKPjbAiTQkFk+DGCNyeq4ZBg0w16hAg=',
    'CardConnect.P2PE.hsn' => '16342SC81196160', /*   "15245PP81678293", "16342SC81196160", "17303PP83577603" */
    'CardConnect.P2PE.ApiUrl' => 'https://bolt.cardpointe.com:443/api',


    'Sms.gateway' => env('SMS_gateway'), // twilio | clickatell
    'Twilio.sid' => env('SMS_Twilio_sid'),
    'Twilio.token' => env('SMS_Twilio_token'),
    'Twilio.from' => env('SMS_Twilio_from'),
    'Twilio.messagingServiceSid' => env('SMS_Twillo_messagingServiceSid'),

    /* clickatell sms app configuration variable */
    'Clickatell.url' => 'http://api.clickatell.com/http/sendmsg',
    'Clickatell.username' => 'Renterval',
    'Clickatell.password' => 'KCXLBgXVNAdGTf',
    'Clickatell.api_id' => '3494659',
    'Clickatell.from' => '***********',
    'stripe' => [
        'key' => 'pk_live_OwX9QH4rBtTPhypoT5DLoJKh',
        // 'secret' => '********************************',
        'secret' => '***********************************************************************************************************',
        'client_id' => 'ca_GHn7XVCx0v0Ql0ul0DHdMDtZvJaaqpB2' // Live client id for Renterval LLC https://dashboard.stripe.com/account/applications/settings
    ],
    'AWSS3' => [
        'base_url' => env('AWS_S3_BASE_URL', 'https://s3.us-east-2.amazonaws.com/'),
        'access_key' => env('AWS_S3_ACCESS_KEY'),
        'access_secret' => env('AWS_S3_ACCESS_SECRET'),
        'version' => env('AWS_S3_VERSION'),
        'region' => env('AWS_S3_REGION'),
        'product_bucket' => env('AWS_S3_PRODUCT_BUCKET'),
        'file_path'=>[
            'media'=> 'content-image/{{store_id}}/media/'
        ]
    ],
    'GOOGLE' => [
        'GOOGLE_APP_ID' => env('GOOGLE_APP_ID'),
        'GOOGLE_APP_SECRET' => env('GOOGLE_APP_SECRET'),
        'GOOGLE_APP_REDIRECT_URL' => env('GOOGLE_APP_REDIRECT_URL')
    ],
    /*
     Stripe
    User: <EMAIL>
    Pass: 2wwGyrk0oWFmsy

    Authorize.net
    User: b2_digital
    Pass: ew7urPnG6Y.HBpEy
    Card Not Present
    Test Account API
    Login ID 5m4SZu6Z
    Transaction Key 7857F7GB5HypsP55
    https://test.authorize.net/ https://developer.authorize.net/

    for paypal:
    clientId =  ARYNFIWGiNH9o97K654RC5Tqfxkao0UIjzmMc_GrUR0kfH1Ila83bD6BDF1DA4sHzmRmTRcEPq5Ks3Z9
    secret  = EEWXWrUyojqTdSJZhVYIaOBQlvLvJoGoFcUNZPSbMvrebRCLrI8icFTWrO3WenImz0TZN8yj2bZLQ0IF

     */

    //  'stripe_test_secret' => 'sk_test_iyisnsD7dzc1fGxBrAvbeoXX', //test mode
    'stripe_test_secret' => '********************************', // live mode

    /* FedEx Info
    Test Account Information

    Test URL: https://wsbeta.fedex.com:443/web-services

    Developer Test Key: 9hcUPoZYlrqExA1V
    Test Password: KfpyoySabcakF3nRQNon4jHVQ

    FedEx Web     Services Testing Information:

    FedEx   Shipping Account Number: *********

    FedEx Meter   Number: *********

    Test FedEx Office Integrator ID: 123Test Client Product ID: TESTTest Client Product Version: 9999

    User: stanbaker74   Pass: Tluq2j0XQqts9BdrHwpb
    */
    // Authentication Key:	 JHiHfpUf3ibgYlUi
//Meter Number:	 *********
    'dashboard_organizer'=>[
        [
            'format'=>'3:3:3:3',
            'serial'=>1,
            'cards'=>[
                [
                    'name'=>'setup-account',
                    'label'=>'Get Ready For Orders',
//                    'card_content' => '<div class="card onboarding-card onboarding-card1" id="validate-account">
//                                        <div class="text-center card-body">
//                                            <img src="./assets/img/admin/lockicon.png" alt="onboarding icon" />
//                                            <p class="card-text font-weight-semibold mb-0">Validate Your Account</p>
//                                        </div>
//                                    </div>'
                ],
                [
                    'name'=>'video-tour',
                    'label'=>'Take A Video Tour',
                    'card_content'=>'<div class="card onboarding-card onboarding-card2 rentmy-custom-card-content">
                    <a class="text-center card-body"><img
                            src="./assets/img/admin/youtube.png" alt="onboarding icon">
                        <p class="card-text font-weight-semibold mb-0">Take A Video Tour</p>
                    </a>
                    <div id="rentmy-custom-card-content-id" style="display: none">
                        <iframe width="750"
                            height="450"
                            src="https://player.vimeo.com/video/*********?autoplay=1&autopause=0&api=1&muted=1">
                        </iframe>
                    </div>
                </div>'
                ],
                [
                    'name'=>'schedule-demo',
                    'label'=>'Schedule Demo',
                ],
                [
                    'name'=>'add-an-order',
                    'label'=>'Add An Order',
                ],
            ]
        ],
        [
            'format'=>'9:3',
            'serial'=>2,
            'cards'=>[
                [
                    'name'=>'order-calendar',
                    'label'=>'Availability Calendar',
                ],
                [
                    'name'=>'top-products',
                    'label'=>'Top Products',
                ],
            ]
        ],
        [
            'format'=>'3:3:3:3',
            'serial'=>3,
            'cards'=>[
                [
                    'name'=>'inventory',
                    'label'=>'Inventory',
                ],
                [
                    'name'=>'create-order-quotes',
                    'label'=>'Create Orders',
                ],
                [
                    'name'=>'reservations',
                    'label'=>'Reservations',
                ],
                [
                    'name'=>'point-of-sale',
                    'label'=>'Point of Sale',
                ],
            ]
        ],
        [
            'format'=>'12',
            'serial'=>4,
            'cards'=>[
                [
                    'name'=>'business-settings',
                    'label'=>'Business Settings',
                ],

            ]
        ],
        [
            'format'=>'12',
            'serial'=>5,
            'cards'=>[
                [
                    'name'=>'grow-your-rental-business',
                    'label'=>'Let’s grow your rental business!',
                ],

            ]
        ],
    ],
    'booking' => [
        "590" => [
            "parent" => 1, // ratio
            "child" => 2, // ratio
        ],
        "781" => [ // teststore15
            "parent" => 1, // ratio
            "child" => 1, // ratio
        ],
        "3285" => [ // Rydables, LLC
            "parent" => 1, // ratio
            "child" => 1, // ratio
        ],
        "782" => [ // teststore17
            "parent" => 1, // ratio
            "child" => 1, // ratio
        ],
        "22" => [ // teststore02
            "parent" => 1, // ratio
            "child" => 1, // ratio
        ],
    ],
];
