<?php
return [
    // all values are in seconds. here 10 request in 3 seconds
    'RateLimiter' => [
        // Default rule for all routes unless overridden
        // if window=10, limit=3, block_duration=1800 , then it means - if there more than 3 request in 10 seconds , ip will be blocked for 30 mins
        'default' => [
            'limit' => 3,
            'window' => 10,
            'block_duration' => 1800, // 30 mins
        ],
        'rules' => [
            // Only override here if you need different limit/widow/block_duration
            'GET:/api/order/:id/email' => ["ip_block" => false], //  order payment email sent
            'POST:/api/orders/customer/:id' => ["ip_block" => false], // order customer details update,
            'POST:/api/authenticate' => ["ip_block" => false], // authenticate
            'POST:/api/users/login' => ["ip_block" => false], // UserLogin
            'POST:/api/users/register' => ["ip_block" => false] // Store Registration
        ]
    ]
];
