export APP_NAME="__APP_NAME__"
export DEBUG="false"
export APP_ENCODING="UTF-8"
export APP_DEFAULT_LOCALE="en_US"
export SECURITY_SALT="__SALT__"
export APP_ENV= 'dev'
#export API_URL= "https://api.universityrecrentals.com/"
export DOMAIN ='.universityrecrentals.com'
#AWS S3 configuration
export AWS_S3_ACCESS_KEY =""
export AWS_S3_ACCESS_SECRET =""
export AWS_S3_VERSION =""
export AWS_S3_REGION=""
export AWS_S3_PRODUCT_BUCKET="local.rentmy"  // local
export GOOGLE_APP_ID=""
export GOOGLE_APP_SECRET=""
export GOOGLE_APP_REDIRECT_URL=""

export SMS_gateway=""
export SMS_Twilio_sid=""
export SMS_Twilio_token=""
export SMS_Twilio_from="+"
export SMS_Twillo_messagingServiceSid=""
