<?php

//use Cake\Core\Plugin;
use Cake\Routing\RouteBuilder;
use Cake\Routing\Router;
use Cake\Routing\Route\DashedRoute;

Router::defaultRouteClass(DashedRoute::class);

Router::scope('/api', function (RouteBuilder $routes) {

    $routes->setExtensions(['pdf']);
    $routes->prefix('v2', function (RouteBuilder $routes) {
        $routes->connect('/products', ['controller' => 'Products', 'action' => 'index', 'allowWithoutToken' => true])->setMethods(['GET','OPTIONS']);
        $routes->connect('/carts', ['controller' => 'Products', 'action' => 'index'])->setMethods(['POST','OPTIONS']);
        $routes->fallbacks(DashedRoute::class);
    });

    //download order pdf
    $routes->connect('/receipt/pdf/order/:id', ['controller' => 'receipt', 'action' => 'orderpdf', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/signature/pdf/order/:id', ['controller' => 'receipt', 'action' => 'signature', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/order/picklist/pdf', ['controller' => 'receipt', 'action' => 'pickListPdf', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);
    /*
     * @deprecated [@Todo remove when api/sdk updated]
     * use instead of /pages/pdf to /receipt/pdf/order/:id
     */
    $routes->connect('/pages/pdf', ['controller' => 'pages', 'action' => 'orderpdf', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);

    /* Rental Calendar */
    $routes->connect('/calendar/order', ['controller' => 'Calendar', 'action' => 'order', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/calendar/delivery', ['controller' => 'Calendar', 'action' => 'delivery', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/calendar/available', ['controller' => 'Calendar', 'action' => 'available', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/calendar/available-in-hour', ['controller' => 'Calendar', 'action' => 'availableInHours', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/calendar/order-in-hour', ['controller' => 'Calendar', 'action' => 'orderInHours', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/calendar/est-available', ['controller' => 'Calendar', 'action' => 'estAvailable', '_method' => ['GET', 'OPTIONS']]);

   // Package available
    $routes->connect('/calendar/package/available', ['controller' => 'Calendar', 'action' => 'packageAvailable', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/calendar/package/available-in-hour', ['controller' => 'Calendar', 'action' => 'packageAvailableInHour', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/synchronize', ['controller' => 'contents', 'action' => 'synchronize', '_method' => ['GET', 'OPTIONS']]);

    /* Templates */
    $routes->connect('/templates', ['controller' => 'templates', 'action' => 'getList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/templates/list/:type', ['controller' => 'templates', 'action' => 'getListByType', '_method' => ['GET', 'OPTIONS']], ['type' => '\d+', 'pass' => ['type']]);
    $routes->connect('/templates', ['controller' => 'templates', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/templates/:type', ['controller' => 'templates', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['type' => '\d+', 'pass' => ['type']]);
    $routes->connect('/templates/view/:id', ['controller' => 'templates', 'action' => 'viewById', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    $routes->connect('/templates/create', ['controller' => 'templates', 'action' => 'createTemplate', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/templates/delete/:id', ['controller' => 'templates', 'action' => 'deleteTemplate', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /* delivery */
    $routes->connect('/delivery-charge-list', ['controller' => 'delivery', 'action' => 'deliveryChargeList', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/delivery-charge-menu', ['controller' => 'delivery', 'action' => 'deliveryChargeMenu', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/shipping', ['controller' => 'delivery', 'action' => 'shipping', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/stores/delivery-settings', ['controller' => 'delivery', 'action' => 'deliverySettings', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/delivery/check-settings', ['controller' => 'delivery', 'action' => 'checkDeliverySettings', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/delivery-settings', ['controller' => 'delivery', 'action' => 'deliveryData', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/delivery-settings', ['controller' => 'delivery', 'action' => 'delivery', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/delivery-zone', ['controller' => 'delivery', 'action' => 'deliveryZone', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/delivery-zone', ['controller' => 'delivery', 'action' => 'deliveryZoneData', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/delivery-zone/:id', ['controller' => 'delivery', 'action' => 'deliveryZoneEdit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/delivery-zone/:id', ['controller' => 'delivery', 'action' => 'deliveryZoneDelete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/delivery-details/:id', ['controller' => 'delivery', 'action' => 'deliveryDetails', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/confirm-shipment/:id', ['controller' => 'delivery', 'action' => 'confirmShipment', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/track-shipment/:id', ['controller' => 'delivery', 'action' => 'trackShipment', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);


    $routes->connect('/stores/multi-store-delivery/list', ['controller' => 'delivery', 'action' => 'multiStoreDeliveryList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/multi-store-delivery/configs', ['controller' => 'delivery', 'action' => 'multiStoreDeliveryActiveList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/stores/multi-store-delivery/create', ['controller' => 'delivery', 'action' => 'multiStoreDeliveryCreate', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/stores/multi-store-delivery/view/:id', ['controller' => 'delivery', 'action' => 'multiStoreDeliveryView', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/stores/multi-store-delivery/update/:id', ['controller' => 'delivery', 'action' => 'multiStoreDeliveryUpdate', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/stores/multi-store-delivery/delete/:id', ['controller' => 'delivery', 'action' => 'deliveryZoneDelete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);


    /* Newsletters */
    $routes->connect('/newsletters', ['controller' => 'Newsletters', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/newsletters', ['controller' => 'Newsletters', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/newsletters/export', ['controller' => 'Newsletters', 'action' => 'export'])->setMethods(['GET', 'OPTIONS']);

    /*Holidays & store-time */
    $routes->connect('/store-time', ['controller' => 'holidays', 'action' => 'storeTimeList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/store-time', ['controller' => 'holidays', 'action' => 'storeTimeSave', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/holidays', ['controller' => 'holidays', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/holidays', ['controller' => 'holidays', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/holidays/:id', ['controller' => 'holidays', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/holidays/:id', ['controller' => 'holidays', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/holidays/:id', ['controller' => 'holidays', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /* Timezone */
    $routes->connect('/timezones/list', ['controller' => 'timezones', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/timezones', ['controller' => 'timezones', 'action' => 'view', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/timezones', ['controller' => 'timezones', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/currency-details', ['controller' => 'timezones', 'action' => 'currencyDetails', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/currency-config', ['controller' => 'timezones', 'action' => 'currencySettings', '_method' => ['GET', 'OPTIONS']]);

    /** admin configuration changes for stores*/
    $routes->connect('/admin/config', ['controller' => 'Config', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/admin/config/:type', ['controller' => 'Config', 'action' => 'index'])->setMethods(['GET', 'OPTIONS'])->setPass(['type']);


    /** tax and tax classes */
    $routes->connect('/admin/tax/classes', ['controller'=>'Tax', 'action'=>'addClass'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/admin/tax/classes/:id', ['controller'=>'Tax', 'action'=>'deleteClass'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/admin/tax/classes/:id/rates', ['controller'=>'Tax', 'action'=>'classRates'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/admin/tax/classes/:id/rates', ['controller'=>'Tax', 'action'=>'addClassRates'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/admin/tax/classes/:id/rates', ['controller'=>'Tax', 'action'=>'deleteClassRates'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/admin/tax/exempt', ['controller'=>'Orders', 'action'=>'exempt'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/admin/tax/calculate', ['controller'=>'Orders', 'action'=>'calculate'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);


    /*--------pages---------------*/
    $routes->connect('/pages/:id', ['controller' => 'pages', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '[\w -]+', 'pass' => ['id']]);
    $routes->connect('/pages/details/:id', ['controller' => 'pages', 'action' => 'details', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/pages/:id', ['controller' => 'pages', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/pages/:id', ['controller' => 'pages', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/pages', ['controller' => 'pages', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/pages', ['controller' => 'pages', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);

    /*--------blog---------------*/
    $routes->connect('/blogs', ['controller' => 'pages', 'action' => 'blogList', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect('/pages/:id/duplicate-check/:type', ['controller' => 'pages', 'action' => 'duplicateChecking', '_method' => ['POST', 'OPTIONS']])->setPass(['id', 'type']);

    $routes->connect('/state-by-country/:id', ['controller' => 'pages', 'action' => 'stateByCountry', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/countries', ['controller' => 'Settings', 'action' => 'countries', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/terms-and-condition', ['controller' => 'Pages', 'action' => 'termsAndCondition']);
    /*------------pages contents */
    $routes->connect('/pages/contents/:id', ['controller' => 'PagesContents', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/pages/contents/:id', ['controller' => 'PagesContents', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/pages/contents', ['controller' => 'PagesContents', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/pages/contents', ['controller' => 'PagesContents', 'action' => 'view', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/pages/contents/:id', ['controller' => 'PagesContents', 'action' => 'details', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    /*--------custom fields---------------*/
    $routes->connect('/custom-fields/:id', ['controller' => 'customs', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/custom-fields/:id', ['controller' => 'customs', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/custom-fields/:id', ['controller' => 'customs', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/custom-fields', ['controller' => 'customs', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/custom-fields/checkout', ['controller' => 'customs', 'action' => 'checkoutCustoms', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/custom-fields', ['controller' => 'customs', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/custom-fields/sort', ['controller' => 'customs', 'action' => 'sortCustomFields', '_method' => ['POST', 'OPTIONS']]);

    /*--------exact time fields---------------*/
    $routes->connect('/exact-datetime/:uid', ['controller' => 'ExactTimes', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['pass' => ['uid']]);
    $routes->connect('/exact-datetime/product/:pid', ['controller' => 'ExactTimes', 'action' => 'getFieldsBy', '_method' => ['GET', 'OPTIONS']], ['pass' => ['pid']]);
    $routes->connect('/exact-datetime', ['controller' => 'ExactTimes', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/exact-datetime/:uid', ['controller' => 'ExactTimes', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['pass' => ['uid']]);
    $routes->connect('/exact-datetime/:uid', ['controller' => 'ExactTimes', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['pass' => ['uid']]);
    $routes->connect('/exact-datetime', ['controller' => 'ExactTimes', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect('/exact-datetime/events', ['controller' => 'ExactTimes', 'action' => 'eventDetails', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/exact-datetime/events/calendar', ['controller' => 'ExactTimes', 'action' => 'eventCalendarDetails', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/exact-datetime/events/:event_id/picklist', ['controller' => 'ExactTimes', 'action' => 'eventPicklist', '_method' => ['GET', 'OPTIONS']])->setPass(['event_id']);

    $routes->connect('/exact-datetime/date-wise-info', ['controller' => 'ExactTimes', 'action' => 'dateWiseInfo', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/exact-datetime/:id/exception/update', ['controller' => 'ExactTimes', 'action' => 'updateDateTimeException', '_method' => ['POST', 'OPTIONS']])->setPass(['id']);
    /* initial default setup (add default product with tag) */
    $routes->connect('/setup/type', ['controller' => 'setup', 'action' => 'getType', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/setup/get-category/', ['controller' => 'setup', 'action' => 'getCategory', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/setup/get-products/', ['controller' => 'setup', 'action' => 'getProducts', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/setup/add-products/', ['controller' => 'setup', 'action' => 'addProducts', '_method' => ['POST', 'OPTIONS']]);

    //----------------------------refund----------------------------------------------------------------------
    $routes->connect('/payments/refund', ['controller' => 'Refunds', 'action' => 'orderRefund'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/payments/refund/:id', ['controller' => 'Refunds', 'action' => 'payment'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/deposits', ['controller' => 'refunds', 'action' => 'index', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/refund', ['controller' => 'refunds', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/refund/:id', ['controller' => 'refunds', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/refund/list/:id', ['controller' => 'refunds', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    //----------------------------Order Notes----------------------------------------------------------------------
    $routes->connect('/orders/:id/notes', ['controller' => 'notes', 'action' => 'add', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/notes/:id', ['controller' => 'notes', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/:id/notes', ['controller' => 'notes', 'action' => 'index', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/:id/item/:item_id/notes', ['controller' => 'notes', 'action' => 'listAccordingToItem', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id', 'item_id']]);
    $routes->connect('/notes/:id', ['controller' => 'notes', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/waiver-sharing/:id/pdf', ['controller' => 'notes', 'action' => 'waiverSharingPdf', '_method' => ['GET', 'OPTIONS'], 'allowWithoutToken' => true], ['id' => '\d+', 'pass' => ['id']]);
    //----------------------------Notifications----------------------------------------------------------------------
    $routes->connect('/notifications', ['controller' => 'Notifications', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    //----------------------------General Notes/Comments for using all over the system----------------------------------------------------------------------
    $routes->connect('/comments', ['controller' => 'comments', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/comments', ['controller' => 'comments', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/comments/:id', ['controller' => 'comments', 'action' => 'view'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/comments/:id', ['controller' => 'comments', 'action' => 'edit'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/comments/:id', ['controller' => 'comments', 'action' => 'delete'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);

    // payment redirect
    $routes->connect('/payments/authenticate', ['controller' => 'payments', 'action' => 'authenticate', 'allowWithoutToken' => true]);
    $routes->connect('/payments/nelnet', ['controller' => 'payments', 'action' => 'nelnet', 'allowWithoutToken' => true]);
    $routes->connect('/payments/stripe-intent', ['controller' => 'payments', 'action' => 'stripeIntent']);
    $routes->connect('/payments/stripe-swipe-status', ['controller' => 'payments', 'action' => 'checkStripeSwipeStatus'])->setPass(['intent_id'])->setMethods(['POST']);
    $routes->connect('/payments/valorpay-swipe-status', ['controller' => 'payments', 'action' => 'checkValorPaySwipeStatus'])->setMethods(['POST']);
    $routes->connect('/payments/transafe-iframe-attr', ['controller' => 'payments', 'action' => 'transafeIFrameAttr']);

    $routes->connect('/payments/touchnet', ['controller' => 'payments', 'action' => 'touchnet']);
    $routes->connect('/payments/touchnet/:order_id/success/:source', ['controller' => 'payments', 'action' => 'touchnetPayment', 'allowWithoutToken' => true])->setPass(['order_id', 'source']);

    //------------------------------------------payment-------------------------------------------------------
    $routes->connect('/payments', ['controller' => 'payments', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/payments', ['controller' => 'payments', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/payments/:id', ['controller' => 'payments', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/payments/:order_id/:type', ['controller' => 'payments', 'action' => 'payment', '_method' => ['POST', 'OPTIONS']], ['order_id' => '\d+', 'type' => '\d+', 'pass' => ['order_id', 'type']]);
    $routes->connect('/payments/:id', ['controller' => 'payments', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    /** @TODO - NEED to remove this route and use latest */
    $routes->connect('/payments/:id/all', ['controller' => 'payments', 'action' => 'all', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/order/:id/payments', ['controller' => 'payments', 'action' => 'summary', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/payments/:id', ['controller' => 'payments', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/payments/:id/amount', ['controller' => 'payments', 'action' => 'amount', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    /** @TODO - NEED to remove this route and use latest */
    $routes->connect('/payment-log/:id', ['controller' => 'payments', 'action' => 'paymentLogs', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    // payment after refund @deprecated
    $routes->connect('/payments/return', ['controller' => 'payments', 'action' => 'paymentSummary', '_method' => ['POST', 'OPTIONS']]);
    // payment summary
    $routes->connect('/payments/summary', ['controller' => 'payments', 'action' => 'paymentSummary', '_method' => ['POST', 'OPTIONS']]);

    // Payment webhook
    $routes->connect('/payments/webhook/:type', ['controller' => 'payments', 'action' => 'webHook','allowWithoutToken' => true,])->setMethods(['POST', 'OPTIONS'])->setPass(['type']);

    //salesReport
    $routes->connect('/sales-report', ['controller' => 'payments', 'action' => 'salesReport']);
    $routes->connect('/payments/application-fee-report', ['controller' => 'Reports', 'action' => 'applicationFeeReport', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/sales-report/export', ['controller' => 'payments', 'action' => 'exportSalesReport']);

    //------------------------------------------product--------------------------------------------------------
    $routes->connect('/products/:id', ['controller' => 'products', 'action' => 'edit', '_method' => ['GET', 'POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id', ['controller' => 'products', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products', ['controller' => 'products', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/products/create', ['controller' => 'Products', 'action' => 'firstProduct'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/products/unique-url', ['controller' => 'Products', 'action' => 'urlDuplicateCheck'])->setMethods(['OPTIONS', 'POST']);

    $routes->connect('/products/:id/duplicate-check/:type', ['controller' => 'products', 'action' => 'duplicateChecking', '_method' => ['POST', 'OPTIONS']])->setPass(['id', 'type']);

    $routes->connect('/products', ['controller' => 'products', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/products/all', ['controller' => 'products', 'action' => 'allProducts', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/products/delete', ['controller' => 'products', 'action' => 'deleteAll', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/products/:id/details', ['controller' => 'products', 'action' => 'details'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/summary', ['controller' => 'products', 'action' => 'summary'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/featured', ['controller' => 'products', 'action' => 'featured', '_method' => ['GET', 'OPTIONS']]);

    /** Product availability **/
    //$routes->connect('/products-availabilities', ['controller' => 'ProductsAvailabilities', 'action' => 'unavailability', '_method' => ['POST', 'POST', 'OPTIONS']]);
    $routes->connect('/products-availabilities/add-unavailability', ['controller' => 'ProductsAvailabilities', 'action' => 'addUnavailability', '_method' => ['POST', 'POST', 'OPTIONS']]);
    $routes->connect('/products-availabilities/add-unavailability/by-date-range', ['controller' => 'ProductsAvailabilities', 'action' => 'addUnavailabilityByDateRange', '_method' => ['POST', 'POST', 'OPTIONS']]);
    $routes->connect('/products-availabilities/remove', ['controller' => 'ProductsAvailabilities', 'action' => 'remove', '_method' => ['POST', 'POST', 'OPTIONS']]);
    $routes->connect('/products/availability', ['controller' => 'ProductsAvailabilities', 'action' => 'products'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/products-availabilities/:id/reservation', ['controller' => 'ProductsAvailabilities', 'action' => 'index'], ['id' => '\d+', 'pass' => ['id']]); // get product reservation
    $routes->connect('/products-availabilities/:id/add', ['controller' => 'ProductsAvailabilities', 'action' => 'add'], ['id' => '\d+', 'pass' => ['id']]); // get product reservation
    $routes->connect('/products-availabilities/edit', ['controller' => 'ProductsAvailabilities', 'action' => 'edit']); // get product reservation
    $routes->connect('/products-availabilities/:id/delete', ['controller' => 'ProductsAvailabilities', 'action' => 'delete'], ['id' => '\d+', 'pass' => ['id']]);
    // get Availability by range or duration
    $routes->connect('/products-availabilities', ['controller' => 'ProductsAvailabilities', 'action' => 'getAvailabilityByRange'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/products-availabilities', ['controller' => 'ProductsAvailabilities', 'action' => 'productWiseQuantityAndAvilability'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/products/availability', ['controller' => 'ProductsAvailabilities', 'action' => 'getAvailability'])->setMethods(['OPTIONS', 'GET']);

    $routes->connect('/products/available-time', ['controller' => 'ProductsAvailabilities', 'action' => 'checkAvailability', '_method' => ['POST', 'OPTIONS']]);
    /** End Availability */
    //excel
    $routes->connect('/products/download/sample', ['controller' => 'Export', 'action' => 'download-sample', 'allowWithoutToken' => true]);
    $routes->connect('/products/excel', ['controller' => 'products', 'action' => 'excel']);
    $routes->connect('/products/import', ['controller' => 'Import', 'action' => 'excel']);
    $routes->connect('/products/import/status', ['controller' => 'Import', 'action' => 'status', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/products/import/history', ['controller' => 'Import', 'action' => 'importHistory'])->setMethods(['GET', 'OPTIONS']);
    //$routes->connect('/products/download/:prefix', ['controller' => 'Import', 'action' => 'download'],['pass' => ['prefix']]);
    $routes->connect('/products/download', ['controller' => 'Import', 'action' => 'download', '_method' => ['GET', 'OPTIONS']]);
    //reservation
    $routes->connect('/products/reservation/list', ['controller' => 'products-availabilities', 'action' => 'view']);

    //product image
    $routes->connect('/promotion-image', ['controller' => 'stores', 'action' => 'promotionImage', '_method' => ['POST', 'POST', 'OPTIONS']]);
    $routes->connect('/products/:id/media/upload', ['controller' => 'products', 'action' => 'mediaUpload'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/:a_id/images', ['controller' => 'products', 'action' => 'images'], ['id' => '\d+', 'a_id' => '[a-z0-9-_]+', 'pass' => ['id', 'a_id']]);
    $routes->connect('/products/:id/feature', ['controller' => 'products', 'action' => 'featureImage'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/media/:id/delete', ['controller' => 'Images', 'action' => 'mediaDelete'], ['id' => '\d+', 'pass' => ['id']]);

    /** Image Config */
    $routes->connect('/products/:id/user/related-products', ['controller' => 'products', 'action' => 'userRelatedProducts'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/media/delete/:product_id', ['controller' => 'Images', 'action' => 'deleteAllImage'], ['product_id' => '\d+', 'pass' => ['product_id']]);
    $routes->connect('/media/sync/:product_id', ['controller' => 'Images', 'action' => 'synImages'], ['product_id' => '\d+', 'pass' => ['product_id']]);

    //product category
    $routes->connect('/products/:id/category', ['controller' => 'products', 'action' => 'category'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/category/add', ['controller' => 'products', 'action' => 'categoryAdd'], ['id' => '\d+', 'pass' => ['id']]);

    //searching products
    $routes->connect('/products/search', ['controller' => 'products', 'action' => 'search']);
    $routes->connect('/products/view/variant-product/:vp_id', ['controller' => 'products', 'action' => 'view'], ['vp_id' => '\d+', 'pass' => ['vp_id']]);

    // product listing && Searching in pos
    $routes->connect('/products/pos', ['controller' => 'Products', 'action' => 'posProducts'])->setMethods(['GET', 'OPTIONS']);

    //Products Tag
    $routes->connect('/products/:id/tags', ['controller' => 'products', 'action' => 'tags'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/tag/add', ['controller' => 'products', 'action' => 'addTags'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/tag/:t_id/delete', ['controller' => 'products', 'action' => 'deleteTag'], ['id' => '\d+', 't_id' => '\d+', 'pass' => ['id', 't_id']]);

    // product custome fields routes
    $routes->connect('/products/custom-fields', ['controller' => 'ProductFields', 'action' => 'viewAllFields'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/products/custom-fields', ['controller' => 'ProductFields', 'action' => 'addFields'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/products/custom-fields/sort', ['controller' => 'ProductFields', 'action' => 'sort'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/products/custom-fields/:slug', ['controller' => 'ProductFields', 'action' => 'editFields'], ['pass' => ['slug']])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/products/custom-fields/:slug', ['controller' => 'ProductFields', 'action' => 'viewField'], ['pass' => ['slug']])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/products/custom-fields/:slug', ['controller' => 'ProductFields', 'action' => 'deleteField'], ['pass' => ['slug']])->setMethods(['DELETE', 'OPTIONS']);

    // product custom fields routes for adding and retriving values
    $routes->connect('/products/custom-fields/values', ['controller' => 'ProductFields', 'action' => 'getFieldsByProduct'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/products/custom-fields/values/:id', ['controller' => 'ProductFields', 'action' => 'getFieldsByProductId'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/products/custom-fields/values/:id', ['controller' => 'ProductFields', 'action' => 'submitFieldsByProductId'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);


    //product price
    $routes->connect('/product-prices/:vp_id/:type', ['controller' => 'ProductPrices', 'action' => 'getPrice', '_method' => ['GET', 'OPTIONS']], ['vp_id' => '\d+', 'type' => '\d+', 'pass' => ['vp_id', 'type']]);
    $routes->connect('/product-prices/:vp_id', ['controller' => 'ProductPrices', 'action' => 'addRentPrice', '_method' => ['POST', 'OPTIONS']], ['vp_id' => '[a-z0-9-_]+', 'pass' => ['vp_id']]);
    $routes->connect('/product-prices/:price_id', ['controller' => 'ProductPrices', 'action' => 'removeRentPrice'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['price_id']);
    $routes->connect('/product-prices/:vp_id/type', ['controller' => 'ProductPrices', 'action' => 'savePriceType', '_method' => ['POST', 'OPTIONS']], ['vp_id' => '\d+', 'pass' => ['vp_id']]);
    $routes->connect('/product-prices/:id/edit', ['controller' => 'ProductPrices', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/product-prices/:id/delete', ['controller' => 'ProductPrices', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/product-prices/:a_id/buy', ['controller' => 'ProductPrices', 'action' => 'addBuyPrice', '_method' => ['POST', 'OPTIONS']], ['a_id' => '[a-z0-9-_]+', 'pass' => ['a_id']]);
    $routes->connect('/product-prices/:id/custom', ['controller' => 'ProductPrices', 'action' => 'addCustomPrice', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/product/get_dates_price_duration', ['controller' => 'ProductPrices', 'action' => 'getDatesByPriceDuration'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/product/get_dates_from_duration', ['controller' => 'ProductPrices', 'action' => 'getDatesByDuration'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/product/get_exact_duration', ['controller' => 'ProductPrices', 'action' => 'getExactDuration'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/prices/modifier/:id', ['controller' => 'ProductPrices', 'action' => 'PriceModifiers', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/prices/modifier/:id', ['controller' => 'ProductPrices', 'action' => 'PriceModifiersGet'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

//    RNTM-3311
    $routes->connect('/product/:id/get-exact-time', ['controller' => 'ExactTimes', 'action' => 'getExactTimeOfProduct'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

    // product settings
    $routes->connect('/product/settings/:id', ['controller' => 'Products', 'action' => 'saveSettings'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/product/settings/:id/batch', ['controller' => 'Products', 'action' => 'saveBatchSettings'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/product/settings/:id/batch', ['controller' => 'Products', 'action' => 'getBatchSettings'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/product/settings/:id/:key', ['controller' => 'Products', 'action' => 'getSettings'])->setMethods(['GET', 'OPTIONS'])->setPass(['id', 'key']);

    // old
    // $routes->connect('/products/:id/:a_id/price', ['controller' => 'products', 'action' => 'getPrice', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'a_id' => '[a-z0-9-_]+', 'pass' => ['id', 'a_id']]);
    // $routes->connect('/products/:id/:a_id/price', ['controller' => 'products', 'action' => 'price', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'a_id' => '[a-z0-9-_]+', 'pass' => ['id', 'a_id']]);

    $routes->connect('/products/:id/:a_id/product/getprice', ['controller' => 'ProductPrices', 'action' => 'getBuyPrice'])->setPass(['id', 'a_id']);

    /* Start Navigation */
    $routes->connect('/navigations-content', ['controller' => 'navigations', 'action' => 'navigationContent'])->setMethods(['GET']);
    $routes->connect('/navigations/:id/content-values', ['controller' => 'navigations', 'action' => 'contentValues'], ['id' => '\d+', 'pass' => ['id']])->setMethods(['GET']);
    $routes->connect('/navigations', ['controller' => 'navigations', 'action' => 'add'])->setMethods(['POST']);
    $routes->connect('/navigations/sort', ['controller' => 'navigations', 'action' => 'sort'])->setMethods(['POST']);
    $routes->connect('/navigations', ['controller' => 'navigations', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/navigations/:id', ['controller' => 'navigations', 'action' => 'edit'], ['id' => '\d+', 'pass' => ['id']])->setMethods(['POST']);
    $routes->connect('/navigations/:id', ['controller' => 'navigations', 'action' => 'view'], ['id' => '\d+', 'pass' => ['id']])->setMethods(['GET']);
    $routes->connect('/navigations/:id', ['controller' => 'navigations', 'action' => 'delete'], ['id' => '\d+', 'pass' => ['id']])->setMethods(['DELETE']);

    $routes->connect('/navigations/parents', ['controller' => 'navigations', 'action' => 'listAllParents', '_method' => ['GET', 'OPTIONS']]);
    /* End Navigation */

    //price
    $routes->connect('/prices/:id/:a_id', ['controller' => 'prices', 'action' => 'edit'], ['id' => '\d+', 'a_id' => '[a-z0-9-_]+', 'pass' => ['id', 'a_id']]); // edit
    $routes->connect('/prices/delete/:id', ['controller' => 'prices', 'action' => 'delete'], ['id' => '\d+', 'pass' => ['id']]); // delete

    //product attribute
    $routes->connect('/products/:id/variant-set/add', ['controller' => 'variants-products', 'action' => 'attributeSetAdd'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variant-set/list', ['controller' => 'variants-products', 'action' => 'productAttributeSet'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variant-set/delete', ['controller' => 'variants-products', 'action' => 'productAttributeSetdelete'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variant', ['controller' => 'products', 'action' => 'attribute'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variant/edit', ['controller' => 'products', 'action' => 'attributeEdit'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variant/delete', ['controller' => 'products', 'action' => 'deleteAttribute'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/batch-variant', ['controller' => 'products', 'action' => 'batchAttribute'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variant/sort', ['controller' => 'products', 'action' => 'variantSort'], ['id' => '\d+', 'pass' => ['id']]);
    /* AssetServicing */
    $routes->connect('/asset-servicing/:id/list', ['controller' => 'AssetServicing', 'action' => 'index', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/asset-servicing/:id', ['controller' => 'AssetServicing', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/asset-servicing', ['controller' => 'AssetServicing', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/asset-servicing/:id', ['controller' => 'AssetServicing', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/asset-servicing/:id', ['controller' => 'AssetServicing', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /* Assets */
    $routes->connect('/products/assets/summary', ['controller' => 'assets', 'action' => 'summary', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/assets/clone', ['controller' => 'assets', 'action' => 'cloneAsset', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/assets/retire', ['controller' => 'assets', 'action' => 'retire', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/assets/status-config', ['controller' => 'assets', 'action' => 'statusConfig', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets/search', ['controller' => 'assets', 'action' => 'search', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets/check-serial', ['controller' => 'assets', 'action' => 'checkSerial', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets', ['controller' => 'assets', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/assets/bulk', ['controller' => 'assets', 'action' => 'bulkAsset', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/assets', ['controller' => 'assets', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets/assign', ['controller' => 'assets', 'action' => 'assign', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets/:id', ['controller' => 'assets', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/assets/:id/details', ['controller' => 'assets', 'action' => 'details', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/assets/:id', ['controller' => 'assets', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/assets/:id', ['controller' => 'assets', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/assets/transfer', ['controller' => 'assets', 'action' => 'transfer', '_method' => ['POST', 'GET', 'OPTIONS']]);
    $routes->connect('/assets/change-status', ['controller' => 'assets', 'action' => 'changeAutoStatus', '_method' => ['POST','GET', 'OPTIONS']]);

    $routes->connect('/assets/scan', ['controller' => 'assets', 'action' => 'scan', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/assets/exchange', ['controller' => 'assets', 'action' => 'exchange', '_method' => ['POST', 'OPTIONS']]);

    // RNTM-2455
    $routes->connect('/assets/revenue/:id', ['controller' => 'assets', 'action' => 'revenue', '_method' => ['GET', 'OPTIONS']])->setPass(['id']);

    # RNTM-1988
    $routes->connect('/assets/only-scan', ['controller' => 'assetExchange', 'action' => 'search', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/assets/:order_id/exchange', ['controller' => 'assetExchange', 'action' => 'exchange', '_method' => ['POST', 'OPTIONS']], ['orderId' => '\d+', 'pass' => ['order_id']]);

    $routes->connect('/assets/comments', ['controller' => 'assets', 'action' => 'comment', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets/tracking-history', ['controller' => 'assets', 'action' => 'trackingHistory', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/assets/add-comment', ['controller' => 'assets', 'action' => 'addComment', '_method' => ['POST', 'OPTIONS']]);


    $routes->connect('/assets/assign-asset', ['controller' => 'assets', 'action' => 'assignAsset']); //not being used anymore
    $routes->connect('/assets/:id/check-assigned', ['controller' => 'assets', 'action' => 'checkAssetAssigned', '_method' => ['GET', 'OPTIONS']])->setPass(['id']); //not being used anymore
    $routes->connect('/pickup/delete/:id', ['controller' => 'assets', 'action' => 'discharge'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);

    $routes->connect('/assets/manage/:order_id', ['controller' => 'AssetOrderManagement', 'action' => 'search',
        '_method' => ['GET', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);

    $routes->connect('/assets/manage/:order_id/assign', ['controller' => 'AssetOrderManagement', 'action' => 'assign',
        '_method' => ['POST', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);


    $routes->connect('/assets/manage/:order_id/', ['controller' => 'AssetOrderManagement', 'action' => 'add',
        '_method' => ['POST', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);


    $routes->connect('/assets/manage/:order_id/check-order', ['controller' => 'AssetOrderManagement', 'action' => 'checkOrder',
        '_method' => ['POST', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);


    $routes->connect('/assets/manage/:order_id/submit-order', ['controller' => 'AssetOrderManagement', 'action' => 'submitOrder',
        '_method' => ['POST', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);

    $routes->connect('/assets/costs', ['controller' => 'Assets', 'action' => 'costSummary'])->setMethods(['GET', 'OPTIONS']);
    /**
     * Order Pickup / return
     */
    $routes->connect('/orders/:order_id/pickup', ['controller' => 'assetReturn', 'action' => 'pickup', 'pickup'])->setMethods(['GET', 'OPTIONS'])->setPass(['order_id']);
    $routes->connect('/orders/:order_id/return', ['controller' => 'assetReturn', 'action' => 'index'])->setMethods(['GET', 'OPTIONS'])->setPass(['order_id']);
    /*
     * asset return
     */

    $routes->connect('/assets/return/search/', ['controller' => 'assetReturn', 'action' => 'search']);
    //  $routes->connect('/assets/return/:order_id', ['controller' => 'assetReturn', 'action' => 'index', '_method' => ['GET', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);
    $routes->connect('/assets/return/:asset_id/:order_item_id', ['controller' => 'assetReturn', 'action' => 'add', '_method' => ['POST', 'OPTIONS']], ['asset_id' => '\d+', 'order_item_id' => '\d+', 'pass' => ['asset_id', 'order_item_id']]);
    $routes->connect('/assets/return', ['controller' => 'assetReturn', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/assets/return/all', ['controller' => 'assetReturn', 'action' => 'returnAll'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/assets/return/:id', ['controller' => 'assetReturn', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /* Components */
    $routes->connect('/components', ['controller' => 'components', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/component/add/', ['controller' => 'components', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/component/:id', ['controller' => 'components', 'action' => 'show'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/component/:id', ['controller' => 'components', 'action' => 'edit'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/component/:id', ['controller' => 'components', 'action' => 'delete'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    /* Asset Components */
    $routes->connect('/asset-components', ['controller' => 'components', 'action' => 'multipleAssetComponents'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/asset-components/:asset_id', ['controller' => 'components', 'action' => 'assetComponents'])->setMethods(['GET', 'OPTIONS'])->setPass(['asset_id']);
    $routes->connect('/assign/asset-component', ['controller' => 'components', 'action' => 'assignAssetComponent'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/delete/asset-component/:asset_id/:component_id', ['controller' => 'components', 'action' => 'deleteAssetComponent'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['asset_id', 'component_id']);

    /* Layout */
    $routes->connect('/layouts', ['controller' => 'stores', 'action' => 'layout', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/layouts', ['controller' => 'stores', 'action' => 'layoutSetting', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/store-products', ['controller' => 'stores', 'action' => 'storeProducts', '_method' => ['GET', 'OPTIONS']]);
    //Layout section ordering
    $routes->connect('/layouts/reorder-section', ['controller' => 'stores', 'action' => 'reorderSection', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/layouts/reorder-section', ['controller' => 'stores', 'action' => 'orderAbleSections', '_method' => ['GET', 'OPTIONS']]);

    //Restock
    $routes->connect('/variant-chain', ['controller' => 'VariantsProducts', 'action' => 'getAttributeChain']);
    $routes->connect('/get-path-of-chain', ['controller' => 'VariantsProducts', 'action' => 'getPathOfChain']);
    $routes->connect('/get-quantity-list', ['controller' => 'VariantsProducts', 'action' => 'getQuantityList']);
    $routes->connect('/update-quantity', ['controller' => 'VariantsProducts', 'action' => 'updateQuantity', '_method' => ['POST', 'OPTIONS']]);

    // Stock alert
    $routes->connect('/stock/alert/update', ['controller' => 'ProductsAvailabilities', 'action' => 'updateStockAlert'])->setMethods(['POST', 'OPTIONS']);

    //product in related
    $routes->connect('/products/user/products-listing', ['controller' => 'products', 'action' => 'userProductsListing']);
    $routes->connect('/products/:id/related', ['controller' => 'products', 'action' => 'related'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/related/:id', ['controller' => 'products', 'action' => 'relatedSearch'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/info', ['controller' => 'products', 'action' => 'info'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/:r_id/related/remove', ['controller' => 'products', 'action' => 'relatedRemove'], ['id' => '\d+', 'r_id' => '\d+', 'pass' => ['id', 'r_id']]);
    $routes->connect('/products/search-product', ['controller' => 'products', 'action' => 'searchProduct']);

    /** @TODO  - need to delete */
    $routes->connect('/products/:uid/user/details/:time', ['controller' => 'products', 'action' => 'userProductDetails'], ['uid' => '[A-Za-z0-9-]+', 'time' => '[A-Za-z0-9-]+', 'pass' => ['uid', 'time']]);
    /** Product Details api for online and 3rd party api */
    $routes->connect('/products/:uid', ['controller' => 'products', 'action' => 'userProductDetails'], ['uid' => '[A-Za-z0-9-]+', 'pass' => ['uid']]);
    $routes->connect('/products/copy', ['controller' => 'products', 'action' => 'copy'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/products/:id/variants', ['controller' => 'VariantsProducts', 'action' => 'variants', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/variants/batch-update', ['controller' => 'VariantsProducts', 'action' => 'variantsBatchUpdate', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    $routes->connect('/products/:id/user/related-products', ['controller' => 'products', 'action' => 'userRelatedProducts'], ['id' => '\d+', 'pass' => ['id']]);

    $routes->connect('/products/export', ['controller' => 'Export', 'action' => 'index']);
    $routes->connect('/products/export/records', ['controller' => 'Export', 'action' => 'totalRecords'])->setMethods(['OPTIONS', 'GET']);
    $routes->connect('/category/products/:uid', ['controller' => 'CategoriesProducts', 'action' => 'products'], ['uid' => '\w+', 'pass' => ['uid']]);
    $routes->connect('/products/category/:id', ['controller' => 'CategoriesProducts', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/bulk/edit', ['controller' => 'products', 'action' => 'bulkEdit'])->setMethods(['POST', 'OPTIONS']);

    //------------------------------------------category----------------------------------------------------------------------
    $routes->connect('/categories/', ['controller' => 'categories', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/categories/:id', ['controller' => 'categories', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/categories/:id', ['controller' => 'categories', 'action' => 'add', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/categories/:id', ['controller' => 'categories', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/categories/', ['controller' => 'categories', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/categories/lists', ['controller' => 'categories', 'action' => 'lists', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/categories/all/*', ['controller' => 'categories', 'action' => 'all', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/categories/:id/sort', ['controller' => 'categories', 'action' => 'sort'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/get/child-categories/:uid', ['controller' => 'categories', 'action' => 'childCategories', '_method' => ['GET', 'OPTIONS']], ['uid' => '[A-Za-z0-9-]+', 'pass' => ['uid']]);
    $routes->connect('/categories/child/:uid', ['controller' => 'categories', 'action' => 'childCategories', '_method' => ['GET', 'OPTIONS']], ['uid' => '[A-Za-z0-9-]+', 'pass' => ['uid']]);
    $routes->connect('/user/categories/', ['controller' => 'categories', 'action' => 'getAttributes']);
    $routes->connect('/user/get-child-variant/', ['controller' => 'categories', 'action' => 'get-child-attribute']);

    $routes->connect('/categories/:id/duplicate-check/:type', ['controller' => 'categories', 'action' => 'duplicateChecking', '_method' => ['POST', 'OPTIONS']])->setPass(['id', 'type']);

    //attribute-sets-categories
    $routes->connect('/variant-sets-categories/add', ['controller' => 'variant-sets-categories', 'action' => 'add']);
    $routes->connect('/variant-sets-categories/view/:id', ['controller' => 'variant-se  ts-categories', 'action' => 'view'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/variant-sets-categories/delete', ['controller' => 'variant-sets-categories', 'action' => 'delete']);

    //variants
    $routes->connect('/variants/value', ['controller' => 'VariantSets', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/variants', ['controller' => 'variant-sets', 'action' => 'variantSetList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/variants', ['controller' => 'variant-sets', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/variants/:id', ['controller' => 'variant-sets', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/variants/:id', ['controller' => 'variant-sets', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    // Variant value
    $routes->connect('/variants/:id', ['controller' => 'variants', 'action' => 'index', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/variants/:id/value', ['controller' => 'variants', 'action' => 'add', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/variants/:v_id/value/:id', ['controller' => 'variants', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['v_id' => '\d+', 'id' => '\d+', 'pass' => ['v_id', 'id']]);
    $routes->connect('/variants/value/:id', ['controller' => 'variants', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    //Suppliers
    $routes->connect('/suppliers/:id', ['controller' => 'suppliers', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/suppliers', ['controller' => 'suppliers', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/suppliers/list', ['controller' => 'suppliers', 'action' => 'lists', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/suppliers', ['controller' => 'suppliers', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/suppliers/:id', ['controller' => 'suppliers', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/suppliers/:id', ['controller' => 'suppliers', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    //-----------------------------------------------ratings--------------------------------------------------------
    $routes->connect('/products/:id/user/products-review', ['controller' => 'ratings', 'action' => 'getRatings'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/:id/user/add-rating', ['controller' => 'ratings', 'action' => 'addRating'], ['id' => '\d+', 'pass' => ['id']]);
    //---------------------------------------------Tag---------------------------------------------------------------
    $routes->connect('/tags', ['controller' => 'tags', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/tags', ['controller' => 'tags', 'action' => 'add', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/tags/sort', ['controller' => 'tags', 'action' => 'sortTags', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/tags/:id', ['controller' => 'tags', 'action' => 'add', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/tags/:id', ['controller' => 'tags', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/products/online', ['controller' => 'Tags', 'action' => 'products']);
    $routes->connect('/products/online/closet', ['controller' => 'Tags', 'action' => 'closetProducts', '_method' => ['POST', 'OPTIONS']]);
    // list with availability
    $routes->connect('/products/online/available', ['controller' => 'Tags', 'action' => 'productWithAvailability', '_method' => ['POST', 'OPTIONS']]);
    //$routes->connect('/products/user/index', ['controller' => 'Tags', 'action' => 'products']);
    $routes->connect('/products/list', ['controller' => 'Tags', 'action' => 'allProducts'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/tag/products', ['controller' => 'ProductsTags', 'action' => 'products']);
    //----------------------------------coupons--------------------------------------------------------------
    $routes->connect('/coupons/get-coupns', ['controller' => 'coupons', 'action' => 'getCoupon']);
    $routes->connect('/coupons', ['controller' => 'coupons', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/coupons', ['controller' => 'coupons', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/coupons/:id', ['controller' => 'coupons', 'action' => 'edit-coupon', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/coupons/:id', ['controller' => 'coupons', 'action' => 'view-coupon', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/coupons/:id', ['controller' => 'coupons', 'action' => 'delete-coupon', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    //--------------------------------carts-----------------------------------------------------
    $routes->connect('/carts/add-discount', ['controller' => 'carts', 'action' => 'addDiscount', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/get-price-value', ['controller' => 'carts', 'action' => 'getPriceValue', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/carts/add-to-cart', ['controller' => 'carts', 'action' => 'add', '_method' => ['OPTIONS', 'POST']]);

//
    $routes->connect('/carts/add-to-cart/by-category', ['controller' => 'carts', 'action' => 'addToCartByCategory', '_method' => ['OPTIONS', 'POST']]);

    $routes->connect('/carts/add-shipping', ['controller' => 'carts', 'action' => 'addShipping', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/carts/delivery', ['controller' => 'carts', 'action' => 'Delivery', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/orders/delivery-cost', ['controller' => 'Delivery', 'action' => 'deliveryCost', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/carts/cart-remove-item', ['controller' => 'carts', 'action' => 'removeItemFromCart', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/carts/apply-coupon', ['controller' => 'carts', 'action' => 'applyCoupon']);
    $routes->connect('/carts/coupon/delete', ['controller' => 'carts', 'action' => 'deleteCoupon'])->setMethods(['OPTIONS','POST']);
    $routes->connect('/coupons/get-value', ['controller' => 'coupons', 'action' => 'getValue', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/carts/update', ['controller' => 'carts', 'action' => 'update', '_method' => ['OPTIONS', 'POST']]);
    //$routes->connect('/carts/:id/update', ['controller' => 'carts', 'action' => 'update', '_method' => ['OPTIONS', 'POST']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/carts/:token', ['controller' => 'carts', 'action' => 'delete', '_method' => ['OPTIONS', 'DELETE']], ['token' => '[a-z0-9-_]+', 'pass' => ['token']]);
    $routes->connect('/carts/:token', ['controller' => 'carts', 'action' => 'details', '_method' => ['OPTIONS', 'GET']], ['token' => '[a-z0-9-_]+', 'pass' => ['token']]);
    $routes->connect('/check-cart/:token', ['controller' => 'Carts', 'action' => 'checkCart', '_method' => ['OPTIONS', 'GET']], ['token' => '[a-z0-9-_]+', 'pass' => ['token']]);
    $routes->connect('/free-shipping/:token', ['controller' => 'Delivery', 'action' => 'checkFreeShipping'])->setMethods(['OPTIONS', 'GET'])->setPass(['token']);
    $routes->connect('/carts/view-charges/:token', ['controller' => 'OrderCharge', 'action' => 'viewCartCharges', '_method' => ['GET', 'OPTIONS']])->setPass(['token']);
    /* Abandoned Cart */
    $routes->connect('/admin/carts/list', ['controller' => 'carts', 'action' => 'abandoned', '_method' => ['OPTIONS', 'GET']]);
    $routes->connect('/carts/:id/send-email', ['controller' => 'carts', 'action' => 'sendAbandonedEmail', '_method' => ['OPTIONS', 'POST']])->setPass(['id']);
    $routes->connect('/carts/send-bulk-email', ['controller' => 'carts', 'action' => 'sendAbandonedBulkEmail', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/carts/delete', ['controller' => 'carts', 'action' => 'deleteCart', '_method' => ['OPTIONS', 'DELETE']]);

    $routes->connect('/carts/:token/update-checkout-info', ['controller' => 'carts', 'action' => 'updateCheckoutInfo', '_method' => ['OPTIONS', 'POST']], ['token' => '[a-z0-9-_]+', 'pass' => ['token']]);
    /* OrderCosts */
    $routes->connect('/order-costs/:id', ['controller' => 'OrderCosts', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/order-costs/:id', ['controller' => 'OrderCosts', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/order-costs/:id', ['controller' => 'OrderCosts', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/order-costs', ['controller' => 'OrderCosts', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/order-costs', ['controller' => 'OrderCosts', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);

    //------------------------------------Orders---------------------------------------
    // order cancel
    $routes->connect('/orders/:id/cancel', ['controller' => 'orders', 'action' => 'cancelOrder'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

    $routes->connect('/orders/:id/status/:status', ['controller' => 'orders', 'action' => 'changeStatus'])->setMethods(['GET', 'OPTIONS'])->setPass(['id', 'status']);
    $routes->connect('/orders/status/:status', ['controller' => 'orders', 'action' => 'changeMultipleStatus'])->setMethods(['POST', 'OPTIONS'])->setPass(['status']);
    // new api for changing status
    $routes->connect('/orders/process/status', ['controller' => 'orders', 'action' => 'changeStatusByProcess'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/orders/:id/update', ['controller' => 'orders', 'action' => 'update', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders', ['controller' => 'orders', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/orders/add-item', ['controller' => 'orders', 'action' => 'addItem', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/package/add-item', ['controller' => 'orders', 'action' => 'addPackageItem', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/item/:id/delete', ['controller' => 'orders', 'action' => 'itemDelete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/:id/review', ['controller' => 'orders', 'action' => 'review', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/:id', ['controller' => 'orders', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/:id', ['controller' => 'orders', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/order-items/:id', ['controller' => 'AssetOrderManagement', 'action' => 'orderItems', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/update-item', ['controller' => 'orders', 'action' => 'updateItem', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/:uid/complete', ['controller' => 'orders', 'action' => 'complete'], ['uid' => '[A-Za-z0-9-]+', 'pass' => ['uid']]);
    $routes->connect('/orders/:uid/ical-download', ['controller' => 'orders', 'action' => 'iCalDownload'], ['uid' => '[A-Za-z0-9-]+', 'pass' => ['uid']]);
    $routes->connect('/orders/:uid', ['controller' => 'orders', 'action' => 'summary'], ['uid' => '[A-Za-z0-9-]+', 'pass' => ['uid']]);
    $routes->connect('/orders', ['controller' => 'InstoreOrder', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/cart-info', ['controller' => 'InstoreOrder', 'action' => 'cartinfo', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/online', ['controller' => 'OnlineOrder', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/order/apply', ['controller' => 'Orders', 'action' => 'makeOrder', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/customer/search', ['controller' => 'customers', 'action' => 'search', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/orders/add-discount', ['controller' => 'orders', 'action' => 'itemDiscount', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/:id/discount', ['controller' => 'orders', 'action' => 'getOrderDiscount'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/copy', ['controller' => 'orders', 'action' => 'copy'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/bulk-update', ['controller' => 'orders', 'action' => 'bulkUpdate', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/bulk-receipt-download', ['controller' => 'orders', 'action' => 'bulkReceiptDownload', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/bulk-picklist-download', ['controller' => 'orders', 'action' => 'bulkPickListDownload', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/orders/details', ['controller' => 'orders', 'action' => 'orderListDetails', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect('/orders/item/:id/status/:status', ['controller' => 'orders', 'action' => 'itemStatusUpdate', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id', 'status']]);
    // multiple order item return
    $routes->connect('/orders/items/return', ['controller' => 'orders', 'action' => 'itemReturn', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/return-request/cancel', ['controller' => 'orders', 'action' => 'cancelReturnRequest', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/return-request/change-ship-id', ['controller' => 'orders', 'action' => 'changeShipId', '_method' => ['POST', 'OPTIONS']]);

    // re-send order email
    $routes->connect('/order/:id/email', ['controller' => 'InstoreOrder', 'action' => 'resendEmail'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    // re-send order waiver sharing
    $routes->connect('/order/:id/email/waiver-sharing', ['controller' => 'InstoreOrder', 'action' => 'sendWaiverSharingEmail'])->setMethods(['POST'])->setPass(['id']);
    $routes->connect('/settings/orders/:id/:type', ['controller' => 'InstoreOrder', 'action' => 'changeOrderSettings'])->setMethods(['OPTIONS', 'POST'])->setPass(['id', 'type']);
    $routes->connect('/order/:id/email/collect-signature', ['controller' => 'InstoreOrder', 'action' => 'sendCollectSignatureEmail'])->setMethods(['POST'])->setPass(['id']);


    $routes->connect('/orders/delivery', ['controller' => 'orders', 'action' => 'delivery', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/order/delivery/:type', ['controller' => 'OrdersDelivery', 'action' => 'deliveryByType', '_method' => ['OPTIONS', 'POST']])->setPass(['type']);
    $routes->connect('/order/delivery/:type/:project_id', ['controller' => 'OrdersDelivery', 'action' => 'deliveryInfo', '_method' => ['OPTIONS', 'GET','POST']])->setPass(['type','project_id']);
    $routes->connect('/order/delivery/:type/check', ['controller' => 'OrdersDelivery', 'action' => 'checkExisting', '_method' => ['OPTIONS', 'POST']])->setPass(['type']);

    // RNTM-3070: order delivery charge update
    $routes->connect('/orders/:id/delivery-charge', ['controller' => 'orders', 'action' => 'updateDeliveryCharge', '_method' => ['OPTIONS', 'POST']])->setPass(['id']);
    //
    $routes->connect('/orders/delivery/details', ['controller' => 'OrdersDelivery', 'action' => 'deliveryDetails', '_method' => ['OPTIONS', 'GET']]);
    $routes->connect('/orders/delivery/status', ['controller' => 'OrdersDelivery', 'action' => 'updateDeliveryStatus'])->setMethods(['POST', 'OPTIONS'])->setPass(['order_id']);
    $routes->connect('/orders/fulfillment/:id', ['controller' => 'OrdersDelivery', 'action' => 'fulfillmentDetails', '_method' => ['OPTIONS', 'GET']])->setPass(['id']);
    $routes->connect('/orders/fulfillment/:id', ['controller' => 'OrdersDelivery', 'action' => 'deleteFulfillment', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/delivery-jobs', ['controller' => 'OrdersDelivery', 'action' => 'fulfillmentOrders', '_method' => ['OPTIONS', 'GET']]);
    // RNTM-3179
    $routes->connect('/orders/:id/fulfillment-update', ['controller' => 'OrdersDelivery', 'action' => 'updateFulfillment', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    //    Closet order item
    $routes->connect('/orders/closet', ['controller' => 'Orders', 'action' => 'addClosetItems', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/:id/exchange-request', ['controller' => 'Orders', 'action' => 'exchangeRequests', '_method' => ['GET', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/orders/:id/closet/swap', ['controller' => 'Orders', 'action' => 'approveClosetSwap', '_method' => ['POST', 'OPTIONS']])->setPass(['id']);

    $routes->connect('/orders/using-product', ['controller' => 'Orders', 'action' => 'orderUsingProduct', '_method' => ['POST', 'OPTIONS']]);

    // Order Item return request - RNTM-2844
    $routes->connect('/orders/with-details', ['controller' => 'Orders', 'action' => 'orderWithDetails', '_method' => ['GET', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/orders/return-delivery-charge', ['controller' => 'Orders', 'action' => 'addReturnDeliveryCharge', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/orders/assigned-assets', ['controller' => 'Orders', 'action' => 'assignedAssetsOfOrders', '_method' => ['GET', 'OPTIONS']]);
    /*bolt card-connect*/


    $routes->connect('/orders/bolt-card-connect', ['controller' => 'orders', 'action' => 'boltCardConnect', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/capture', ['controller' => 'orders', 'action' => 'capture', '_method' => ['POST', 'OPTIONS']]);
    //$routes->connect('/orders/refund', ['controller' => 'orders', 'action' => 'refund', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/orders/void', ['controller' => 'orders', 'action' => 'void', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/cancel', ['controller' => 'orders', 'action' => 'cancel', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/print-receipt', ['controller' => 'orders', 'action' => 'printReceipt', 'allowWithoutToken' => true, '_method' => ['POST', 'OPTIONS']]);

    /** READ Card */
    $routes->connect('/payments/swipe', ['controller' => 'Payments', 'action' => 'swipe'])->setMethods(['POST', 'OPTIONS']);

    /* accept quote*/
    $routes->connect('/quotes', ['controller' => 'quote', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/quotes', ['controller' => 'quote', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/quotes/:order_id/manual-item', ['controller' => 'quote', 'action' => 'addManualOrderItem'])->setMethods(['POST', 'OPTIONS'])->setPass(['order_id']);
    $routes->connect('/quote/from-wishlist', ['controller' => 'Quote', 'action' => 'addQuoteFromWishList','_method' => ['POST', 'OPTIONS']]);
    /* accept quote*/
    $routes->connect('/orders/accept-quote', ['controller' => 'orders', 'action' => 'acceptQuote'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/orders/:id/custom-fields', ['controller' => 'orders', 'action' => 'additionalField'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/editable/custom-fields', ['controller' => 'orders', 'action' => 'editableAdditionalField'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    //$routes->connect('/orders/:id/custom-fields', ['controller' => 'orders', 'action' => 'customFields'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    //$routes->connect('/orders/:id/custom-fields', ['controller' => 'orders', 'action' => 'updateOrderCustomFields'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/custom-fields', ['controller' => 'orders', 'action' => 'updateAdditionalField'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);

    /* additional field details */
    $routes->connect('/view-additional-field/:order_id/:additional_field_id', ['controller' => 'Orders', 'action' => 'viewAdditionalField'])->setMethods(['GET', 'OPTIONS'])->setPass(['order_id', 'additional_field_id']);


    /*Enduring payments*/
    $routes->connect('/orders/:id/enduring/cancel', ['controller' => 'EnduringRental', 'action' => 'cancelOrderEnduringProcess'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/:item_id/enduring/cancel', ['controller' => 'EnduringRental', 'action' => 'cancelEnduringProcess'])->setMethods(['GET', 'OPTIONS'])->setPass(['id', 'item_id']);
    $routes->connect('/orders/:id/:item_id/enduring/start-payment', ['controller' => 'EnduringRental', 'action' => 'startEnduringPayment'])->setMethods(['GET', 'OPTIONS'])->setPass(['id', 'item_id']);
    $routes->connect('/orders/:id/enduring/retrieve', ['controller' => 'EnduringRental', 'action' => 'retrieveEnduringProcess'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/enduring/change-plan', ['controller' => 'EnduringRental', 'action' => 'changeOrderEnduringPlan'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/:item_id/enduring/change-plan', ['controller' => 'EnduringRental', 'action' => 'changeEnduringPlan'])->setMethods(['POST', 'OPTIONS'])->setPass(['id', 'item_id']);
    //after order enduring
    $routes->connect('/orders/:id/enduring', ['controller' => 'EnduringRental', 'action' => 'getEndurableData'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/enduring', ['controller' => 'EnduringRental', 'action' => 'makeEnduring'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    //enduring manual payment
    $routes->connect('/orders/:id/enduring/payment', ['controller' => 'EnduringRental', 'action' => 'enduringPayment'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/cancel/manual/enduring', ['controller' => 'EnduringRental', 'action' => 'cancelManualEnduringProcess'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/orders/:id/edit/manual/enduring', ['controller' => 'EnduringRental', 'action' => 'editEnduring'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);

    $routes->connect('/enduring/webhook/:type', ['controller' => 'EnduringRental', 'action' => 'webHook','allowWithoutToken' => true,])->setMethods(['POST', 'OPTIONS'])->setPass(['type']);

    /*Payment Gateway*/
    $routes->connect('/payments/gateways', ['controller' => 'PaymentGateways', 'action' => 'config', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/payments/gateway', ['controller' => 'PaymentGateways', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/payments/gateway', ['controller' => 'PaymentGateways', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/payments/gateway/:id', ['controller' => 'PaymentGateways', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/payments/gateway/:id', ['controller' => 'PaymentGateways', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/payments/gateway/:id/test', ['controller' => 'PaymentGateways', 'action' => 'testGateway', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/payments/gateway/:name', ['controller' => 'PaymentGateways', 'action' => 'detailByName', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['name']]);


    $routes->connect('/products/reservation/order', ['controller' => 'orders', 'action' => 'reservationOrder']);

    //--------------------------------------Order Additional Charge----------------------------------/
    $routes->connect('/orders/add-charge', ['controller' => 'OrderCharge', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/orders/delete-charge/:id', ['controller' => 'OrderCharge', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/view-charges/:order_id/:id', ['controller' => 'OrderCharge', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['order_id' => '\d+', 'id' => '\d+', 'pass' => ['order_id', 'id']]);
    $routes->connect('/orders/view-charges/:order_id', ['controller' => 'OrderCharge', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['order_id' => '\d+', 'pass' => ['order_id']]);
    $routes->connect('/orders/additional-charges', ['controller' => 'OrderChargesConfig', 'action' => 'calculateAdditionalCharge'])->setMethods(['OPTIONS', 'POST']);

    $routes->connect('/orders/additional-charges/create', ['controller' => 'OrderCharge', 'action' => 'additionalServiceChargeAdd'])->setMethods(['OPTIONS', 'POST']);
    //------------------------------------------Config data-----------------------------------------
    // @deprecated
    $routes->connect('/shipping-method', ['controller' => 'pages', 'action' => 'shippingMethod']);

    $routes->connect('/payments/methods', ['controller' => 'pages', 'action' => 'getPaymentMethod']);
    $routes->connect('/payments/gateway/settings', ['controller' => 'pages', 'action' => 'paymentGatewaySettings', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/payments/gateway/list', ['controller' => 'pages', 'action' => 'paymentGatewayList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/shipping/gateway/settings', ['controller' => 'shipping', 'action' => 'shippingGatewaySettings', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/shipping/gateway/list', ['controller' => 'shipping', 'action' => 'shippingGatewayList', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect('/tax', ['controller' => 'pages', 'action' => 'taxValue']);
    $routes->connect('/clear-cache', ['controller' => 'pages', 'action' => 'clearCache']);
    $routes->connect('/payments/card', ['controller' => 'pages', 'action' => 'payments']);
    $routes->connect('/image-upload', ['controller' => 'pages', 'action' => 'imageUpload']);
    $routes->connect('/get-settings', ['controller' => 'pages', 'action' => 'defaultLocation', 'allowWithoutToken' => true]);
    $routes->connect('/guest-token', ['controller' => 'pages', 'action' => 'guestToken', 'allowWithoutToken' => true]);
    $routes->connect('/products/locations', ['controller' => 'pages', 'action' => 'locations']);
    $routes->connect('/contactus', ['controller' => 'pages', 'action' => 'contactus']);
    $routes->connect('/send-feedback', ['controller' => 'pages', 'action' => 'sendFeedback', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/service-request', ['controller' => 'pages', 'action' => 'serviceRequest', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/import-sample', ['controller' => 'setup', 'action' => 'importSample', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/import-sample/:id', ['controller' => 'setup', 'action' => 'sampleProduct', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/captcha', ['controller' => 'settings', 'action' => 'contactPageCaptcha', '_method' => ['GET', 'OPTIONS']]);

    //------------------------------Search------------------------------------------------------------
    $routes->connect('/search/products', ['controller' => 'Search', 'action' => 'products']);
    $routes->connect('/search/products/package', ['controller' => 'Search', 'action' => 'packageSearch']);

    /** Package */
    $routes->connect('/package', ['controller' => 'Package', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/product-packages', ['controller' => 'Package', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/product-packages/:id', ['controller' => 'Package', 'action' => 'productPackages', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/product-packages/:id', ['controller' => 'Package', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/product-packages/:id', ['controller' => 'Package', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/package/:uid/details/:time', ['controller' => 'Package', 'action' => 'packageDetails'], ['uid' => '[A-Za-z0-9-]+', 'time' => '[A-Za-z0-9-]+', 'pass' => ['uid', 'time']]);
    $routes->connect('/package-details/:uid/:time', ['controller' => 'Package', 'action' => 'getPackageDetails'], ['uid' => '[A-Za-z0-9-]+', 'time' => '[A-Za-z0-9-]+', 'pass' => ['uid', 'time']]);
    $routes->connect('/package/:uid/term/:time', ['controller' => 'Package', 'action' => 'getPackageTerm'], ['uid' => '[A-Za-z0-9-]+', 'time' => '[A-Za-z0-9-]+', 'pass' => ['uid', 'time']]);
    $routes->connect('/package/:uid/availability', ['controller' => 'Package', 'action' => 'getPackageTerm'], ['uid' => '[A-Za-z0-9-]+', 'time' => '[A-Za-z0-9-]+', 'pass' => ['uid', 'time']]);
    $routes->connect('/package/add-to-cart', ['controller' => 'carts', 'action' => 'addPackageToCart', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/package/add-to-cart/buy', ['controller' => 'carts', 'action' => 'addPackageToCartForBuyItems'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/get-package-price', ['controller' => 'carts', 'action' => 'getPackagePrice', '_method' => ['OPTIONS', 'POST']]);

    //-------------------------------Register Report--------------------------------------------------
    $routes->connect('/denomination', ['controller' => 'registers', 'action' => 'denomination']);
    $routes->connect('/register/report', ['controller' => 'registers', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/register/report', ['controller' => 'registers', 'action' => 'getReport', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/register/get-data', ['controller' => 'registers', 'action' => 'getData', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/location-terminal-list', ['controller' => 'registers', 'action' => 'locationTerminalList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/register/report/daily', ['controller' => 'registers', 'action' => 'reportDaily', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/register_list', ['controller' => 'registers', 'action' => 'registerList', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/register/export', ['controller' => 'registers', 'action' => 'exportRegister', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/sales/refund', ['controller' => 'registers', 'action' => 'getRefundAndSales', '_method' => ['POST', 'OPTIONS']]);

    //-----------------------------------stores and apps-------------------------------------------------------------
    //$routes->resources('Stores');
    $routes->connect('/store-setup/:id', ['controller' => 'stores', 'action' => 'updateSetup', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    $routes->connect('/stores/list', ['controller' => 'stores', 'action' => 'getAllStore', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/stores/user', ['controller' => 'users', 'action' => 'storeUser', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/stores/user/activate/', ['controller' => 'users', 'action' => 'activeStoreUser', 'allowWithoutToken' => true]);

    $routes->connect('/stores/:id', ['controller' => 'stores', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/stores', ['controller' => 'stores', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/stores', ['controller' => 'stores', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/stores/:id/store-logo', ['controller' => 'stores', 'action' => 'uploadStoreLogo', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/stores/logo', ['controller' => 'stores', 'action' => 'deleteLogo'])->setMethods(['DELETE', 'OPTIONS']); // delete store logo
    $routes->connect('/stores/:id', ['controller' => 'stores', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/stores/:id', ['controller' => 'stores', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/stores/custom-css', ['controller' => 'contents', 'action' => 'customCss', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/stores/custom-css', ['controller' => 'contents', 'action' => 'getCustomCss', '_method' => ['OPTIONS', 'GET']]);

    $routes->connect('/stores/sitemap', ['controller' => 'contents', 'action' => 'getStoreSiteMap', '_method' => ['OPTIONS', 'GET'], 'allowWithoutToken' => true]);
    $routes->connect('/stores/:slug/meta/:type', ['controller' => 'stores', 'action' => 'metaInfoByType', '_method' => ['OPTIONS', 'GET'], 'allowWithoutToken' => true])->setPass(['slug', 'type']);

    // js
    $routes->connect('/stores/custom-js', ['controller' => 'contents', 'action' => 'customJs', '_method' => ['OPTIONS', 'POST']]);
    $routes->connect('/stores/custom-js', ['controller' => 'contents', 'action' => 'getCustomJs', '_method' => ['OPTIONS', 'GET']]);

    // RNTM-3730
    $routes->connect('/stores-option/:type', ['controller' => 'StoreOptions', 'action' => 'add', '_method' => ['OPTIONS', 'POST']])->setPass(['type']);
    $routes->connect('/stores-option/:type', ['controller' => 'StoreOptions', 'action' => 'listByType', '_method' => ['OPTIONS', 'GET']])->setPass(['type']);

    $routes->connect('/user-terminal/:id', ['controller' => 'StoresUsers', 'action' => 'getUserTerminal'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/user-terminal/:id', ['controller' => 'StoresUsers', 'action' => 'getUserTerminal'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/locations/choose', ['controller' => 'stores-users', 'action' => 'choose', '_method' => ['POST', 'OPTIONS']]);

    /** Manage API Keys  */
    $routes->post('/apps', ['controller' => 'Applications', 'action' => 'create']);
    $routes->post('/apps/:id', ['controller' => 'Applications', 'action' => 'edit'])->setPass(['id']);
    $routes->delete('/apps/:id', ['controller' => 'Applications', 'action' => 'delete'])->setPass(['id']);
    $routes->get('/apps/:id', ['controller' => 'Applications', 'action' => 'view'])->setPass(['id']);
    $routes->get('/apps', ['controller' => 'Applications', 'action' => 'index']);
    $routes->post('/apps/access-token', ['controller' => 'Applications', 'action' => 'makeAppsToken', 'allowWithoutToken' => true]);
    $routes->get('/apps/integrations/:reference', ['controller' => 'Applications', 'action' => 'viewByReference'])->setPass(['reference']);
    //RNTM-3219
    $routes->post('/apps/access-token-by-location', ['controller' => 'Applications', 'action' => 'accessTokenByLocation']);


    /*Locations*/
    $routes->connect('/locations/list', ['controller' => 'locations', 'action' => 'locationList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/locations', ['controller' => 'locations', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/locations', ['controller' => 'locations', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/locations/all', ['controller' => 'locations', 'action' => 'all', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/locations/:id', ['controller' => 'locations', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/locations/:id', ['controller' => 'locations', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/locations/:id', ['controller' => 'locations', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/locations/online', ['controller' => 'locations', 'action' => 'getOnlineLocation', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /** Store   Terminals */
    $routes->connect('/terminals', ['controller' => 'stores-terminals', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/terminals', ['controller' => 'stores-terminals', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/terminals/:id', ['controller' => 'stores-terminals', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/terminals/:id', ['controller' => 'stores-terminals', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    //StoresTerminals
    $routes->connect('/register-user', ['controller' => 'StoresTerminals', 'action' => 'registerUser']);
    $routes->connect('/register-user', ['controller' => 'StoresTerminals', 'action' => 'registerUser']);

    //-----------------------------contents---------------------------------------
    $routes->connect('/contents/upload-image', ['controller' => 'contents', 'action' => 'mediaUpload']);
    $routes->connect('/contents/tags', ['controller' => 'contents', 'action' => 'getAllContentTags']);
    $routes->connect('/contents', ['controller' => 'contents', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/contents', ['controller' => 'contents', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/contents/:id', ['controller' => 'contents', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/contents/:id', ['controller' => 'contents', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/dynamic-contents', ['controller' => 'contents', 'action' => 'PageDynamicContents', '_method' => ['GET', 'OPTIONS']]);

    /** ------------------------- Store configs ( it is actually content ----------------------------- */
    $routes->connect('/store-config/:type', ['controller' => 'stores', 'action' => 'getConfig', '_method' => ['GET', 'OPTIONS']], ['type' => '[\w-]+', 'pass' => ['type']]);
    $routes->connect('/store-config', ['controller' => 'stores', 'action' => 'getConfig', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/cost-type-names', ['controller' => 'stores', 'action' => 'getCostTypeNames', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/store-config/update/:type', ['controller' => 'stores', 'action' => 'updateConfig', '_method' => ['POST', 'OPTIONS']], ['type' => '[\w-]+', 'pass' => ['type']]);
    $routes->connect('/store-config/delete/smtp', ['controller' => 'stores', 'action' => 'deleteSmtp', '_method' => ['DELETE', 'OPTIONS']]);
    $routes->connect('/default-config/:config_ame', ['controller' => 'stores', 'action' => 'staticAppConfig', '_method' => ['GET', 'OPTIONS']], ['config_ame' => '[\w-]+', 'pass' => ['config_ame']]);
    $routes->connect('/store-config/for-ssr', ['controller' => 'stores', 'action' => 'getConfigByStoreSlug', '_method' => ['GET', 'OPTIONS'], 'allowWithoutToken' => true]);
    $routes->connect('/store-config/update', ['controller' => 'stores', 'action' => 'updateConfigBySlug', '_method' => ['POST', 'OPTIONS']]);
    //------------------------users section------------------------------------------
    $routes->connect('/roles', ['controller' => 'roles', 'action' => 'index','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/roles/create', ['controller' => 'roles', 'action' => 'create','_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/roles/update', ['controller' => 'roles', 'action' => 'update','_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/roles/:id', ['controller' => 'roles', 'action' => 'view','_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/roles/:id', ['controller' => 'roles', 'action' => 'delete','_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);


    $routes->connect('/users/permissions', ['controller' => 'roles', 'action' => 'permissions','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/users/permissions', ['controller' => 'roles', 'action' => 'permissionsStore','_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/users/admin', ['controller' => 'users', 'action' => 'admin']);
    $routes->connect('/users/:id/account', ['controller' => 'users', 'action' => 'account'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/address/*', ['controller' => 'users', 'action' => 'address'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/addresses/:id/delete', ['controller' => 'addresses', 'action' => 'delete'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/:id/info', ['controller' => 'users', 'action' => 'info'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/register', ['controller' => 'users', 'action' => 'register', 'allowWithoutToken' => true]);
    $routes->connect('/store/signup', ['controller' => 'users', 'action' => 'storeSignup', 'allowWithoutToken' => true]);
    $routes->connect('/users/forgot-password', ['controller' => 'users', 'action' => 'forgotPassword', 'allowWithoutToken' => true]);
    $routes->connect('/users/change-password/*', ['controller' => 'users', 'action' => 'changePassword'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/:id/admin/change-password', ['controller' => 'users', 'action' => 'adminChangePassword'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/resend-activation/:id', ['controller' => 'users', 'action' => 'resendActivation'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/reset-password/:id', ['controller' => 'users', 'action' => 'resetPassword', 'allowWithoutToken' => true], ['id' => '\w+', 'pass' => ['id']]);
    $routes->connect('/users/avatar/*', ['controller' => 'users', 'action' => 'changeAvatar'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/:id/dashboard', ['controller' => 'users', 'action' => 'dashboard'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/:id/delete', ['controller' => 'users', 'action' => 'delete'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/users/activate/', ['controller' => 'users', 'action' => 'activate', 'allowWithoutToken' => true]);
    $routes->connect('/users/activate-with-email/', ['controller' => 'users', 'action' => 'activateWithEmail', 'allowWithoutToken' => true]);
    $routes->connect('/users/activate-with-sms/', ['controller' => 'users', 'action' => 'activateWithSmsCode', 'allowWithoutToken' => true]);
    $routes->connect('/users/customers', ['controller' => 'users', 'action' => 'customers']); // searching existing customer with 'search' parameter
    $routes->connect('/users/customer/:id', ['controller' => 'users', 'action' => 'customer'], ['id' => '\d+', 'pass' => ['id']]); // customer details
    $routes->connect('/customer/add', ['controller' => 'users', 'action' => 'customerAdd']); // add customer
    $routes->connect('/orders/customer/:id', ['controller' => 'users', 'action' => 'customerView', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]); // edit customer
    $routes->connect('/orders/customer/:id', ['controller' => 'Customers', 'action' => 'updateOrderAddress', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]); // edit customer
    $routes->connect('/users/login', ['controller' => 'users', 'action' => 'login', 'allowWithoutToken' => true]);
    $routes->connect('/authenticate', ['controller' => 'users', 'action' => 'authenticate', 'allowWithoutToken' => true]);
    $routes->connect('/users/check-code/:id', ['controller' => 'users', 'action' => 'checkCode', 'allowWithoutToken' => true], ['id' => '\w+', 'pass' => ['id']]);
    $routes->connect('/stores/check-store', ['controller' => 'stores', 'action' => 'checkStore', 'allowWithoutToken' => true]);
    $routes->connect('/stores/change-url', ['controller' => 'stores', 'action' => 'changeStoreUrl'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/users/add', ['controller' => 'users', 'action' => 'add']);
    $routes->connect('/users/index', ['controller' => 'users', 'action' => 'index']);
    $routes->connect('/users/resend-activation-mail', ['controller' => 'users', 'action' => 'resendActivationMail', 'allowWithoutToken' => true, '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/users/check-email', ['controller' => 'users', 'action' => 'checkEmail', 'allowWithoutToken' => true, '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/users/update-pin', ['controller' => 'Users', 'action' => 'updatePin', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/users/authorize-pin', ['controller' => 'Users', 'action' => 'authorizePin', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/users/token', ['controller' => 'Access', 'action' => 'storeWiseToken']);
    $routes->connect('/users/contacts', ['controller' => 'users', 'action' => 'getStoreUsersContact', '_method' => ['GET', 'OPTIONS']]);

    // store default date range
    $routes->connect('/store/default-rental-date', ['controller' => 'Stores', 'action' => 'defaultDateRange', '_method' => ['GET', 'OPTIONS']]);
    // save users device token
    $routes->connect('/users/device-token', ['controller' => 'users', 'action' => 'saveDeviceToken', '_method' => ['POST', 'OPTIONS'], 'allowWithoutToken' => true]);

    // User settings
    $routes->connect('/user/settings-update', ['controller' => 'users', 'action' => 'saveUserSettings', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/users/settings/:input_type', ['controller' => 'users', 'action' => 'getUserSettings', '_method' => ['GET', 'OPTIONS']])->setPass(['input_type']);

    /* unused api */
    $routes->connect('/users/store-login', ['controller' => 'users', 'action' => 'storeLogin', 'allowWithoutToken' => true]);
    $routes->connect('/user/sending-sms', ['controller' => 'orders', 'action' => 'sendingSms']);
    $routes->connect('/users/payments', ['controller' => 'payments', 'action' => 'add'])->setMethods(['POST']);
    $routes->connect('/users/order', ['controller' => 'orders', 'action' => 'user-order']);
    $routes->connect('/no-price', ['controller' => 'pages', 'action' => 'noPrice'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/test', ['controller' => 'pages', 'action' => 'test']);


    /** customer */
    $routes->connect('/customers', ['controller' => 'customers', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/customers', ['controller' => 'customers', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/customers/:id', ['controller' => 'customers', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/customers/:id', ['controller' => 'customers', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/customers/:id', ['controller' => 'customers', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/customers/login', ['controller' => 'customers', 'action' => 'login'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/register', ['controller' => 'customers', 'action' => 'register'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/change-password', ['controller' => 'customers', 'action' => 'changePassword'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/change-avatar', ['controller' => 'customers', 'action' => 'changeAvatar'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/orders', ['controller' => 'customers', 'action' => 'customerOrder'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/customers/orders/:id', ['controller' => 'customers', 'action' => 'getOrderDetails', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/customers/activate', ['controller' => 'customers', 'action' => 'checkToken'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/customers/activate', ['controller' => 'customers', 'action' => 'activateCustomerPassword'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/address', ['controller' => 'CustomerAddresses', 'action' => 'getAddressByType'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/customers/address', ['controller' => 'CustomerAddresses', 'action' => 'addCustomerAddress'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/address/:id', ['controller' => 'CustomerAddresses', 'action' => 'editCustomerAddress'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/address/:id', ['controller' => 'CustomerAddresses', 'action' => 'viewCustomerAddress'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/address/:id', ['controller' => 'CustomerAddresses', 'action' => 'deleteCustomerAddress'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/logout', ['controller' => 'customers', 'action' => 'logout'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/customers/forgot-password', ['controller' => 'customers', 'action' => 'forgotPassword'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/reset-password/:activation_key', ['controller' => 'customers', 'action' => 'resetPassword'])->setMethods(['POST', 'OPTIONS'])->setPass(['activation_key']);
    $routes->connect('/customers/profile', ['controller' => 'customers', 'action' => 'viewProfile'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/customers/profile', ['controller' => 'customers', 'action' => 'editProfile'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/profile', ['controller' => 'customers', 'action' => 'editProfile'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/customers/sso/:type', ['controller' => 'customers', 'action' => 'ssoLogin'])->setMethods(['POST', 'OPTIONS'])->setPass(['type']);
    $routes->connect('/customer/:id', ['controller' => 'customers', 'action' => 'loginWithId'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customer/:id/settings', ['controller' => 'customers', 'action' => 'customerSettings'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);

    // RNTM-3207
    $routes->connect('/client/login', ['controller' => 'customers', 'action' => 'clientLogin'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/client/confirmation', ['controller' => 'customers', 'action' => 'clientConfirm'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/client/register', ['controller' => 'customers', 'action' => 'clientRegister'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/vendor/list', ['controller' => 'customers', 'action' => 'vendorList'])->setMethods(['GET', 'OPTIONS']);

    // customer subscription plans
    $routes->connect('/customers/plans', ['controller' => 'CustomerPlans', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/customers/plan', ['controller' => 'CustomerPlans', 'action' => 'create'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/plan/:id', ['controller' => 'CustomerPlans', 'action' => 'update'])->setMethods(['PUT', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/plan/:id', ['controller' => 'CustomerPlans', 'action' => 'delete'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);

    // customer subscription
    $routes->connect('/customers/:id/subscribe', ['controller' => 'CustomerSubscription', 'action' => 'subscribe'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/:id/subscribe/confirm', ['controller' => 'CustomerSubscription', 'action' => 'subscribeConfirm'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/:id/subscribe/change-plan', ['controller' => 'CustomerSubscription', 'action' => 'changeSubscriptionPlan'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/:id/subscribe/cancel-plan', ['controller' => 'CustomerSubscription', 'action' => 'cancelSubscription'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/:id/subscribe/billing-invoices', ['controller' => 'CustomerSubscription', 'action' => 'customerBillingInvoices'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/:id/subscribe/change-default-card', ['controller' => 'CustomerSubscription', 'action' => 'changeDefaultCard'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    // customer subscription webhook
    $routes->connect('/customers/subscription/webhook', ['controller' => 'CustomerSubscription', 'action' => 'webhook'])->setMethods(['POST', 'OPTIONS']);

    // customer membership coupon
    $routes->connect('/membership/coupons', ['controller' => 'MembershipCoupon', 'action' => 'couponList'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/membership/coupon', ['controller' => 'MembershipCoupon', 'action' => 'couponCreate'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/membership/coupon/:id', ['controller' => 'MembershipCoupon', 'action' => 'couponDetails'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/membership/coupon/:id', ['controller' => 'MembershipCoupon', 'action' => 'couponDelete'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);

    // customer membership promotion code
    $routes->connect('/membership/promotions', ['controller' => 'MembershipCoupon', 'action' => 'promotionList'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/membership/promotion', ['controller' => 'MembershipCoupon', 'action' => 'promotionCreate'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/membership/promotion/:id', ['controller' => 'MembershipCoupon', 'action' => 'promotionDelete'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);

    // customer membership charges
    $routes->connect('/customers/:id/membership-charges', ['controller' => 'CustomerMembership', 'action' => 'customerAdditionalCharges'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/membership-charges', ['controller' => 'CustomerMembership', 'action' => 'addAdditionalCharge'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/customers/:customer_id/membership-charges/:id', ['controller' => 'CustomerMembership', 'action' => 'removeAdditionalCharge'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['customer_id', 'id']);

    // customer membership point balance
    $routes->connect('/customers/:id/balance', ['controller' => 'CustomerMembership', 'action' => 'updateCustomerBalance'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/:id/balance-logs', ['controller' => 'CustomerMembership', 'action' => 'balanceLog'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

    // admin routes for customers add edit etc etc..
    $routes->connect('/customers/auth/change-password/:id', ['controller' => 'customers', 'action' => 'changePasswordAdmin'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/customers/auth/change-avatar/:id', ['controller' => 'customers', 'action' => 'changeAvatarAdmin'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);


    /** Atrium  */
    $routes->connect('/customers/atrium', ['controller' => 'Atrium', 'action' => 'studentLookup', '_method' => ['POST', 'OPTIONS']]);

    /* testing endpoints */

    $routes->connect('/anet', ['controller' => 'TestPayments', 'action' => 'authorizeNet', '_method' => ['POST']]);
    $routes->connect('/stripe', ['controller' => 'TestPayments', 'action' => 'index', '_method' => ['POST']]);
    $routes->connect('/stripe/swipe-test-payment', ['controller' => 'TestPayments', 'action' => 'swipeTestPayment', '_method' => ['POST']]);


    /* subscription endpoints */

    $routes->connect('/subscription/details/:id', ['controller' => 'subscription', 'action' => 'getUpgradDetails', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect(
        '/subscription/get-plans-by-name',
        ['controller' => 'subscription', 'action' => 'getPlansByProduct', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);

    $routes->connect(
        '/subscription/create',
        ['controller' => 'subscription', 'action' => 'subscribe', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect(
        '/subscription/change-plan',
        ['controller' => 'subscription', 'action' => 'changePlan', '_method' => ['POST', 'OPTIONS']]);

    // change custom subscription plans
    $routes->connect(
        '/subscription/custom/change-plan',
        ['controller' => 'subscription', 'action' => 'changeCustomSubscriptionPlan', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect(
        '/subscription',
        ['controller' => 'subscription', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect(
        '/subscription/features',
        ['controller' => 'subscription', 'action' => 'addFeatures'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect(
        '/subscription/plans',
        ['controller' => 'subscription', 'action' => 'storePlanList'])->setMethods(['GET', 'OPTIONS']);

    $routes->connect(
        '/subscription/plans/on-boarding',
        ['controller' => 'subscription', 'action' => 'onBoardingPlanList', 'allowWithoutToken' => true])->setMethods(['GET', 'OPTIONS']);

    $routes->connect(
        '/subscription/plans/:id/details',
        ['controller' => 'subscription', 'action' => 'planDetails', 'allowWithoutToken' => true])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

    $routes->connect(
        '/store/subscription',
        ['controller' => 'subscription', 'action' => 'makeSubscription', 'allowWithoutToken' => true])->setMethods(['POST', 'OPTIONS']);

    //######################

    $routes->connect(
        '/subscription/product/:name',
        ['controller' => 'subscription', 'action' => 'defineProduct', '_method' => ['POST', 'OPTIONS']],
        ['name' => '[\w-]+', 'pass' => ['name']]);

    $routes->connect(
        '/subscription/plan',
        ['controller' => 'subscription', 'action' => 'createPlan', '_method' => ['POST', 'OPTIONS']]);


    $routes->connect(
        '/subscription/invoices',
        ['controller' => 'subscription', 'action' => 'getInvoices', '_method' => ['GET', 'OPTIONS']],
        ['id' => '[\w-]+', 'pass' => ['id']]);
    $routes->connect(
        '/subscription/card-info',
        ['controller' => 'subscription', 'action' => 'cardInfo', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect(
        '/subscription/add-card',
        ['controller' => 'subscription', 'action' => 'addCard', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect(
        '/subscription/edit-card',
        ['controller' => 'subscription', 'action' => 'editCard', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect(
        '/subscription/delete-card',
        ['controller' => 'subscription', 'action' => 'deleteCard', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect(
        '/subscription/change-default-card',
        ['controller' => 'subscription', 'action' => 'changeDefaultCard', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect(
        '/subscription/get-products',
        ['controller' => 'subscription', 'action' => 'getProducts', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect(
        '/subscription/get-plans',
        ['controller' => 'subscription', 'action' => 'getPlans', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);

    $routes->connect(
        '/subscription/:store_id/confirm',
        ['controller' => 'Users', 'action' => 'confirmSubscription', 'allowWithoutToken' => true, '_method' => ['POST', 'OPTIONS']])->setPass(['store_id']);

    $routes->connect(
        '/subscription/webhook/:type',
        ['controller' => 'subscription', 'action' => 'webhook', 'allowWithoutToken' => true])->setMethods(['POST', 'OPTIONS'])->setPass(['type']);
    $routes->connect(
        '/subscription/create-invoice',
        ['controller' => 'subscription', 'action' => 'createInvoice', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect(
        '/subscription/payment-history',
        ['controller' => 'subscription', 'action' => 'paymentHistory', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect(
        '/subscription/get-invoice/:invoice_id',
        ['controller' => 'subscription', 'action' => 'getInvoice', '_method' => ['GET', 'OPTIONS']],
        ['invoice_id' => '[\w-]+', 'pass' => ['invoice_id']]);

    $routes->connect(
        '/store/subscription-details',
        ['controller' => 'subscription', 'action' => 'subscriptionDetails', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect(
        '/store/subscription-cancel',
        ['controller' => 'subscription', 'action' => 'subscriptionCancel', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect(
        '/subscription/test',
        ['controller' => 'subscription', 'action' => 'createRenewalInvoice', '_method' => ['GET', 'OPTIONS']],
        ['invoice_id' => '[\w-]+', 'pass' => ['invoice_id']]);
    $routes->connect('/billing/features/:plan_id', ['controller' => 'Subscription', 'action' => 'features'])->setMethods(['GET', 'OPTIONS'])->setPass(['plan_id']);
    ####################

    $routes->connect('/users/check-credentials/', ['controller' => 'users', 'action' => 'checkCredentials', 'allowWithoutToken' => true]);
    $routes->connect('/distance', ['controller' => 'distance', 'action' => 'index']);


    // shipping method
    // @deprecated
    $routes->connect('/shipping/validate-address/', ['controller' => 'shipping', 'action' => 'validateAddress']);
    $routes->connect('/shipping/change-status/:id', ['controller' => 'shipping', 'action' => 'changeStatus'], ['id' => '[\w -]+', 'pass' => ['id']]);
    $routes->connect('/fedex-service-list/', ['controller' => 'shipping', 'action' => 'fedexServiceList']);
    $routes->connect('/connect-upx/', ['controller' => 'shipping', 'action' => 'connectUps']);

    $routes->connect('/usps_rate/', ['controller' => 'shipping', 'action' => 'uspsRate']);

    /**********************/

    $routes->connect('/register/report/add', ['controller' => 'registers', 'action' => 'add']);
    $routes->connect('/connect-carrier/', ['controller' => 'shipping', 'action' => 'connectCarrier']);
    $routes->connect('/update-carrier/:id', ['controller' => 'shipping', 'action' => 'updateCarrier'], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/delete-carrier/:id', ['controller' => 'shipping', 'action' => 'deleteCarrier'], ['id' => '[\w -]+', 'pass' => ['id']]);
    $routes->connect('/carrier-list/', ['controller' => 'shipping', 'action' => 'index']);
    $routes->connect('/carrier-services/:id', ['controller' => 'shipping', 'action' => 'carrierServices'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/carrier-services/:id', ['controller' => 'shipping', 'action' => 'saveCarrierServices'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);

    $routes->connect('/carrier-package-list/:id', ['controller' => 'shipping', 'action' => 'carrierPackages'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/carrier-package-list/:id', ['controller' => 'shipping', 'action' => 'saveCarrierPackages'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/carrier-package/:id', ['controller' => 'shipping', 'action' => 'saveCarrierPackages'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/carrier-package/create', ['controller' => 'shipping', 'action' => 'createPackages'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/carrier-package/custom/create', ['controller' => 'shipping', 'action' => 'createCustomPackages'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/carrier-package/custom', ['controller' => 'shipping', 'action' => 'getCustomPackages'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/carrier-package/custom/delete/:id', ['controller' => 'shipping', 'action' => 'deleteCustomPackage'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/carrier-package/standard', ['controller' => 'shipping', 'action' => 'getStandardPackages'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/carrier-package/standard/update', ['controller' => 'shipping', 'action' => 'updateStandardPackages'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/shipping-carrier-list/', ['controller' => 'shipping', 'action' => 'carrierList']);
    $routes->connect('/shipping/rate/', ['controller' => 'shipping', 'action' => 'estimatedRate']);
    $routes->connect('/shipping/create-label/:ship_id/:type', ['controller' => 'Shipping', 'action' => 'createLabel'])->setMethods(['POST', 'OPTIONS'])->setPass(['ship_id', 'type']);
    $routes->connect('/shipping/orders', ['controller' => 'shipping', 'action' => 'orders'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/shipping/:id', ['controller' => 'shipping', 'action' => 'shipmentDetails'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/shipping/update/:id', ['controller' => 'shipping', 'action' => 'update'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/shipping/rate/:id', ['controller' => 'shipping', 'action' => 'rates'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/shipping/rate/:id', ['controller' => 'shipping', 'action' => 'getRatesForOrder'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/shihipping/ratepping/create-shipment/', ['controller' => 'shipping', 'action' => 'createShipment'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/shipping/cancel/:id', ['controller' => 'shipping', 'action' => 'cancelShipment'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/shipping/order/:id/additional-packages', ['controller' => 'shipping', 'action' => 'addShippingPackageForOrder'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/shipping/order/delete-package', ['controller' => 'shipping', 'action' => 'deleteShippingPackage'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/shipping/order/add-packages', ['controller' => 'shipping', 'action' => 'addOrderShippingPackages'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/shipping/order/:id/new-shipment', ['controller' => 'shipping', 'action' => 'createNewShipment'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

    $routes->connect('/shipping/bulk-action/export', ['controller' => 'shipping', 'action' => 'bulkExport', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/shipping/bulk-action/labels', ['controller' => 'shipping', 'action' => 'bulkLabels', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/shipping/bulk-action/picklist-labels', ['controller' => 'shipping', 'action' => 'bulkPicklistLabels', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/shipping/bulk-action/receipts-labels', ['controller' => 'shipping', 'action' => 'bulkReceiptsLabels', '_method' => ['POST', 'OPTIONS']]);

    #  $routes->connect('/get-shipping-rate/', ['controller' => 'shipping', 'action' => 'getShippingRate']);
    $routes->connect('/shipping/shipengine/sync', ['controller' => 'shipping', 'action' => 'syncCarrier'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/shipping/:ship_id/cancel', ['controller' => 'Shipping', 'action' => 'cancelShipping'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['ship_id']);
    /** @deprecated */
    $routes->connect('/inventory-summary', ['controller' => 'products', 'action' => 'inventorySummary']);


    // currency  settings

    $routes->connect('/currency-settings', ['controller' => 'Currencies', 'action' => 'index', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/currency-settings', ['controller' => 'Currencies', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/currency-settings/:id', ['controller' => 'Currencies', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/currency-settings/:id', ['controller' => 'Currencies', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/currency-settings/:id', ['controller' => 'Currencies', 'action' => 'delete', '_method' => ['POST', 'DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /** ========================================== UPDATE  ANY MODEL Values ============================== */
    //$routes->connect('/update-value/:model/:id', ['controller' => 'Assets', 'action' => 'updateFieldValues', '_method' => ['POST', 'OPTIONS']], ['model' => '[\w -]+', 'id' => '[\w -]+', 'pass' => ['model', 'id']]);
    $routes->connect('/update-value/Assets/:id', ['controller' => 'Assets', 'action' => 'updateAssetStatus', '_method' => ['POST', 'OPTIONS']], ['id' => '[\w -]+', 'pass' => ['id']]);

    /** Quickbook */
    $routes->connect('/quickbook', ['controller' => 'Quickbook', 'action' => 'index', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/quickbook/export/invoice/:id', ['controller' => 'Quickbook', 'action' => 'exportInvoice', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/quickbook/setup', ['controller' => 'Quickbook', 'action' => 'authorize', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/quickbook/token', ['controller' => 'Quickbook', 'action' => 'token', '_method' => ['POST', 'OPTIONS']]);

    /** mail chimp */
    $routes->connect('/mailchimp/setup', ['controller' => 'Mailchimp', 'action' => 'authorize', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/mailchimp/disconnect', ['controller' => 'Mailchimp', 'action' => 'disconnect', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/mailchimp/token', ['controller' => 'Mailchimp', 'action' => 'token', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/mailchimp/list', ['controller' => 'Mailchimp', 'action' => 'allList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/mailchimp/segments/:id', ['controller' => 'Mailchimp', 'action' => 'segments'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/mailchimp/customer/export', ['controller' => 'Mailchimp', 'action' => 'customerExport', '_method' => ['POST', 'OPTIONS']]);

    /** stripe terminal */
    $routes->connect('/stripe/terminal/create-token', ['controller' => 'Stripe', 'action' => 'createTerminalConnectionToken'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/stripe/terminal/:terminal_id/cancel', ['controller' => 'Stripe', 'action' => 'cancelStripeReader'])->setMethods(['GET', 'OPTIONS'])->setPass(['terminal_id']);

    /** Stripe Connect  */
    $routes->connect('/stripe/token', ['controller' => 'Stripe', 'action' => 'token'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/stripe/disconnect', ['controller' => 'Stripe', 'action' => 'disconnect'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/stripe/test', ['controller' => 'Stripe', 'action' => 'test'])->setMethods(['POST', 'OPTIONS']);
    /** Generic CMS  */
    $routes->connect('/generic/data/:value', ['controller' => 'Pages', 'action' => 'genericData', 'allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']], ['value' => '[\w -]+', 'pass' => ['value']]);

    /** Atrium API */
//    $routes->connect('/atrium/users', ['controller' => 'Atrium', 'action' => 'listUsers','allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);
//    $routes->connect('/atrium/institutions', ['controller' => 'Atrium', 'action' => 'institutions','allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']]);
//    $routes->connect('/atrium/institutions/:code/:type', ['controller' => 'Atrium', 'action' => 'getInstitutions','allowWithoutToken' => true, '_method' => ['GET', 'OPTIONS']], ['code' => '[\w -]+', 'type' => '[\w -]+', 'pass' => ['code','type']]);

    // some test api for s3 upload
    $routes->connect('/s3/list-buckets', ['controller' => 'S3', 'action' => 'listBuckets'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/s3/upload-files', ['controller' => 'S3', 'action' => 'uploadFiles'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/s3/list-files', ['controller' => 'S3', 'action' => 'listFiles'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/s3/delete-files', ['controller' => 'S3', 'action' => 'deleteFiles'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/image/exist/:id/:name', ['controller' => 'Images', 'action' => 's3ImageExist'])->setMethods(['GET', 'OPTIONS'])->setPass(['id', 'name']);

    /** Questionaries */
    $routes->connect('/questionaries', ['controller' => 'Pages', 'action' => 'getQuestionaries', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/questionaries', ['controller' => 'Onboarding', 'action' => 'questionaries', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/save-store-options', ['controller' => 'Pages', 'action' => 'saveStoreOptions'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/admin/onboarding', ['controller' => 'Onboarding', 'action' => 'quicksetupBoard', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/admin/dashboard-organizer', ['controller' => 'Onboarding', 'action' => 'dashboardOrganizer', '_method' => ['GET', 'OPTIONS']]);

    /** POS screen customer view */
    $routes->connect('/pos/view/:token', ['controller' => 'Customers', 'action' => 'posView', '_method' => ['GET', 'OPTIONS']], ['token' => '[\w -]+', 'pass' => ['token']]);
    $routes->connect('/pos/clear/:token', ['controller' => 'Customers', 'action' => 'posClear', '_method' => ['GET', 'OPTIONS']], ['token' => '[\w -]+', 'pass' => ['token']]);
    $routes->connect('/pos/signature', ['controller' => 'Customers', 'action' => 'saveSignature', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/pos/signature/collect', ['controller' => 'Customers', 'action' => 'collectSignature', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/pos/confirm-order', ['controller' => 'InstoreOrder', 'action' => 'confirmPOSOrder'])->setMethods(['POST', 'OPTIONS']);


    /** Video Wall  */
    $routes->connect('/video-wall/list', ['controller' => 'VideoWall', 'action' => 'index', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/video-wall', ['controller' => 'VideoWall', 'action' => 'add', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/video-wall/:id', ['controller' => 'VideoWall', 'action' => 'edit', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/video-wall/:id', ['controller' => 'VideoWall', 'action' => 'delete', '_method' => ['DELETE', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/video-wall/:id', ['controller' => 'VideoWall', 'action' => 'view', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    /** TMP media upload  */
    $routes->connect('/media/upload', ['controller' => 'Media', 'action' => 'upload', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/media/upload/editor', ['controller' => 'Media', 'action' => 'uploadEditor', '_method' => ['POST', 'OPTIONS'], 'allowWithoutToken' => true]);
    $routes->connect('/media/upload/multiple', ['controller' => 'Media', 'action' => 'uploadMultiple', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/media/list', ['controller' => 'Media', 'action' => 'fileList', '_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/media/delete', ['controller' => 'Media', 'action' => 'fileRemove', '_method' => ['DELETE', 'OPTIONS']]);

    /** Reporting */
    $routes->connect('/reports/onrent', ['controller' => 'Reports', 'action' => 'onRent', '_method' => ['POST', 'GET', 'OPTIONS']]);
    $routes->connect('/reports/sales', ['controller' => 'Reports', 'action' => 'sales']);
    $routes->connect('/reports/sales/export', ['controller' => 'Reports', 'action' => 'salesReport']);
    $routes->connect('/reports/inventory-in-hand', ['controller' => 'Reports', 'action' => 'inHandInventory', '_method' => ['POST', 'GET', 'OPTIONS']]);
    $routes->connect('/reports/inventory-by-location', ['controller' => 'Reports', 'action' => 'inventoryByLocation'])->setMethods(['POST', 'GET', 'OPTIONS']);
    $routes->connect('/reports/customer', ['controller' => 'Reports', 'action' => 'customer', '_method' => ['POST', 'GET', 'OPTIONS']]);
    $routes->connect('/reports/customer/:id/order-products', ['controller' => 'Reports', 'action' => 'customerOrderProducts', '_method' => ['GET', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/reports/transactions', ['controller' => 'Reports', 'action' => 'transactions', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/reports/tax', ['controller' => 'Reports', 'action' => 'tax', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/reports/charges', ['controller' => 'Reports', 'action' => 'charges', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/reports/orders-sales', ['controller' => 'Reports', 'action' => 'orderSales', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/reports/waiver-sharing', ['controller' => 'Reports', 'action' => 'waiverSharing', '_method' => ['POST', 'OPTIONS']]);

    $routes->connect('/reports/automatic-tax-rates', ['controller' => 'Reports', 'action' => 'automaticTaxRate', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/store/automatic-tax-locations', ['controller' => 'Tax', 'action' => 'automaticTaxLocations', '_method' => ['GET', 'OPTIONS']]);

    // RNTM-2937: prep report
    $routes->connect('/reports/upcoming-rentals', ['controller' => 'Reports', 'action' => 'upComingRental', '_method' => ['GET', 'OPTIONS']]);

    //commissions reporting
    $routes->connect('/reports/commissions', ['controller' => 'Reports', 'action' => 'commissions', '_method' => ['POST', 'OPTIONS']]);

    //commissions reporting
    $routes->connect('/reports/rental-duration', ['controller' => 'Reports', 'action' => 'rentalDuration', '_method' => ['GET', 'OPTIONS']]);
    //RNTM-3125 product revenue
    $routes->connect('/reports/product-revenue', ['controller' => 'Reports', 'action' => 'productRevenue', '_method' => ['GET', 'OPTIONS']]);

    $routes->connect('/admin/reports/:type', ['controller' => 'Reports', 'action' => 'getSavedReports', '_method' => ['GET', 'OPTIONS']])->setPass(['type']);
    $routes->connect('/admin/reports', ['controller' => 'Reports', 'action' => 'saveReports', '_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/admin/reports/:type/:id', ['controller' => 'Reports', 'action' => 'getSavedReportsById', '_method' => ['GET', 'OPTIONS']])->setPass(['type', 'id']);
    $routes->connect('/admin/reports/:type/:id', ['controller' => 'Reports', 'action' => 'deleteSavedReport', '_method' => ['DELETE', 'OPTIONS']])->setPass(['type', 'id']);
    $routes->connect('/admin/reports/:type/:id', ['controller' => 'Reports', 'action' => 'editSavedReport', '_method' => ['POST', 'OPTIONS']])->setPass(['type', 'id']);

    /** Store config (locations, tags, categories, variants, variant sets, payment gatewasy, shipping/ delivery */
    $routes->connect('/settings', ['controller' => 'Settings', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);

    $routes->connect('/settings/email-notification', ['controller' => 'Settings', 'action' => 'emailNotifications'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/settings/email-notification/show/:id', ['controller' => 'Settings', 'action' => 'showEmailNotifications'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/settings/email-notification/create', ['controller' => 'Settings', 'action' => 'createEmailNotifications'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/settings/email-notification/update', ['controller' => 'Settings', 'action' => 'updateEmailNotifications'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/settings/email-notification/delete/:id', ['controller' => 'Settings', 'action' => 'deleteEmailNotifications'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/settings/email-notification/test-trigger', ['controller' => 'Settings', 'action' => 'testEventTrigger'])->setMethods(['POST', 'OPTIONS']);

    $routes->connect('/addons', ['controller' => 'Addons', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/addons', ['controller' => 'Addons', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/addons/edit', ['controller' => 'Addons', 'action' => 'edit'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/addons/:uid', ['controller' => 'Addons', 'action' => 'view'])->setMethods(['GET', 'OPTIONS'])->setPass(['uid']);
    $routes->connect('/addons/:uid', ['controller' => 'Addons', 'action' => 'delete'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['uid']);
    $routes->connect('/products/:id/addons', ['controller' => 'Addons', 'action' => 'products'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);

    $routes->connect('/connect/session', ['controller' => 'Settings', 'action' => 'setSession', 'allowWithoutToken' => true])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/connect/session/:type', ['controller' => 'Settings', 'action' => 'getSession', 'allowWithoutToken' => true])->setMethods(['GET', 'OPTIONS'])->setPass(['type']);

    /** Jobs API */
    $routes->connect('/jobs', ['controller' => 'Jobs', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/jobs/list', ['controller' => 'Jobs', 'action' => 'getList'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/jobs', ['controller' => 'Jobs', 'action' => 'add'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/jobs/:id', ['controller' => 'Jobs', 'action' => 'edit'])->setMethods(['POST', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/jobs/:id', ['controller' => 'Jobs', 'action' => 'view'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/jobs/:id', ['controller' => 'Jobs', 'action' => 'delete'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/jobs/:id/:item_id', ['controller' => 'Jobs', 'action' => 'deleteItem'])->setMethods(['DELETE', 'OPTIONS'])->setPass(['id', 'item_id']);
    $routes->connect('/jobs/assign', ['controller' => 'Jobs', 'action' => 'assign'])->setMethods(['POST', 'OPTIONS']);

    // book API isbndb
    $routes->connect('/books/search', ['controller' => 'Search', 'action' => 'booksSearch'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/books/create', ['controller' => 'Products', 'action' => 'importFromIsbnDB'])->setMethods(['OPTIONS', 'POST']);
    //$routes->connect('/books/search', ['controller' => 'Search', 'action' => 'booksSearch'])->setMethods(['POST', 'OPTIONS']);


    $routes->connect('/gomerchant/vault/create', ['controller' => 'Gomerchant', 'action' => 'createVault'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/gomerchant/vault/:vaultKey', ['controller' => 'Gomerchant', 'action' => 'viewVault'])->setMethods(['GET', 'POST'])->setPass(['vaultKey']);
    $routes->connect('/gomerchant/vault/addCard', ['controller' => 'Gomerchant', 'action' => 'addCard'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/gomerchant/1stpayauthorize', ['controller' => 'Gomerchant', 'action' => 'vaultPayAuthorize'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/gomerchant/1stpaysale', ['controller' => 'Gomerchant', 'action' => 'vaultPaySale'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/recurring', ['controller' => 'Recurring', 'action' => 'index', 'allowWithoutToken' => true]);
    //$routes->connect('/gomerchant/recurring_modify', ['controller' => 'Gomerchant', 'action' => 'recurringModify'])->setMethods(['OPTIONS','POST']);
    $routes->connect('/orders/recurring/:order_id', ['controller' => 'Orders', 'action' => 'getRecurringDetails'])->setMethods(['GET', 'OPTIONS'])->setPass(['order_id']);
    $routes->connect('/orders/recurring/:order_id', ['controller' => 'Orders', 'action' => 'recurringModify'])->setMethods(['POST', 'OPTIONS'])->setPass(['order_id']);

    //settings orders additional charges
    $routes->connect('/settings/orders/additional-charges', ['controller' => 'OrderChargesConfig', 'action' => 'index'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/settings/orders/additional-charges/view/:id', ['controller' => 'OrderChargesConfig', 'action' => 'view'])->setMethods(['GET', 'OPTIONS'])->setPass(['id']);
    $routes->connect('/settings/orders/additional-charges/create', ['controller' => 'OrderChargesConfig', 'action' => 'add'])->setMethods(['OPTIONS', 'POST']);
    $routes->connect('/settings/orders/additional-charges/edit/:id', ['controller' => 'OrderChargesConfig', 'action' => 'edit'])->setMethods(['OPTIONS', 'PUT'])->setPass(['id']);
    $routes->connect('/settings/orders/additional-charges/delete/:id', ['controller' => 'OrderChargesConfig', 'action' => 'delete'])->setMethods(['OPTIONS', 'DELETE'])->setPass(['id']);


    // Status API
    $routes->connect('/settings/status/:type', ['controller' => 'Status', 'action' => 'index'])->setPass(['type'])->setMethods(['GET', 'OPTIONS']);
    $routes->connect('/settings/status/update', ['controller' => 'Status', 'action' => 'update'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/settings/status/delete/:id', ['controller' => 'Status', 'action' => 'delete'])->setMethods(['OPTIONS', 'DELETE'])->setPass(['id']);

    $routes->connect('/order/status', ['controller' => 'Status', 'action' => 'orderStatus']);

    $routes->connect('/order/delivery/estimate', ['controller' => 'Orders', 'action' => 'deliveryEstimate'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/orders/:id/multi-store-delivery', ['controller' => 'Orders', 'action' => 'multiStoreDeliveryViewByOrder', '_method' => ['GET', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);
    $routes->connect('/orders/:id/multi-store-delivery', ['controller' => 'Orders', 'action' => 'multiStoreDeliveryUpdateByOrder', '_method' => ['POST', 'OPTIONS']], ['id' => '\d+', 'pass' => ['id']]);

    $routes->connect('/dashboard/report', ['controller' => 'Users', 'action' => 'dashboardReports'])->setMethods(['GET', 'OPTIONS']);

    $routes->connect('/gateway/freedom-pay/init/:paymentMethod', ['controller' => 'Payments', 'action' => 'initFreedomPay'])->setPass(['paymentMethod'])->setMethods(['POST', 'OPTIONS']);
    $routes->connect('/gateway/freedom-pay/init', ['controller' => 'Payments', 'action' => 'initFreedomPay'])->setMethods(['POST', 'OPTIONS']);


    $routes->connect('/smtp/connect/google', ['controller' => 'Stores', 'action' => 'connectWithGoogle',  'allowWithoutToken' => true,])->setMethods(['GET', 'OPTIONS']);

    /** ------------------------- Checklist ----------------------------- */
    $routes->connect('/checklist', ['controller' => 'stores', 'action' => 'checkList','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/checklist', ['controller' => 'stores', 'action' => 'createCheckListItem','_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/checklist/:id', ['controller' => 'stores', 'action' => 'editCheckListItem','_method' => ['PUT', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/checklist/:id', ['controller' => 'stores', 'action' => 'deleteCheckListItem','_method' => ['DELETE', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/orders/:id/checklist', ['controller' => 'Orders', 'action' => 'orderChecklist','_method' => ['POST', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/orders/:id/checklist', ['controller' => 'Orders', 'action' => 'viewOrderChecklist','_method' => ['GET', 'OPTIONS']])->setPass(['id']);

    // collect signature
    $routes->connect('/orders/signature', ['controller' => 'Customers', 'action' => 'collectSignatureFromOnline', '_method' => ['POST', 'OPTIONS']]);
    /** ------------------------- Commission ----------------------------- */
    $routes->connect('/userlist/commissions', ['controller' => 'commission', 'action' => 'users','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/commissions', ['controller' => 'commission', 'action' => 'index','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/commissions', ['controller' => 'commission', 'action' => 'store','_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/commissions/:id', ['controller' => 'commission', 'action' => 'show','_method' => ['GET', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/commissions/:id', ['controller' => 'commission', 'action' => 'update','_method' => ['PUT', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/commissions/:id', ['controller' => 'commission', 'action' => 'delete','_method' => ['DELETE', 'OPTIONS']])->setPass(['id']);

    $routes->connect('/commissions/:type/:content_id', ['controller' => 'commission', 'action' => 'orderCommission','_method' => ['GET', 'OPTIONS']])->setPass(['type', 'content_id']);
    $routes->connect('/commissions/user', ['controller' => 'commission', 'action' => 'addNewUserToCommission','_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/commissions/user/:user_id/:type/:content_id', ['controller' => 'commission', 'action' => 'deleteUserCommission','_method' => ['DELETE', 'OPTIONS']])->setPass(['user_id', 'type', 'content_id']);

    /** Google service apis */
    $routes->connect('/google/address-validator', ['controller' => 'Addresses', 'action' => 'addressValidate', '_method' => ['POST', 'OPTIONS'], 'allowWithoutToken' => true]);
    $routes->connect('/google/image-generator', ['controller' => 'AI', 'action' => 'generateImage', '_method' => ['POST', 'OPTIONS']]);

    // V2 api
    $routes->connect('/v2/carts/add', ['controller' => 'carts', 'action' => 'v2add', '_method' => ['OPTIONS', 'POST']]);


    /** ROUTIFIC API */
    $routes->connect('/routific/submit',['controller'=>'Routific','action'=>'submit','_method'=>['POST','OPTIONS'],'allowWithoutToken'=>true]);

    /** custom form submit */
    $routes->connect('/custom-form/submit/:type',['controller'=>'Pages','action'=>'customFormSubmit','_method'=>['POST','OPTIONS']])->setPass(['type']);

//
    $routes->connect('/checkout/process',['controller'=>'Carts','action'=>'checkoutProcess','_method'=>['POST','OPTIONS']]);

    // wishlist
    $routes->connect('/wishlists', ['controller' => 'WishList', 'action' => 'index','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/wishlist', ['controller' => 'WishList', 'action' => 'add','_method' => ['POST', 'OPTIONS']]);
    $routes->connect('/wishlist/:id/send-email', ['controller' => 'WishList', 'action' => 'sendEmail','_method' => ['POST', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/wishlist/:token', ['controller' => 'WishList', 'action' => 'view','_method' => ['GET', 'OPTIONS']])->setPass(['token']);
    $routes->connect('/wishlist/:token', ['controller' => 'WishList', 'action' => 'delete','_method' => ['DELETE', 'OPTIONS']])->setPass(['token']);
    $routes->connect('/wishlist-item/:id', ['controller' => 'WishList', 'action' => 'deleteWithListItems','_method' => ['DELETE', 'OPTIONS']])->setPass(['id']);
    $routes->connect('/wishlist-item/:id/update', ['controller' => 'WishList', 'action' => 'updateWithListItems','_method' => ['PUT', 'PATCH', 'OPTIONS']])->setPass(['id']);

    // valorPayTech
    $routes->connect('/valorpay/client-token', ['controller' => 'ValorPay', 'action' => 'getClientToken','_method' => ['GET', 'OPTIONS']]);
    $routes->connect('/valorpay/terminal/:terminal_id/cancel', ['controller' => 'Valorpay', 'action' => 'cancelValorpayReader'])->setMethods(['GET', 'OPTIONS'])->setPass(['terminal_id']);

    $routes->connect('/payments/terminal/:terminal_id/:gateway/cancel', ['controller' => 'Payments', 'action' => 'cancelReader'])->setMethods(['GET', 'OPTIONS'])->setPass(['terminal_id', 'gateway']);

    $routes->fallbacks(DashedRoute::class);

});

// version 2 APIs
Router::scope('/api', function (RouteBuilder $routes) {

    $routes->prefix('v2', ['namespace' => 'App\Controller\V2'], function (RouteBuilder $routes) {
        $routes->connect('/product/:slug', [
            'controller' => 'Products',
            'action' => 'getProductDetails'
        ])->setPass(['slug']);

        $routes->connect('/package/:slug', [
            'controller' => 'Packages',
            'action' => 'getPackageDetails'
        ])->setPass(['slug']);
    });
});
//Plugin::routes();

?>
