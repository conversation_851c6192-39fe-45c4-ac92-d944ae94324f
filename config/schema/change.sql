-- RNTM-3285
ALTER TABLE refunds ADD COLUMN deposit_amount float null default 0 after amount;
--             RNTM-3350
php bin/cake.php Order s3WaiverSharing


--RNTM-3376 migrations
 php bin/cake.php migrations mark_migrated --target=20240508073356 --only
php bin/cake.php migrations migrate


php bin/cake.php TemplateDefaultUpdate receiptTemplateName
php bin/cake.php EmailNotification attachmentConfigToAllStore


php bin/cake.php order tacAcceptedFlag

--     RNTM-3441
    php bin/cake.php location updateAccessLogForLocations

    -- RNTM-3532
    php bin/cake.php products productEstConfigToReferenceProductTable 3285
-- RNTM-3568
    php bin/cake.php config defaultDeliveryAckToStores
--     RNTM-3557
php bin/cake.php exactTime locationSpecificStartTime

--     RNTM-3625
php bin/cake.php PricesModifier locationSpecificPricing
--     cronjob script for abandoned cart.
     php bin/cake.php abandoned-cart

    -- RNTM -3824
    php bin/cake.php config activateConfigForAllStore ai_assistant.active
    php bin/cake.php config activateConfigForAllStore ai_assistant.image.active


--     RNTM-3825
php bin/cake.php order assetReturnChargeToOrderCharge

--     product prices indexing
CREATE INDEX idx_variants_products_id_price_type
    ON product_prices (variants_products_id, price_type);

-- RNTM-3929
INSERT INTO currencies (label, code, symbol, status) VALUES ('Dominican Peso', 'DOP','RD$', 1);

-- RNTM-3927
php bin/cake.php store defaultSiteNameToMetaTitle

--     RNTM-4131
    php bin/cake.php custom_values sortCustomFields

--     RNTM-4376
    php bin/cake.php navigation addBlogToNavigation

--     RNTM-4492
php bin/cake.php navigation blogNavigationLabelRestore
