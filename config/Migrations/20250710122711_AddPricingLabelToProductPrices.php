<?php
use Migrations\AbstractMigration;

class AddPricingLabelToProductPrices extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('product_prices');
        $table->addColumn('pricing_label', 'string', [
            'default' => null,
            'limit' => 255,
            'null' => true,
            'after' => 'label',
        ]);
        $table->update();
    }
}
