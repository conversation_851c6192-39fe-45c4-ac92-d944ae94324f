<?php
use Migrations\AbstractMigration;

class AddTypeToTags extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('tags');
        $table->addColumn('type', 'string', [
            'default' => 'product',
            'limit' => 30,
            'null' => false,
            'after' => 'status'
        ]);
        $table->update();
    }
}
