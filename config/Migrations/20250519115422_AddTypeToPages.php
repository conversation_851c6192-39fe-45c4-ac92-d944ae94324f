<?php
use Migrations\AbstractMigration;

class AddTypeToPages extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('pages');
        $table->addColumn('type', 'string', [
            'default' => 'page',
            'limit' => 50,
            'null' => false,
            'after' => 'status'
        ]);


        $table->addColumn('tags', 'text', [
            'default' => null,
            'limit' => 255,
            'null' => true,
            'after' => 'type'
        ]);

        $table->addColumn('featured_image', 'string', [
            'default' => null,
            'limit' => 200,
            'null' => true,
            'after' => 'parent_id',
        ]);

        $table->addColumn('thumbnail_image', 'string', [
            'default' => null,
            'limit' => 200,
            'null' => true,
            'after' => 'featured_image'
        ]);

        $table->update();
    }
}
