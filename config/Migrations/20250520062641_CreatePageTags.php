<?php
use Migrations\AbstractMigration;

class CreatePageTags extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('page_tags');
        $table->addColumn('page_id', 'integer', [])
            ->addColumn('tag_id', 'integer', [])
            ->addColumn('status', 'integer', [
                'default' => 1,
            ])
            ->addColumn('created', 'datetime', [])
            ->addColumn('modified', 'datetime', [])
            ;
        $table->create();
    }
}
