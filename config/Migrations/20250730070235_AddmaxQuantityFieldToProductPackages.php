<?php
use Migrations\AbstractMigration;

class AddmaxQuantityFieldToProductPackages extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('product_packages');
        $table->addColumn('max_quantity', 'integer', [
            'default' => null,
            'null' => true,
            'after' => 'quantity',
        ]);
        $table->update();
    }
}
