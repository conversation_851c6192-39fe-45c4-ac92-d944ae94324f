<?php
use Migrations\AbstractMigration;

class CreateRateLimits extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     * @return void
     */
    public function change()
    {
        $table = $this->table('rate_limits')
        ->addColumn('ip', 'string', ['limit' => 45, 'null' => false])
        ->addColumn('user_agent', 'text', ['null' => true])
        ->addColumn('endpoint', 'string', ['limit' => 255, 'null' => false])
        ->addColumn('attempts', 'integer', ['default' => 0, 'null' => false])
        ->addColumn('last_attempt', 'datetime', ['null' => true])
        ->addColumn('blocked_until', 'datetime', ['null' => true])
        ->addColumn('created', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
        ->addColumn('modified', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
        ->addIndex(['ip', 'endpoint'], ['unique' => true, 'name' => 'unique_rate']);
        $table->create();
    }
}
