export APP_NAME="__APP_NAME__"
export DEBUG="false"
export APP_ENCODING="UTF-8"
export APP_DEFAULT_LOCALE="en_US"
export SECURITY_SALT="__SALT__"
export APP_ENV= 'dev'
#export API_URL= "https://api.universityrecrentals.com/"
export DOMAIN ='.universityrecrentals.com'
#AWS S3 configuration
export AWS_S3_ACCESS_KEY =""
export AWS_S3_ACCESS_SECRET =""
export AWS_S3_VERSION =""
export AWS_S3_REGION=""
export AWS_S3_PRODUCT_BUCKET="local.rentmy"  // local
export GOOGLE_APP_ID=""
export GOOGLE_APP_SECRET=""
export GOOGLE_APP_REDIRECT_URL=""

export SMS_gateway=""
export SMS_Twilio_sid=""
export SMS_Twilio_token=""
export SMS_Twilio_from="+"
export SMS_Twillo_messagingServiceSid=""

#EMAIL Config
export EMAIL_FROM_NAME="RentMy"
export EMAIL_FROM_EMAIL="<EMAIL>"

export APPLICATION_FEE_PERCENT=7
export ADMIN_EMAIL_TO='<EMAIL>'
export ADMIN_EMAIL_BCC='<EMAIL>'
export ORDER_EMAILS='<EMAIL>,<EMAIL>'
export PARTNER_HOST='http://partner.rentmy.leaperdev.rocks'
export CLIENT_HOST='http://client.rentmy.leaperdev.rocks'
export SERVER_IP='************,**************'
export HOST='http://rentmy.leaperdev.rocks'
export GOOGLE_API_KEY='AIzaSyCNDGKLAho8CCg_y67mwLkY89zzjJkOcAA'
export GOOGLE_GEO_API_KEY='AIzaSyDbvPlnG6m-_3PcsSdfpZSAVs9-ZWtEaCA'
export STORE_HOST='http://,.rentmy.leaperdev.rocks'
export SHIP_ENGINE_API_TOKEN='Y3/g2dcz6bDBUpOz7oAl/J8Hvm3tzt4MZqVEPPX3McA'
export SHIP_ENGINE_API_ENDPOINT='https://api.shipengine.com/v1'
export CLIENT_API='http://client-api-stage.rentmy.leaperdev.rocks'
export TIMEZONE='America/Chicago'
export TMP_IMG_UPLOAD_DIR='/var/www/tmp/'
export DIGEST_SENDING_TIME='3:00'

export GENIUS_RECORDS_URL=https://genius.leapinglogic.com/records/endpoint
export GENIUS_KEY=ddCAxK3hcY5fRqu5Z5
