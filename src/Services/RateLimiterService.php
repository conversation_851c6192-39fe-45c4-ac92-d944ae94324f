<?php

namespace App\Services;

use App\Lib\RentMy\RentMy;
use Cake\I18n\Time;
use Cake\Network\Exception\ForbiddenException;

class RateLimiterService
{
    protected $RateLimits;
    protected $ip;
    protected $userAgent;
    protected $path;
    protected $limit;
    protected $method;
    protected $window; // seconds
    protected $blockDuration; // seconds

    public function __construct($ip, $userAgent, $path, $method, $limit = 10, $window = 180, $blockDuration = 1800)
    {
        RentMy::addModel(['RateLimits']);
        $this->ip = $ip;
        $this->userAgent = $userAgent;
        $this->path = $path;
        $this->limit = $limit;
        $this->window = $window;
        $this->blockDuration = $blockDuration;
        $this->method = $method;
    }

    /**
     * @throws \Exception
     */
    public function check()
    {
        $now = Time::now();
        $rate = RentMy::$Model['RateLimits']->find()
            ->where(['ip' => $this->ip, 'endpoint' => $this->path])
            ->first();

        if (!empty($rate) && $rate->blocked_until && $rate->blocked_until->gt($now)) {
            throw new \Exception("You're blocked. Try again later.");
        }

        if (!$rate) {
            $rate = RentMy::$Model['RateLimits']->newEntity([
                'ip' => $this->ip,
                'user_agent' => $this->userAgent,
                'endpoint' => $this->path,
                'attempts' => 1,
                'last_attempt' => $now,
            ]);
        } else {
            $diff = $now->getTimestamp() - $rate->last_attempt->getTimestamp();

            if ($diff > $this->window) {
                $rate->attempts = 1;
            } else {
                $rate->attempts += 1;
            }

            $rate->last_attempt = $now;
        }

        if ($rate->attempts > $this->limit) {
            $rate->blocked_until = $now->addSeconds($this->blockDuration);
        }

        RentMy::$Model['RateLimits']->save($rate);

        if ($rate->blocked_until && $rate->blocked_until->gt($now)) {
            throw new \Exception("Rate limit exceeded. You are blocked for a while.");
        }
    }
}
