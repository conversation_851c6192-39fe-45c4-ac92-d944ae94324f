<?php

namespace App\Services\Receipt;

use App\Lib\ProductContainer;
use App\Lib\RentMy\RentMy;
use App\Lib\S3;
use Cake\Core\Configure;
use Cake\I18n\Number;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Utility\Hash;

class OrderReceipt extends Receipt
{
    private $order;
    private $content;
    private $storeConfig;
    private $template;
    private $replacingObj;
    private $storeInfo;
    private $orderShowableId;
    private $productContainer;
    private $pdfTemplate;
    private $orderId;
    private $formattedDates;
    private $paymentInfo;
    private $fullfilmentInfo;

    private static $checkboxImageUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co/static/icons/checked.png';

    public function __construct()
    {
        parent::__construct();
        $this->productContainer  = new  ProductContainer();

    }

    public function getStoreConfig()
    {
        return $this->storeConfig;
    }

    private function setContent()
    {
        RentMy::addModel(['Contents']);
        $contents = RentMy::$Model['Contents']->find()->where(['store_id' => RentMy::$store->id, 'location' => $this->order['location']])->where(['tag' => 'site_specific'])->first();
        $store_text = [];
        if ($contents) {
            $store_text = $contents = json_decode($contents->contents, true);
            $contents_product_details = $contents['product_details'];
            $contents = $contents['general'];
        }
        $this->content = [
            'store_text' => $store_text ?? [],
            'product_details' => $contents_product_details ?? [],
            'contents' => $contents,
        ];
    }

    public function getContent()
    {
        return $this->content;
    }

    public function setOrder($orderId)
    {
        $this->orderId = $orderId;
        RentMy::addModel(['Orders', 'Locations']);
        $this->order = RentMy::$Model['Orders']->find()
            ->where(['Orders.id' => $orderId])
            ->contain(['OrderItems', 'OrderItems.Products', 'Payments', 'Coupons', 'OrderItems.ItemAssets', 'OrderItems.ItemAssets.Assets'])
            ->first();

        RentMy::getStore($this->order->store_id, $this->order->location);

        $this->order->store_name = RentMy::$store->slug;
        $this->order->pickup = !empty($this->order->pickup) ? RentMy::$Model['Locations']->get($this->order->pickup)->name : '';

        if (!empty($this->order->rent_start)) {
            $this->order->actual_rent_start = $this->order->rent_start = RentMy::toStoreTimeZone($this->order->rent_start);
            $this->order->actual_rent_end = $this->order->rent_end = RentMy::toStoreTimeZone($this->order->rent_end);
        }
        $customArr = array();
        if (!empty($this->order->custom_values)) {
            $customFields = json_decode($this->order->custom_values, true);
            foreach ($customFields as $filed)
                $customArr['custom.' . $filed['field_name']] = $filed['field_values'];
        }


        $this->order['custom_fields'] = $customArr;
        $this->order['coupon'] = !empty($this->order->coupon) ? '(' . $this->order->coupon->code . ')' : '';
        $this->order['created'] = RentMy::format_date(RentMy::toStoreTimeZone($this->order->created), false, false, true);
        foreach (RentMy::getOrderStatus() as $childs) {
            $orderStatus[$childs['id']] = $childs['label'];
            if (!empty($childs['child']))
                foreach ($childs['child'] as $child)
                    $orderStatus[$child['id']] = $child['label'];
        }

        $this->order['status'] = Hash::get($orderStatus, $this->order->status);
        $this->order['total'] = RentMy::$Model['Orders']->getOrderTotal($this->order);
        $this->order['options'] = json_decode($this->order->options, true);

        if (!empty($this->order->rent_start)){
            $this->formattedDates['rental_start_date'] = RentMy::format_date($this->order->actual_rent_start, false, true, true);
            $this->formattedDates['rental_start_time'] = Time::parse($this->order->actual_rent_start)->format('h:i A');
            $this->formattedDates['rental_end_date'] = RentMy::format_date($this->order->actual_rent_end, false, true, true);
            $this->formattedDates['rental_end_time'] = Time::parse($this->order->actual_rent_end)->format('h:i A');
            $this->formattedDates['rental_start_day'] = Time::parse($this->order->actual_rent_start)->format('d');
        }

        $this->orderShowableId = !empty(RentMy::$storeConfig['order']['order_sequence']['active'])?RentMy::getOrderPrefix() . $this->order->store_order_id:$this->orderId;

        $s3Obj = new S3();
        if ($s3Obj->exist('orders/' . 'signature_' . $this->order->id . '.png')) {
            $this->order['signature'] = RentMy::makeS3Url(DS . 'orders' . DS . 'signature_' . $this->order->id . '.png');
        }
        if (!empty($this->order->rent_start)) {
            $this->order->rent_start = RentMy::format_date($this->order->rent_start, true, false, true);
            $this->order->rent_end = RentMy::format_date($this->order->rent_end, true, false, true);
        }

        $this->setCurrencyFormat();
        $this->setOrderRelated();
        return $this;
    }

    public function getProductSpecificTemplateIds()
    {
        RentMy::addModel(['Templates']);
        $templates = RentMy::$Model['Templates']->find()->select(['id', 'store_id', 'location', 'options'])->where([
            'store_id' => $this->order->store_id,
            'location' => $this->order->location,
            'options LIKE' => '%products%',
            'type' => 1
        ])->toArray();

        $ids = [];
        $orderProductIds = array_column($this->order->order_items, 'product_id');

        foreach ($templates as $template){
            $options = !empty($template->options) ? json_decode($template->options, true) : [];
            if (!empty($options['products'])){

                $productIds = array_column($options['products'], 'product_id');


                if (count(array_intersect($productIds, $orderProductIds)) > 0){
                    $ids[] = $template['id'];
                }
            }
        }
        return $ids;
    }

    public function setPaymentInfo()
    {
        RentMy::addModel(['Payments', 'OrderItems']);
        /** Payment Info Start */
        $payment_method = '';
        $transaction_reference = '';

        $payment_amount = '';
        $change_amount = '';
        $amount_tendered = '';
        $orderPayments = [];

        if (!empty($this->order->payments)) {
            $payment_method = $this->order->payments[0]['payment_gateway'];// Hash::get(Configure::read('paymentContent'), $order->payments[0]->content_id);
            $transaction_reference = $this->order->payments[0]->transaction_id ?? '';
            $payment_amount = $this->order->payments[0]->payment_amount ?? '';
            $change_amount = $this->order->payments[0]->change_amount ?? '';
            $amount_tendered = $this->order->payments[0]->amount_tendered ?? '';
            foreach ($this->order->payments as $payment) {
                if (in_array($payment->status, [1, 2])) {
                    $pcontent = 'Other';
                    if (in_array($payment->content_id, [1, 2]))
                        $pcontent = 'Card';
                    elseif ($payment->content_id == 3)
                        $pcontent = 'Card swipe';
                    elseif ($payment->content_id == 4)
                        $pcontent = 'Cash';
                    elseif ($payment->content_id == 7)
                        $pcontent = 'Atrium';
                    elseif (in_array($payment->content_id, [5, 6]))
                        $pcontent = $payment->payment_gateway;

                    if ($payment->payment_method == 'Authorized')
                        $pcontent .= ' (Authorized)';

                    $orderPayments['list'][] = ['content' => $pcontent, 'amount' => Number::precision($payment->payment_amount, 2)];
                }
            }

        }
        $_order = clone $this->order;
        if (is_array($_order['tax'])){
            $_order['tax'] = !empty($_order['tax']['total']) ? $_order['tax']['total'] : 0;
        }
        $paymentSummary = RentMy::$Model['Payments']->paymentSummary($_order);
        $orderPayments['summary'] = [
            'total' => $paymentSummary['grand_total'],
            'order_total' => $paymentSummary['order_total'],
            'paid' => $paymentSummary['paid'],
            'due' => $paymentSummary['due'],
            'deposit' => $paymentSummary['deposit'],
            'amount_tendered' => $paymentSummary['amount_tendered'],
            'amount_change' =>   $paymentSummary['amount_change'],
        ];
        $this->order['payments'] = $orderPayments;

        $this->paymentInfo['payment_method'] = $payment_method;
        $this->paymentInfo['transaction_reference'] = $transaction_reference;
        $this->paymentInfo['payment_amount'] = $paymentSummary['paid'];
        $this->paymentInfo['change_amount'] = $change_amount;
        $this->paymentInfo['amount_tendered'] = $amount_tendered;
        /** Payment Info End */
        return $this;
    }

    public function setFulfillment()
    {
        RentMy::addModel(['DeliveryDetails', 'OrderAddresses']);

        $deliveryAddress = $this->productContainer->getDeliveryAddress($this->order);

        $orderAddress = RentMy::$Model['OrderAddresses']->find()->where(['order_id' => $this->order->id])->first();
        $customerAddress = $this->productContainer->getCustomerAddress($this->order, $orderAddress);
        $this->order->address = $customerAddress;
        $this->order->multi_store_delivery = [];
        if (!empty($orderAddress->multi_store_delivery)){
            $multiStoreDeliveryAddresses = json_decode($orderAddress->multi_store_delivery, true);
            if (!empty($multiStoreDeliveryAddresses['request']['drop_address']))
                $this->order->multi_store_delivery['drop_address'] =  $this->productContainer->getDeliveryAddress($multiStoreDeliveryAddresses['request']['drop_address']);

            if (!empty($multiStoreDeliveryAddresses['request']['pickup_address']))
                $this->order->multi_store_delivery['pickup_address'] = $this->productContainer->getDeliveryAddress($multiStoreDeliveryAddresses['request']['pickup_address']);
        }

        $this->order['delivery_method'] = RentMy::$Model['DeliveryDetails']->_getDeliveryMethod($this->order);
        // get delivery zone name
        if ($this->order->shipping_method == 2) {
            $delivery = RentMy::$Model['DeliveryDetails']->find()->where(['order_id' => $this->order->id])->order(['id' => 'ASC'])->first();
            $deliveryZoneConfig = json_decode($delivery['config'], true);
            $this->order->delivery_zone = $deliveryZoneConfig['name']??'';
        }

        $delivery_flow_label = '';
        if (!empty($order->options['multi_store_delivery']['delivery_flow'])){
            $deliveryFlows = Configure::read('delivery_flow_types');
            $order_delivery_flow = $order->options['multi_store_delivery']['delivery_flow'];
            $key = array_search($order_delivery_flow, array_column($deliveryFlows, 'value'));
            $delivery_flow_label = $deliveryFlows[$key]['label'];
        }

        $this->fullfilmentInfo['delivery_flow_label'] = $delivery_flow_label;
        $this->fullfilmentInfo['delivery_address'] = $deliveryAddress;
        $this->fullfilmentInfo['customer_address'] = $customerAddress;
        return $this;
    }

    public function setOrderItem()
    {
        RentMy::addModel(['Products', 'OrderProductOptions', 'Products', 'Images']);
        $itemData = [];
        $rentalCharge = 0;
        $productDescription = [];
        foreach ($this->order->order_items as $item) {
            //check if manual item
            if ($item->product_id == 0){
                $vProduct = json_decode($item->options, true);
                $vProduct['is_manual_item'] = true;
                $item['product'] = $vProduct;
            }

            $item->additional = json_decode($item->additional, true);
            $item->discount = [
                'off_amount' => $item->off_amount,
                'coupon_amount' => $item->coupon_amount,
                'discount_sub_total' => $item->additional['discount_sub_total'] ?? 0,
                'coupon_sub_total' => $item->additional['coupon_sub_total'] ?? 0
            ];

            if (!empty($item->additional['automatic_coupon_sub_total']) && ($item->discount['coupon_sub_total'] > $item->additional['automatic_coupon_sub_total']))
                $item->discount['coupon_sub_total'] = $item->additional['automatic_coupon_sub_total'];

            $item->order_product_options = RentMy::$Model['OrderProductOptions']->find()
                ->select(['id', 'options', 'quantity', 'price'])->where(['content_item_id' => $item->id, 'content_type' => 'order'])
                ->map(function ($fields) {
                    $fields['options'] = json_decode($fields['options'], true);
                    if (!empty($fields['options']))
                        $fields['values'] = implode(';', array_column($fields['options'], 'value'));

                    return $fields;
                })->toArray();

            $customFields = [];
            if (!empty($item->order_product_options)) {
                foreach ($item->order_product_options as $options) {
                    foreach ($options['options'] as $option) {
                        if (array_key_exists($option['label'], $customFields)) {
                            array_push($customFields[$option['label']], $option);
                        } else {
                            $customFields[$option['label']][] = $option;
                        }

                    }
                }
            }
            $item->custom_fields = $customFields;

            $assets = [];
            foreach ($item['item_assets'] as $asset){
                if (!empty($asset['asset']['serial_no']))
                    $assets[] = $asset['asset']['serial_no'];
            }
            $item['asset_codes'] = $assets;
            $rentalCharge += $item->price;
            $product = RentMy::$Model['Products']->find()->where(['id'=>$item['product_id']])->first();
            $productDescription[] = $product->name;

            $pImages = RentMy::$Model['Images']->image_by_store_plan('cart', ['variants_products_id' => $item['variants_products_id']])->toArray();
            $imagePath = '';
            foreach ($pImages as $image) {
                if (!empty($image['image_small'])) {
                    $imagePath = RentMy::makeS3Url('products/'.$this->order['store_id'].'/'.$item['product_id'].'/' . $image['image_small']);
                    break;
                }
            }

            if (empty($imagePath)){
                $imagePath = Router::url('/img/product-image-placeholder.jpg', true);
            }

            $item['image'] = $imagePath;

            // variant products
            RentMy::addModel(['VariantsProducts']);
            $vp = RentMy::$Model['VariantsProducts']->find()->where(['id'=>$item['variants_products_id']])->first();
            $item['barcode'] = $vp['barcode'] ?? '';

            $itemData[] = $item;

        }

        $this->order->order_items = $itemData;

        $this->order['rental_charge'] = $rentalCharge;
        $this->order['product_description'] = $productDescription;


        RentMy::addModel(['OrderAssets']);

        $orderAsset = RentMy::$Model['OrderAssets']->find()->where(['order_id'=>$this->orderId])->contain('Assets')->toArray();

        foreach ($orderAsset as $oAsset){
            $serial = explode('-', $oAsset['asset']->serial_no);
            $assets[] = $serial[0];
        }

        $this->order['assets'] = !empty($assets) ? $assets : [];


        return $this;
    }

    private function setOrderRelated(): OrderReceipt
    {
        RentMy::addModel(['TaxLookup', 'OrderCharge', 'Orders']);
        $this->order->tax = RentMy::$Model['TaxLookup']->getTaxData('order', $this->orderId);

        $this->order->price_with_tax = RentMy::$storeConfig['tax']['price_with_tax'] ?? null;
        $orderChargeObj = RentMy::$Model['OrderCharge']->find()->where(['order_id' => $this->orderId, 'store_id' => RentMy::$store->id]);

        if (in_array(RentMy::$store->id, [3285, 590])){
            $orderChargeObj = $orderChargeObj->map(function ($charge){
                $config = !empty($charge['config']) ? json_decode($charge['config'], true) : [];
                if (!empty($config['is_required'])){
                    return null;
                }
                return $charge;
            })->filter();
        }
        $this->order->charges = array_values($orderChargeObj->toArray());

        $this->order['qr_code'] = RentMy::$Model['Orders']->orderBarcode($this->orderId);
        $domain = RentMy::storeDomain();
        if (isset(RentMy::$storeConfig['order']['waiver_sharing']['active']) && RentMy::$storeConfig['order']['waiver_sharing']['active']){
            if ($this->order['event_location'] == 'WP Plugin')
                $domain = RentMy::storeDomain(true);

            $this->order['waiver_sharing_link'] = $domain . '/order/' . $this->orderId . '/waiver_sharing';
        }

        $this->order['signature_link'] = $domain . '/order/' . $this->orderId . '/collect_signature';
        $links = RentMy::$Model['Orders']->orderPaymentLink($this->orderId, $this->order);
        $this->order['make_payment_link'] = $links['make_payment_link'] ?? '';
        $this->order['review_order_link'] = $links['review_order_link'] ?? '';
        $this->order['review_quote_link'] = $links['review_quote_link'] ?? '';

        return $this;
    }

    public function setStoreInfo()
    {

        $store = RentMy::$store;
        $this->setContent();

        $contents_product_details = $this->content['product_details'] ?? [];
        $contents = $this->content['contents'] ?? [];
        $this->storeInfo = [
            'address' => $contents['address'] ?? '',
            'email' => $contents['email'] ?? '',
            'phone' => $contents['phone'] ?? '',
        ];
        $this->storeInfo['logo'] = !empty($store['logo']) ? RentMy::makeS3Url('/store-logo/' . $store['id'] . '/' . $store['logo']) : null;
        $this->storeInfo['start_date'] = empty($contents_product_details['start_date']) ? 'Rent Start: ' : $contents_product_details['start_date'] . ': ';
        $this->storeInfo['end_date'] = empty($contents_product_details['end_date']) ? 'Rent End: ' : $contents_product_details['end_date'] . ': ';
        $this->storeInfo['plan'] = !empty($store['store_type']) ? strtoupper($store['store_type']) : '';
        $this->order['store'] = $this->storeInfo;
        $this->storeConfig = RentMy::$storeConfig;
        return $this;
    }

    public function setEventSpecificData($event = '')
    {
        switch ($event){
            case 'closet_exchange':
                RentMy::addModel(['OrderExchange', 'OrderItems']);
                $requests = RentMy::$Model['OrderExchange']->find()->where(['OrderExchange.store_id'=>RentMy::$store->id,'OrderExchange.order_id'=>$orderId, 'OrderExchange.status'=>0])->contain(['Products'])->map(function ($exchange){
                    $exchange['requested_item'] = RentMy::$Model['OrderItems']->find()->where(['OrderItems.id'=>$exchange['order_item_id']])->contain(['Products'])->first();
                    return $exchange;

                })->toArray();
                $this->order->order_requested_items = $requests;
                break;
            default:
                break;
        }
        return $this;
    }

    private function setCurrencyFormat(): OrderReceipt
    {

        $this->order['currency_format'] = ['pre' => true, 'post' => true, 'symbol' => '$', 'code' => 'USD', 's' => ''];
        if (!empty(RentMy::$storeConfig['currency_format']))
            $this->order['currency_format'] = RentMy::$storeConfig['currency_format'];

        return $this;
    }
    public function setTemplate(string $id)
    {
        RentMy::addModel(['Templates']);
        $this->template = RentMy::$Model['Templates']->find()->where(['id' => $id])->first();
        $this->parser()->bindingDynamicContentOfTemplate();

        $this->template['logo'] = !empty($this->order['store']['logo']) ? $this->order['store']['logo'] : '';
        return $this;
    }

    private function parser(): OrderReceipt
    {

        $productDescription = !empty($this->order['product_description']) ? $this->order['product_description'] : [];
        $contents = $this->content['contents'];

        $replacingObj = [
            'rental.start.date' => !empty($this->formattedDates['rental_start_date']) ? $this->formattedDates['rental_start_date'] : '',
            'rental.start.time' => !empty($this->formattedDates['rental_start_time']) ? $this->formattedDates['rental_start_time'] : '',
            'rental.end.date' => !empty($this->formattedDates['rental_end_date']) ? $this->formattedDates['rental_end_date'] : '',
            'rental.end.time' => !empty($this->formattedDates['rental_end_time']) ? $this->formattedDates['rental_end_time'] : '',

            'order.amount_tendered' => !empty($this->paymentInfo['amount_tendered']) ? RentMy::currencyFormat($this->order,$this->paymentInfo['amount_tendered']) : 0,
            'order.change_amount' => !empty($this->paymentInfo['change_amount']) ? RentMy::currencyFormat($this->order,$this->paymentInfo['change_amount']) : 0,
            'order.payment_amount' => !empty($this->paymentInfo['payment_amount']) ? RentMy::currencyFormat($this->order,$this->paymentInfo['payment_amount']) : 0,
            'order.payment_method' => !empty($this->paymentInfo['payment_method']) ? $this->paymentInfo['payment_method'] : 0,
            'order.delivery_address' => $this->fullfilmentInfo['delivery_address'] ?? '',
            'order.transaction_reference' => !empty($this->paymentInfo['transaction_reference']) ? $this->paymentInfo['transaction_reference'] : '',

            'order.order_id' => $this->orderShowableId,
            'order.delivery' => $this->order->delivery_method,
            'order.delivery_zone' => $this->order->delivery_zone,
            'customer.name' => $this->order->first_name . ' ' . $this->order->last_name,
            'customer.firstname' => $this->order->first_name,
            'customer.email' => $this->order->email,
            'customer.mobile' => $this->order->mobile,
            'customer.address' => $this->fullfilmentInfo['customer_address'] ?? '',
            'order.date' => $this->order['created'],
            'order.status' => $this->order->status,
            'order.waiver_sharing_link'=> !empty($this->order['waiver_sharing_link'])?$this->order['waiver_sharing_link']:'',
            'order.signature_link'=> !empty($this->order['signature_link'])?$this->order['signature_link']:'',
            'order.payment_link'=>  $this->order['make_payment_link'],

            'store.name' => $this->order->store_name,
            'location_name' =>  empty($contents) ? '' : $contents['business_name'],
            'store.address' => empty($contents) ? '' : $contents['address'],
            'store.email' => empty($contents) ? '' : $contents['email'],
            'store.phone' => empty($contents) ? '' : $contents['phone'],

            'order.start_date' => empty($this->formattedDates['rental_start_date']) ? '' : $this->formattedDates['rental_start_date'],
            'order.start_time' => empty($this->formattedDates['rental_start_time']) ? '' : $this->formattedDates['rental_start_time'],
            'order.end_date' => empty($this->formattedDates['rental_end_date']) ? '' : $this->formattedDates['rental_end_date'],
            'order.end_time' => empty( $this->formattedDates['rental_end_time']) ? '' :  $this->formattedDates['rental_end_time'],
            'input_checkbox' => '<img src="' . self::$checkboxImageUrl . '" height="40" width="40">',
            'tos_checkbox' => '<img src="' . self::$checkboxImageUrl . '" height="40" width="40">',
            'rental.start.day' => empty($this->order->actual_rent_start) ? '' : Time::parse($this->order->actual_rent_start)->format('jS'),
            'rental.renewal.day' => empty($this->order->actual_rent_end) ? '' : Time::parse($this->order->actual_rent_end)->subDays(1)->format('jS'),

            'order.paid_amount' => !empty($this->paymentInfo['payment_amount']) ? RentMy::currencyFormat($this->order, $this->paymentInfo['payment_amount']) : [],

            'order.next_payment' => RentMy::currencyFormat($this->order, $this->order['rental_charge']) . (!empty($rental_type)?' ('.$rental_type.')':''),
            'order.unit_rent' => $this->order['rental_charge']>0?RentMy::currencyFormat($this->order,$this->order['rental_charge'] - 1) . (!empty($rental_type)?' ('.$rental_type.')':''):RentMy::currencyFormat($this->order, 0),
            'order.item_details' => implode(', ', $productDescription),
            'order.assets' => implode(', ',$this->order['assets']),
            'order.type' => $this->order->type,

            'order.digital_signature' => !empty($this->order['signature'])?'<img src="'.$this->order['signature'].'" />':'',
            'order.total' => RentMy::currencyFormat($this->order, $this->order->total),
            'order.delivery_charge' => RentMy::currencyFormat($this->order, $this->order->delivery_charge??0),

            'order.loaded_pickup_charge' => '',
            'order.loaded_delivery_charge' => '',
            'order.drop_off_charge' => RentMy::currencyFormat($this->order, $this->order->options['multi_store_delivery']['charges']['drop_off']??0),
            'order.move_charge' => RentMy::currencyFormat($this->order, $this->order->options['multi_store_delivery']['charges']['move']??0),
            'order.pickup_charge' => RentMy::currencyFormat($this->order, $this->order->options['multi_store_delivery']['charges']['pickup']??0),
            'order.delivery_flow' => $this->fullfilmentInfo['delivery_flow_label'] ?? '',
            'order.review_link' => $this->order['review_order_link']
        ];

        if (!empty($order->multi_store_delivery)){
            if (!empty($order->multi_store_delivery['drop_address']))
                $replacingObj['order.delivery_address'] = $order->multi_store_delivery['drop_address'];

            if (!empty($order->multi_store_delivery['pickup_address']))
                $replacingObj['order.pickup_address'] = $order->multi_store_delivery['pickup_address'];
        }

        if (!empty($order->options['multi_store_delivery'])){
            $replacingObj['order.drop_off_charge'] = RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['drop_off']['fixed_charge']) .' + '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['drop_off']['additional_charge']) .' ('.number_format($order->options['multi_store_delivery']['flows']['drop_off']['total_distance'], 2).' mi) : '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['charges']['drop_off']);
            $replacingObj['order.move_charge'] = RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['move']['fixed_charge']) .' + '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['move']['additional_charge']) .' ('.number_format($order->options['multi_store_delivery']['flows']['move']['total_distance'], 2).' mi) : '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['charges']['move']);
            $replacingObj['order.pickup_charge'] = RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['pickup']['fixed_charge']) .' + '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['pickup']['additional_charge']) .' ('.number_format($order->options['multi_store_delivery']['flows']['pickup']['total_distance'], 2).' mi) : '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['charges']['pickup']);
            $replacingObj['order.loaded_pickup_charge'] = !empty($order->options['multi_store_delivery']['flows']['storage']['loaded']['pickup'])?RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['storage']['loaded']['pickup']['fixed_charge']) .' + '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['storage']['loaded']['pickup']['additional_charge']) .' ('.number_format($order->options['multi_store_delivery']['flows']['storage']['loaded']['pickup']['distance'], 2).' mi) : '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['storage']['loaded']['pickup']['charge']):'';
            $replacingObj['order.loaded_delivery_charge'] = !empty($order->options['multi_store_delivery']['flows']['storage']['loaded']['delivery'])?RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['storage']['loaded']['delivery']['fixed_charge']) .' + '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['storage']['loaded']['delivery']['additional_charge']) .' ('.number_format($order->options['multi_store_delivery']['flows']['storage']['loaded']['delivery']['total_distance'], 2).' mi) : '. RentMy::currencyFormat($order, $order->options['multi_store_delivery']['flows']['storage']['loaded']['delivery']['charge']):'';
        }

        $replacingObj = array_merge($this->order['custom_fields'] ?? [], $replacingObj);
        $this->replacingObj = $replacingObj;
        return $this;
    }

    public function getOrder()
    {
        return $this->order;
    }

    private function bindingDynamicContentOfTemplate(): OrderReceipt
    {

        $pdfDynamicTemplate = $this->template;
        $replacingObj = $this->replacingObj;
        $pdfDynamicTemplate->content_body = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->content_body);

        $pdfDynamicTemplate->general = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->general);
        if ($this->order->type == 2) {
            $pdfDynamicTemplate->general = str_replace('Order Status', 'Quote Status', $pdfDynamicTemplate->general);
        }
        $pdfDynamicTemplate->address_body = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->address_body);
        $pdfDynamicTemplate->title = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->title);
        $pdfDynamicTemplate->store_address = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->store_address);
        $pdfDynamicTemplate->message_1 = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->message_1);
        $pdfDynamicTemplate->message_2 = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->message_2);
        $pdfDynamicTemplate->terms_conditions = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->terms_conditions);
        $pdfDynamicTemplate->options = json_decode($pdfDynamicTemplate->options, true);
        if (!empty($pdfDynamicTemplate->options['store_name_text']))
            $pdfDynamicTemplate->options['store_name_text'] = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->options['store_name_text']);

        if (!empty($pdfDynamicTemplate->options['customer_info_text']))
            $pdfDynamicTemplate->options['customer_info_text'] = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->options['customer_info_text']);

        if (!empty($pdfDynamicTemplate->options['start_date_text']))
            $pdfDynamicTemplate->options['start_date_text'] = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->options['start_date_text']);

        if (!empty($pdfDynamicTemplate->options['start_time_text']))
            $pdfDynamicTemplate->options['start_time_text'] = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->options['start_time_text']);

        if (!empty($pdfDynamicTemplate->options['end_date_text']))
            $pdfDynamicTemplate->options['end_date_text'] = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->options['end_date_text']);

        if (!empty($pdfDynamicTemplate->options['end_time_text']))
            $pdfDynamicTemplate->options['end_time_text'] = self::replaceTemplateText($replacingObj, $pdfDynamicTemplate->options['end_time_text']);

        $this->template = $pdfDynamicTemplate;

        $this->getProductSpecificTermsAndConditions();

        return $this;
    }

    private function getProductSpecificTermsAndConditions()
    {
        $termsAndConditions = [];
        $replacingObj = $this->replacingObj;
        $productIds = array_values(array_column(
            array_filter($this->order['order_items'], function ($item) {
                return empty($item['parent_id']);
            }),
            'product_id'
        ));
        RentMy::addModel(['Products']);
        $products = RentMy::$Model['Products']->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
        ])->where(['id IN' => $productIds])->toArray();

        RentMy::addModel(['Pages']);
        $pages = RentMy::$Model['Pages']->find()->where([
            'store_id' => $this->order['store_id'],
            'location' => $this->order['location'],
        ])->innerJoinWith('ReferenceProducts', function ($query) use ($productIds){
            return $query->where(['product_id IN' => $productIds]);
        })->contain(['ReferenceProducts' => function ($query) use ($productIds) {
            return $query->where(['product_id IN' => $productIds]);
        }])->toArray();

        foreach ($pages as $page){
            $contents = !empty($page->contents) ? json_decode($page->contents, true) : [];
            foreach ($page->reference_products as $referenceProduct){
                $termsAndConditions[] = array_merge([
                    "parent" => false,
                    "page_name" => $page->name,
                    "product_id" => $referenceProduct->product_id,
                    "product_name" => $products[$referenceProduct->product_id],
                    "content" => self::replaceTemplateText($replacingObj, $contents['content'])
                ]);
            }
        }

        $this->template['product_specific_terms'] = $termsAndConditions;
    }

    public function getTemplate()
    {
        return $this->template;
    }

    public function getStoreInfo()
    {
        return $this->storeInfo;
    }

    public function getSmsContent()
    {
        $orderUid = RentMy::randomnum(3) . $this->orderId;
        $domain = RentMy::storeDomain(false, true);
        $link = $domain . '/order/' . $orderUid;

        $msg = str_replace('{{link}}', $link, $this->template->sms_body);
        $order_quote = ($this->order->type == 2) ? 'Here is your quote' : 'Thank you for your order';
        $msg = str_replace('{{order_quote}}', $order_quote, $msg);
        $msg = self::replaceTemplateText($this->replacingObj, $msg);

        return $msg;
    }

    public function getOrderShowableId()
    {
        return $this->orderShowableId;
    }



}
