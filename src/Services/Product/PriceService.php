<?php

namespace App\Services\Product;

use App\Lib\RentMy\RentMy;

class PriceService
{
    public function __construct()
    {
        RentMy::addModel(['ProductPrices']);
    }

    public function getFormattedPrices($product)
    {

        return RentMy::$Model['ProductPrices']->getPriceDetails($product->variants_product);
    }

    public function getRecurringPrices($product)
    {
        return RentMy::$Model['ProductPrices']->getRecurringPriceList($product->prices[0] ?? []);
    }
}
