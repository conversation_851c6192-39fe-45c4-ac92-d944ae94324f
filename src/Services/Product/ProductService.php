<?php

namespace App\Services\Product;


use App\Lib\RentMy\RentMy;
use Cake\Datasource\Exception\RecordNotFoundException;


class ProductService
{
    private $storeId;
    private $location;
    private $data;

    public function __construct(array $data)
    {
        $this->storeId = RentMy::$store->id;
        $this->location = RentMy::$token->location;
        $this->data = $data;
    }

    public function getDetails(string $slug): array
    {
        RentMy::addModel(['Products', 'VariantsProducts']);

        $options = [
            'product_fields' => ['Products.id', 'Products.sales_tax', 'Products.deposit_amount', 'Products.name', 'Products.description', 'buy_price', 'rent_price',
                'Products.variant_set', 'Products.driving_license', 'Products.keyword', 'Products.store_id', 'Products.is_tracked','Products.options', 'client_specific_id', 'Products.status', 'Products.options'],

        ];
        $product = RentMy::$Model['Products']->getProductWithDefaultVariant($slug, $this->storeId, $this->location, $this->data['asset_id'] ?? null, $options);

        if (empty($product)) {
            throw new RecordNotFoundException('Product not found');
        }

        // Load sub-services
        $variantService = new VariantService($this->location, $this->data);
        $imageService = new ImageService();
        $priceService = new PriceService();
        $availabilityService = new AvailabilityService($this->data);
        $formatter = new ProductFormatter();

        // Delegate logic
        $product->images = $imageService->getProductImages($product);
        $setData = $variantService->getVariantData($product);
        $product->variant_list = $setData['list'];
        $product->variant_set_list = $setData['set_list'];
        $product->default_variant = $variantService->getDefaultVariant($product);
        $product->prices = $priceService->getFormattedPrices($product);
        $product->recurring_prices = $priceService->getRecurringPrices($product);

        $imageService->filterRelevantImages($product);
        $formatter->attachExactTime($product);
        $formatter->attachSEO($product);
        $formatter->applyClientSpecificId($product);
        $formatter->applyBookingInfo($product);
        if(isset($product['options'])) unset($product['options']);
        return $formatter->toArray($product);
    }
}
