<?php

namespace App\Services\Product;


use App\Lib\RentMy\RentMy;

class ImageService
{
    public function getProductImages($product)
    {
        RentMy::addModel(['Images']);
        return RentMy::$Model['Images']->image_by_store_plan('product_details', ['product_id' => $product->id])->toArray();
    }

    public function filterRelevantImages(&$product)
    {
        $images = [];
        foreach ($product->images as $img) {
            if ($img->variants_products_id == $product->variants_product['id']) {
                $images[] = ['image_large' => $img->image_large, 'image_small' => $img->image_small, 'status' => $img->status];
            }
        }

        if (empty($images)) {
            foreach ($product->images as $img) {
                if (!empty($img->image_large) || !empty($img->image_small)) {
                    $images[] = ['image_large' => $img->image_large, 'image_small' => $img->image_small, 'status' => $img->status];
                }
            }
        }

        $product->images = $images;
    }
}
