<?php

namespace App\Services\Product;

use App\Lib\RentMy\RentMy;

class VariantService
{
    private $location;
    private $data;

    public function __construct($location, $data)
    {
        $this->location = $location;
        $this->data = $data;
    }

    public function getVariantData($product)
    {
        $sets = json_decode($product->variant_set);
        if (empty($sets) || $sets[0] == 1) {
            return ['list' => [], 'set_list' => []];
        }

        return [
            'list' => RentMy::$Model['Products']->_getParentAttributeList($this->location, $product->id, $sets, $this->data),
            'set_list' => RentMy::$Model['Products']->_getVariantsets($sets)
        ];
    }

    public function getDefaultVariant($product)
    {
        if (empty($product->variants_product)) {
            return [];
        }

        return RentMy::$Model['Products']->_getDefaultAttribute($product->variants_product->id);
    }
}
