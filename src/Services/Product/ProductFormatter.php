<?php

namespace App\Services\Product;

use App\Lib\RentMy\RentMy;
use Cake\Core\Configure;
use Cake\I18n\Time;

class ProductFormatter
{
    public function attachSEO(&$product)
    {
        $options = json_decode($product->options ?? '{}', true);
        if (!empty($options['seo'])) {
            RentMy::addModel(['Stores']);
            $product["seo"] = $options['seo'];
            if (!empty($product['seo']['meta_title'])) {
                $product['seo']['meta_title'] = RentMy::$Model['Stores']
                    ->parsingSiteName(RentMy::$store->slug, $product['seo']['meta_title']);
            }
        }
    }

    public function applyClientSpecificId(&$product)
    {
        $product->client_specific_id = $product->client_specific_id ?? $product->id;
    }

    public function applyBookingInfo(&$product)
    {
        $options = json_decode($product->options ?? '{}', true);
        if (!is_array($options)) {
            $options = [];
        }

        if (!empty($options['booking'])) {
            $product->booking = true;
            $config = Configure::read('booking');
            $product->booking_config = $config[RentMy::$store->id] ?? ['parent' => 1, 'child' => 1];
        }

        $priceType = 0;
        if(!empty($product->recurring_prices)){
            $priceType = $product->recurring_prices[0]['price_type'];
        }

        $product->enduring_rental = !empty($options['enduring_rental']) && ($priceType == 5);

        RentMy::addModel(['Holidays']);
        $product['band_pricing'] = RentMy::$Model['Holidays']->getBandPricing($product['id']);
    }

    public function toArray($product): array
    {
        $product = $product->toArray();
        unset($product['variants'], $product['variants_product']);
        return $product;
    }

    /**
     * Optional: Availability-related logic
     * Call this later if you want to reintroduce availability enrichment
     */
    public function attachExactTime(&$product)
    {
        $options = json_decode($product->options ?? '{}', true);
        if (!is_array($options)) {
            $options = [];
        }

        if (!empty($options['exact_date'])) {
            $exactDates = RentMy::extactDates($product->id);
            $product->rent_start = $exactDates['start_date'] ?? null;
            $product->rent_end = $exactDates['end_date'] ?? null;
            $product->exact_date = true;
        }

        if (!empty($options['exact_time'])) {
            $product->extact_durations = RentMy::exactTimes('', $product->id);
            $product->exact_time = !empty($product->extact_durations['times']);
        }

        if (!empty($options['exact_time_with_days'])) {
            $this->attachExactTimeWithDays($product);
        }
    }

    private function attachExactTimeWithDays(&$product)
    {
        RentMy::addModel(['ReferenceProducts', 'ExactTimes']);

        $timeIds = RentMy::$Model['ReferenceProducts']->find('list', [
            'valueField' => 'reference_id'
        ])->where([
            'reference_type' => 'ExactTime',
            'product_id' => $product['id'],
        ])->toList();

        $dayObj = RentMy::$Model['ExactTimes']
            ->find()
            ->select(['days', 'start_date', 'end_date'])
            ->where(['id IN' => $timeIds]);

        $days = $dayObj->reduce(function ($days, $item) {
            return array_merge($days, json_decode($item['days'] ?? '[]', true));
        }, []);

        $validDate = $dayObj->map(function ($day) {
            return [
                'start_date' => Time::parse($day['start_date'])->format("Y-m-d"),
                'end_date' => Time::parse($day['end_date'])->format("Y-m-d"),
            ];
        })->toArray();

        $product['est_available_days'] = array_values(array_unique($days));
        $product['est_valid_dates'] = $validDate;
    }
}
