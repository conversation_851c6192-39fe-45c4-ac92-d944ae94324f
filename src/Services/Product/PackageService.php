<?php

namespace App\Services\Product;

use App\Lib\RentMy\RentMy;
use Cake\Utility\Hash;

class PackageService
{
    private $storeId;
    private $location;
    private $data;
    public function __construct(array $data)
    {
        $this->storeId = RentMy::$store->id;
        $this->location = RentMy::$token->location;
        $this->data = $data;
    }

    /**
     * @throws \Exception
     */
    public function getDetails(string $slug)
    {
        RentMy::addModel(['ProductsSettings', 'ProductsAvailabilities', 'CartItems', 'Quantities', 'Products', 'ProductPackages', 'ProductPrices']);
        $package = RentMy::$Model['Products']->getPackageWithDefaultVariant($slug, $this->storeId, $this->location, []);


        if (empty($package) || $package->type != 2) {
            throw new \Exception('Invalid request');
        }

        $products = $this->getVariantIds($package);
        $variantIds = Hash::extract($products, '{n}.variants.0.id');
        $package->products = $products;
//        $imageService = new ImageService();
        $priceService = new PriceService();
        $availabilityService = new AvailabilityService($this->data);
        $formatter = new ProductFormatter();

//        $package->price = $priceService->getFormattedPrices($package);
//        $recurring = RentMy::$Model['ProductPrices']->getRecurringPriceList($package->price[0]);
//        if (!empty($recurring)) {
//            $package['recurring_prices'] = $recurring;
//        }

        $package->price = $package->prices = $priceService->getFormattedPrices($package);

        $package->recurring_prices = $priceService->getRecurringPrices($package);

        $formatter->attachExactTime($package);
        $formatter->attachSEO($package);
        $formatter->applyClientSpecificId($package);
        $formatter->applyBookingInfo($package);
        // package content
        $packageKeys = ['package_content', 'package_dynamic_bundle_builder', 'package_max_item_count'];
        $packageContents = RentMy::$Model['ProductsSettings']->find()->where(['product_id' => $package->id, 'p_key IN' => $packageKeys])->toArray();
        foreach($packageContents as $packageContent){
            $package[$packageContent['p_key']] = $packageContent['value'];
        }

        if(isset($package['prices'])) unset($package['prices']);
        if(isset($package['options'])) unset($package['options']);
        return $package;
    }


    public function getVariantIds($package)
    {
        RentMy::addModel(['ProductPackages']);
        $productPackages = RentMy::$Model['ProductPackages']->find()
            ->where(['ProductPackages.store_id' => $this->storeId])
            ->where(['ProductPackages.package_id' => $package->id])
            ->toArray();

        if (!empty($productPackages)) {
            $productIds = Hash::extract($productPackages, '{n}.product_id');
            $quantities = Hash::extract($productPackages, '{n}.quantity');
            $packageProducts = RentMy::$Model['ProductPackages']->productDetails($productIds, $productPackages, $this->location);
            $products = [];
            foreach ($productIds as $productId) {
                foreach ($packageProducts as $packageProduct) {
                    if ($productId == $packageProduct['id']) {
                        $products[] = $packageProduct;
                    }
                }
            }


            return $products;
        }
    }

}
