<?php

namespace App\Services\Product;

use App\Lib\RentMy\RentMy;
use Cake\Core\Configure;
use Cake\I18n\Time;

class AvailabilityService
{
    private $queryParam;

    public function __construct($queryParam)
    {
        $this->queryParam = $queryParam;
    }

    public function attachAvailabilityData(&$product)
    {
        $options = json_decode($product->options ?? '{}', true);
        if (!is_array($options)) $options = [];

        // Exact date logic
        if (!empty($options['exact_date'])) {
            $exactDates = RentMy::extactDates($product->id);
            $product->rent_start = $exactDates['start_date'] ?? null;
            $product->rent_end = $exactDates['end_date'] ?? null;
            $product->exact_date = true;
        }

        // Exact time logic
        if (!empty($options['exact_time'])) {
            $product->extact_durations = RentMy::exactTimes('', $product->id);
            $product->exact_time = !empty($product->extact_durations['times']);
        }

        if (!empty($options['exact_time_with_days'])) {
            $this->attachExactTimeWithDays($product, $options);
        }

        // Booking Config
        if (!empty($options['booking'])) {
            $product->booking = true;
            $config = Configure::read('booking');
            $product->booking_config = $config[RentMy::$store->id] ?? ['parent' => 1, 'child' => 1];
        }

        $product->enduring_rental = !empty($options['enduring_rental']);
        RentMy::addModel(['Holidays']);
        $product['band_pricing'] = RentMy::$Model['Holidays']->getBandPricing($product['id']);
    }

    private function attachExactTimeWithDays(&$product, &$options)
    {
        RentMy::addModel(['ReferenceProducts', 'ExactTimes']);

        $options['exact_time_ids'] = RentMy::$Model['ReferenceProducts']->find('list', [
            'valueField' => 'reference_id'
        ])->where([
            'reference_type' => 'ExactTime',
            'product_id' => $product['id'],
        ])->toList();

        $dayObj = RentMy::$Model['ExactTimes']
            ->find()
            ->select(['days', 'start_date', 'end_date'])
            ->where(['id IN' => $options['exact_time_ids']]);

        $days = $dayObj->reduce(function ($days, $item) {
            return array_merge($days, json_decode($item['days'] ?? '[]', true));
        }, []);

        $validDate = $dayObj->map(function ($day) {
            return [
                'start_date' => Time::parse($day['start_date'])->format("Y-m-d"),
                'end_date' => Time::parse($day['end_date'])->format("Y-m-d"),
            ];
        })->toArray();

        $product['est_available_days'] = array_values(array_unique($days));
        $product['est_valid_dates'] = $validDate;
    }
}
