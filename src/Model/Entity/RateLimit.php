<?php
namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * RateLimit Entity
 *
 * @property int $id
 * @property string $ip
 * @property string|null $user_agent
 * @property string $action
 * @property int $attempts
 * @property \Cake\I18n\FrozenTime|null $last_attempt
 * @property \Cake\I18n\FrozenTime|null $blocked_until
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 */
class RateLimit extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array
     */
    protected $_accessible = [
        'ip' => true,
        'user_agent' => true,
        'endpoint' => true,
        'attempts' => true,
        'last_attempt' => true,
        'blocked_until' => true,
        'created' => true,
        'modified' => true,
    ];
}
