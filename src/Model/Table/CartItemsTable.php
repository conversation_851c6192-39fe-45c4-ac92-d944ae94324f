<?php

namespace App\Model\Table;

use App\Lib\CartConfig;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use Cake\Validation\Validator;

class CartItemsTable extends Table
{
    private $Products;

    public function initialize(array $config)
    {
        parent::initialize($config);
        $this->Products = TableRegistry::get('Products');

        $this->setTable('cart_items');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
        $this->addBehavior('Tree');

        $this->belongsTo('Carts', [
            'foreignKey' => 'cart_id',
            'joinType' => 'INNER'
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER'
        ]);
        $this->belongsTo('Qty', [
            'className' => 'Quantities',
            'foreignKey' => 'quantity_id'
        ]);

        $this->belongsTo('ItemVariant', [
            'className' => 'VariantsProducts',
            'foreignKey' => 'variants_products_id'
        ]);
    }

    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');

        $validator
            ->integer('quantity')
            ->requirePresence('quantity', 'create')
            ->notEmpty('quantity');

        $validator
            ->numeric('price')
            ->requirePresence('price', 'create')
            ->notEmpty('price');

        $validator
            ->scalar('rental_type')
            ->maxLength('rental_type', 100)
            ->allowEmpty('rental_type');

        $validator
            ->scalar('rental_duration')
            ->maxLength('rental_duration', 100)
            ->allowEmpty('rental_duration');

        $validator
            ->dateTime('rent_start')
            ->allowEmpty('rent_start');

        $validator
            ->dateTime('rent_end')
            ->allowEmpty('rent_end');

        return $validator;
    }

    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['cart_id'], 'Carts'));
        $rules->add($rules->existsIn(['product_id'], 'Products'));

        return $rules;
    }

    /**
     *  add Package Item
     * @param $data
     * @param $cart
     */
    public function addPackageItem($data, $cart)
    {
        $requestQuantity = $data['quantity'];
        /*
         * merging same variants together
         */
        if (isset($data['products']) && is_array($data['products'])) {
            $products = [];
            foreach ($data['products'] as $product) {
                if (!empty($products) && in_array($product['variants_products_id'], array_column($products, 'variants_products_id'))) {
                    foreach ($products as &$_product)
                        if ($product['variants_products_id'] == $_product['variants_products_id'])
                            $_product['quantity'] += $product['quantity'];
                } else {
                    $products[] = $product;
                }
            }
            $data['products'] = $products;
        }

        if (isset($data['id']) && !empty($data['id'])) {
            $cartItem = $this->get($data['id']);
        } else {
            if ($data['rental_type'] == 'buy') {
                $rentalType[] = 'buy';
            } else {
                $rentalType = ['hourly', 'daily', 'weekly', 'monthly', 'fixed', 'rent'];
            }
            // find any existing product in the cart .
            $cartItem = $this->find()
                ->where(['cart_id' => $cart->id, 'variants_products_id' => $data['variants_products_id'],
                    'location' => $data['location'], 'rental_type IN' => $rentalType])
                ->first();

            $differentVariant = false;
            if (!empty($cartItem)){
                foreach ($data['products'] as $product){
                    $item = $this->find()
                        ->where(['cart_id' => $cart->id, 'variants_products_id' => $product['variants_products_id'], 'parent_id' => $cartItem['id'],
                            'location' => $data['location'], 'rental_type IN' => $rentalType])
                        ->first();
                    if (empty($item)){
                        $differentVariant = true;
                        break;
                    }

                }
            }

            if (empty($cartItem) || $differentVariant) {
                $cartItem = $this->newEntity();
            } else { // for existing products add quantity with existing quantity
                $data['quantity'] = $cartItem->quantity + $data['quantity'];
            }
        }

        $variant = $this->Products->_getDefaultAttribute($data['variants_products_id']);
        $data['rental_duration'] = !empty($data['rental_duration']) ? $data['rental_duration'] : 1;
        $data['deposit_amount'] = isset($data['deposit_amount']) ? $data['deposit_amount'] : 0;
        $data['deposit_amount'] = $data['deposit_amount'] * $data['quantity'];

        if (!empty($data['membership']) && $data['rental_type'] != 'buy'){
            $data['price'] = 0;
        }

        $priceWithoutTax = $subTotal = $data['price'] * $data['quantity'] * $data['rental_duration'];

        // automatic discount
        RentMy::addModel(['Coupons']);
        // returns sub_total && off_amount .
        $coupon = RentMy::$Model['Coupons']->fixedOfferCoupon([
            'cart_id' => $cart->id, 'token' => $data['token'],
            'product_id' => $data['package_id'], 'sub_total' => $subTotal, 'quantity' => $data['quantity'], 'price' => $data['price']
        ]);


        $tax = $priceWithoutTax * $data['sales_tax'] * 0.01;
        $total = $coupon['sub_total'] + $tax;


        $cartData = [
            'variant_chain_id' => $variant['variant_chain_id'],
            'variant_chain_name' => $variant['variant_chain_name'],
            'product_id' => $data['package_id'],
            'store_id' => RentMy::$store->id,
            'term' => $data['term'] ?? 1,
            'quantity' => $data['quantity'],
            'quantity_id' => $data['quantity_id'],
            'location' => $data['location'],
            'variants_products_id' => $data['variants_products_id'],
            'parent_id' => 0,
            'cart_id' => $cart->id,
            'rental_type' => $data['rental_type'],
            'rent_start' => $data['rent_start'],
            'rent_end' => $data['rent_end'],
            'price' => $data['price'],
            'sales_tax' => $data['sales_tax'] ?? 0,
            'rental_duration' => $data['rental_duration'] ?? 1,
            'sub_total' => $coupon['sub_total'],
            'substantive_price' => $priceWithoutTax,
            'sales_tax_price' => $tax,
            'total' => $priceWithoutTax + $tax,
            'product_type' => 2,
            'deposit_amount' => $data['deposit_amount'],
            'automatic_coupon_amount' => $coupon['off_amount'],
            'vendor_discount_amount' => $coupon['vendor_discount_amount']
        ];

        if(!empty($coupon['off_amount'] + $coupon['vendor_discount_amount'])) {
            $cartItemOptions = [];
            $cartData['automatic_coupon_amount'] = $coupon['off_amount'];
            $cartItemOptions['automatic_coupon_sub_total'] =  $coupon['sub_total'];
            $cartItemOptions['automatic_coupon_discount'] = $coupon['off_amount'];
            $cartItemOptions['vendor_discount_amount'] = $coupon['vendor_discount_amount'];
            if (($coupon['off_amount'] + $coupon['vendor_discount_amount']) > $cartData['substantive_price']){
                $cartData['sub_total'] = 0;
                $cartItemOptions['automatic_coupon_sub_total'] = 0;
            }
            $cartData['additional'] = json_encode($cartItemOptions);
        }
        // recurring for cart specific items
        $data['product_id'] = $data['package_id'];
        $recurring = $this->itemRecurring($data);
        $options = [];

        if (!empty($recurring))
            $options = $recurring;

        if (!empty($data['package_dynamic_bundle_builder'])){
            $options['package_dynamic_bundle_builder'] = true;
            $options['products'] = $data['products'];
        }

        $cartData['options'] = !empty($options) ? json_encode($options) : [];

        $cartItem = $this->patchEntity($cartItem, $cartData);

        $this->save($cartItem);
        $data['parent_id'] = $cartItem->id;
        $data['cart_id'] = $cart->id;
        foreach ($data['products'] as $item) {
            $this->_addPackageChild($data, $item);
        }

        RentMy::addModel(['OrderProductOptions']);
        RentMy::$Model['OrderProductOptions']->create($cartItem->id, $cartItem->cart_id, $requestQuantity, $data);
        return $cartItem;
        //$this->_updatePackageQuantity($data);
    }

    /* private function _updatePackageQuantity($data)
     {
         $cartItemObj = $this->find();
         $cartItem = $cartItemObj->select([
             'quantity' => $cartItemObj->func()->sum('quantity')
         ])
             ->where(['cart_id' => $data['cart_id']])
             ->where(['parent_id' => $data['parent_id']])
             ->toArray();
         $tQuantity = empty($cartItem) ? 0 : $cartItem[0]['quantity'];

         $cItem = $this->get($data['parent_id']);
         $cItem->quantity = $tQuantity;
         $this->save($cItem);
     }*/

    public function updateChildQuantity($item)
    {
        if ($item['product_type'] != 2)
            return;

        $options = !empty($item['options']) ? json_decode($item['options'], true) : [];

        RentMy::addModel(['ProductPackages']);
        $childs = $this->find()->where(['parent_id' => $item['id']])->toArray();
        foreach ($childs as $child){
            if (!empty($options['package_dynamic_bundle_builder'])){
                $productPackageCollection = new Collection($options['products']);
                $filteredResult = array_filter(
                    $options['products'],
                    function($item) use ($child) {
                        return $item['product_id'] === $child['product_id'] && $item['variants_products_id'] === $child['variants_products_id'];
                    }
                );
                $productPackage = !empty($filteredResult) ? array_values($filteredResult)[0] : null;
            }

            $childrenOptions = !empty($child['options']) ? json_decode($child['options'], true) : [];
            if (empty($childrenOptions['initial_quantity'])){
                $childrenOptions['initial_quantity'] = $child['quantity'];
                $childrenOptions['initial_parent_quantity'] = $item['quantity'];
            }


            if(empty($productPackage)){
                $productPackage = RentMy::$Model['ProductPackages']->find()->select(['quantity'])->where(['product_id' => $child['product_id'], 'package_id' => $item['product_id']])->first();
            }

            $productPackageQuantity = $productPackage->quantity;

            if (!empty($childrenOptions['initial_quantity']) && (($childrenOptions['initial_quantity'] / $childrenOptions['initial_parent_quantity']) != $productPackageQuantity)) {
                $productPackageQuantity = $childrenOptions['initial_quantity'];
            }

            $child['options'] = !empty($childrenOptions) ? json_encode($childrenOptions) : null;
            $child['quantity'] = $item['quantity'] * $productPackageQuantity;
            $this->save($child);

        }

    }

    private function _addPackageChild($data, $item)
    {
        $quantity = TableRegistry::get('Quantities')->find()
            ->where(['variants_products_id' => $item['variants_products_id']])
            ->where(['location' => $data['location']])
            ->first();

        $cartItem = $this->find()->where([
            'cart_id' => $data['cart_id'],
            'variants_products_id' => $item['variants_products_id'],
            'location' => $data['location'],
            'parent_id'  => $data['parent_id']
        ])->first();
        if (empty($cartItem)) {
            $cartItem = $this->newEntity();
        }

        $cartChilledTotalQuantity = $this->find() ->select([
            'quantity' => $this->find()->func()->sum('quantity'),
            ])->where([
            'cart_id' => $data['cart_id'],
            'product_id' => $item['product_id'],
            'variants_products_id !=' => $item['variants_products_id'],
            'location' => $data['location'],
            'parent_id'  => $data['parent_id']
        ])->first();

        $itemQuantity = $item['quantity'] * $data['quantity'];

        $variant = $this->Products->_getDefaultAttribute($item['variants_products_id']);
        $cartItem->variant_chain_id = $variant['variant_chain_id'];
        $cartItem->variant_chain_name = $variant['variant_chain_name'];

        $cartItem->product_id = $item['product_id'];
        $cartItem->store_id = $data['store_id'];
        $cartItem->location = $data['location'];
        $cartItem->variants_products_id = $item['variants_products_id'];
        $cartItem->quantity_id = $quantity->id;
        $cartItem->quantity = $itemQuantity;
        $cartItem->parent_id = $data['parent_id'];
        $cartItem->cart_id = $data['cart_id'];
        $cartItem->product_type = CartConfig::PRODUCT;
        $cartItem->rental_type = $data['rental_type'];
        $cartItem->rent_start = $data['rent_start'];
        $cartItem->rent_end = $data['rent_end'];
        $cartItem->price = 0;

        $this->save($cartItem);
    }

    /**
     *
     * @param type $cart
     * @param type $data
     * @return boolean
     */
    public function addItem($cart, $data)
    {
        $requestQuantity = $data['quantity'];

        // booking
        if (!empty($data['booking'])){
            $totalOptionPrice = array_sum(array_map(function($item){
                return $item['quantity'] * $item['price'];
            },$data['custom_fields']));
            $data['price'] = $totalOptionPrice / $data['quantity'];
        }

        if (!empty($data['id'])) {
            $cartItem = $this->get($data['id']);
        } else {
            if ($data['rental_type'] == 'buy') {
                $rentalType[] = 'buy';
            } else {
                $rentalType = ['hourly', 'daily', 'weekly', 'monthly', 'fixed', 'rent'];
            }
            // find any existing product in the cart .
            $cartItem = $this->find()
                ->where(['cart_id' => $cart->id, 'variants_products_id' => $data['variants_products_id'],
                    'location' => $data['location'], 'parent_id' => 0, 'rental_type IN' => $rentalType])
                ->first();
            if (empty($cartItem)) {
                $isNew = true;
                $cartItem = $this->newEntity();
            } else { // for existing products add quantity with existing quantity
                $isNew = false;
                $data['quantity'] = $cartItem->quantity + $data['quantity'];
                // If product options exist, get existing price & quantity  and add new price and get the average.
                RentMy::addModel(['OrderProductOptions']);
                if (!empty($data['booking'])){
                    RentMy::$Model['OrderProductOptions']->createForBooking($cartItem->id, $cartItem->cart_id, $requestQuantity, $data);
                }else{
                    RentMy::$Model['OrderProductOptions']->create($cartItem->id, $cartItem->cart_id, $requestQuantity, $data);
                }
                if (!empty($data['custom_fields'][0])) {
                    RentMy::addModel(['OrderProductOptions']);
                    $price =   RentMy::$Model['OrderProductOptions']->getAverage('cart', $cartItem->cart_id, $cartItem->id);
                    $data['price'] = !empty($price['unit_price']) ? $price['unit_price'] : $data['price'];
                    $subTotalFromOptions = !empty($price['sub_total']) ? $price['sub_total'] : ( $data['price'] * ($cartItem['quantity'] - 1) *  $data['rental_duration']);
                }
            }
        }

        // check seasonal price data here and update the price thoroughly and return
        RentMy::addModel(['ProductPrices']);
        $data['price'] = RentMy::$Model['ProductPrices']->seasonalPrice($data['price'], $data['rent_start'], $data['product_id']);
        // ends of seasonal pricing

        // making price 0 for rental items and membership
        if (!empty($data['membership']) && $data['rental_type'] != 'buy'){
            $data['price'] = 0;
        }

        $data['deposit_amount'] = isset($data['deposit_amount']) ? $data['deposit_amount'] : 0;
        $data['deposit_amount'] = $data['deposit_amount'] * $data['quantity'];
        $priceWithoutTax = $subTotal = !empty($subTotalFromOptions) ? $subTotalFromOptions : ($data['price'] * $data['quantity'] * $data['rental_duration']);

        // automatic discount
        RentMy::addModel(['Coupons']);
        // returns sub_total && off_amount .
        $coupon = RentMy::$Model['Coupons']->fixedOfferCoupon([
            'cart_id' => $cart->id, 'token' => $data['token'],
            'product_id' => $data['product_id'], 'sub_total' => $subTotal, 'quantity' => $data['quantity'], 'price' => $data['price']
        ]);

        //$tax = round(($priceWithoutTax * ($data['sales_tax'] * 0.01)),2);
        //$tax = $priceWithoutTax * ($data['sales_tax'] * 0.01);
        //$total = $coupon['sub_total'] + $tax;
        $total = $coupon['sub_total'];

        $cartData = [
            'cart_id' => $cart->id,
            'variants_products_id' => $data['variants_products_id'],
            'location' => $data['location'],
            'variant_chain_id' => $data['variant_chain_id'],
            'variant_chain_name' => $data['variant_chain_name'],
            'product_id' => $data['product_id'],
            'price' => $data['price'],
            'sales_tax'=>$data['sales_tax'],
            'quantity' => $data['quantity'],
            'quantity_id' => $data['quantity_id'],
            'store_id' => $data['store_id'],
            'term' => isset($data['term']) ? $data['term'] : 1,
            'deposit_amount' => $data['deposit_amount'],
            'rental_duration' => $data['rental_duration'],
            'sub_total' => $coupon['sub_total'],
            'substantive_price' => $priceWithoutTax,
            'rental_type' => $data['rental_type'],
            'driving_license_required' => $this->_getBooleanValue($data['driving_license_required']),
            'total' => $total,
            'automatic_coupon_amount' => $coupon['off_amount'],
            'vendor_discount_amount' => $coupon['vendor_discount_amount']
        ];

        if(!empty($coupon['off_amount'] + $coupon['vendor_discount_amount'])) {
            $cartItemOptions = [];
            $cartData['automatic_coupon_amount'] = $coupon['off_amount'];
            $cartItemOptions['automatic_coupon_sub_total'] =  $coupon['sub_total'];
            $cartItemOptions['automatic_coupon_discount'] = $coupon['off_amount'];
            $cartItemOptions['vendor_discount_amount'] = $coupon['vendor_discount_amount'];
            if (($coupon['off_amount'] + $coupon['vendor_discount_amount']) > $cartData['substantive_price']){
                $cartData['sub_total'] = 0;
                $cartItemOptions['automatic_coupon_sub_total'] = 0;
            }
            $cartData['additional'] = json_encode($cartItemOptions);
        }

//        recurring for cart specific items
            $recurring = $this->itemRecurring($data);
            if (!empty($recurring))
                $cartData['options'] = json_encode($recurring);

         $options = !empty($cartData['options']) ? json_decode($cartData['options'], true) : [];

         if (!empty($data['price_id'])){
             $options['price_id'] = $data['price_id'];
         }
        $cartData['options'] = json_encode($options);
        if (!empty($data['assets'])) {
            $cartData['assets'] = $data['assets'];
        } else {
            $cartData['assets'] = null;
        }

        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            $cartData['rent_start'] = $data['rent_start'];
            $cartData['rent_end'] = $data['rent_end'];
        }
        // print_r($cartData);
        $cartItem = $this->patchEntity($cartItem, $cartData);
        if ($this->save($cartItem)) {
            if ($isNew) {
                RentMy::addModel(['OrderProductOptions']);
                if (!empty($data['booking'])){
                    RentMy::$Model['OrderProductOptions']->createForBooking($cartItem->id, $cartItem->cart_id, $requestQuantity, $data);
                }else{
                    RentMy::$Model['OrderProductOptions']->create($cartItem->id, $cartItem->cart_id, $requestQuantity, $data);
                }

            }
            return $cartItem;
        }
        return false;
    }

    public function itemRecurring($data){
        RentMy::addModel(['Products', 'ProductPrices']);
        $selected_product = RentMy::$Model['Products']->find()->where(['store_id'=>RentMy::$store->id,'id' => $data['product_id']])->first();
        $productOptions = !empty($selected_product['options'])?json_decode($selected_product['options'], true):[];

        if (!empty($data['recurring']) && !empty($data['price_id']) && !empty($productOptions['enduring_rental'])) {
            RentMy::addModel(['ProductPrices']);
            $rental = ['before_rental', 'after_rental'];
            if ((RentMy::$storeConfig['arb']['active']) && (in_array(RentMy::$storeConfig['arb']['store_active'], $rental))) {
                $priceDetails = RentMy::$Model['ProductPrices']->find()->where(['id' => $data['price_id'], 'variants_products_id' => $data['variants_products_id']])->first();
                $recurring['recurring'] = [
                    'price_id' => $data['price_id'],
                    'duration_type' => $priceDetails['duration_type'],
                    'duration' => $priceDetails['duration'],
                    'payment_type' => RentMy::$storeConfig['arb']['store_active'],
                    'term' => $priceDetails['max_duration']
                ];
                return $recurring;
            }
        }
        return [];
    }

    /**
     *
     * @param  $cart
     * @param  $data
     * @return boolean
     */
    public function addItemWithoutDate($cart, $data)
    {
        $requestQuantity = $data['quantity'];
        $rentalType = ['hourly', 'daily', 'weekly', 'monthly', 'fixed', 'rent'];
        // find any existing product in the cart .
        $cartItem = $this->find()
            ->where(['cart_id' => $cart->id, 'variants_products_id' => $data['variants_products_id'],
                'location' => $data['location'], 'parent_id' => 0, 'rental_type IN' => $rentalType])
            ->first();
        if (empty($cartItem)) {
            $cartItem = $this->newEntity();
        } else { // for existing products add quantity with existing quantity
            $data['quantity'] = $cartItem->quantity + $data['quantity'];
        }

        $data['deposit_amount'] = isset($data['deposit_amount']) ? $data['deposit_amount'] : 0;
        $data['sales_tax'] = 0;
        $priceWithoutTax = $subTotal = 0;
        $tax = 0.00;
        $total = 0.00;

        $cartData = [
            'cart_id' => $cart->id,
            'variants_products_id' => $data['variants_products_id'],
            'location' => $data['location'],
            'variant_chain_id' => $data['variant_chain_id'],
            'variant_chain_name' => $data['variant_chain_name'],
            'product_type' => $data['product_type'],
            'product_id' => $data['product_id'],
            'price' => 0.00,
            'quantity' => $data['quantity'],
            'quantity_id' => $data['quantity_id'],
            'store_id' => RentMy::$store->id,
            'term' => isset($data['term']) ? $data['term'] : 1,
            'deposit_amount' => $data['deposit_amount'],
            'sales_tax' => $data['sales_tax'],
            'rental_duration' => $data['rental_duration'] ?? 1,
            'sub_total' => $subTotal,
            'substantive_price' => $priceWithoutTax,
            'sales_tax_price' => $tax,
            'rental_type' => 'rent',
            'driving_license_required' => $this->_getBooleanValue($data['driving_license_required']),
            'total' => $total
        ];

        $cartItem = $this->patchEntity($cartItem, $cartData);

        if ($this->save($cartItem)) {
            if ($cartItem->product_type == 2) {// package items .
                RentMy::addModel(['Quantities', 'Products']);
                foreach ($data['products'] as $item) {
                    $quantity = RentMy::$Model['Quantities']->find()
                        ->where(['variants_products_id' => $item['variants_products_id']])
                        ->where(['location' => $data['location']])
                        ->first();
                    $variant = RentMy::$Model['Products']->_getDefaultAttribute($item['variants_products_id']);
                    $packageItemData = [
                        'variant_chain_id' => $variant['variant_chain_id'],
                        'variant_chain_name' => $variant['variant_chain_name'],
                        'product_id' => $item['product_id'],
                        'store_id' => RentMy::$store->id,
                        'location' => $data['location'],
                        'variants_products_id' => $item['variants_products_id'],
                        'quantity_id' => $quantity->id,
                        'quantity' => $item['quantity'] * $data['quantity'],
                        'parent_id' => $cartItem->id,
                        'cart_id' => $data['cart_id'],
                        'product_type' => 2, //CartConfig::PRODUCT,
                        'rental_type' => $data['rental_type'],
                        'price' => 0
                    ];
                    $packageCartItem = $this->newEntity($packageItemData);
                    $this->save($packageCartItem);
                }
            }

            RentMy::addModel(['OrderProductOptions']);
            RentMy::$Model['OrderProductOptions']->create($cartItem->id, $cartItem->cart_id, $requestQuantity, $data);

            return $cartItem;
        }
        return false;
    }


    /**
     * Update cart item price and rental duration
     * @param $cartItem
     * @return cartItem
     */
    function updateProductPrice($cart, $cartItem)
    {

        $priceWithoutTax = $subTotal = $cartItem->rental_price * $cartItem->quantity * $cartItem->rental_duration;
//        $offAmount = !empty($cartItem['off_amount']) ? ($priceWithoutTax * ($cartItem['off_amount'] * 0.01)) : 0;
        $offAmount = !empty($cartItem['off_amount']) ? $cartItem['off_amount'] : 0;
        $subTotal = $priceWithoutTax - $offAmount;

        RentMy::addModel(['OrderProductOptions']);
        $price = RentMy::$Model['OrderProductOptions']->getAverage('cart', $cartItem->cart_id, $cartItem->id);

//        if (!empty($price['unit_price']))
//            $cartItem->rental_price = $price['unit_price'];


        $cartItemOptions = !empty($cartItem['additional']) ? json_decode($cartItem['additional'], true) : [];
        if (isset($cartItemOptions['coupon_sub_total'])) unset($cartItemOptions['coupon_sub_total']);
        if (isset($cartItemOptions['coupon_discount'])) unset($cartItemOptions['coupon_discount']);
        if (isset($cartItemOptions['coupon_type'])) unset($cartItemOptions['coupon_type']);

        if (!empty($price['sub_total']))
            $subTotal = $price['sub_total'];

        // automatic discount
        RentMy::addModel(['Coupons']);
        // returns sub_total && off_amount .
        $coupon = RentMy::$Model['Coupons']->fixedOfferCoupon([
            'cart_id' => $cart->id, 'token' => $cart->uid,
            'product_id' => $cartItem['product_id'], 'sub_total' => $subTotal,
            'quantity' => $cartItem->quantity, 'price' => $cartItem->rental_price
        ]);

        $tax = $priceWithoutTax * $cartItem->sales_tax * 0.01;
        $total = $coupon['sub_total'] + $tax;

        if ($cartItem['membership_checkout'] && $cartItem['rental_type'] !== 'buy'){
            $tax = $total = $subTotal = $cartItem->rental_price = 0;
        }
        $cartItem->price = $cartItem->rental_price;
        $cartItem->sub_total = $coupon['sub_total'];
        $cartItem->substantive_price = $priceWithoutTax;
        $cartItem->sales_tax_price = $tax;
        $cartItem->total = $total;
        $cartItem->coupon_amount = 0; // initializing while changing date
        //$cartItem->rent_start = $cartItem->start_date;
        //$cartItem->rent_end = $cartItem->end_date;
        if ($cartItem['membership_checkout'] && $cartItem['rental_type'] !== 'buy'){
            $cartItem->price = $cartItem->sub_total = $cartItem->sales_tax_price = $cartItem->total = $cartItem->rental_price = 0;
        }
        if(!empty($coupon['off_amount'] + $coupon['vendor_discount_amount'])) {
            $cartItemOptions = [];
            $cartItem->vendor_discount_amount = $coupon['vendor_discount_amount'];
            $cartItem->automatic_coupon_amount = $coupon['off_amount'];
            $cartItemOptions['automatic_coupon_sub_total'] =  $coupon['sub_total'];
            $cartItemOptions['automatic_coupon_discount'] = $coupon['off_amount'];
            $cartItemOptions['vendor_discount_amount'] = $coupon['vendor_discount_amount'];
            if (($coupon['off_amount'] + $coupon['vendor_discount_amount']) > $cartItem['substantive_price']){
                $cartItem['sub_total'] = 0;
                $cartItemOptions['automatic_coupon_sub_total'] = 0;
            }

        }

        $cartItem['additional'] = json_encode($cartItemOptions);
        $this->save($cartItem);
        return $cartItem;
    }


    public function _getBooleanValue($value)
    {
        $booleanValue = false;
        $type = gettype($value);
        if ($type == 'string') {
            $booleanValue = $value == 'true' ? true : false;
        } else if ($type == 'boolean') {
            $booleanValue = $value;
        } else if ($type == 'integer') {
            $booleanValue = $value == 1 ? true : false;
        }
        return $booleanValue;
    }

    /**
     *
     * @param type $rentStart
     * @param type $duration
     * @param type $rentType
     * @deprecated
     * GET End date from start date and price duration
     */
    public function _getRentEndTime($rentStart, $duration = null, $rentType = null, $term = 1)
    {
        $rentEnd = '';
        $reduceSecond = 0;
        //if(empty(RentMy::$storeConfig['show_start_time'])){
        // $reduceSecond= 60;
        //}
        if ($duration && $rentType) {
            $startTimeInSeconds = strtotime($rentStart);
            if ($rentType == 'hourly') {
                $rentEnd = $startTimeInSeconds + (($duration * 3600 * $term) - $reduceSecond);
            } else if ($rentType == 'daily') {
                $rentEnd = $startTimeInSeconds + (($duration * 24 * 3600 * $term) - $reduceSecond);
            } else if ($rentType == 'weekly') {
                $term = (7 * $term);
                $rentEnd = $startTimeInSeconds + (($duration * 24 * 3600 * $term) - $reduceSecond);
            } else if ($rentType == 'monthly') {
                $term = (30 * $term);
                $rentEnd = $startTimeInSeconds + (($duration * 24 * 3600 * $term) - $reduceSecond);
            } else {
                $rentEnd = strtotime($rentStart);
            }
        }

        $rentalDayCalcType = empty(RentMy::$storeConfig['rental_day']) ? '24' : RentMy::$storeConfig['rental_day'];
        if ($rentalDayCalcType == 'calendar'){
            if (strtotime($rentStart) < (strtotime("today", $rentEnd) - 1)){
               // $rentEnd   = strtotime("today", $rentEnd) - 1;
            }
        }

        if ($rentEnd) { //now formatting
            $rentEnd = date('Y-m-d H:i', $rentEnd);
        }
        return $rentEnd;
    }

    /*
     * @deprecated
     * Checking Availability of a product
     */
    public function availability($data)
    {
        $start = date('Y-m-d', strtotime($data['rent_start']));
        $rentEndTime = !empty($data['rent_end']) ? $data['rent_end'] : $this->_getRentEndTime($data['rent_start'], $data['rental_duration'], $data['rental_type'], $data['term']);
        $end = date('Y-m-d', strtotime($rentEndTime));
        $options = array();
        $options = array_merge(array("product_id" => $data['product_id']), $options);
        $options = array_merge(array(
            'AND' => array(
                "DATE_FORMAT(start_date,'%Y-%m-%d') >=" => $start,
                "DATE_FORMAT(end_date ,'%Y-%m-%d') <=" => $end,
                "status IN" => [1, 2],
            )
        ), $options);

        $availableObj = TableRegistry::get('ProductsAvailabilities');
        $availableQuery = $availableObj->find();
        $available = $availableQuery
            ->select([
                'quantity' => $availableQuery->func()->sum('quantity')
            ])
            ->where($options)
            ->first();

        return empty($available->quantity) ? 0 : $available->quantity;
    }

    public function removeItem($cart, $data)
    {
        $cartItem = $this->find()->where(['id' => $data['cart_item_id']])->first();
        if ($cartItem) {
            if ($this->delete($cartItem)) {
                RentMy::addModel(['OrderProductOptions']);
                RentMy::$Model['OrderProductOptions']->deleteAll(['content_item_id' => $cartItem->id, 'content_type' => 'cart']);
                //now have to update cart table
                $this->Carts->_updatePriceNQty($cart);
                return true;
            }
        }
        return false;
    }

    /**
     * This function is used for calcuting total items exist in a cart for any variant product
     * @param $token
     * @param $quantity_id = unique(product_id + variants_product_id + location)
     * @return  total items in cart
     */
    function available($token, $quantity_id)
    {
        $finder = $this->find()->contain(['Carts'])->where(['Carts.uid' => $token, 'CartItems.quantity_id' => $quantity_id]);
        $finder->select(['count' => $finder->func()->sum('CartItems.quantity')]);
        $counter = $finder->first();
        return empty($counter['count']) ? 0 : $counter['count'];
    }

    /**
     * This function is used for calcuting total items exist in a cart for any package
     * @param $token
     * @return int | total package in cart
     */
    function packageAvailable($token, $variants_products_id)
    {
        $finder = $this->find()->contain(['Carts'])->where(['Carts.uid' => $token, 'CartItems.variants_products_id' => $variants_products_id]);
        $finder->select(['count' => $finder->func()->sum('CartItems.quantity')]);
        $counter = $finder->first();
        return empty($counter['count']) ? 0 : $counter['count'];
    }

    /**
     * add required addons items to cart
     * @param $cart
     * @param $data
     */
    function addRequiredAddonsItems($cart, $item, $data)
    {

        if (empty($data['required_addons'])) {
            return;
        }
        foreach ($data['required_addons'] as $addon) {
            if (!empty($addon['quantity'])) {
                $variant = $this->Products->_getDefaultAttribute($addon['variants_products_id']);
                $variant_chain_id = $variant['variant_chain_id'];
                $variant_chain_name = $variant['variant_chain_name'];
                $parent_id = $item->id;
                $cartData = [
                    'cart_id' => $cart->id,
                    'variants_products_id' => $addon['variants_products_id'],
                    'location' => $data['location'],
                    'variant_chain_id' => $variant_chain_id,
                    'variant_chain_name' => $variant_chain_name,
                    'product_id' => $addon['product_id'],
                    'product_type' => 3,
                    'price' => 0,
                    'quantity' => $addon['quantity'],
                    'quantity_id' => $addon['quantity_id'],
                    'store_id' => RentMy::$store->id,
                    'term' => 1,
                    'deposit_amount' => 0,
                    //'sales_tax' => 0,
                    'rental_duration' => 1,
                    'sub_total' => 0,
                    'substantive_price' => 0,
                    //'sales_tax_price' => 0,
                    'rental_type' => $data['rental_type'],
                    'driving_license_required' => 0,
                    'total' => 0,
                    'parent_id' => $parent_id,
                    'additional' => json_encode(['addon_initial_quantity' => $addon['quantity']])
                ];
                //    RentMy::dbg($cartData);
                if ($data['rental_type'] == 'buy') {
                    $rentalType[] = 'buy';
                } else {
                    $rentalType = ['hourly', 'daily', 'weekly', 'monthly', 'fixed', 'rent'];
                    if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
                        $cartData['rent_start'] = $data['rent_start'];
                        $cartData['rent_end'] = $data['rent_end'];
                    }
                }
                // find any existing product in the cart .
                $cartItem = $this->find()
                    ->where(['cart_id' => $cart->id, 'variants_products_id' => $addon['variants_products_id'],
                        'location' => $data['location'], 'parent_id' => $parent_id, 'rental_type IN' => $rentalType])
                    ->first();
                if (empty($cartItem)) {
                    $cartItem = $this->newEntity();
                    $cartData['additional'] = json_encode(['addon_initial_quantity' => $addon['quantity']]);
                } else { // for existing products add quantity with existing quantity
                    $cartData['quantity'] = $cartItem->quantity + $addon['quantity'];
                    $cartAdditions = json_decode($cartItem->additional, true);
                    $cartAdditions['addon_initial_quantity'] = $addon['quantity'];
                    $cartData['additional'] = json_encode($cartAdditions);
                    $cartData['quantity'] = $cartItem->quantity + $addon['quantity'];
                }

                // get item buy price
                if ($data['rental_type'] == 'buy') {
                    RentMy::addModel(['ProductPrices']);
                    $price = RentMy::$Model['ProductPrices']->find()->where(['variants_products_id' => $addon['variants_products_id'], 'duration_type' => 'base'])->first();
                    if (!empty($price['price'])) {
                        $data['sales_tax'] = empty($data['sales_tax']) ? 0 : $data['sales_tax'];
                        $priceWithoutTax = $subTotal = $price['price'] * $cartData['quantity'];

                        // automatic discount
                        RentMy::addModel(['Coupons']);
                        // returns sub_total && off_amount .
                        $coupon = RentMy::$Model['Coupons']->fixedOfferCoupon([
                            'cart_id' => $cart->id, 'token' => $data['token'],
                            'product_id' => $addon['product_id'], 'sub_total' => $subTotal, 'quantity' => $cartData['quantity'], 'price' => $price['price']
                        ]);
                        $cartData['price'] = $price['price'];
                        // $cartData['sales_tax']         = $data['sales_tax'];
                        $cartData['sub_total'] = $coupon['sub_total'];
                        $cartData['substantive_price'] = $priceWithoutTax;
                        // $cartData['sales_tax_price']   = $priceWithoutTax * ($data['sales_tax'] * 0.01);
                        //$cartData['total']             = $coupon['sub_total'] + $cartData['sales_tax_price'];
                        $cartData['total'] = $coupon['sub_total'];
                    }
                }
                $this->patchEntity($cartItem, $cartData);
                $this->save($cartItem);
            }
        }
    }


    /**
     * Update addons cart items when master item quantity is changed
     * @param $cartItem
     */
    public function updateCartAddon($cartItem)
    {
        $addon_items = $this->find()->where([
            'cart_id' => $cartItem->cart_id,
            'parent_id' => $cartItem->id,
            'product_type' => 3
        ])->toArray();
        //RentMy::dbg($addon_items);
        if (!empty($addon_items)) {
            foreach ($addon_items as $item) {
                $additional = json_decode($item['additional'], true);
                $initialQuantity = empty($additional['addon_initial_quantity']) ? 1 : $additional['addon_initial_quantity'];
                $item->quantity = $cartItem['quantity'] * $initialQuantity;
                $this->save($item);
            }
        }
    }

    /**
     * format assets data for add on cart CTA.
     * @param $data [''serial_no']
     * @return array $response[success] boolean
     */

    public function updateAssetsItem($data)
    {
        if (empty($data['asset_id'])) {
            return ['success' => false, 'data' => []];
        }

        $response['success'] = true;
        $response['data'] = [];

        if (empty($data['cart_id'])) {
            $response['data'] = json_encode([
                [
                    'id' => $data['asset_id'],
                    'name' => $data['serial_no'],
                ]
            ]);
            return $response;
        }

        $assetExists = $this->find()
            ->where([
                'cart_id' => $data['cart_id'],
                'product_id' => $data['product_id'],
                'variants_products_id' => $data['variants_products_id'],
                'quantity_id' => $data['quantity_id'],
            ])->first();

        if (!empty($assetExists)) {
            $asset_arr = !empty($assetExists->assets) ? json_decode($assetExists->assets, true) : [];

            // Check if the asset ID already exists in the array
            $assetExists = array_filter($asset_arr, function($ast_obj) use ($data) {
                return $ast_obj['id'] == $data['asset_id'];
            });

            if (!empty($assetExists)) {
                // If the asset ID exists, set success to false and return
                $response['success'] = false;
                return $response;
            } else {
                // If the asset ID doesn't exist, append the new asset and encode the result
                $asset_arr[] = [
                    'id' => $data['asset_id'],
                    'name' => $data['serial_no'],
                ];

                // Remove duplicates and encode the result
                $result = array_unique($asset_arr, SORT_REGULAR);
                $response['data'] = json_encode($result);
            }

        } else {
            $response['data'] = json_encode([
                [
                    'id' => $data['asset_id'],
                    'name' => $data['serial_no'],
                ]
            ]);
        }

        return $response;
    }


    /**
     * Update each items sales tax based on tax lookup rate
     * @param $cart_id
     * @param $tax_id
     * @param $tax
     */
    function cartItemTotal($item)
    {
        return ($item['sub_total'] + $item['deposit_amount']);
    }
}
