<?php

namespace App\Model\Table;

use App\Lib\RentMy\RentMy;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

class ApiLogsTable extends Table
{
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('api_logs');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Users', [
            'foreignKey' => 'user_id',
            'joinType' => 'INNER'
        ]);
    }

    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');
        return $validator;
    }

    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['user_id'], 'Users'));

        return $rules;
    }

    public function keepLog($data)
    {
        $logData = [];
        if (isset($data['user_id']) && !empty($data['user_id'])) {
            $logData['user_id'] = $data['user_id'];
        }
        if (isset($data['token']) && !empty($data['token'])) {
            $logData['token'] = $data['token'];
        }
        $entity = $this->newEntity();
        $logData = $this->patchEntity($entity, $logData);
        if ($this->save($logData)) {
            return true;
        }
        return false;
    }

    public function addToLog($options)
    {
        $data = [
            'user_id' => $options['user_id']?? '',
            'store_id'=> $options['store_id']?? '',
            'method'=> $options['method'],
            'content_id' => !empty($options['content_id']) ? $options['content_id'] : '',
            'content' => $options['content']?? '',
            'action' => $options['action'] ?? '',
            'text' => !empty($options['text']) ? $options['text'] : '',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'os' => $this->getOS(),
            'browser' => $this->getBrowser(),
            'device' => $this->detectDevice(),
            'token' => $options['token']?? '',
            'request' => $options['request']?? '',
            'response' => $options['response']?? '',
        ];

        $log = $this->newEntity();
        $log = $this->patchEntity($log, $data);
        $this->save($log);

        $data['response'] = (string) $data['response'];

        $customer_id = '';
        if( isset($data['request']['customer_id']) ) {
            $customer_id = $data['request']['customer_id'];
        }
        $dataRequestJson = json_decode($data['request'], true);
        if( isset($dataRequestJson['password']) ) {
            $dataRequestJson['password'] = '**********';
            $data['request'] = json_encode($dataRequestJson);
        }

        RentMy::logToGenius([
            "event" => "api_log",
            "status" => $options['method'],
            "description" => $options['content'] ?? '',
            "value" => '',
            "custom_content" => json_encode($data),
            "ref2" => $customer_id,
        ]);

    }

    function getOS()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $os_platform = "Unknown OS Platform";
        $os_array = array(
            '/windows nt 10/i' => 'Windows 10',
            '/windows nt 6.3/i' => 'Windows 8.1',
            '/windows nt 6.2/i' => 'Windows 8',
            '/windows nt 6.1/i' => 'Windows 7',
            '/windows nt 6.0/i' => 'Windows Vista',
            '/windows nt 5.2/i' => 'Windows Server 2003/XP x64',
            '/windows nt 5.1/i' => 'Windows XP',
            '/windows xp/i' => 'Windows XP',
            '/windows nt 5.0/i' => 'Windows 2000',
            '/windows me/i' => 'Windows ME',
            '/win98/i' => 'Windows 98',
            '/win95/i' => 'Windows 95',
            '/win16/i' => 'Windows 3.11',
            '/macintosh|mac os x/i' => 'Mac OS X',
            '/mac_powerpc/i' => 'Mac OS 9',
            '/linux/i' => 'Linux',
            '/ubuntu/i' => 'Ubuntu',
            '/iphone/i' => 'iPhone',
            '/ipod/i' => 'iPod',
            '/ipad/i' => 'iPad',
            '/android/i' => 'Android',
            '/blackberry/i' => 'BlackBerry',
            '/webos/i' => 'Mobile'
        );

        foreach ($os_array as $regex => $value) {
            if (preg_match($regex, $user_agent)) {
                $os_platform = $value;
            }
        }
        return $os_platform;
    }

    function getBrowser()
    {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        $browser = "Unknown Browser";
        $browser_array = array(
            '/msie/i' => 'Internet Explorer',
            '/firefox/i' => 'Firefox',
            '/safari/i' => 'Safari',
            '/chrome/i' => 'Chrome',
            '/edge/i' => 'Edge',
            '/opera/i' => 'Opera',
            '/netscape/i' => 'Netscape',
            '/maxthon/i' => 'Maxthon',
            '/konqueror/i' => 'Konqueror',
            '/mobile/i' => 'Handheld Browser'
        );
        foreach ($browser_array as $regex => $value) {
            if (preg_match($regex, $user_agent)) {
                $browser = $value;
            }
        }
        return $browser;
    }

    function detectDevice()
    {
        $userAgent = $_SERVER["HTTP_USER_AGENT"];
        $devicesTypes = array(
            "computer" => array("msie 10", "msie 9", "msie 8", "windows.*firefox", "windows.*chrome", "x11.*chrome", "x11.*firefox", "macintosh.*chrome", "macintosh.*firefox", "opera"),
            "tablet" => array("tablet", "android", "ipad", "tablet.*firefox"),
            "mobile" => array("mobile ", "android.*mobile", "iphone", "ipod", "opera mobi", "opera mini"),
            "bot" => array("googlebot", "mediapartners-google", "adsbot-google", "duckduckbot", "msnbot", "bingbot", "ask", "facebook", "yahoo", "addthis")
        );
        $deviceName = 'Unknown';
        foreach ($devicesTypes as $deviceType => $devices) {
            foreach ($devices as $device) {
                if (preg_match("/" . $device . "/i", $userAgent)) {
                    $deviceName = $deviceType;
                }
            }
        }
        return ucfirst($deviceName);
    }
}
