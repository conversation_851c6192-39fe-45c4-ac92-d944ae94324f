<?php

namespace App\Model\Table;


use App\Lib\RentMy\RentMy;
use Cake\Event\Event;
use Cake\ORM\Entity;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

class OrderProductOptionsTable extends Table
{
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('order_product_options');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');
        $this->addBehavior('Timestamp');

        $this->belongsTo('CartItems', [
            'foreignKey' => 'cart_Item_id',
            'joinType' => 'INNER'
        ]);
    }

    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');
        return $validator;
    }

    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['cart_Item_id'], 'CartItems'));
        return $rules;
    }


    /**
     * @param Event $event
     * @param Entity $entity
     */
    public function afterSave(Event $event, Entity $entity)
    {
        $this->_eventToRecalculate($entity);
    }

    /**
     * @param Event $event
     * @param Entity $entity
     */
    public function afterDelete(Event $event, Entity $entity)
    {
        $this->_eventToRecalculate($entity);
    }

    public function _eventToRecalculate($entity)
    {
        $contentItem = 'CartItems';
        $content = 'Carts';
        if ($entity->content_type == 'order') {
            $contentItem = 'OrderItems';
            $content = 'Order';
        }


        RentMy::addModel([$content, $contentItem]);

        $itemDetails = $this->getItemizedAndTotal($entity->content_item_id, $entity->content_type);
        RentMy::$Model[$contentItem]->updateAll(['price' => $itemDetails['sub_total']], ['id' => $entity->content_item_id]);

 //       $cartItem = RentMy::$Model[$contentItem]->find()->where(['id' => $entity->content_item_id])->first();
//        $cart = RentMy::$Model[$content]->find()->where(['id' => $entity->content_id])->first();
//        RentMy::$Model[$contentItem]->updateProductPrice($cart, $cartItem);
    }

    /**
     * @param $contentItemId
     * @param $contentId
     * @param $requestedQuantity
     * @param $data
     * @param string $type
     */
    public function create($contentItemId, $contentId, $requestedQuantity, $data, $type = 'cart')
    {
        if (empty($data['custom_fields']))
            return;

        $existingEntity = [];
        $existingEntities = $this->find()->where(['content_Item_id' => $contentItemId, 'content_type' => $type])->toArray();
        if (!empty($existingEntities)) {
            $ids = array_column($data['custom_fields'], 'id');
            foreach ($existingEntities as $entity) {
                $options = json_decode($entity->options, true);
                $optionIds = array_column($options, 'id');
                sort($optionIds);
                sort($ids);
                if ($ids == $optionIds)
                    $existingEntity = $entity;
            }
        }

        if (!empty($existingEntity)) {
            $CartProductOption = $this->patchEntity($existingEntity, ['quantity' => ($requestedQuantity + $existingEntity->quantity)]);
        } else {
            $price = $data['price'];
            $productId = $data['product_id'] ?? $data['package_id'];
            RentMy::addModel(['ProductFieldValues', 'ProductFields']);
            foreach ($data['custom_fields'] as &$custom_field) {
                $_value = RentMy::$Model['ProductFieldValues']->find()->where(['store_id' => RentMy::$store->id, 'product_id' => $productId, 'name' => $custom_field['name'], 'value' => $custom_field['value']])->first();
                if (empty($_value))
                    continue;
                $custom_field['price_type'] = $_value->price_type;
                $custom_field['price_amount'] = $_value->price_amount;

                $productField = RentMy::$Model['ProductFields']->find()->select(['options', 'id'])->where(['store_id' => RentMy::$store->id, 'name' => $custom_field['name']])->first();
                if (!empty($productField))
                    $custom_field['options'] = $productField->options;
            }

            $CartProductOption = $this->newEntity([
                'content_type' => $type,
                'content_id' => $contentId,
                'content_item_id' => $contentItemId,
                'product_id' => $productId,
                'options' => json_encode($data['custom_fields']),
                'quantity' => $requestedQuantity,
                'price' => $price,
            ]);
        }

        $this->save($CartProductOption);
    }


    public function createForBooking($contentItemId, $contentId, $requestedQuantity, $data, $type = 'cart')
    {
        if (empty($data['custom_fields']))
            return;


        foreach ($data['custom_fields'] as $custom_field){
            $existingEntity = [];
            $existingEntities = $this->find()->where(['content_Item_id' => $contentItemId, 'content_type' => $type])->toArray();
            if (!empty($existingEntities)) {
                foreach ($existingEntities as $entity) {
                    $options = json_decode($entity->options, true);
                    $optionIds = array_column($options, 'id');
                    if (in_array($custom_field['id'], $optionIds)){
                        $existingEntity = $entity;
                        break;
                    }
                }
            }

            if (!empty($existingEntity)) {
                $CartProductOption = $this->patchEntity($existingEntity, ['quantity' => ($custom_field['quantity'] + $existingEntity->quantity)]);
            } else {
                $price = $custom_field['price'];
                $productId = $data['product_id'] ?? $data['package_id'];
                RentMy::addModel(['ProductFieldValues', 'ProductFields']);
                $_value = RentMy::$Model['ProductFieldValues']->find()->where(['store_id' => RentMy::$store->id, 'product_id' => $productId, 'name' => $custom_field['name'], 'value' => $custom_field['value']])->first();
                if (empty($_value))
                    continue;
                $custom_field['price_type'] = $_value->price_type;
                $custom_field['price_amount'] = $_value->price_amount;
                $productField = RentMy::$Model['ProductFields']->find()->select(['options', 'id'])->where(['store_id' => RentMy::$store->id, 'name' => $custom_field['name']])->first();
                if (!empty($productField))
                    $custom_field['options'] = $productField->options;

                $CartProductOption = $this->newEntity([
                    'content_type' => $type,
                    'content_id' => $contentId,
                    'content_item_id' => $contentItemId,
                    'product_id' => $productId,
                    'options' => json_encode([$custom_field]),
                    'quantity' => $custom_field['quantity'],
                    'price' => $price,
                ]);
            }

            $this->save($CartProductOption);
        }


    }
    /**
     * @param $itemId
     * @param bool $isIncrement
     * @param string $type
     */
    public function updateQuantity($itemId, $isIncrement = true, $type = 'cart', $optionId = false)
    {

        $conditions = ['content_item_id' => $itemId, 'content_type' => $type];
        if ($optionId)
            $conditions['id'] = $optionId;

        $item = $this->find()->select(['id', 'quantity', 'content_type', 'content_item_id', 'content_id'])->where($conditions)->order(['id' => 'ASC'])->first();
        if (!empty($item)) {
            $qty = $item['quantity'];
            $isIncrement ? $qty++ : $qty--;

            //delete entity if qty is zero
            if ($qty == 0) {
                $this->delete($item);
                return;
            }

            $entity = $this->patchEntity($item, ['quantity' => $qty]);
            $this->save($entity);
        }
    }

    /**
     * @param $itemId
     * @param float $price
     * @param string $type
     */
    public function updatePrice($itemId, $price, $type = 'cart')
    {
        $productOptions = $this->find()->where(['content_item_id' => $itemId, 'content_type' => $type])->toArray();
        if (empty($productOptions)) {
            return;
        }
        RentMy::addModel(['ProductFieldValues']);
        foreach ($productOptions as &$productOption) {
            $options = json_decode($productOption['options'], true);
            $modifiedOptions = [];
            $priceTotal = 0;
            foreach ($options as $custom_field) {
                $_value = RentMy::$Model['ProductFieldValues']->find()->where(['store_id' => RentMy::$store->id, 'name' => $custom_field['name'], 'value' => $custom_field['value']])->first();
                if (empty($_value))
                    continue;

                if ($_value->price_type == 2)
                    $priceTotal += $_value->price_amount;
                elseif ($_value->price_type == 1)
                    $priceTotal += ($price * $_value->price_amount / 100);
                $custom_field['product_price'] = $price;
                $modifiedOptions[] = $custom_field;
            }
            $entityData = [
                'price' => $price + $priceTotal,
                'options' => json_encode($modifiedOptions)
            ];

            $op = $this->patchEntity($productOption, $entityData);
            if ($this->save($op)) {
                return true;
            }
        }
    }

    /**
     * @param $items
     */
    public function migrateCartToOrder($items)
    {
        if (empty($items))
            return;

        foreach ($items as $item)
            $this->updateAll($item['data'], $item['where']);

    }

    /**
     * @param $contentId
     * @param string $type
     */
    public function deleteAllFor($contentId, $type = 'cart')
    {
        $this->deleteAll([
            'content_type' => $type,
            'content_id' => $contentId,
        ]);
    }

    /**
     * @param $contentItemId
     * @param $type
     * @return array
     */
    public function getItemizedAndTotal($contentItemId, $type = 'cart')
    {
        $total = 0;
        $qty = 0;
        $items = $this->find()
            ->select(['id', 'options', 'quantity', 'price'])->where(['content_item_id' => $contentItemId, 'content_type' => $type])
            ->map(function ($item) use (&$total, &$qty) {
                $item['options'] = json_decode($item['options'], true);
                $item['exchange'] = false;
                if (!empty($item['options'])) {
                    $item['values'] = implode(';', array_column($item['options'], 'value'));
                   foreach ($item['options'] as &$option){
                       $option['options'] = !empty($option['options']) ? json_decode($option['options'], true) : [];
                       $exchangeableConfig = isset($option['options']['exchange'])? $option['options']['exchange'] : false;
                       if ($exchangeableConfig && in_array(strtolower($option['value']), ['yes', 'refill']))
                           $item['exchange'] = true;
                   }

                }

                $total += $item->quantity * $item->price;
                $qty += $item->quantity;
                //price convert to total price just to show
                $item->unit_price = $item->price;
                $item->price = round($item->quantity * $item->price, 2);

                return $item;
            })->toArray();

        if ($qty==0)
            return ['items' => $items, 'total' => $total, 'sub_total' => 0];

        return ['items' => $items, 'total' => $total, 'sub_total' => round($total / $qty, 2)];
    }

    /**
     * get product option price by cart item or order item
     * @param $contentItemId
     * @param string $type
     * @return mixed
     */
    public function getTotalByItem($contentItemId, $type = 'cart')
    {
        $total = $this->find()
            ->select(['sum' => 'SUM(price*quantity)'])->where(['content_item_id' => $contentItemId, 'content_type' => $type])
            ->first();

        return $total->sum;
    }

    /**
     * @param $content_type
     * @param $content_id
     * @param $content_item_id
     * @return float|int
     */
    public function getAverage($content_type,$content_id,$content_item_id){
        $unit_price= $sub_total = 0;
        $optionSummaryObj = $this->find();
        $optionSummary = $optionSummaryObj
            ->select([
                'price' => $optionSummaryObj->func()->sum('price*quantity'),
                'count' => $optionSummaryObj->func()->sum('quantity')])
            ->where(['content_id' => $content_id, 'content_item_id' => $content_item_id, 'content_type' => $content_type])
            ->first();
        if (!empty($optionSummary)) {
            $unit_price = round($optionSummary['price'] / $optionSummary['count'],2);
            $sub_total = $optionSummary['price'] ;

        }
        return ['unit_price'=>$unit_price,'sub_total'=> $sub_total];
    }
    /**
     * Get total product option price for any order or cart
     * @param $contentId - cart_id | order_id
     * @param string $type - cart | order
     * @return mixed
     */
    public function getTotal($contentId, $type = 'cart')
    {
        $total = $this->find()
            ->select(['sum' => 'SUM(price*quantity)'])->where(['content_id' => $contentId, 'content_type' => $type])
            ->first();

        return $total->sum;
    }

    /**
     * @param $pricing
     * @param $customFields
     * @return mixed
     */
    public function calculatePricing($pricing, $customFields)
    {
        if (empty($pricing))
            return $pricing;

        $productId = $pricing[0]->product_id;
        $_customFields = [];
        RentMy::addModel(['ProductFieldValues']);
        foreach ($customFields as $field) {
            $field = RentMy::$Model['ProductFieldValues']->find()
                ->select(['id', 'price_amount', 'price_type'])
                ->where(['store_id' => RentMy::$store->id, 'product_id' => $productId, 'name' => $field['name'], 'value' => $field['value']])
                ->first();

            if (!empty($field))
                $_customFields[] = ['price_amount' => $field->price_amount, 'price_type' => $field->price_type];
        }

        foreach ($pricing as $priceObj) {
            $customPrice = $promoPrice = 0;
            foreach ($_customFields as $field) {
                if ($field['price_type'] == 2) {
                    $customPrice += $field['price_amount'];
                    $promoPrice += $field['price_amount'];
                } elseif ($field['price_type'] == 1) {
                    $customPrice += ($priceObj->price * $field['price_amount'] / 100);
                    if (is_numeric($priceObj->promo_price))
                        $promoPrice += ($priceObj->promo_price * $field['price_amount'] / 100);
                }
            }

            $priceObj->price += $customPrice;
            if (!empty($priceObj->flex_price)){
                $priceObj->flex_price += $customPrice;
            }

            if (is_numeric($priceObj->promo_price) && !empty($priceObj->promo_price))
                $priceObj->promo_price += $promoPrice;
        }

        return $pricing;
    }

    /**
     * @param $contentId
     * @param string $type
     * @return boolean
     */
    public function hasExchangeable($contentId, $type = 'cart')
    {
        $isExchangeable = false;
        $this->find()
            ->select(['id', 'options'])->where(['content_id' => $contentId, 'content_type' => $type])
            ->map(function ($item) use (&$isExchangeable) {
                $item['options'] = json_decode($item['options'], true);
                if (!empty($item['options'])) {
                    foreach ($item['options'] as &$option){
                        $option['options'] = !empty($option['options']) ? json_decode($option['options'], true) : [];
                        $exchangeableConfig = isset($option['options']['exchange'])? $option['options']['exchange'] : false;
                        if ($exchangeableConfig && in_array(strtolower($option['value']), ['yes', 'refill']))
                            $isExchangeable = true;
                    }
                }
            })->toArray();

        return $isExchangeable;
    }
}
