<?php
namespace App\Model\Table;

use App\Lib\RentMy;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * SystemStatus Model
 *
 * @property \App\Model\Table\SystemStatusTable&\Cake\ORM\Association\BelongsTo $ParentSystemStatus
 * @property \App\Model\Table\ContentTypesTable&\Cake\ORM\Association\BelongsTo $ContentTypes
 * @property \App\Model\Table\StoresTable&\Cake\ORM\Association\BelongsTo $Stores
 * @property \App\Model\Table\SystemStatusTable&\Cake\ORM\Association\HasMany $ChildSystemStatus
 *
 * @method \App\Model\Entity\SystemStatus get($primaryKey, $options = [])
 * @method \App\Model\Entity\SystemStatus newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\SystemStatus[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\SystemStatus|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\SystemStatus saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\SystemStatus patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\SystemStatus[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\SystemStatus findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class SystemStatusTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('system_status');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
        $this->addBehavior('Tree');

    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmptyString('id', null, 'create');

        return $validator;
    }

    public function getStoreWiseStatus($statuses)
    {
        if ($statuses['store_id'] == 0) {
            $status = $this->find()->where(['store_id' => RentMy\RentMy::$store->id, 'reference_id' => $statuses['id']])->first();
            if (!empty($status))
                return $status;
        }

        return $statuses;
    }

    /**
     * Gets the child status IDs for a given parent status ID.
     *
     * @param int $parentId The ID of the parent status.
     * @return array An array of integer IDs for the children, or an empty array if none are found.
     */
    public static function getChildStatusIds($parentId)
    {
        // Call getOrderStatus() to ensure the static property self::$orderStatus is populated.
        // This leverages the existing caching mechanism.
        $allStatuses = RentMy\RentMy::getOrderStatus();

        // Iterate through the parent-level statuses
        foreach ($allStatuses as $status) {
            // Check if the current status is the parent we are looking for
            if ($status['id'] == $parentId) {
                // If the parent is found, check if it has children
                if (isset($status['child']) && is_array($status['child']) && !empty($status['child'])) {
                    // Use array_column to efficiently extract just the 'id' from the child array
                    return array_column($status['child'], 'id');
                }
                // If the parent is found but has no children, we can stop searching.
                break;
            }
        }

        // Return an empty array if the parent ID was not found or has no children.
        return [];
    }
}
