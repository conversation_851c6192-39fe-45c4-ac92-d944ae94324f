<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * PageTags Model
 *
 * @property \App\Model\Table\PagesTable&\Cake\ORM\Association\BelongsTo $Pages
 * @property \App\Model\Table\TagsTable&\Cake\ORM\Association\BelongsTo $Tags
 *
 * @method \App\Model\Entity\PageTag get($primaryKey, $options = [])
 * @method \App\Model\Entity\PageTag newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\PageTag[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\PageTag|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\PageTag saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\PageTag patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\PageTag[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\PageTag findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class PageTagsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('page_tags');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Pages', [
            'foreignKey' => 'page_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Tags', [
            'foreignKey' => 'tag_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
//        $validator
//            ->integer('id')
//            ->allowEmptyString('id', null, 'create');
//
//        $validator
//            ->integer('status')
//            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules)
    {
//        $rules->add($rules->existsIn(['page_id'], 'Pages'));
//        $rules->add($rules->existsIn(['tag_id'], 'Tags'));

        return $rules;
    }

    public function getTags($id)
    {
        $p_tag = $this->find('all')
            ->select(['id'])
            ->contain(['Tags' => function ($q) {
                return $q->select(['Tags.id', 'Tags.name']);
            }
            ])
            ->where(['PageTags.page_id' => $id])
            ->toArray();
        $data = array();
        foreach ($p_tag as $row) {
            $tag = array();
            $tag['id'] = $row->tag->id;
            $tag['name'] = $row->tag->name;
            $data[] = $tag;
        }
        return $data;
    }
}
