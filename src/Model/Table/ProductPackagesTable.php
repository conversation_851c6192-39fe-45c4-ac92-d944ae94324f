<?php

namespace App\Model\Table;

use App\Lib\CartConfig;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Cake\Validation\Validator;

class ProductPackagesTable extends Table
{
    private $Products, $VariantsProducts, $Quantities;

    /**
     * Initialize method
     */
    public function initialize(array $config)
    {
        parent::initialize($config);
        $this->Products = TableRegistry::get('Products');
        $this->VariantsProducts = TableRegistry::get('VariantsProducts');
        $this->Quantities = TableRegistry::get('Quantities');

        $this->setTable('product_packages');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER'
        ]);
    }

    /**
     * Default validation rules.
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     */
    public function buildRules(RulesChecker $rules)
    {
        $rules->add($rules->existsIn(['product_id'], 'Products'));

        return $rules;
    }

    /**
     * package's products details for default variants combination
     *
     * @param $productIds
     * @param $productPackages
     * @param $location
     * @param int $timestamp
     * @return array
     */
    public function packageDetails_unused($productIds, $productPackages, $location, $timestamp = 360)
    {
        $condPVariants = array();
        $condPVariants = array_merge(array('VariantsProducts.is_last' => 1), $condPVariants);
        $condPVariants = array_merge(array('VariantsProducts.is_default' => 1), $condPVariants);
        $condPVariants = array_merge(array('VariantsProducts.status' => 1), $condPVariants);
        $products = $this->Products->find()
            ->select(['Products.id', 'Products.deposit_amount', 'Products.name', 'Products.description',
                'Products.variant_set', 'Products.driving_license', 'Products.keyword', 'Products.is_tracked'])
            ->contain('VariantsProducts', function ($q) use ($condPVariants, $location) {
                return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                    ->contain('Qty', function ($q) use ($location) {
                        return $q->select(['Qty.id', 'Qty.quantity', 'Qty.available'])
                            ->where(['Qty.location' => $location]);
                    })
                    ->where($condPVariants);
            })
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status', 'Images.variants_products_id']);
            })
            ->where(['Products.id IN' => $productIds])
            ->toArray();
        $data = array();
        foreach ($products as $product) {
            $aData = array();
            $pQuantity = Hash::extract($productPackages, '{n}[product_id=' . $product->id . ']')[0]->quantity;
            $p_variant = '';
            if (!empty($product->variants_product)) {
                $variant = $product->variants_product;
                if ($variant->chain == 1) {
                    $product->variant_set = json_encode([1]);
                }
                $variant_chain = $this->VariantsProducts->getVariantChain(json_decode($product->variant_set, true), $variant->chain);
                $available = 0;
                $quantity = $pQuantity;

                if (!empty($variant)) {
                    if (!empty($variant->qty) && $product->is_tracked == 1) {
                        $variant->qty->quantity = $variant->qty->available;
                    }
                    $pVariant['quantity'] = empty($variant->qty) ? ['quantity' => 0] : $variant->qty;
                    /* Available Quantity */
                    $available = $this->Products->_quantityAvailable($product, $variant->qty);
                }
                $term = (int)($available / $quantity);
                $p_variant = [
                    'id' => $variant->id,
                    'variant_chain' => $variant_chain,
                    'available' => $available,
                    'quantity' => $quantity,
                    'term' => $term,
                ];
            }

            $aData['id'] = $product->id;
            $aData['name'] = $product->name;
            $aData['quantity'] = $pQuantity;
            $aData['images'] = $product->images;
            $aData['variant'] = $p_variant;

            $data[] = $aData;
        }
        return $data;
    }


    public function productDetails($productIds, $productPackages, $location, $timestamp = 360)
    {
        $condPVariants = array();
        $condPVariants = array_merge(array('pVariants.is_last' => 1), $condPVariants);
        $condPVariants = array_merge(array('pVariants.status' => 1), $condPVariants);
        $products = $this->Products->find()
            ->select(['Products.id', 'Products.deposit_amount', 'Products.name', 'Products.description',
                'Products.variant_set', 'Products.driving_license', 'Products.keyword', 'Products.is_tracked'])
            ->contain('pVariants', function ($q) use ($condPVariants, $location) {
                return $q->select(['pVariants.id', 'pVariants.product_id', 'pVariants.chain', 'pVariants.barcode', 'pVariants.price_type', 'is_default', 'sequence_no'])
                    ->contain('Qty', function ($q) use ($location) {
                        return $q->select(['Qty.id', 'Qty.quantity', 'Qty.available'])
                            ->where(['Qty.location' => $location]);
                    })
                    ->order(['sequence_no' => 'ASC', 'is_default'=>'DESC'])
                    ->where($condPVariants);
            })
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status', 'Images.variants_products_id']);
            })
            ->where(['Products.id IN' => $productIds])
            ->toArray();
        $data = array();
        foreach ($products as $product) {
            $aData = array();
            $pQuantity = Hash::extract($productPackages, '{n}[product_id=' . $product->id . ']')[0]->quantity;
            $pMaxQuantity = Hash::extract($productPackages, '{n}[product_id=' . $product->id . ']')[0]->max_quantity;
            $variants = array();
            foreach ($product->p_variants as $variant) {
                if ($variant->chain == 1) {
                    $product->variant_set = json_encode([1]);
                }
                $variant_chain = $this->VariantsProducts->getVariantChain(json_decode($product->variant_set, true), $variant->chain);

                $available = 0;
                //$quantity = $pQuantity;

//                if (!empty($variant)) {
//                    if (!empty($variant->qty) && $product->is_tracked == 1) {
//                        $variant->qty->quantity = $variant->qty->available;
//                    }
//                    $pVariant['quantity'] = empty($variant->qty) ? ['quantity' => 0] : $variant->qty;
//                    /* Available Quantity */
//                    $available = $this->Products->_quantityAvailable($product, $variant->qty);
//                }
                $variants[] = [
                    'id' => $variant->id,
                    'variant_chain' => $variant_chain,
                    'is_default' => $variant->is_default,
//                    'available' => $available,
//                    'quantity' => $quantity
                ];
            }

            $default = array_shift($variants);
            $variantCollection = new Collection($variants);
//            $variants = $variantCollection->sortBy('variant_chain', SORT_ASC, SORT_STRING);
            $variants = $variantCollection;
            $variants = $variants->toArray();
            array_unshift($variants, $default);

            $product->variants = $variants;

            $aData['id'] = $product->id;
            $aData['name'] = $product->name;
            $aData['variant_set'] = $product->variant_set;
            $aData['quantity'] = $pQuantity;
            $aData['max_quantity'] = $pMaxQuantity;
            $aData['images'] = $product->images;
            $aData['variants'] = $product->variants;

            $data[] = $aData;
        }
        return $data;
    }

    /**
     *
     * @deprecated
     * @param $data = variant[], product_id, quantity
     * @param null $timestamp
     * @param null $dateTime
     * @return $variants
     */
    public function getAvailablePackage($productPackages, $variantIds, $location, $dateTime = null)
    {
        $variants = array();
        foreach ($variantIds as $vId) {
            $variant = $this->VariantsProducts->get($vId);
            $product = $this->Products->get($variant->product_id);
            $quantity = $this->Quantities->find()->where(['location' => $location])->where(['variants_products_id' => $vId])->first();
            $pQuantity = Hash::extract($productPackages, '{n}[product_id=' . $variant->product_id . ']')[0]->quantity;

//            if ($variant->chain == 1) {
//                $product->variant_set = json_encode([1]);
//            }
            //$variant_chain = $this->VariantsProducts->getVariantChain(json_decode($product->variant_set, true), $variant->chain);
            $available = 0;

            if (!empty($variant)) {
                if (!empty($quantity) && $product->is_tracked == 1) {
                    $quantity->quantity = $quantity->available;
                }
                /* Available Quantity */
                $available = $this->Products->_quantityAvailable($product, $quantity,  $dateTime);
            }
            if ($variant->chain == 1) {
                $product->variant_set = json_encode([1]);
            }
            RentMy::addModel(['VariantsProducts']);
            $variant_chain = RentMy::$Model['VariantsProducts']->getVariantChain(json_decode($product->variant_set, true), $variant->chain);

            $variants[] = [
                'id' => $variant->id,
                'product_name' => $product->name,
                'variant_chain' => $variant_chain,
                //'available' => $available,
                //'quantity' => $pQuantity,
                'term' => (int)($available / $pQuantity),
            ];
        }

//        $term = Hash::extract($variants, '{n}.term');
//        return min($term);
        array_multisort( array_column($variants, "term"), SORT_ASC, $variants );
        return $variants;
    }

    public function packageDetails($productIds, $productPackages, $location, $timestamp = 360)
    {
        $condPVariants = array();
        $condPVariants = array_merge(array('pVariants.is_last' => 1), $condPVariants);
        $condPVariants = array_merge(array('pVariants.status' => 1), $condPVariants);
        $products = $this->Products->find()
            ->select(['Products.id', 'Products.deposit_amount', 'Products.name', 'Products.description',
                'Products.variant_set', 'Products.driving_license', 'Products.keyword', 'Products.is_tracked'])
            ->contain('pVariants', function ($q) use ($condPVariants, $location) {
                return $q->select(['pVariants.id', 'pVariants.product_id', 'pVariants.chain', 'pVariants.barcode', 'pVariants.price_type'])
                    ->contain('Qty', function ($q) use ($location) {
                        return $q->select(['Qty.id', 'Qty.quantity', 'Qty.available'])
                            ->where(['Qty.location' => $location]);
                    })
                    ->where($condPVariants);
            })
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status', 'Images.variants_products_id']);
            })
            ->where(['Products.id IN' => $productIds])
            ->toArray();
        $data = array();
        foreach ($products as $product) {
            $aData = array();
            $pQuantity = Hash::extract($productPackages, '{n}[product_id=' . $product->id . ']')[0]->quantity;
            $variants = array();
            foreach ($product->p_variants as $variant) {
                if ($variant->chain == 1) {
                    $product->variant_set = json_encode([1]);
                }
                $variant_chain = $this->VariantsProducts->getVariantChain(json_decode($product->variant_set, true), $variant->chain);
                $available = 0;
                $quantity = $pQuantity;

                if (!empty($variant)) {
                    if (!empty($variant->qty) && $product->is_tracked == 1) {
                        $variant->qty->quantity = $variant->qty->available;
                    }
                    $pVariant['quantity'] = empty($variant->qty) ? ['quantity' => 0] : $variant->qty;
                    /* Available Quantity */
                    $available = $this->Products->_quantityAvailable($product, $variant->qty);
                }
                $variants[] = [
                    'id' => $variant->id,
                    'variant_chain' => $variant_chain,
                    'available' => $available,
                    'quantity' => $quantity
                ];
            }
            $product->variants = $variants;

            $aData['id'] = $product->id;
            $aData['name'] = $product->name;
            $aData['quantity'] = $pQuantity;
            $aData['images'] = $product->images;
            $aData['variants'] = $product->variants;

            $data[] = $aData;
        }
        return $data;
    }

}
