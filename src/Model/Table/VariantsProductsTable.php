<?php

namespace App\Model\Table;

use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\Event\Event;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

class VariantsProductsTable extends Table
{

    /**
     * Initialize method
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('variants_products');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
        $this->addBehavior('Tree');

        $this->belongsTo('Variants', [
            'foreignKey' => 'variant_id',
            'joinType' => 'INNER'
        ]);
        $this->belongsTo('VariantSets', [
            'foreignKey' => 'set_id',
            'joinType' => 'INNER'
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasOne('BasePrice', [
            'className' => 'ProductPrices',
            'foreignKey' => 'variants_products_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasOne('CategoriesProducts', [
            'className' => 'CategoriesProducts',
            'foreignKey' => 'product_id',
            'bindingKey' => 'product_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasOne('ProductsTags', [
            'className' => 'ProductsTags',
            'foreignKey' => 'product_id',
            'bindingKey' => 'product_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasOne('Images', [
            'className' => 'Images',
            'foreignKey' => 'variants_products_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasOne('Qty', [
            'className' => 'Quantities',
            'foreignKey' => 'variants_products_id'
        ]);

        $this->belongsTo('DefaultProduct', [
            'className' => 'Products',
            'foreignKey' => 'product_id',
            'joinType' => 'INNER'
        ])->setConditions(['is_default' => 1]);

        $this->hasOne('ProductsSuppliers', [
            'className' => 'ProductsSuppliers',
            'foreignKey' => 'product_id',
            'bindingKey' => 'product_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasMany('ProductsAvailabilities', [
            'foreignKey' => 'variants_products_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->hasMany('ProductPrices', [
            'foreignKey' => 'variants_products_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->hasOne('CartItem', [
            'className' => 'CartItems',
            'foreignKey' => 'variants_products_id'
        ]);
    }

//    /**
//     * Delete product details when any attribute products combination deletes
//     * @param $event
//     */
//    function afterDelete(Event $event)
//    {
//        $entityData = $event->data['entity'];
//        $id = $entityData->id;
//        if ($entityData->is_last == 1) {
//            $productDetailsObj = TableRegistry::get('ProductsDetails');
//            if (!empty($id)) {
//                $pDetails = $productDetailsObj->find()->where(['at_p_id' => $id])->first();
//                if (!empty($pDetails)) {
//                    $productDetailsObj->delete($pDetails);
//                }
//            }
//        }
//
//    }
//
//    /**
//     * Update product details when attribute products are saved .
//     * @param Event $event
//     */
//    function afterSave(Event $event)
//    {
//        $productDetailsObj = TableRegistry::get('ProductsDetails');
//        $productDetailsObj->updateDetails($event);
//    }


    /**
     * Default validation rules.
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');

        return $validator;
    }

    /*
     * get all Attributes
     */
    public function getAttributeList($id)
    {
        $variants = $this->find('all')
            ->select(['VariantsProducts.variant_id'])
            ->where(['VariantsProducts.product_id' => $id, 'status' => 1])
            ->toArray();
        if (empty($variants)) {
            return [];
        }
        $ids = array();
        foreach ($variants as $row) {
            $ids[] = $row->variant_id;
        }
        $options     = array('Variants.id IN' => $ids);
        $variantSets = TableRegistry::get('VariantSets');
        $variants    = $variantSets->find('all')
            ->select(['VariantSets.id', 'VariantSets.store_id', 'VariantSets.name'])
            ->contain(['Variants' => function ($q) use ($options) {
                return $q->select(['Variants.id', 'Variants.variant_set_id', 'Variants.name'])
                    ->where($options);
            }])
            ->toArray();
        $data        = array();
        foreach ($variants as $row) {
            if (!empty($row->variants)) {
                $data[] = $row;
            }
        }
        return $data;
    }

    public function saveAttribute($data, $productid)
    {
        $pdata     = array();
        $attribute = $this->find()
            ->where(['product_id' => $productid, 'variant_id' => $data])
            ->first();
        if (empty($attribute)) {
            $attribute           = $this->newEntity();
            $pdata['product_id'] = $productid;
            $pdata['variant_id'] = $data;
            $attribute           = $this->patchEntity($attribute, $pdata);
            $this->save($attribute);
        }
        return;
    }

    /*
    * get attribute chain
    */
    public function getAttributeChain($id)
    {
        RentMy::addModel(['ProductPrices']);
        $data       = array();
        $aData      = array();
        $productObj = TableRegistry::get('Products');
        $product    = $productObj->get($id);
        if (empty($product->variant_set)) {
            return $data[] = $aData;
        }


        $variantSets = json_decode($product->variant_set); //3,1,4
        // start new changes
        $variantProducts = $this->find()
            ->contain(['Variants', 'VariantSets'])
            ->where([
                'VariantsProducts.product_id' => $id,
                'VariantsProducts.status' => 1
            ])
            ->order(['VariantsProducts.sequence_no' => 'ASC']) // or DESC as needed
            ->groupBy('chain')
            ->toArray();

        $i = 0;
        foreach ($variantProducts as $variantProduct) {
            if ($variantSets[0] != $variantProduct[0]['variant_set']['id']) {
                continue;
            }
            foreach ($variantProduct as $j => $attr) {
                $prices = RentMy::$Model['ProductPrices']->find()->where([
                    'variants_products_id' => $attr['id'],
                    'location' => RentMy::$token->location,
                    'current' => 1
                ])->first();
                $aData[$i]['variant_set'][$j] = array('variant_set_id' => $attr['variant_set']['id'],
                    'id' => $attr['variant']['id'],
                    'name' => $attr['variant']['name'],
                    'variant_set_name' => $attr['variant_set']['name']);
                $aData[$i]['ids'][]           = $attr['id'];
                if ($attr['is_last'] == 1) {
                    $getQuantity                = $this->Qty->getQuantity($attr['id']);
                    $aData[$i]['barcode']       = $attr['barcode'];
                    $aData[$i]['bin_location']  = $getQuantity[0]['bin_location'] ?? '';
                    $aData[$i]['weight_unit']   = $attr['weight_unit'];
                    $aData[$i]['weight_amount'] = $attr['weight_amount'];
                    $aData[$i]['location']      = $getQuantity;
                    $aData[$i]['default']       = $attr['is_default'];
                    $aData[$i]['price_type']    = $prices->price_type ?? 3;
                    $height                     = '';
                    $width                      = '';
                    $length                     = '';
                    if (!empty($attr['volume'])) {
                        $volume = explode('x', $attr['volume']);
                        $height = $volume[0];
                        $width  = $volume[1];
                        $length = $volume[2];
                    }
                    $aData[$i]['height'] = $height;
                    $aData[$i]['width']  = $width;
                    $aData[$i]['length'] = $length;
                    $aData[$i]['sequence_no'] = $attr['sequence_no'] ?? 1;

                }
            }
            $i++;
        }
        // end new changes
//        foreach ($variantSets as $i => $val) {
//            $productAttributes = $this->find()
//                ->where(['product_id' => $id, 'set_id' => $val])
//                ->contain(['Variants', 'VariantSets'])
//                ->toArray();
//            //   RentMy::dbg($productAttributes);
//            foreach ($productAttributes as $j => $productAttribute) {
//                $aData[$j]['variant_set'][$i] = array('variant_set_id' => $productAttribute['variant_set']['id'],
//                    'id' => $productAttribute['variant']['id'],
//                    'name' => $productAttribute['variant']['name'],
//                    'variant_set_name' => $productAttribute['variant_set']['name']);
//                $aData[$j]['ids'][] = $productAttribute['id'];
//                if ((count($variantSets) - 1) == $i) {
//                    $getQuantity = $this->Qty->getQuantity($productAttribute['id']);
//                    $aData[$j]['barcode'] = $productAttribute['barcode'];
//                    $aData[$j]['bin_location'] = $getQuantity[0]['bin_location'] ?? '';
//                    $aData[$j]['weight_unit'] = $productAttribute['weight_unit'];
//                    $aData[$j]['weight_amount'] = $productAttribute['weight_amount'];
//                    $aData[$j]['location'] = $getQuantity;
//                    $aData[$j]['default'] = $productAttribute['is_default'];
//                    $aData[$j]['price_type'] = $productAttribute['price_type'];
//                    $height = '';
//                    $width = '';
//                    $length = '';
//                    if (!empty($productAttribute['volume'])) {
//                        $volume = explode('x', $productAttribute['volume']);
//                        $height = $volume[0];
//                        $width = $volume[1];
//                        $length = $volume[2];
//                    }
//                    $aData[$j]['height'] = $height;
//                    $aData[$j]['width'] = $width;
//                    $aData[$j]['length'] = $length;
//
//                }
//            }
//        }
        return $data[] = $aData;
    }

    public function _productWeight($pId)
    {
        $product = TableRegistry::get('Products')->get($pId);
        if ($product->is_default_weight) {
            $unassign = $this->find()
                ->where(['is_last' => 1])
                ->where(['product_id' => $pId, 'set_id' => 1])
                ->first();
            if ($unassign) {
                $v_ps = $this->find()
                    ->where(['VariantsProducts.set_id !=' => 1])
                    ->where(['VariantsProducts.is_last' => 1])
                    ->where(['VariantsProducts.product_id' => $pId])
                    ->toArray();
                foreach ($v_ps as $v_p):
                    $v_p['weight_amount'] = empty($unassign['weight_amount']) ? 0 : $unassign['weight_amount'];
                    $v_p['weight_unit']   = empty($unassign['weight_unit']) ? '' : $unassign['weight_unit'];
                    $this->save($v_p);
                endforeach;
            }
        }
        return;
    }

    /*
     * Add
     */
    public function add($id, $data)
    {
        $parentId      = 0;
        $barcode       = 0;
        $weight_amount = 0;
        $weight_unit   = '';
        $chain         = array();
        $last          = false;
        for ($i = 0; $i < count($data['variant_id']); $i++) {
            $attribute = $data['variant_id'][$i];
            $chain[]   = $attribute;
        }

        $attr = $this->find()
            ->where(['chain' => implode('-', $chain)])
            ->where(['product_id' => $id])
            ->first();
        if (empty($attr)) {

            //if empty variant chain then do default by force
            $variantsChains = $this->getAttributeChain($id);
            if (count($variantsChains) == 0)
                $data['default'] = true;

            for ($i = 0; $i < count($data['variant_id']); $i++) {
                $attribute = $data['variant_id'][$i];
                $set       = $data['set_id'][$i];
                if ($i == count($data['variant_id']) - 1) {
                    $barcode       = $data['barcode'];
                    $weight_amount = empty($data['weight_amount']) ? 0 : $data['weight_amount'];
                    $weight_unit   = empty($data['weight_unit']) ? '' : $data['weight_unit'];
                    $last          = 1;
                }
                $aData                  = array();
                $aData['product_id']    = $id;
                $aData['variant_id']    = $attribute;
                $aData['store_id']      = $data['store_id'];
                $aData['set_id']        = $set;
                $aData['parent_id']     = $parentId;
                $aData['barcode']       = $barcode;
                $aData['weight_amount'] = $weight_amount;
                $aData['weight_unit']   = $weight_unit;
                $aData['is_last']       = $last;
                $aData['chain']         = implode('-', $chain);

                if (!empty($data['height']) && !empty($data['width']) && !empty($data['length'])) {
                    $aData['volume'] = $data['height'] . 'x' . $data['width'] . 'x' . $data['length'];
                }

                $attribute = $this->newEntity();
                $attribute = $this->patchEntity($attribute, $aData);
                if (!$this->save($attribute)) {
                    return false;
                }
                if ($last) {
                    // default in product table
                    $productObj = TableRegistry::get('Products');
                    $product    = $productObj->get($id);
                    if (empty($product->variants_products_id)) {
                        $product->variants_products_id = $attribute->id;
                        $productObj->save($product);
                    } else if (!empty($data['default'])) {
                        $product->variants_products_id = $attribute->id;
                        $productObj->save($product);
                    }
                    // default in attribute product table
                    $products = $this->find()->where(['product_id' => $id])->where(['is_last' => 1])->toArray();
                    if (count($products) > 1) {
                        $this->_productWeight($id);
                    }
                    if (count($products) == 1) {
                        $checkUnassign = $this->find()
                            ->where(['product_id' => $id, 'set_id' => 1])
                            ->first();
                        if (!empty($checkUnassign)) {
                            $checkUnassign['is_default'] = 1;
                            $checkUnassign['status']     = 1;
                            $this->save($checkUnassign);
                        }
                    } else if (count($products) == 2) {
                        $check = $this->find()
                            ->where(['is_last' => 1])
                            ->where(['product_id' => $id, 'set_id !=' => 1])
                            ->first();
                        if (!empty($check)) {
                            $check['is_default'] = 1;
                            $this->save($check);
                        }
                        $checkUnassign = $this->find()
                            ->where(['is_default' => 1])
                            ->where(['product_id' => $id, 'set_id' => 1])
                            ->first();
                        if (!empty($checkUnassign)) {
                            $checkUnassign['is_default'] = 0;
                            $checkUnassign['status']     = 0;
                            $this->save($checkUnassign);
                        }
                        $unassign = $this->find()
                            ->where(['product_id' => $id, 'set_id' => 1])
                            ->first();
                        $this->addPriceImage($unassign['id'], $check);
                    } else if (!empty($data['default'])) {
                        $check = $this->find()
                            ->where(['product_id' => $id, 'set_id !=' => 1])
                            ->first();
                        if (!empty($check)) {
                            $check['is_default'] = 0;
                            $this->save($check);
                        }
                        $aP               = $this->get($attribute->id);
                        $aP['is_default'] = 1;
                        $this->save($aP);
                    }
                }
                $parentId = $attribute->id;
            }
            // Insert to Quantity Table
            $data['variants_products_id'] = $attribute->id;
            if (!empty($data['location'])) {
                if (!$this->Qty->add($data)) {
                    return false;
                }
            }
            return $data['variants_products_id'];
//            return true;
        }
        return false;
    }

    /**
     * This function needs to be modified
     * @param $a_set
     * @param $chain
     * @param string $implodeDelimeter
     * @return array|string
     */
    public function getVariantChain($a_set, $chain, $implodeDelimeter = ', ', $options = [])
    {
        $setName = array();
        if (!empty($options['custom_builder'])) {
            foreach ($a_set as $a) {
                $setName[] = $this->VariantSets->get($a)->name;
            }

            $attrName = array();
            $a_chain  = explode('-', $chain);
            foreach ($a_chain as $v) {
                $attrName[] = $this->Variants->get($v)->name;
            }
            $chainString = array();

            for ($i = 0; $i < count($setName); $i++) {
                $chainString[] =  $attrName[$i];
                $chainStringWitSet[] = $setName[$i] ;

            }
            $attrName = implode($implodeDelimeter, $chainString);
            return ['attr_name'=> $attrName,'attr'=> $chainString,'attr_set'=> $chainStringWitSet];
        } else {
            foreach ($a_set as $a) {
                $setName[] = $this->VariantSets->get($a)->name;
            }

            $attrName = array();
            $a_chain  = explode('-', $chain);
            foreach ($a_chain as $v) {
                $attrName[] = $this->Variants->get($v)->name;
            }
            $chainString = array();

            for ($i = 0; $i < count($setName); $i++) {
                $chainString[] = $setName[$i] . ': ' . $attrName[$i];
            }
            $attrName = implode($implodeDelimeter, $chainString);
            return $attrName;
        }

    }

    /*
         * Edit
         */
    public function edit($data)
    {
        $barcode  = 0;
        $quantity = 0;
        $location = 0;
        $chain    = array();
        $last     = false;
        for ($i = 0; $i < count($data['variant_id']); $i++) {
            $attribute = $data['variant_id'][$i];
            $chain[]   = $attribute;
        }
        $attr = $this->find()
            ->where(['chain' => implode('-', $chain)])
            ->where(['id NOT IN' => $data['ids']])
            ->where(['product_id' => $data['product_id']])
            ->first();
        if (empty($attr)) {
            for ($i = 0; $i < count($data['ids']); $i++) {
                $attr_p               = $this->get($data['ids'][$i]);
                $attr_p['set_id']     = $data['set_id'][$i];
                $attr_p['variant_id'] = $data['variant_id'][$i];
                $attr_p['chain']      = implode('-', $chain);
                if ($i == count($data['ids']) - 1) {
                    $last = 1;
                    if (!empty($data['weight_amount'])) {
                        $attr_p['weight_amount'] = $data['weight_amount'];
                    }
                    if (!empty($data['weight_unit'])) {
                        $attr_p['weight_unit'] = $data['weight_unit'];
                    }
                    if (!empty($data['height']) && !empty($data['width']) && !empty($data['length'])) {
                        $attr_p['volume'] = $data['height'] . 'x' . $data['width'] . 'x' . $data['length'];
                    }
                    if (isset($data['barcode'])) {
                        $attr_p['barcode'] = $data['barcode'];
                    }
                    $attr_p['is_last'] = $last;
                }
                if (!$this->save($attr_p)) {
                    return false;
                }
                if ($last) {
                    $productObj = TableRegistry::get('Products');
                    $product    = $productObj->get($attr_p->product_id);
                    if (empty($product->variants_products_id)) {
                        $product->variants_products_id = $attr_p->id;
                        $productObj->save($product);
                    } else if (!empty($data['default'])) {
                        $product->variants_products_id = $attr_p->id;
                        $productObj->save($product);
                    }
                    // default in attribute product table
                    $check = $this->find()
                        ->where(['is_default' => 1])
                        ->where(['product_id' => $attr_p->product_id, 'set_id !=' => 1])
                        ->first();
                    if (empty($check)) {
                        $aP               = $this->get($attr_p->id);
                        $aP['is_default'] = 1;
                        $this->save($aP);
                    } else if (!empty($data['default'])) {
                        $check['is_default'] = 0;
                        $this->save($check);
                        $aP               = $this->get($attr_p->id);
                        $aP['is_default'] = 1;
                        $this->save($aP);
                    }
                    $change_status = $this->find()->where(['product_id' => $attr_p->product_id])->toArray();
                    if (count($change_status) > 1) {
                        $find_unassign         = $this->find()->where(['product_id' => $attr_p->product_id, 'set_id' => 1])->first();
                        if(!empty($find_unassign)) {
                            $find_unassign->status = 0;
                            $this->save($find_unassign);
                        }
                    }
                }
            }
            $data['variants_products_id'] = $attr_p->id;

            if (!$this->Qty->edit($data)) {
                return false;
            }
            return $data['variants_products_id'];
        }
        return false;
    }

    public function import($id, $data)
    {
        $parentId = 0;
        $barcode  = 0;
        $chain    = array();
        $last     = false;
        for ($i = 0; $i < count($data['variant_id']); $i++) {
            $attribute = $data['variant_id'][$i];
            $chain[]   = $attribute;
        }

        $attr = $this->find()
            ->where(['chain' => implode('-', $chain)])
            ->where(['product_id' => $id])
            ->first();
        if (empty($attr)) {
            for ($i = 0; $i < count($data['variant_id']); $i++) {
                $attribute = $data['variant_id'][$i];
                $set       = $data['set_id'][$i];
                if ($i == count($data['variant_id']) - 1) {
                    $barcode = $data['barcode'];
                    $last    = 1;
                }
                $aData               = array();
                $aData['product_id'] = $id;
                $aData['variant_id'] = $attribute;
                $aData['store_id']   = $data['store_id'];
                $aData['set_id']     = $set;
                $aData['parent_id']  = $parentId;
                $aData['barcode']    = $barcode;
                $aData['is_last']    = $last;
                $aData['chain']      = implode('-', $chain);

                $attribute = $this->newEntity();
                $attribute = $this->patchEntity($attribute, $aData);
                if (!$this->save($attribute)) {
                    return false;
                }
                if ($last) {
                    // default in product table
                    $productObj = TableRegistry::get('Products');
                    $product    = $productObj->get($id);
                    if (empty($product->variants_products_id)) {
                        $product->variants_products_id = $attribute->id;
                        $productObj->save($product);
                    } else if (!empty($data['default'])) {
                        $product->variants_products_id = $attribute->id;
                        $productObj->save($product);
                    }
                    // default in attribute product table
                    $products = $this->find()->where(['product_id' => $id])->where(['is_last' => 1])->toArray();
                    if (count($products) == 1) {
                        $checkUnassign = $this->find()
                            ->where(['product_id' => $id, 'set_id' => 1])
                            ->first();
                        if (!empty($checkUnassign)) {
                            $checkUnassign['is_default'] = 1;
                            $checkUnassign['status']     = 1;
                            $this->save($checkUnassign);
                        }
                    } else if (count($products) == 2) {
                        $check = $this->find()
                            ->where(['is_last' => 1])
                            ->where(['product_id' => $id, 'set_id !=' => 1])
                            ->first();
                        if (!empty($check)) {
                            $check['is_default'] = 1;
                            $this->save($check);
                        }
                        $checkUnassign = $this->find()
                            ->where(['is_default' => 1])
                            ->where(['product_id' => $id, 'set_id' => 1])
                            ->first();
                        if (!empty($checkUnassign)) {
                            $checkUnassign['is_default'] = 0;
                            $checkUnassign['status']     = 0;
                            $this->save($checkUnassign);
                        }
                        $unassign = $this->find()
                            ->where(['product_id' => $id, 'set_id' => 1])
                            ->first();
                        $this->addPriceImage($unassign['id'], $check);
                    } else if (!empty($data['default'])) {
                        $check = $this->find()
                            ->where(['product_id' => $id, 'set_id !=' => 1])
                            ->first();
                        if (!empty($check)) {
                            $check['is_default'] = 1;
                            $this->save($check);
                        }
                        $aP               = $this->get($attribute->id);
                        $aP['is_default'] = 1;
                        $this->save($aP);
                    }
                    $products             = $this->find()->where(['product_id' => $id])->where(['is_last' => 1])->last();
                    $products->is_default = 1;
                    $this->save($products);
                }
                $parentId = $attribute->id;
            }
            // Insert to Quantity Table
            $data['variants_products_id'] = $attribute->id;
            return true;
        }
        return false;
    }

    /*add images & price after first unassigned product*/
    private function addPriceImage($unassign_id, $second_pro)
    {
        $imgObj   = TableRegistry::get('Images');
        $imgs     = $imgObj->find()->where(['variants_products_id' => $unassign_id])->toArray();
        $priceObj = TableRegistry::get('Prices');
        $prices   = $priceObj->find()->where(['variants_products_id' => $unassign_id])->toArray();
        foreach ($prices as $price_data) {
            $newPrice                      = $priceObj->newEntity();
            $price                         = json_decode(json_encode($price_data), True);
            $price['variants_products_id'] = $second_pro['id'];
            unset($price['id']);
            $newPrice = $priceObj->patchEntity($newPrice, $price);
            $priceObj->save($newPrice);
        }

        foreach ($imgs as $img_data) {
            $newImg                      = $imgObj->newEntity();
            $img                         = json_decode(json_encode($img_data), True);
            $img['variants_products_id'] = $second_pro['id'];
            unset($img['id']);
            $newImg = $imgObj->patchEntity($newImg, $img);
            $imgObj->save($newImg);
        }
    }


    /*
     * @deprecated
     * Get all products Quantity by attribute and location
     */
    public function VariantsProductsList($conditions, $pageNo, $limit, $last_product_id)
    {
        $offset = ($pageNo - 1) * $limit;

        if (!empty($last_product_id)) {
            $lastProduct = $this->find()
                ->select(['VariantsProducts.id', 'VariantsProducts.chain', 'VariantsProducts.product_id', 'VariantsProducts.barcode',
                    'VariantsProducts.cost', 'Products.name', 'Products.variant_set', 'Supplier.name'
                ])
                ->contain('Products', function ($q) {
                    return $q->contain('Supplier');
                })
                ->where($conditions)
                ->where(['VariantsProducts.product_id' => $last_product_id])
                ->toArray();
        }
        $VariantsProducts = $this->find()
            ->select(['VariantsProducts.id', 'VariantsProducts.chain', 'VariantsProducts.product_id', 'VariantsProducts.barcode',
                'VariantsProducts.cost', 'VariantsProducts.purchase_date', 'Products.name', 'Products.variant_set', 'Supplier.name'])
            ->contain('Products', function ($q) {
                return $q->contain('Supplier');
            })
            ->where($conditions)
            ->order(['VariantsProducts.product_id' => 'ASC'])
            ->offset($offset)
            ->limit($limit)
            ->toArray();
        //pr($VariantsProducts);exit;
        $total = $this->find()
            ->contain(['Products'])
            ->where($conditions)
            ->count();

        if (!empty($last_product_id)) {
            array_shift($VariantsProducts);
            $VariantsProducts = array_merge($lastProduct, $VariantsProducts);
        }

        $groupByProduct  = array();
        $distinctProduct = array();
        foreach ($VariantsProducts as $variantsProduct) {
            if (!in_array($variantsProduct->product_id, $distinctProduct)) {
                $distinctProduct[] = $variantsProduct->product_id;
            }
            $groupByProduct[$variantsProduct->product_id][] = $variantsProduct;
        }
        if ($total > ($pageNo * $limit)) {
            $lastProductId = empty($distinctProduct) == true ? 0 : $distinctProduct[count($distinctProduct) - 1];
            array_pop($groupByProduct);
        } else {
            $lastProductId = null;
        }
        $variantsProductList = array();

        foreach ($groupByProduct as $key => $a_p) {
            $aData = array();
            foreach ($a_p as $p) {
                $aData[$p->chain][] = $p;
            }
            $variantsProductList[] = $aData;
        }

        $data = array(
            'variantsProductList' => $variantsProductList,
            'last_product_id' => $lastProductId,
            'total' => $total,
        );
        return $data;
    }


    /**
     * @param $conditions
     * @param $pageNo
     * @param $limit
     * @param $last_product_id
     * @return array
     * @deprecated
     */
    public function VariantsProductsList_old($conditions, $pageNo, $limit, $last_product_id)
    {
        $offset = ($pageNo - 1) * $limit;

        if (!empty($last_product_id)) {
            $lastProduct = $this->find()
                ->select(['VariantsProducts.id', 'VariantsProducts.chain', 'VariantsProducts.product_id', 'VariantsProducts.barcode',
                    'VariantsProducts.cost', 'Products.name', 'Products.variant_set', 'Supplier.name'])
                ->contain('Products', function ($q) {
                    return $q->contain('Supplier');
                })
                ->where($conditions)
                ->where(['VariantsProducts.product_id' => $last_product_id])
                //->order(['VariantsProducts.product_id' => 'ASC'])
                ->toArray();
        }
        $VariantsProducts = $this->find()
            ->select(['VariantsProducts.id', 'VariantsProducts.chain', 'VariantsProducts.product_id', 'VariantsProducts.barcode',
                'VariantsProducts.cost', 'VariantsProducts.purchase_date', 'Products.name', 'Products.variant_set', 'Supplier.name'])
            ->contain('Products', function ($q) {
                return $q->contain('Supplier');
            })
            ->where($conditions)
            ->order(['VariantsProducts.product_id' => 'ASC'])
            ->offset($offset)
            ->limit($limit)
            ->toArray();

        $total = $this->find()
            ->contain('Products', function ($q) {
                return $q->contain('Supplier');
            })
            ->where($conditions)
            ->order(['VariantsProducts.product_id' => 'ASC'])
            ->count();

        if (!empty($last_product_id)) {
            array_shift($VariantsProducts);
            $VariantsProducts = array_merge($lastProduct, $VariantsProducts);
        }

        $groupByProduct  = array();
        $distinctProduct = array();
        foreach ($VariantsProducts as $variantsProduct) {
            if (!in_array($variantsProduct->product_id, $distinctProduct)) {
                $distinctProduct[] = $variantsProduct->product_id;
            }
            $groupByProduct[$variantsProduct->product_id][] = $variantsProduct;
        }
        if ($total > ($pageNo * $limit)) {
            $lastProductId = empty($distinctProduct) == true ? 0 : $distinctProduct[count($distinctProduct) - 1];
            array_pop($groupByProduct);
        } else {
            $lastProductId = null;
        }
        $variantsProductList = array();
        foreach ($groupByProduct as $key => $a_p) {
            $aData = array();
            foreach ($a_p as $p) {
                $chain = explode('-', $p->chain);
                array_shift($chain);                     // $location = $chain[0];
                $a_chain           = implode('-', $chain);
                $aData[$a_chain][] = $p;
            }
            $variantsProductList[] = $aData;
        }
        $data = array(
            'variantsProductList' => $variantsProductList,
            'last_product_id' => $lastProductId,
            'total' => $total,
        );
        return $data;
    }

    /**
     * This function used to get the list of
     * all variants and combinations for products
     * @param array $productIDs
     * @return array
     */
    function productVariantList($productIDs = [])
    {

        if (!empty($productIDs)) {
            RentMy::addModel(['Variants']);
            $variants = RentMy::$Model['Variants']->getAll();
            //   pr($variants);
            $variantProducts = $this->find()
                ->where(['product_id IN' => $productIDs, 'status' => 1, 'is_last' => 1])
                ->groupBy('product_id')
                ->map(function ($items) use ($variants) {
                    $chainCount = count(explode('-', $items[0]->chain));
                    $products   = [];
                    foreach ($items as $variantProduct) {
                        $exp = explode('-', $variantProduct->chain);
                        for ($i = 0; $i <= $chainCount; $i++) {
                            foreach ($exp as $j => $e) {
                                if ($i == $j) {
                                    $products[$i][] = $e;
                                }
                            }
                        }

                    }
                    foreach ($products as $i => $product) {
                        $products[$i] = array_unique($product);
                    }
                    foreach ($products as $i => $product) {
                        foreach ($product as $j => $p) {
                            if ($p == 1) {
                                $vProducts[$i] = ['id' => $p, 'name' => 'Unassigned'];
                            } else {
                                if (!empty($variants[$p]))
                                    $vProducts[$i][] = ['id' => $p, 'name' => $variants[$p]];
                            }

                        }
                    }
                    return $vProducts;
                })
                ->toArray();
            // RentMy::dbg($variantProducts);
            // exit();
            return $variantProducts;

        }
    }

    /**
     * Delete products variant
     *  // Step 1: fetch deleted variants products id , check if it is default variant
     * // Step 2: count any existing variants products with status = 1 , is last =1 except this variant products id
     * // Step 3: if count > 0 && deleted variants_product_id.is_default ==0, remove this & remove image , price, quantity
     * // Step 4 : if count > 0 && delete variants_product_id.is_default ===1 , remove this & set  any other variant as default
     * // set products.variants_products_id = new default variants id & remove image, price , quantity
     * // Step 5 : if count <=0, update status = 1 of unassigned variants .  remove this &  set products.variants_products_id = new default variants id
     * // & remove image, price , quantity
     * @param $product_id
     * @param $variants_products_id
     * @return bool
     */
    public function removeProductVariant($product_id, $variants_products_id)
    {
        RentMy::addModel(['Products', 'Images', 'Prices', 'ProductPrices', 'Quantities']);
        $variantProduct = $this->find()->where(['id' => $variants_products_id])->first();
        $product        = RentMy::$Model['Products']->find()->where(['id' => $product_id])->first();
        if (empty($variantProduct) || empty($product)) {
            return false;
        }
        $checkActiveVariantsCount = $this->find()
            ->where(['product_id' => $product_id, 'status' => 1, 'is_last' => 1, 'id !=' => $variants_products_id])->count();

        $removeVariantProducts = false;
        $setDefaultAsOthers    = false;
        $updateProductVariant  = false;
        if ($checkActiveVariantsCount > 0 && $variantProduct->is_default == 0) { // step 3
            $removeVariantProducts = true;
        } elseif ($checkActiveVariantsCount > 0 && $variantProduct->is_default == 1) {// step 4
            $removeVariantProducts = true;
            $setDefaultAsOthers    = true;
            $updateProductVariant  = true;
        } elseif ($checkActiveVariantsCount <= 0) {  //step 5

            $removeVariantProducts       = true;
            $updateProductVariant        = true;
            $findFirst                   = $this->find()
                ->where(['product_id' => $product_id, 'id !=' => $variants_products_id, 'is_last' => 1, 'status' => 0])
                ->order(['id' => 'ASC'])
                ->first();
            if(!empty($findFirst )) {
                $update_variants_products_id = $findFirst['id'];
                $findFirst->status           = 1;
                $findFirst->is_default       = 1;
                $this->save($findFirst);
            }
        }

        if ($setDefaultAsOthers) { // add another variant as default
            $findFirst                   = $this->find()
                ->where(['product_id' => $product_id, 'id !=' => $variants_products_id, 'is_last' => 1, 'status' => 1])
                ->order(['id' => 'ASC'])
                ->first();
            $update_variants_products_id = $findFirst['id'];
            $findFirst->is_default       = 1;
            $this->save($findFirst);
        }


        if ($updateProductVariant) { // update products table default variant product id
            if (!empty($update_variants_products_id)) {
                $product->variants_products_id = $update_variants_products_id;
                RentMy::$Model['Products']->save($product);
            }

        }
        if ($removeVariantProducts) { // remove variants products entity , image, price , quantity
            RentMy::$Model['Quantities']->deleteAll(['variants_products_id' => $variants_products_id]); // delete quantities of this variant
            RentMy::$Model['ProductPrices']->deleteAll(['variants_products_id' => $variants_products_id]);  // delete pricing of these variant
            RentMy::$Model['Images']->variantImageDelete($variants_products_id);  // delete image of this variant
        }

        if ($this->deleteAll(['chain' => $variantProduct['chain'], 'product_id' => $product_id])) {
            //if ($this->delete($variantProduct)) {
            return true;
        }

    }
}
