<?php

namespace App\Model\Table;

use App\Lib\RentMy\RentMy;
use Cake\Core\Configure;
use Cake\ORM\Table;

class OrderNotesTable extends Table
{

    /**
     * Initialize method
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('order_notes');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Users', [
            'foreignKey' => 'user_id'
        ]);

    }

    /**
     * This function is used to add order nodes & and all order logs
     * @param $type
     * @param array $options
     */
    public function addOrderLog($type, $options = [])
    {
        $logMessage = [
            'order_create_with_user' => '{0} created {1}.',
            'order_create_with_guest' => '{0} created.',
            'change_status_auto' => 'Status automatically  changed to {0}',
            'change_status' => '{0} manually set status {1}',
            'Payments' => '{1} payment {2}: {3}', //[POS] Stripe payment received: $50.00 / [POS] Stripe payment captured: $50.00 / [POS] Stripe payment authorized: $50.00
            'AcceptQuote' => '{0} accepted quote',

        ];
        $noteData = [
            'store_id' => $options['store_id'],
            'order_id' => $options['order_id'],
            'user_id' => $options['user_id'] ?? '',
            'content_type' => $type,
            'content_id' => $options['content_id'],
            'source' => $options['source']??''
        ];
        $note = $name = '';
        // get user name
        RentMy::addModel(['Users', 'Customers']);
        if (!empty($options['user_id'])) {
            if ((!empty($options['user_type'])) && ($options['user_type'] == 'customer')) {
                $user = RentMy::$Model['Customers']->find()->where(['id' => $options['user_id'], 'store_id' => $options['store_id']])->first();
                $name = $user['first_name'] . ' ' . $user['last_name'];
            } else {
                $user = RentMy::$Model['Users']->find()->where(['id' => $options['user_id']])->first();
                $name = $user['first_name'] . ' ' . $user['last_name'];
            }
            $options['name'] = $name;
        }


        switch ($type) {
            case 'Create':
                RentMy::addModel(['Orders', 'Users', 'Customers']);
                $order = RentMy::$Model['Orders']->find()->where(['id' => $options['order_id']])->first();
                if (!empty($options['user_id'])) {
                    $note = str_replace('{0}', $name, $logMessage['order_create_with_user']);
                    $note = str_replace('{1}', ($order['type'] == 1) ? 'Order' : 'Quote', $note);
                } else {
                    $note = str_replace('{0}', ($order['type'] == 1) ? 'Order' : 'Quote', $logMessage['order_create_with_guest']);
                }
                $noteData['options'] = ['user_type' => ($options['user_type']) ?? '', 'order_type' => $order->type, 'status' => $order->status];
                break;
            case 'Payments':
                $options['name'] = $name;
                $note = self::logOrderPayments($logMessage, $options);
                break;
            case 'Refund':
                $notes = self::refund($options);
                break;
            case 'RefundCanceled':
                $notes = self::refundCanceled($options);
                break;
            case 'AcceptQuote':
                $note = self::acceptQuote($logMessage, $options);
                break;
            case 'ChangeStatus':
                RentMy::addModel(['Orders', 'Customers', 'Users']);
                $order = RentMy::$Model['Orders']->find()->where(['id' => $options['order_id']])->first();
                foreach (RentMy::getOrderStatus() as $childs) {
                    $orderStatus[$childs['id']] = $childs['label'];
                    if (!empty($childs['child'])) {
                        ;
                        foreach ($childs['child'] as $child) {
                            $orderStatus[$child['id']] = $child['label'];
                        }
                    }
                }

                $options['auto'] = $options['auto'] ?? false;
                if ($options['auto']) { // auto status change log
                    $note = str_replace('{0}', $orderStatus[$order->status], $logMessage['change_status_auto']);
                } else { // manual
                    $note = str_replace('{1}', $orderStatus[$order->status], $logMessage['change_status']);
                    $note = str_replace('{0}', $name, $note);
                }
                break;
            case 'AddItem':
                if (empty($options['is_exchange']))
                    $note = self::products('add', $options);
                break;
            case 'UpdateItem':
                $note = self::products('update', $options);
                break;
            case 'DeleteItem':
                $note = self::products('delete', $options);
                break;
            case 'AddDiscount':
                break;
            case 'AddCoupon':
                break;
            case 'AssignAsset':
                $note = self::assignAsset($options);
                break;
            case 'RemoveAssignedAsset':

                break;
            case 'ReturnAsset':
                $note = self::returnAsset($options);
                break;
            case 'AdditionalCharge':
                $note = self::additionalCharge($options);
                break;
            case 'RemoveAdditionalCharge':
                break;
            case 'CreateShippingLabel':
                $note = self::createShippingLabel($options);
                break;
            case 'CreateReturnShippingLabel':
                break;
            case 'RecurringPayments': // when recurring payment received
                $note = self::RecurringPayments($options);
                break;
            case 'RecurringCancel': // where recurring cancelled
                $note = self::RecurringCancel($options);
                break;
            case 'RecurringFailed': // when recurring payments failed
                $note = self::RecurringFailed($options);
                break;
            case 'RecurringCreated': // when recurring started
                $note = 'Recurring process started.';
                break;
            case 'WaiverSharingAdd': // when recurring started
                $note = 'Waiver signed ';
                if (!empty($options['name'])){
                    $name = $options['name'];
                    $note .= "by $name";
                    if (!empty($options['email'])){
                        $email = $options['email'];
                        $note .= "($email)";
                    }
                }
                break;
            case 'WaiverSharingDelete': // when recurring started
                $note = 'Waiver ';
                if (!empty($options['name'])){
                    $name = $options['name'];
                    $note .= "signed by $name";
                    if (!empty($options['email'])){
                        $email = $options['email'];
                        $note .= "($email)";
                    }
                }
                $note .= ' has been removed';
                break;
            case 'PickupLocationChange':
                RentMy::addModel(['Locations', 'Users']);
                if (!empty($options['prev_location']))
                    $prevLocation = RentMy::$Model['Locations']->get($options['prev_location'])->name;
                if (!empty($options['new_location']))
                    $newLocation = RentMy::$Model['Locations']->get($options['new_location'])->name;

                $note = 'Pickup location has been changed';
                if (!empty($prevLocation) && !empty($newLocation))
                    $note .= " from $prevLocation  to $newLocation";

                if (!empty($options['user_id'])){
                    $user = RentMy::$Model['Users']->find()->where(['id'=>$options['user_id']])->first();
                    if (!empty($user)){
                        $note .= " by ".$user['first_name'] . ' ' . $user['last_name'];
                    }
                }
                break;

            case 'ReturnLocationChange':
                RentMy::addModel(['Locations', 'Users']);
                if (!empty($options['prev_location']))
                    $prevLocation = RentMy::$Model['Locations']->get($options['prev_location'])->name;
                if (!empty($options['new_location']))
                    $newLocation = RentMy::$Model['Locations']->get($options['new_location'])->name;

                $note = 'Return location has been changed';
                if (!empty($prevLocation) && !empty($newLocation))
                    $note .= " from $prevLocation  to $newLocation";

                if (!empty($options['user_id'])){
                    $user = RentMy::$Model['Users']->find()->where(['id'=>$options['user_id']])->first();
                    if (!empty($user)){
                        $note .= " by ".$user['first_name'] . ' ' . $user['last_name'];
                    }
                }
                break;

            case 'ResendPaymentEmail':
                $note = 'Payment email sent';
                break;
            case 'PaymentFailed':
                $note = $options['error_message'];
                break;
            case 'FulfillmentUpdate':
                RentMy::addModel(['Orders', 'Users', 'Customers']);
                $order = RentMy::$Model['Orders']->find()->where(['id' => $options['order_id']])->first();
                $fulfillmentStatus = [
                    '1' => 'pickup',
                    '2' => 'Delivery',
                    '3' => 'Delivery',
                    '4' => 'Shipping',
                    '5' => 'Shipping',
                    '6' => 'Shipping',
                    '7' => 'Shipping',
                ];
                $fromStatus = $fulfillmentStatus[$options['previous_shipping_method']];
                $toStatus = $fulfillmentStatus[$order->shipping_method];
                $noteBy = '';
                if (!empty($options['name']))
                    $noteBy .= ' by '.$options['name'];

                $notes = [];
                if ($fromStatus != $toStatus)
                    $notes[] = "Fulfillment has been changed from $fromStatus to $toStatus" . $noteBy;

                if (isset($options['previous_charge']) && ($options['previous_charge'] != $order->delivery_charge)){
                    $previousCharge = RentMy::currencyFormat(['currency_format' => RentMy::$storeConfig['currency_format']], $options['previous_charge']);
                    $newCharge = RentMy::currencyFormat(['currency_format' => RentMy::$storeConfig['currency_format']], $order->delivery_charge);
                    $notes[] = "Delivery charge has been updated from $previousCharge to $newCharge" . $noteBy;
                }

                if (($fromStatus == $toStatus) && empty($note)){
                    $notes[] = "Fulfillment address or type has been updated" . $noteBy;
                }
                break;
            case 'ReturnProcess':
                RentMy::addModel(['OrderAssets', 'Assets']);

                if (!empty($options['asset_id'])) {
                    $asset = RentMy::$Model['Assets']->find()->where(['id' => $options['asset_id']])->first();
                }

                $note = 'Return request accepted';
                if (!empty($options['return_request']))
                    $note = 'Return request';

                if (!empty($options['cancel']))
                    $note = 'Return request has been canceled';

                if (!empty($options['return_status']) && $options['return_status'] == '7'){
                    $note = 'Return request rejected for missing';
                }

                if (!empty($options['order_item_id'])){
                    $note .= ' for order item #' . $options['order_item_id'];
                }

                if (!empty($asset)){
                    $note .= ' ( asset#'.$asset->serial_no .')';
                }else{
                    $note .= ' ( qty#'.$options['return_quantity'] .')';
                }

                if (!empty($options['return_status'])){
                    $assetReturnStatus= Configure::read('assetReturnStatus');
                    $returnStatus = $assetReturnStatus[$options['return_status']];
                    $note .= 'condition ('.$returnStatus['label']. ')';
                }

                if (!empty($options['batch_id'])){
                    $note .= ' with return id#'.$options['batch_id'];
                }

                if (!empty($options['name'])){
                    $note .= ' by' . $options['name'];
                }


                $noteData['options'] = json_encode($options);
                break;
            case "OrderItemExchange":
                $notes = !empty($options['logs']) ? $options['logs'] : [];
                break;
            case "QuoteAccepted":
                $note = 'Quote converted to order';
                break;
            case "QuoteConfirmed":
                $note = 'Quote confirmed by the customer';
                break;
            case "QuoteCreatedFromWishList":
                $note = 'Quote created from wishlist';
                break;
            case "deletePayment":
                if ($options['payment_data']['payment_amount']){
                    $note = 'Payment of amount ' . RentMy::currencyFormat('', $options['payment_data']['payment_amount']) . ' has been deleted';
                }else{
                    $note = 'Payment has been deleted';
                }

                if (!empty($options['user_id'])){
                    $user = RentMy::$Model['Users']->find()->where(['id'=>$options['user_id']])->first();
                    if (!empty($user)){
                        $note .= " by ".$user['first_name'] . ' ' . $user['last_name'];
                    }
                }
                break;
        }
        if (!empty($note)) {
            $noteData['note'] = $note;
            $orderNote = $this->newEntity($noteData);
            $this->save($orderNote);
        }

        if (!empty($notes)){
            foreach ($notes as $note){
                $noteData['note'] = $note;
                $orderNote = $this->newEntity($noteData);
                $this->save($orderNote);
            }
        }

    }

    /**
     * @param $options
     * @return string|string[]
     */
    private function additionalCharge($options)
    {
        $message = 'Additional charge added. Amount : {0}';
        $note = '';
        RentMy::addModel(['OrderCharge']);
        $charge = RentMy::$Model['OrderCharge']->find()->where(['id' => $options['content_id']])->first();
        if (!empty($charge)) {
            $note = str_replace('{0}', '$'.$charge['amount'], $message);
        }
        return $note;

    }

    /**
     * create log note for payment order log
     * @param $messages
     * @param $options
     *
     * @return string|string[]
     */
    private function logOrderPayments($messages, $options)
    {
        RentMy::addModel(['Payments', 'PaymentGateways']);
        $payment_id = $options['content_id'];
        $note = '';
        if (!empty($payment_id)) {
            $payments = RentMy::$Model['Payments']->find()->where(['id' => $payment_id])->first();
            $note = !empty($options['source']) ? str_replace('{0}', $options['source'], $messages['Payments']) : $messages['Payments'];
            $note = str_replace('{1}', $payments['payment_gateway'], $note);
            $payment_method = ($payments['payment_method'] == 'Authorized') ?
                ($payments['is_void'] == 1) ? 'voided' :
                    'authorized' : 'received';
            $note = str_replace('{2}', $payment_method, $note);
            $note = str_replace('{3}', '$' . number_format($payments['payment_amount'], 2), $note);

        }
        return $note;
    }

    /**
     * @param $message
     * @param $options
     * @return string|string[]
     */
    private function acceptQuote($message, $options)
    {
        return str_replace('{0}', $options['name'], $message['AcceptQuote']);
    }

    /**
     * product add/edit/delete logs
     * @param $type
     * @param $options
     * @return string|string[]
     */
    private function products($type, $options)
    {

        $note = '';
        RentMy::addModel(['OrderItems', 'Products']);
        if ($type == 'add') {
            $message = '{0} added.';
            $orderItems = RentMy::$Model['OrderItems']->find()->where(['id' => $options['content_id']])->first();
            if (!empty($orderItems)) {
                $product = RentMy::$Model['Products']->find()->where(['id' => $orderItems['product_id']])->first();
                $product_name = $product['name'];
                $product_name .= ($orderItems['variant_chain_name'] != 'Unassigned') ? ' (' . $orderItems['variant_chain_name'] . ')' : '';
                $note = str_replace('{0}', $product_name, $message);
            }
            return $note;
        } elseif ($type == 'update') {
            $message = '{0} updated.';
            $orderItems = RentMy::$Model['OrderItems']->find()->where(['id' => $options['content_id']])->first();
            if (!empty($orderItems)) {
                $product = RentMy::$Model['Products']->find()->where(['id' => $orderItems['product_id']])->first();
                $product_name = $product['name'];
                $product_name .= ($orderItems['variant_chain_name'] != 'Unassigned') ? ' (' . $orderItems['variant_chain_name'] . ')' : '';
                $note = str_replace('{0}', $product_name, $message);
            }
            return $note;
        } elseif ($type == 'delete') {
            $message = '{0} removed.';
            $product = RentMy::$Model['Products']->find()->where(['id' => $options['product_id']])->first();
            $product_name = $product['name'];
            $product_name .= ($options['variant'] != 'Unassigned') ? ' (' . $options['variant'] . ')' : '';
            $note = str_replace('{0}', $product_name, $message);
            return $note;
        }
    }

    /**
     * assign asset log
     * @param $options
     * @return string|string[]
     */
    private function assignAsset($options)
    {
        $message = 'Asset {0} assigned for {1}';
        RentMy::addModel(['OrderAssets', 'Products']);
        $orderAsset = RentMy::$Model['OrderAssets']->find()->where(['OrderAssets.id' => $options['content_id']])
            ->select(['Assets.serial_no', 'OrderItems.product_id', 'OrderItems.variant_chain_name'])
            ->contain(['Assets', 'OrderItems'])
            ->first();
        $product = RentMy::$Model['Products']->find()->select(['name'])->where(['id' => $orderAsset['order_item']['product_id']])->first();
        $product_name = $product['name'];
        $product_name .= ($orderAsset['order_item']['variant_chain_name'] != 'Unassigned') ? ' (' . $orderAsset['order_item']['variant_chain_name'] . ')' : '';
        $note = str_replace('{0}', $orderAsset['asset']['serial_no'], $message);
        $note = str_replace('{1}', $product_name, $note);
        return $note;

    }

    /**
     * assign asset log
     * @param $options
     * @return string|string[]
     */
    private function returnAsset($options)
    {
        $message = 'Asset {0} returned for {1}. Additional charge {2}';
        RentMy::addModel(['OrderAssets', 'Products']);
        $orderAsset = RentMy::$Model['OrderAssets']->find()->where(['OrderAssets.id' => $options['content_id']])
            ->select(['return_charge', 'Assets.serial_no', 'OrderItems.product_id', 'OrderItems.variant_chain_name'])
            ->contain(['Assets', 'OrderItems'])
            ->first();
        $product = RentMy::$Model['Products']->find()->select(['name'])->where(['id' => $orderAsset['order_item']['product_id']])->first();
        $product_name = $product['name'];
        $product_name .= ($orderAsset['order_item']['variant_chain_name'] != 'Unassigned') ? ' (' . $orderAsset['order_item']['variant_chain_name'] . ')' : '';
        $note = str_replace('{0}', $orderAsset['asset']['serial_no'], $message);
        $note = str_replace('{1}', $product_name, $note);
        $note = str_replace('{2}', '$' . $orderAsset['return_charge'], $note);
        return $note;

    }

    /**
     * @param $options
     * @return string
     */
    private function createShippingLabel($options)
    {
        return 'Shipping label is created.';
    }

    // when recurring payment received
    private function RecurringPayments($options)
    {
        $messages = '{0} payment received: {1}';
        RentMy::addModel(['Payments', 'PaymentGateways']);
        $payment_id = $options['content_id'];
        $note = '';
        if (!empty($payment_id)) {
            $payments = RentMy::$Model['Payments']->find()->where(['id' => $payment_id])->first();
            $note = str_replace('{0}', $payments['payment_gateway'], $messages);
            $note = str_replace('{1}', '$' . number_format($payments['payment_amount'], 2), $note);
        }
        return $note;
    }

    private function RecurringCancel($options)
    {

    }

    private function RecurringFailed($options)
    {
        $messages = 'Recurring status changed to {0}';
        $note = '';
        $note = str_replace('{0}', $options['note'], $note);
        return $note;
    }

    /**
     * @param $options
     * @return string|string[]
     */
    private function refund($options)
    {
        $messages = '{0} refunded.';
        RentMy::addModel(['Refunds']);
        $refund_id = $options['content_id'];
        $note = '';
        $notes = [];
        if (!empty($refund_id)) {
            $refunds = RentMy::$Model['Refunds']->find()->where(['id' => $refund_id])->first();
            if (!empty($refunds['amount'])){
                $notes[] = str_replace('{0}', '$' . number_format($refunds['amount'], 2), $messages);
            }
            if (!empty($refunds['deposit_amount'])){
                $notes[] = str_replace('{0}', '$' . number_format($refunds['deposit_amount'], 2), $messages);
            }
        }
        return $notes;
    }

    private function refundCanceled($options){
        $messages = "{0} refund canceled.";
        $notes = [];
        $refundedAmount = RentMy::currencyFormat(null, $options['amount']);
        $notes[] = str_replace('{0}', $refundedAmount, $messages);
        return $notes;
    }
}
