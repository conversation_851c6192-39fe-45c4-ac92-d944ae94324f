<?php
namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * RateLimits Model
 *
 * @method \App\Model\Entity\RateLimit get($primaryKey, $options = [])
 * @method \App\Model\Entity\RateLimit newEntity($data = null, array $options = [])
 * @method \App\Model\Entity\RateLimit[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\RateLimit|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RateLimit saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\RateLimit patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\RateLimit[] patchEntities($entities, array $data, array $options = [])
 * @method \App\Model\Entity\RateLimit findOrCreate($search, callable $callback = null, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class RateLimitsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('rate_limits');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator)
    {
//        $validator
//            ->integer('id')
//            ->allowEmptyString('id', null, 'create');
//
//        $validator
//            ->scalar('ip')
//            ->maxLength('ip', 45)
//            ->requirePresence('ip', 'create')
//            ->notEmptyString('ip');
//
//        $validator
//            ->scalar('user_agent')
//            ->allowEmptyString('user_agent');
//
//        $validator
//            ->scalar('endpoint')
//            ->maxLength('action', 255)
//            ->requirePresence('action', 'create')
//            ->notEmptyString('action');
//
//        $validator
//            ->integer('attempts')
//            ->notEmptyString('attempts');
//
//        $validator
//            ->dateTime('last_attempt')
//            ->allowEmptyDateTime('last_attempt');
//
//        $validator
//            ->dateTime('blocked_until')
//            ->allowEmptyDateTime('blocked_until');

        return $validator;
    }
}
