<?php

namespace App\Model\Table;

use App\Lib\RentMy\Price;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Cake\Validation\Validator;

class ProductPricesTable extends Table
{
    private $VariantsProducts, $Products;

    /**
     * Initialize method
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->VariantsProducts = TableRegistry::get('VariantsProducts');
        $this->Products = TableRegistry::get('Products');

        $this->setTable('product_prices');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('VariantsProducts', [
            'foreignKey' => 'variants_products_id'
        ]);

        $this->belongsTo('Products', [
            'foreignKey' => 'product_id'
        ]);
    }

    /**
     * Default validation rules.
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     */
    public function buildRules(RulesChecker $rules)
    {
        //$rules->add($rules->existsIn(['variants_products_id'], 'VariantsProducts'));

        return $rules;
    }

    /**
     *  add Base Price
     * @param $data ['base_price']
     * @param $data ['product_id']
     * @param $vp_id
     */
    public function addBasePrice($vp_id, $data)
    {
        $productPriceObj = $this->find();
        $location = $data['location'] ?? RentMy::$token->location;
        $productPriceObj->where(['duration_type' => 'base', 'variants_products_id' => $vp_id, 'location' => $location]);
        if (!empty($data['is_vp'])) {
            $productPriceObj->where(['is_vp' => 1]);
        }
        $productPrices = $productPriceObj->toArray();
        // remove all unused prices if added by mistake or duplicate data.
        if (count($productPrices) > 1) {
            $this->deleteAll(['duration_type' => 'base', 'variants_products_id' => $vp_id, 'id !=' => $productPrices[0]->id, 'location' => $location]);
        }
        $productPrice= $productPrices[0];
        $data = [
            'price' => $data['base_price'],
            'duration_type' => 'base',
            'variants_products_id' => $vp_id,
            'store_id' => RentMy::$store->id,
            'location' => $location,
            'product_id' => $data['product_id'],
            'is_vp' => !empty($data['is_vp']) ? 1 : 0,
            'promo_price' => $data['promo_price'] ?? 0
        ];
        if (empty($productPrice)) {
            $productPrice = $this->newEntity();

        }
        $productPrice = $this->patchEntity($productPrice, $data);
        $this->save($productPrice);
        return $productPrice;
    }

    /**
     * Add rent price
     * @param $aData
     * @return bool
     */
    public function add($aData)
    {
        if ($aData['price_type'] == 2) {
            $productPrice = $this->find('all')
                ->where(['variants_products_id' => $aData['variants_products_id']])
                ->where(['price_type' => $aData['price_type']])
                ->where(['is_vp' => $aData['is_vp']])
                ->where(['location' => $aData['location']])
                ->first();
            if ($productPrice) {
                $aData['id'] = $productPrice->id;
            }
        } else if ($aData['price_type'] == 3) {
            $productPrice = $this->find('all')
                ->where(['variants_products_id' => $aData['variants_products_id']])
                ->where(['price_type' => $aData['price_type']])
                ->where(['duration_type' => $aData['duration_type']])
                ->where(['is_vp' => $aData['is_vp']])
                ->where(['location' => $aData['location']])
                ->first();
            if ($productPrice) {
                $aData['id'] = $productPrice->id;
            }
        }
        $vProduct = $this->VariantsProducts->get($aData['variants_products_id']);
        $aData['product_id'] = $vProduct->product_id;
        $aData['promo_price'] = empty($aData['promo_price']) ? 0 : $aData['promo_price'];
        $aData['location'] = RentMy::$token->location;
        $aData['current'] = true;
        if (!empty($aData['id'])) {
            $productPrice = $this->get($aData['id']);
        } else {
            $productPrice = $this->newEntity();
        }
        $productPrice = $this->patchEntity($productPrice, $aData);
        if (!$this->save($productPrice)) {
            return false;
        }
        return true;
    }

    /**
     * Add price according to variant
     * for Flex, Flat, Fixed Prices
     * @param $data
     * @return bool
     */
    public function savePrice($data)
    {
        if ($data['price_type'] == 4) {
            /********* Flex Rental Rate *************/
            foreach ($data['data'] as $row) {
                $row['price_type'] = $data['price_type'];
                $row['store_id'] = $data['store_id'];
                $row['location'] = $data['location'] ?? RentMy::$token->locaiton;
                $row['variants_products_id'] = $data['variants_products_id'];
                $row['is_vp'] = !empty($data['is_vp']) ? 1 : 0;
                $this->_productPrice($row);
            }
        } else if ($data['price_type'] == 3) {
            /********* Flat Rental Rate *************/
            $data['data']['variants_products_id'] = $data['variants_products_id'];
            $data['data']['price_type'] = $data['price_type'];
            $data['data']['store_id'] = $data['store_id'] ?? RentMy::$store->id;
            $data['data']['location'] = $data['location'] ?? RentMy::$token->locaiton;
            $data['data']['is_vp'] = !empty($data['is_vp']) ? 1 : 0;

            $this->removeExistingFixedPrice($data['data']);
            $this->_productPrice($data['data']);
        } else if ($data['price_type'] == 2) {
            /********* Fixed Rental Rate *************/
            $aData = array();
            $aData['price'] = $data['data']['price'];
            $aData['price_type'] = $data['price_type'];
            $aData['store_id'] = $data['store_id'];
            $aData['location'] = $data['location'] ?? RentMy::$token->locaiton;
            $aData['variants_products_id'] = $data['variants_products_id'];
            $aData['duration_type'] = 'fixed';
            $aData['is_vp'] = !empty($data['is_vp']) ? 1 : 0;
            $aData['batch_id'] = $data['data']['batch_id'] ?? '';
            if (isset($aData['price'])) {
                $this->add($aData);
            }
        }else if($data['price_type'] == 5){

            foreach ($data['data'] as $durationType => $row) {

                foreach ($row as $price) {
                    if (empty($price['duration']) || empty($price['price'])) {
                        continue;
                    }
                    $priceData = [];
                    $priceData['price_type'] = $durationType;
                    $priceData['price_type'] = $data['price_type'];
                    $priceData['store_id'] = $data['store_id'];
                    $priceData['location'] = $data['location'] ?? RentMy::$token->locaiton;
                    $priceData['variants_products_id'] = $data['variants_products_id'];
                    $priceData['is_vp'] = !empty($data['is_vp']) ? 1 : 0;
                    $priceData[$durationType] = $price;

                    $this->_productPrice($priceData);
                }

            }
        }
        return true;
    }

    /**
     * Fixed prices data can't be deleted directly using delete action.
     * This function is used to delete existing fixed price type data
     * when duration type(hourly, daily, weekly, monthly) and price is empty.
     * used from add/edit fixed pricing data .
     * @param $data | array
     */
    private function removeExistingFixedPrice($data)
    {
        foreach ($data as $i => $d) {
            if (in_array($i, array('hourly', 'daily', 'weekly', 'monthly'))) {
                $durationType = $i;
                $productPriceObj = $this->find();
                $productPriceObj->where(['variants_products_id' => $data['variants_products_id']]);
                $productPriceObj->where(['price_type' => $data['price_type']]);
                $productPriceObj->where(['duration_type' => $durationType]);
                $productPriceObj->where(['is_vp' => $data['is_vp']]);
                $productPriceObj->where(['location' => $data['location']]);
                $productPrice = $productPriceObj->first();
                if (empty($d['duration']) && empty($d['price'])) {
                    if (!empty($productPrice)) {
                        $this->delete($productPrice);
                    }
                }
            }
        }
    }

    /**
     * Save rent price
     * @param $row
     * @return bool
     */

    private function _productPrice($row)
    {
        if (!empty($row['hourly'])) {
            $aData = $row['hourly'];
            $aData['duration'] = empty($aData['duration']) ? 1 : $aData['duration'];
            $aData['label'] = empty($aData['label']) ? $aData['duration'] > 1 ? 'hours' : 'hour' : $aData['label'];
            $aData['price_type'] = $row['price_type'];
            $aData['store_id'] = $row['store_id'];
            $aData['location'] = $row['location'];
            $aData['variants_products_id'] = $row['variants_products_id'];
            $aData['duration_type'] = 'hourly';
            $aData['is_vp'] = $row['is_vp'];
            if (isset($aData['term'])) {
                $aData['max_duration'] = $aData['term'];
            }
            if (in_array($row['price_type'], [3, 5]) && isset($aData['price'])) {
                $this->add($aData);
            } else if ($row['price_type'] == 4 && isset($aData['price']) && isset($aData['flex_price'])) {
                $this->add($aData);
            }
        }
        if (!empty($row['daily'])) {
            $aData = $row['daily'];
            $aData['duration'] = empty($aData['duration']) ? 1 : $aData['duration'];
            $aData['label'] = empty($aData['label']) ? $aData['duration'] > 1 ? 'days' : 'day' : $aData['label'];
            $aData['price_type'] = $row['price_type'];
            $aData['store_id'] = $row['store_id'];
            $aData['location'] = $row['location'];
            $aData['variants_products_id'] = $row['variants_products_id'];
            $aData['duration_type'] = 'daily';
            $aData['is_vp'] = $row['is_vp'];
            if (isset($aData['term'])) {
                $aData['max_duration'] = $aData['term'];
            }
            if (in_array($row['price_type'], [3, 5]) && isset($aData['price'])) {
                $this->add($aData);
            } else if ($row['price_type'] == 4 && isset($aData['price']) && isset($aData['flex_price'])) {
                $this->add($aData);
            }
        }
        if (!empty($row['weekly'])) {
            $aData = $row['weekly'];
            $aData['duration'] = empty($aData['duration']) ? 1 : $aData['duration'];
            $aData['label'] = empty($aData['label']) ? $aData['duration'] > 1 ? 'weeks' : 'week' : $aData['label'];
            $aData['price_type'] = $row['price_type'];
            $aData['store_id'] = $row['store_id'];
            $aData['location'] = $row['location'];
            $aData['variants_products_id'] = $row['variants_products_id'];
            $aData['duration_type'] = 'weekly';
            $aData['is_vp'] = $row['is_vp'];
            if (isset($aData['term'])) {
                $aData['max_duration'] = $aData['term'];
            }
            if (in_array($row['price_type'], [3, 5]) && isset($aData['price'])) {
                $this->add($aData);
            } else if ($row['price_type'] == 4 && isset($aData['price']) && isset($aData['flex_price'])) {
                $this->add($aData);
            }
        }
        if (!empty($row['monthly'])) {
            $aData = $row['monthly'];
            $aData['duration'] = empty($aData['duration']) ? 1 : $aData['duration'];
            $aData['label'] = empty($aData['label']) ? $aData['duration'] > 1 ? 'months' : 'month' : $aData['label'];
            $aData['price_type'] = $row['price_type'];
            $aData['store_id'] = $row['store_id'];
            $aData['location'] = $row['location'];
            $aData['variants_products_id'] = $row['variants_products_id'];
            $aData['duration_type'] = 'monthly';
            $aData['is_vp'] = $row['is_vp'];
            if (isset($aData['term'])) {
                $aData['max_duration'] = $aData['term'];
            }

            if (in_array($row['price_type'], [3, 5]) && isset($aData['price'])) {
                $this->add($aData);
            } else if ($row['price_type'] == 4 && isset($aData['price']) && isset($aData['flex_price'])) {
                $this->add($aData);
            }
        }
        return true;
    }

    /**
     * Get product price settings list
     * @param $vpId
     * @param $type
     * @return array
     */
    public function getPrice($vpId, $type, $is_vp = 0)
    {
        $productPrices = $this->find('all')
            ->where(['variants_products_id' => $vpId])
            ->where(['price_type' => $type])
            ->where(['is_vp' => $is_vp])
            ->where(['location' => RentMy::$token->location])
            ->toArray();
        if (empty($productPrices)) {
            return;
        }
        $price = array();
        if ($type == 2) {
            $price['price'] = $productPrices[0]->price;
            $price['id'] = $productPrices[0]->id;
        } else if ($type == 3) {
            $price = $this->_priceFormatting($productPrices);
        } else if ($type == 4) {
            $price = $productPrices;
        } else if ($type == 5) {
            $grouped = [];
            foreach ($productPrices as $item) {
                $type = $item['duration_type'];
                $grouped[$type][] = $item;
            }
            $price = $grouped;
        }
        return $price;
    }

    private function _priceFormatting($prices)
    {
        $data = array();
        foreach ($prices as $price) {
            if ($price->duration_type == 'hourly') {
                $data['hourly'] = ['duration' => $price->duration, 'price' => $price->price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                if (RentMy::$storeConfig['inventory']['promo_price']){
                    $data['hourly'] = ['duration' => $price->duration, 'price' => $price->price, 'promo_price' => $price->promo_price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                }
            }
            if ($price->duration_type == 'daily') {
                $data['daily'] = ['duration' => $price->duration, 'price' => $price->price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                if (RentMy::$storeConfig['inventory']['promo_price']){
                    $data['daily'] = ['duration' => $price->duration, 'price' => $price->price, 'promo_price' => $price->promo_price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                }
            }
            if ($price->duration_type == 'weekly') {
                $data['weekly'] = ['duration' => $price->duration, 'price' => $price->price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                if (RentMy::$storeConfig['inventory']['promo_price']){
                    $data['weekly'] = ['duration' => $price->duration, 'price' => $price->price, 'promo_price' => $price->promo_price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                }
            }
            if ($price->duration_type == 'monthly') {
                $data['monthly'] = ['duration' => $price->duration, 'price' => $price->price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                if (RentMy::$storeConfig['inventory']['promo_price']){
                    $data['monthly'] = ['duration' => $price->duration, 'price' => $price->price, 'promo_price' => $price->promo_price, 'id' => $price->id, 'term' => $price->max_duration, 'pricing_label' => $price->pricing_label];
                }
            }
        }
        return $data;
    }

    /** Now deprecated
     * calculate price while add to cart
     * @param $data
     * @return array|int
     */
    public function getItemPrice($data, $priceType, $priceData)
    {
        $timeDifference = $this->_timeDifference($data);
        $priceDuration = $this->_priceDuration($priceData);
        $price = 0;
        if ($priceType == 2) {
            $price = $priceData->price;
        } else if ($priceType == 3) {
            $multiplier = ceil($timeDifference / $priceDuration);
            $price = $priceData->price * $multiplier;
        } else if ($priceType == 4) {
            $multiplier = floor($timeDifference / $priceDuration);
            $basicPrice = $priceData->price * $multiplier;
            /* Additional Price */
            $restDuration = ($timeDifference % $priceDuration);
            $additionPrice = 0;
            if ($restDuration > 0) {
                $additionalPriceData = $this->_getAdditionalPrice($data, $restDuration, $priceType);
                $flexPriceDuration = $this->_flexPriceDuration($additionalPriceData);
                $flexMultiplier = ceil($restDuration / $flexPriceDuration);
                $additionPrice = $additionalPriceData->flex_price * $flexMultiplier;
            }
            $price = $basicPrice + $additionPrice;
        }
        return $price;
    }

    /**
     * calculate price while add to cart
     * @param $data
     * @param $priceType
     * @param $priceData
     * @return float|int
     */
    public function getPriceValue($data, $priceType, $priceData = array())
    {
        $timeDifference = $this->_timeDifference($data);
        if ($timeDifference <= 0)
            $timeDifference = (60 * 60);
        $price = 0;
        if ($priceType == 2) { // fixed
            $price = $priceData->price;
        } else if ($priceType == 3) { // flat
            $priceDuration = $this->_priceDuration($priceData);
            $multiplier = ceil($timeDifference / $priceDuration);
            $price = $priceData->price * $multiplier;
        } else if ($priceType == 4) { // flex
            $price = $this->_getFlexPrice($data, $timeDifference);
        }
        return $price;
    }

    /**
     * calculate price while add to cart
     * @param $data
     * @param $priceType
     * @param $priceData
     * @return float|int
     * @deprecatd  - original code exist on Price.php file
     */
    public function getPriceValueWithFlexOption($data, $priceType, $priceData = array())
    {
        $timeDifference = $this->_timeDifference($data);
        //$price = $this->_getFlexPrice($data, $timeDifference);
        $price = 0;
        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            $productPrices = $this->find()
                ->where(['variants_products_id' => $data['variants_products_id']])
                ->where(['price_type' => 4])
                ->order(['price' => 'DESC'])
                ->toArray();
            $productPrices = RentMy::formatPricing($productPrices);
            foreach ($productPrices as $productPrice) {
                $productPrice->time_duration = $this->_priceDuration($productPrice);
                $allDurations[] = ['id' => $productPrice->id, 'duration' => $productPrice->time_duration];
            }
            $closest = RentMy::getClosestnumber($allDurations, $timeDifference);
            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
            $allPrice = array();
            foreach ($productPrices as $i => $priceData) {
                if ($closest == $priceData->id) {
                    $priceDuration = $this->_priceDuration($priceData);
                    $multiplier = ceil($timeDifference / $priceDuration);
                    if ($multiplier - $priceData->max_range > 0) {
                        $basicPrice = $priceData->price * $priceData->max_range;
                        $basicDuration = $priceDuration * $priceData->max_range;

                        $restDuration = ($timeDifference - $basicDuration);
                        $flexPriceDuration = $this->_flexPriceDuration($priceData);
                        $flexMultiplier = $restDuration > 0 ? ceil($restDuration / $flexPriceDuration) : 0;
                        $additionPrice = $priceData->flex_price * $flexMultiplier;
                        $price = $basicPrice + $additionPrice;
                    } else {
                        $price = ($priceData->price * $multiplier);
                    }
                    //pr($price);
                }
                //$allPrice[] = $price;
            }
            //return min($allPrice);
        }

        return $price;
    }

    /**
     * get Rental Type
     * @param $data
     * @return string
     */
    public function getRentalType($data)
    {
        $timeDifference = $this->_timeDifference($data);
        $rentalType = 'hourly';
        if ($timeDifference >= (60 * 60 * 24 * 27)) {
            $rentalType = 'monthly';
        } elseif ($timeDifference >= (60 * 60 * 24 * 7)) {
            $rentalType = 'weekly';
        } elseif ($timeDifference >= (60 * 60 * 24)) {
            $rentalType = 'daily';
        } elseif ($timeDifference >= (60 * 60)) {
            $rentalType = 'hourly';
        }
        return $rentalType;
    }

    public function _getFlexPrice($data, $timeDifference)
    {
        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            RentMy::addModel(['ProductsAvailabilities']);
            $data['rent_end'] = RentMy::$Model['ProductsAvailabilities']->actualEndDateWithoutEndDateCalculation($data['rent_start'], $data['rent_end']);

            $productPrices = $this->find()
                ->where(['variants_products_id' => $data['variants_products_id']])
                ->where(['price_type' => 4])
                ->order(['price' => 'DESC'])
                ->toArray();

            $productPrices = RentMy::formatPricing($productPrices, $data);
            foreach ($productPrices as $productPrice) {
                $productPrice->time_duration = $this->_priceDuration($productPrice);
            }
            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
            $allPrice = array();
            foreach ($productPrices as $i => $priceData) {
                $priceDuration = $this->_priceDuration($priceData);
                $multiplier = ceil($timeDifference / $priceDuration);
                if ($multiplier - $priceData->max_range > 0) {
                    $basicPrice = $priceData->price * $priceData->max_range;
                    $basicDuration = $priceDuration * $priceData->max_range;

                    $restDuration = ($timeDifference - $basicDuration);
                    $flexPriceDuration = $this->_flexPriceDuration($priceData);
                    $flexMultiplier = $restDuration > 0 ? ceil($restDuration / $flexPriceDuration) : 0;
                    $additionPrice = $priceData->flex_price * $flexMultiplier;
                    $price = $basicPrice + $additionPrice;
                } else {
                    $price = ($priceData->price * $multiplier);
                }
                $allPrice[] = $price;
            }
            return min($allPrice);
        }
    }

    /**
     * @Deprecated
     * @param $data
     * @param $timeDiff
     * @param $priceType
     * @return mixed
     */
    public function _getAdditionalPrice($data, $timeDiff, $priceType)
    {
        if (!empty($timeDiff) && !empty($priceType)) {
            $productPrices = $this->find()
                ->where(['variants_products_id' => $data['variants_products_id']])
                ->where(['price_type' => $priceType])
                ->order(['flex_price' => 'DESC'])
                ->toArray();
            foreach ($productPrices as $productPrice) {
                $productPrice->time_duration = $this->_flexPriceDuration($productPrice);
            }
            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
            $pData = $productPrices[0];
            foreach ($productPrices as $i => $productPrice) {
                if ($productPrice->time_duration <= $timeDiff) {
                    $pData = $productPrices[$i];
                }
            }
            return $pData;
        }
    }

    private function _priceDuration($priceData)
    {
        $priceDuration = 1;
        if ($priceData->duration_type == 'hourly') {
            $priceDuration = $priceData->duration * 60 * 60;
        } else if ($priceData->duration_type == 'daily') {
            $priceDuration = $priceData->duration * 60 * 60 * 24;
        } else if ($priceData->duration_type == 'weekly') {
            $priceDuration = $priceData->duration * 60 * 60 * 24 * 7;
        } else if ($priceData->duration_type == 'monthly') {
            $priceDuration = $priceData->duration * 60 * 60 * 24 * 30;
        }
        return $priceDuration;
    }

    private function _flexPriceDuration($priceData)
    {
        $priceDuration = 1;
        if ($priceData->duration_type == 'hourly') {
            $priceDuration = $priceData->flex_duration * 60 * 60;
        } else if ($priceData->duration_type == 'daily') {
            $priceDuration = $priceData->flex_duration * 60 * 60 * 24;
        } else if ($priceData->duration_type == 'weekly') {
            $priceDuration = $priceData->flex_duration * 60 * 60 * 24 * 7;
        } else if ($priceData->duration_type == 'monthly') {
            $priceDuration = $priceData->flex_duration * 60 * 60 * 24 * 30;
        }
        return $priceDuration;
    }

    private function _flexPriceDuration_old($priceData)
    {
        $flexPriceDuration = 1;
        if ($priceData->duration_type == 'hourly') {
            $flexPriceDuration = 60 * 60;
        } else if ($priceData->duration_type == 'daily') {
            $flexPriceDuration = 60 * 60 * 24;
        } else if ($priceData->duration_type == 'weekly') {
            $flexPriceDuration = 60 * 60 * 24 * 7;
        } else if ($priceData->duration_type == 'monthly') {
            $flexPriceDuration = 60 * 60 * 24 * 30;
        }
        return $flexPriceDuration;
    }

    public function _timeDifference($data)
    {
        return strtotime($data['rent_end']) - strtotime($data['rent_start']);
    }

    /**
     *  Price data
     * @param $vp
     * @return array
     */
    public function priceDetails($vp, $locationId='')
    {
        $location = !empty(RentMy::$token->location) ? RentMy::$token->location : $locationId;

        $productPrices = $this->find('all')
            ->where(['variants_products_id' => $vp->id])
            ->where(['current' => 1])
            ->where(['duration_type !=' => 'base'])
            ->where(['location' => $location])
            ->order(['price' => 'ASC'])
            ->toArray();
        $productPrices = RentMy::formatPricing($productPrices);

        $priceType = $vp->price_type;

        if (!empty($productPrices)){
            $priceType = $productPrices[0]['price_type'];
        }
        $price = array();
        $basePrice = $this->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $vp->id])->where(['location' => $location])->toArray();
        if (empty($productPrices) && !empty($basePrice)) {
            $priceType = $basePrice[0]['price_type'];
        }
        $base = !empty($basePrice) ? [
            'id' => $basePrice[0]->id,
            'price'=>  $basePrice[0]->price,
            'regular_price'=>  $basePrice[0]->price
        ] : '';
        if (!empty(RentMy::$storeConfig['inventory']['promo_price']) && !empty($base)){
            $base['price'] =!empty( $basePrice[0]->promo_price) ? $basePrice[0]->promo_price :  $basePrice[0]->price;
        }
        if ($priceType == 2) {
            $fixed = !empty($productPrices[0]) ? ['id' => $productPrices[0]->id, 'price' => $productPrices[0]->price] : '';
            $price[] = ['base' => $base, 'fixed' => $fixed];
        } else if (in_array($priceType, [3, 4, 5])) {
            $result = $this->_formatPrice($productPrices);
            $result['base'] = $base;
            $price[] = $result;
        }
        return $price;
    }

    private function _formatPrice($prices)
    {
        $data = array();
        foreach ($prices as $price) {
            $rentData = array(
                'id' => $price->id,
                'duration' => $price->duration,
                'price' => $price->price,
                'regular_price' => $price->price,
                'label' => $price->label,
                'flex_price' => $price->flex_price,
            );
            if (!empty(RentMy::$storeConfig['inventory']['promo_price'])){
                $rentData['price'] = !empty($price->promo_price) ? $price->promo_price : $price->price;
            }

            if ($price->duration_type == 'hourly') {
                $rentData['label'] = ($price->duration > 1) ? 'hours' : 'hour';
                $data['hourly'][] = $rentData;
            }
            if ($price->duration_type == 'daily') {
                $rentData['label'] = ($price->duration > 1) ? 'days' : 'day';
                $data['daily'][] = $rentData;
            }
            if ($price->duration_type == 'weekly') {
                $rentData['label'] = ($price->duration > 1) ? 'weeks' : 'week';
                $data['weekly'][] = $rentData;
            }
            if ($price->duration_type == 'monthly') {
                $rentData['label'] = ($price->duration > 1) ? 'months' : 'month';
                $data['monthly'][] = $rentData;
            }
        }
        return $data;
    }

    /**
     *  Price for product details & package details with rental start-end time
     * @param $vp
     * @param null $rent_start
     * @return array
     */
    public function getPriceDetails($vp, $rent_start = null)
    {
        $productPrices = $this->find('all')
            ->where(['variants_products_id' => $vp->id])
            ->where(['current' => 1])
            ->where(['duration_type !=' => 'base'])
            ->where(['location' => RentMy::$token->location])
            ->order(['price' => 'ASC'])
            ->toArray();
        $productPrices = RentMy::formatPricing($productPrices); // for vp & normal pricing

        if (empty($productPrices)) {
            $price = array();
            $basePrice = $this->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $vp->id])->where(['location' => RentMy::$token->location])->toArray();
            $basePrice = RentMy::formatPricing($basePrice);

            $base = !empty($basePrice) ? ['id' => $basePrice[0]->id, 'price' => $basePrice[0]->price] : '';
            if (RentMy::$storeConfig['inventory']['promo_price']){
                $base = !empty($basePrice) ? ['id' => $basePrice[0]->id, 'price' =>!empty($basePrice[0]->promo_price) ?  $basePrice[0]->promo_price : $basePrice[0]->price] : '';
            }

            $price[] = ['base' => $base];
            return $price;
        }

        $priceType = $productPrices[0]['price_type'];

        $rent_start = $datetime = empty($rent_start) ? RentMy::getDefaultStartDate(RentMy::toStoreTimeZone(Time::parse('now'), 'Y-m-d')) : $rent_start;

        $rent_start = $min_date = $datetime = RentMy::min_date($datetime);
        $price = array();
        $basePrice = $this->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $vp->id])->where(['location' => RentMy::$token->location])->toArray();
        $basePrice = RentMy::formatPricing($basePrice);
        $base = !empty($basePrice) ? ['id' => $basePrice[0]->id, 'price' => $basePrice[0]->price] : '';
        if (RentMy::$storeConfig['inventory']['promo_price']){
            $base = !empty($basePrice) ? ['id' => $basePrice[0]->id, 'price' =>!empty($basePrice[0]->promo_price) ?  $basePrice[0]->promo_price : $basePrice[0]->price] : '';
        }

        if ($priceType == 2) {
            $fixed = !empty($productPrices[0]) ? ['id' => $productPrices[0]->id, 'price' => $productPrices[0]->price, 'rent_start' => $rent_start, 'rent_end' => $rent_start, 'min_date' => $min_date] : '';
            $price[] = ['base' => $base, 'fixed' => $fixed];
        } else if (in_array($priceType, [3,4,5])) {
            $result = $this->_formatRentPrice($productPrices, $rent_start , $min_date);
            $result['base'] = $base;
            $price[] = $result;
        }

        $productId = $productPrices[0]['product_id'] ?? $vp->product_id;
        // check seasonal price data here and update the price thoroughly and return
        $price = $this->seasonalPrice($price, $datetime, $productId);
        // ends of seasonal pricing

        return $price;
    }

    /**
     * Seasonal pricing calculations
     * @param $price
     * @param $datetime
     * @return float|int
     */
    public function seasonalPrice($price, $datetime, $productId = '')
    {

        RentMy::addModel(['Holidays']);
//        check for date range of season. if it is in the date range or not
        $utcDate = RentMy::toUTC($datetime, 'Y-m-d');

        if (!empty($productId)){
            $seasonData = RentMy::$Model['Holidays']->find()->where([
                'store_id' => RentMy::$store->id,
                'location' => RentMy::$token->location,
                'type' => 'season',
                'DATE(start_date) <=' => $utcDate,
                'DATE(end_date) >=' => $utcDate,
            ])->innerJoinWith('ReferenceProducts', function ($query) use ($productId){
                return $query->where(['product_id' => $productId]);
            })->first();

        }

        if (empty($seasonData)){
            $seasonData = RentMy::$Model['Holidays']->find()
                ->where([
                    'store_id' => RentMy::$store->id,
                    'location' => RentMy::$token->location,
                    'type' => 'season',
                    'DATE(start_date) <=' => $utcDate,
                    'DATE(end_date) >=' => $utcDate,
                    'ReferenceProducts.id IS NULL'
                ])
                ->leftJoinWith('ReferenceProducts')
                ->first();

        }
        if (!empty($seasonData->id)) {
//            if got amount and amount type is not null then proceed
            if (!empty($seasonData->amount) && !empty($seasonData->amount_type)) {
//                if price type is a array type like daily,hourly etc. then update all prices in array one by one.
                if (!empty($price[0]) && is_array($price[0])) {
                    if ($seasonData->amount_type == 'amount') {
                        foreach ($price[0] as $key => $pr) {
                            if (empty($pr)) {
                                continue;
                            }
                            if (!empty($price[0][$key][0]['price'])) {
                                $price[0][$key][0]['price'] = $pr[0]['price'] + $seasonData->amount ?? 0; // if amount type is simple price then just add it to previous price
                            } else {
                                $price[0][$key]['price'] = $pr['price'] + $seasonData->amount ?? 0; // if amount type is simple price then just add it to previous price
                            }
                        }
                    } else {
                        foreach ($price[0] as $key => $pr) {
                            if (empty($pr)) {
                                continue;
                            }
                            if (!empty($price[0][$key][0]['price'])) {
                                $price[0][$key][0]['price'] = $pr[0]['price'] * (($seasonData->amount ?? 0) / 100); // if amount type is percentage then multiply it to previous price
                            } else {
                                $price[0][$key]['price'] = $pr['price'] * (($seasonData->amount ?? 0) / 100); // if amount type is simple price then just add it to previous price
                            }
                        }
                    }
                } else {
//                    if flat price is entered then simply update it.
                    if ($seasonData->amount_type == 'amount') {
                        $price = $price + $seasonData->amount;
                    } else {
                        $price = $price * ($seasonData->amount / 100);
                    }
                }
            }
        }

        return $price;
    }

    public function bandPrice($price, $startDate, $endDate, $productId = '')
    {

        RentMy::addModel(['Holidays']);
//        check for date range of season. if it is in the date range or not
        $startDay = Time::parse($startDate)->format("N");
        $endDay = Time::parse($endDate)->format("N");
        $conditions = [
            'store_id' => RentMy::$store->id,
            'location' => RentMy::$token->location,
            'type' => 'band_pricing',
            'WEEKDAY(start_date) + 1 =' => $startDay,
            'WEEKDAY(end_date) + 1 =' => $endDay,
        ];

        if (!empty($productId)){
            $seasonData = RentMy::$Model['Holidays']->find()->where($conditions)->innerJoinWith('ReferenceProducts', function ($query) use ($productId){
                return $query->where(['product_id' => $productId]);
            })->first();
        }

        if (empty($seasonData)){
            $conditions[] = 'ReferenceProducts.id IS NULL';
            $seasonData = RentMy::$Model['Holidays']->find()
                ->where($conditions)
                ->leftJoinWith('ReferenceProducts')
                ->first();

        }
        if (!empty($seasonData->id)) {
//            if got amount and amount type is not null then proceed
            if (!empty($seasonData->amount) && !empty($seasonData->amount_type)) {
//                if price type is a array type like daily,hourly etc. then update all prices in array one by one.
                if (!empty($price[0]) && is_array($price[0])) {
                    if ($seasonData->amount_type == 'amount') {
                        foreach ($price[0] as $key => $pr) {
                            if (empty($pr)) {
                                continue;
                            }
                            if (!empty($price[0][$key][0]['price'])) {
                                $price[0][$key][0]['price'] = $pr[0]['price'] + $seasonData->amount ?? 0; // if amount type is simple price then just add it to previous price
                            } else {
                                $price[0][$key]['price'] = $pr['price'] + $seasonData->amount ?? 0; // if amount type is simple price then just add it to previous price
                            }
                        }
                    } else {
                        foreach ($price[0] as $key => $pr) {
                            if (empty($pr)) {
                                continue;
                            }
                            if (!empty($price[0][$key][0]['price'])) {
                                $price[0][$key][0]['price'] = $pr[0]['price'] * (($seasonData->amount ?? 0) / 100); // if amount type is percentage then multiply it to previous price
                            } else {
                                $price[0][$key]['price'] = $pr['price'] * (($seasonData->amount ?? 0) / 100); // if amount type is simple price then just add it to previous price
                            }
                        }
                    }
                } else {
//                    if flat price is entered then simply update it.
                    if ($seasonData->amount_type == 'amount') {
                        $price = $price + $seasonData->amount;
                    } else {
                        $price = $price * ($seasonData->amount / 100);
                    }
                }
            }
        }

        return $price;
    }

    /**
     * This function is used to format db price list and
     * add rental start, end date with each pricing list
     * @param $prices
     * @param null $rent_start
     * @param null $min_date
     * @return array
     */
    private function _formatRentPrice($prices, $rent_start = null, $min_date=null)
    {
        //$rent_start = date('Y-m-d H:i');
        $data = array();
        foreach ($prices as $price) {
            $rent_end = RentMy::getEndDateFromDuration($rent_start, 1, $price->duration_type, $price->duration);
            if (empty(RentMy::$storeConfig['show_start_time']) || empty(RentMy::$storeConfig['show_end_time'])) {
                $start = Time::parse($rent_start)->format('Y-m-d');
                $end = Time::parse($rent_end)->format('Y-m-d');

                if ($start != $end) {
                    $rent_end = RentMy::getDefaultEndDate($end, false);
                }
            }elseif (RentMy::$storeConfig['rental_day'] == 'calendar'){
                $start = Time::parse($rent_start)->format('Y-m-d');
                $end = Time::parse($rent_end)->format('Y-m-d');

                if ($start != $end) {
                    $rent_end = RentMy::getDefaultEndDate($end, false);
                }
            }
            $rentData = array(
                'id' => $price->id,
                'duration' => $price->duration,
                'duration_type' => $price->duration_type,
                'price' => $price->price , // promo price will be only for standard pricing
                'label' => $price->label,
                'flex_price' => $price->flex_price,
                'max_duration' => $price->max_duration,
                'price_type' => $price->price_type,
                'pricing_label' => $price->pricing_label,
                'rent_start' => $rent_start,
                'rent_end' => $rent_end,
                'min_date' => Time::parse($min_date)->format('Y-m-d H:00') //RentMy::min_date($rent_start)
            );
            if (RentMy::$storeConfig['inventory']['promo_price']){
                $rentData['price'] =!empty($price->promo_price) ? $price->promo_price : $price->price;
            }

            if (!empty($price['max_duration']) && ($price->price_type == 3)) {
                $rentData['term'] = $price->max_duration;
            }
            if ($price->duration_type == 'hourly') {
                $rentData['label'] = 'hour';
                $data['hourly'][] = $rentData;
            }
            if ($price->duration_type == 'daily') {
                $rentData['label'] = 'day';
                $data['daily'][] = $rentData;
            }
            if ($price->duration_type == 'weekly') {
                $rentData['label'] = 'week';
                $data['weekly'][] = $rentData;
            }
            if ($price->duration_type == 'monthly') {
                $rentData['label'] = 'month';
                $data['monthly'][] = $rentData;
            }
        }
        return $data;
    }

    public function getPriceData($data, $priceType)
    {
        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            $timeDiff = $this->_timeDifference($data);
            $productPrices = $this->find()
                ->where(['variants_products_id' => $data['variants_products_id']])
                ->where(['price_type' => $priceType])
                ->where(['duration_type !=' => 'base'])
                ->order(['price' => 'DESC'])
                ->toArray();
            $productPrices = RentMy::formatPricing($productPrices);
            foreach ($productPrices as $productPrice) {
                $productPrice->time_duration = $this->_priceDuration($productPrice);
            }
            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
            $pData = $productPrices[0];
            foreach ($productPrices as $i => $productPrice) {
                if ($productPrice->time_duration <= $timeDiff) {
                    $pData = $productPrices[$i];
                }
            }
            return $pData;
        }
    }

    /**
     * @TODO
     * This function should be deprecated.
     * Should use the default functions
     * can be removed from the application
     * location constrain not added as method is depreceated
     */
    public function getOption($storeId, $location)
    {
        $store = TableRegistry::get('Stores')->get($storeId);
        $store_config = json_decode($store->config, true);
        if (!empty($store_config)) {
            $config = $store_config;
        } else {
            $config = ['show_end_date' => true, 'rental_price_option' => true,
                'currency_format' => ['code' => 'USD', 'pre' => true, 'post' => true, 'symbol' => '$']];
        }
        $timezone = (empty($store->settings) || !isset(json_decode($store->settings, true)[$location]['timezone']))
            ? Configure::read('TIMEZONE') : json_decode($store->settings, true)[$location]['timezone'];
        $config['timezone'] = $timezone;
        $response = array(
            'rental_price_option' => $config['rental_price_option'] ?? true,
            'show_start_date' => $config['show_start_date'] ?? true,
            'show_end_date' => $config['show_end_date'] ?? true,
            'show_start_time' => $config['show_start_time'] ?? true,
            'show_end_time' => $config['show_end_time'] ?? true,
        );
        return $response;
    }

    /**
     * @param $pId
     * @deprecated
     */
    public function savePurchaseTypeToProduct($pId)
    {
        $products = $this->VariantsProducts->find()
            ->select(['VariantsProducts.product_id'])
            ->contain(
                'BasePrice', function ($q) {
                return $q->select(['BasePrice.id', 'price', 'BasePrice.duration_type', 'BasePrice.duration', 'BasePrice.variants_products_id'])
                    ->where(["BasePrice.duration_type LIKE '%" . 'base' . "%'"]);
            })
            ->where(['VariantsProducts.is_last' => 1, 'VariantsProducts.status' => 1])
            ->where(['VariantsProducts.product_id' => $pId])
            ->order(['VariantsProducts.product_id' => 'ASC'])
            ->toArray();
        //pr($products);exit();
        foreach ($products as $i => $p) {
            $product = $this->Products->get($p['product_id']);
//            $rent = false;
//            if (($p['base_price']['hourly_price'] > 0) ||
//                ($p['base_price']['daily_price'] > 0) ||
//                ($p['base_price']['weekly_price'] > 0)) {
//                $rent = true;
//            }

            $priceData = $this->find('all')
                ->where(['product_id' => $pId])
                ->where(['duration_type IN ' => ['hourly', 'daily', 'weekly', 'monthly', 'fixed']])
                ->first();
            $rent = empty($priceData) ? false : true;
            $buy = empty($p->base_price) ? false : true;


            if ($buy && ($rent)) {
                $product->purchase_type = 'all#buy#rent';
                $this->Products->save($product);
            } else if ($rent && !$buy) {
                $product->purchase_type = 'rent';
                $this->Products->save($product);
            } else if (!$rent && $buy) {
                $product->purchase_type = 'buy';
                $this->Products->save($product);
            }
        }
    }

    /**
     * This function update products table based on price buy or rent
     * Also it is saving buy price, min & max rental price on products table
     * this reporting is used for searching.
     *
     *
     *
     * @param $productId
     */
    public function updatePriceToProduct($productId)
    {
        RentMy::addModel(['Products', 'VariantsProducts']);
        $product = RentMy::$Model['Products']->find()->where(['id' => $productId])->first();
        $variant = RentMy::$Model['VariantsProducts']->find()->where(['id' =>  $product->variants_products_id])->first();

        $buyPrice = $this->find()
            ->where(['product_id' => $productId, 'duration_type' => 'base',
                'variants_products_id' => $product->variants_products_id, 'is_vp' => 0])->first();

        $rentPrice = $this->find()
            ->where(['product_id' => $productId, 'variants_products_id' => $product->variants_products_id, 'price_type' => $variant->price_type,
                'duration_type IN' => ['hourly', 'daily', 'weekly', 'monthly', 'fixed'], 'is_vp' => 0])
            ->order(['price' => 'ASC'])->toArray();

        //purchase type
        $buy = !empty($buyPrice);
        $rent = !empty($rentPrice);
        if ($buy && $rent) {
            $product->purchase_type = 'all#buy#rent';
        } else if ($rent && !$buy) {
            $product->purchase_type = 'rent';
        } else if (!$rent && $buy) {
            $product->purchase_type = 'buy';
        }

        //generate for rent
        $min_promo_price = $min_regular_price = null;
        if (!empty($rentPrice)) {
            $collection = new Collection($rentPrice);
            $minPrice = ($collection->min('price'));
            $min_regular_price = $min_promo_price = $minPrice->price ?? 0;
            if(($minPrice->price_type == 3) && isset(RentMy::$storeConfig['inventory']) && !empty(RentMy::$storeConfig['inventory']['promo_price']))
                $min_promo_price = empty($minPrice->promo_price) ? $minPrice->price : $minPrice->promo_price;
            //$maxPrice = ($collection->max('price'))->price;
        }
        $product->rent_price = $min_promo_price;
        $product->regular_rent_price = $min_regular_price;

        //generate for buy
        $product->buy_price = $buyPrice->price ?? null;
        if (isset(RentMy::$storeConfig['inventory']) && !empty(RentMy::$storeConfig['inventory']['promo_price']))
            $product->buy_price = empty($buyPrice->promo_price) ? $product->buy_price : $buyPrice->promo_price;

        $product->regular_buy_price = $buyPrice->price ?? null;
        //$product->max_price = $maxPrice ?? 0;

        $this->Products->save($product);
        return $product;

    }

    /**
     * Returns product rental price from any date range
     * @param $product_id
     * @param $variant_product_id
     * @param $start_date
     * @param $end_date
     */
    function getRentalPriceByDates($product_id, $variant_product_id, $start_date, $end_date, $cartItem=[], $option_type='cart', $custom_fields = [], $options=[])
    {
        RentMy::addModel(['VariantsProducts', 'OrderProductOptions']);
        $data = ['variants_products_id' => $variant_product_id, 'product_id' => $product_id, 'rent_start' => $start_date, 'rent_end' => $end_date, 'custom_fields' => $custom_fields, 'price_id'=>$options['price_id'], 'location' => $options['location'] ?? RentMy::$token->location];

        $price = $this->find()->where(['current' => 1])->where(['variants_products_id' => $data['variants_products_id']])->where(['location' => $data['location']])->first();
        $priceType = $price->price_type;
        if ($priceType == 2) { // fixed price
            $price = $this->find()->where(['duration_type' => 'fixed'])->where(['variants_products_id' => $data['variants_products_id']])->where(['location' => $data['location']])->toArray();
            $price = RentMy::formatPricing($price, ['custom_fields' => $custom_fields]);
            $rental_price = $price[0]->price;
            if (RentMy::$storeConfig['inventory']['promo_price']){
                $rental_price = !empty($price[0]->promo_price) ? $price[0]->promo_price : $rental_price;
            }
        } else if ($priceType == 3) { // flat price
            $rental_price = (new Price())->getFlatPrice($data);
        } else if ($priceType == 4) { // flex price
            $rental_price = (new Price())->getFlexPrice($data);
        } else if ($priceType == 5) { // recurring price
            $rental_price = (new Price())->getRecurringPrice($data);
        }

        // check seasonal price data here and update the price thoroughly and return
        $rental_price = $this->seasonalPrice($rental_price, $start_date, $product_id);
        $rental_price = $this->bandPrice($rental_price, $start_date, $end_date, $product_id);

        if (!empty($cartItem)){
            RentMy::$Model['OrderProductOptions']->updatePrice($cartItem->id, $rental_price, $option_type);
        }
        // ends of seasonal pricing
        return $rental_price;
    }

    /**
     * format recurring pricing list
     * if configuration is okay return weekly & monthly pricing when duration is =1
     * @param $prices
     * @return array
     */
    public function getRecurringPriceList($prices)
    {
        $recurring = [];
        $rental = ['before_rental', 'after_rental'];
        if ((RentMy::$storeConfig['arb']['active']) && (in_array(RentMy::$storeConfig['arb']['store_active'], $rental))) {
            foreach ($prices as $key => $allPrices) {
                if ($key != 'base') {
                    foreach ($allPrices as $j => $allPrice) {
                        if (in_array($key, ['daily', 'monthly', 'weekly'])) {
                            $recurring[] = $allPrice;
                        }
                    }
                }
            }
        }
        return $recurring;
    }
}
