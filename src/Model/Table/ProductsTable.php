<?php

namespace App\Model\Table;

use App\Lib\RentMy\RentMy;
use App\Model\Enum\ProductConfig;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;
use Cake\Datasource\EntityInterface;
use Cake\Event\Event;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Utility\Hash;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;

class ProductsTable extends Table
{

    /**
     * Initialize method
     */
    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->setTable('products');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Stores', [
            'foreignKey' => 'store_id',
            'joinType' => 'INNER'
        ]);
        $this->belongsTo('Users', [
            'foreignKey' => 'user_id',
            'joinType' => 'INNER'
        ]);

        $this->hasMany('Images', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);

        $this->hasMany('ProductsSettings', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT',
            'dependent' => true
        ]);
        $this->hasMany('OrderItems', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->hasMany('Prices', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
//        $this->hasMany('Refunds', [
//            'foreignKey' => 'product_id',
//            'joinType' => 'LEFT',
//            'dependent' => true
//        ]);
        $this->hasMany('ProductsAvailabilities', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->hasMany('CartItems', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT'
        ]);

        $this->hasMany('ProductPrices', [
            'foreignKey' => 'product_id',
            'cascadeCallbacks' => true
        ]);
        $this->hasMany('Quantities', [
            'foreignKey' => 'product_id',
            'joinType' => 'LEFT',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->belongsToMany('Categories', [
            'foreignKey' => 'product_id',
            'targetForeignKey' => 'category_id',
            'joinTable' => 'categories_products',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->belongsToMany('Variants', [
            'foreignKey' => 'product_id',
            'targetForeignKey' => 'variant_id',
            'joinTable' => 'variants_products',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);
        $this->belongsToMany('Tags', [
            'foreignKey' => 'product_id',
            'targetForeignKey' => 'tag_id',
            'joinTable' => 'products_tags',
            'dependent' => true,
            'cascadeCallbacks' => true
        ]);

        $this->hasOne('BasePrice', [
            'className' => 'ProductPrices',
            'foreignKey' => 'product_id'
        ]);

        $this->hasOne('DepositeTax', [
            'className' => 'ProductsSettings',
            'foreignKey' => 'product_id'
        ])->setConditions(['p_key' => 'deposite_tax']);

        $this->hasOne('VariantsProducts', [
            'className' => 'VariantsProducts',
            'foreignKey' => 'product_id'
        ]);
        $this->hasMany('pVariants', [
            'className' => 'VariantsProducts',
            'foreignKey' => 'product_id'
        ]);
        $this->hasOne('AttributeChain', [
            'className' => 'VariantsProducts',
            'foreignKey' => 'product_id'
        ]);

        $this->belongsTo('Supplier', [
            'className' => 'Suppliers',
            'foreignKey' => 'supplier_id',
            'joinType' => 'LEFT'
        ]);
        $this->hasOne('ProductsTags', [
            'className' => 'ProductsTags',
            'foreignKey' => 'product_id',
            'bindingKey' => 'id',
            'joinType' => 'LEFT'
        ]);

        // this relation is only for searching if we need
        $this->hasOne('ProductAssets', [
            'className' => 'Assets',
            'foreignKey' => 'product_id',
        ]);
    }

    /**
     * Default validation rules.
     */
    public function validationDefault(Validator $validator)
    {
        $validator
            ->integer('id')
            ->allowEmpty('id', 'create');

        $validator
            ->scalar('uuid')
            ->maxLength('uuid', 255)
            ->allowEmpty('uuid');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmpty('name');


        $validator
            ->scalar('description')
            ->allowEmpty('description');


//        $validator
//            ->integer('supplier_id')
//            ->requirePresence('supplier_id', 'create')
//            ->notEmpty('supplier_id');

        $validator
            ->numeric('sales_tax')
            ->allowEmpty('sales_tax');

//        $validator
//            ->numeric('idw_tax')
//            ->allowEmpty('idw_tax');

        $validator
            ->boolean('deposit_required')
            ->allowEmpty('deposit_required');

        $validator
            ->numeric('deposit_amount')
            ->allowEmpty('deposit_amount');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     */
    public function buildRules(RulesChecker $rules)
    {
        //$rules->add($rules->existsIn(['store_id'], 'Stores'));
        // $rules->add($rules->existsIn(['user_id'], 'Users'));
        //$rules->add($rules->existsIn(['supplier_id'], 'Supplier'));

        return $rules;
    }





//    /**
//     * This Event function is checking client subscription plan and active product count
//     * Free user will show water mark and can add unlimited products.
//     * Starter store can't active more than 10 products && remove watermark
//     * Bronze user can't active more thant 50 products && remove watermark
//     * Silver user can't active more thant 1500 products && remove watermark
//     * @param Event $event
//     * @return bool
//     */
//    public function beforeSave(Event $event)
//    {
//        $entity = $event->getData('entity');
//        if ($entity->status == 1) {
//            $storePlan = strtoupper(RentMy::$store->store_type);
//            $productCount = $this->find()->where(['store_id' => RentMy::$store->id, 'status' => 1])->count();
//            if ($storePlan != 'FREE') {
//                if (($storePlan == 'STARTER') && ($productCount > 10)) {
//                    $entity->status = 2;
//                } elseif (($storePlan == 'BRONZE') && ($productCount > 50)) {
//                    $entity->status = 2;
//                } elseif (($storePlan == 'SILVER') && ($productCount > 1500)) {
//                    $entity->status = 2;
//                }
//            }
//
//
//        }
//        return true;
//    }

    /** Delete asset on product delete */
    function afterDeleteCommit(Event $event)
    {
        $entity = $event->getData('entity');
        $is_tracked = $entity->is_tracked;
        $product_id = $entity->id;
        if ($is_tracked) {
            $assetObj = TableRegistry::getTableLocator()->get('Assets');
            $assetObj->removeAssetsOnProductDelete($product_id);
        }
    }


    /*
     * Get Product Basic Info
     */
    public function getProductBasic($id)
    {
        $commonArr = array('Products.id', 'Products.uuid', 'Products.is_default_weight', 'Products.type', 'Products.variant_set',
            'Products.store_id', 'Products.featured', 'Products.name',  'Products.sequence_no', 'Products.description',
            'Products.driving_license', 'Products.status', 'Products.supplier_id',
            'Products.sales_tax', 'Products.deposit_amount','Products.options',
            'Products.supply_id', 'Products.keyword', 'Products.is_tracked', 'Products.ship_own_box', 'client_specific_id', 'free_shipping', 'Products.url');
        $results = $this->find()
            ->select($commonArr)
            ->where(['Products.id' => $id])
            ->first();
        $results->supplier_name = '';
        if (!empty($results->supplier_id)) {
            $supplierObj = TableRegistry::get('Suppliers');
            $supplier = $supplierObj->get($results->supplier_id);
            $results->supplier_name = $supplier->name;
        }
        $results->variant_set = empty($results->variant_set) ? [] : json_decode($results->variant_set, true);

        $options = empty($results->options) ? [] : json_decode($results->options, true);
       if (!empty($options['exact_time'])){
           $results->exact_date_time = 'exact_time';
       }elseif (!empty($options['exact_date'])){
           $results->exact_date_time = 'exact_date';
       }elseif (!empty($options['exact_time_with_days'])){
           $results->exact_date_time = 'exact_time_with_days';
           RentMy::addModel(['ReferenceProducts']);
           $results['exact_time_ids'] = RentMy::$Model['ReferenceProducts']->find('list', [
               'valueField' => 'reference_id'
           ])->where([
               'reference_type' => 'ExactTime',
               'product_id' => $id,
           ])->toList();
       }else{
           $results->exact_date_time = '';
       }

        if (isset($options['excluded_fulfilment_type'])){
            $results['excluded_fulfilment_type'] = $options['excluded_fulfilment_type'];
        }

        $results->sales_tax = $this->getSalesTaxClass($id);
        $results->enduring_rental = !empty($options['enduring_rental']);
        $results->booking = !empty($options['booking']);

        $v_p = TableRegistry::get('VariantsProducts')->find()
            ->where(['VariantsProducts.set_id' => 1])
            ->where(['VariantsProducts.product_id' => $id])
            ->first();

        $quantity = TableRegistry::get('Quantities')->find();
        $t_quantity = $quantity->select(['quantity' => $quantity->func()->sum('quantity')])->where(['product_id' => $id])->first();
        $height = '';
        $width = '';
        $length = '';
        if (!empty($v_p->volume)) {
            $volume = explode('x', $v_p->volume);
            $height = $volume[0];
            $width = $volume[1];
            $length = $volume[2];
        }
        $asset = TableRegistry::get('Assets')->find()->where(['product_id' => $id, 'status' => 1])->count();
        $results->weight_amount = $v_p->weight_amount ?? '';
        $results->weight_unit = empty($v_p->weight_unit) ? 'pound' : $v_p->weight_unit;
        $results->product_quantity = $t_quantity->quantity;
        $results->height = $height;
        $results->width = $width;
        $results->length = $length;
        $results->product_asset = $asset;

        if (!empty($options['seo'])){
            $results["seo"] = $options['seo'];
        }
        unset($results->options);
        return $results;
    }

    /**
     * Main function getting product details
     *
     * Get Product Details
     */
    public function getProduct($id, $queryParam)
    {
        RentMy::addModel(['ProductsAvailabilities', 'ProductPrices', 'Images', 'Assets', 'ExactTimes', 'Holidays']);
        $location = $queryParam['location'];

        // Step 1 : get product, default variant product , quantity details
        $prodFields = ['Products.id', 'Products.sales_tax', 'Products.deposit_amount', 'Products.name', 'Products.description', 'buy_price', 'rent_price',
            'Products.variant_set', 'Products.driving_license', 'Products.keyword', 'Products.store_id', 'Products.is_tracked','Products.options', 'client_specific_id', 'Products.status', 'Products.options'];
        $conditions = [
            'VariantsProducts.is_last' => 1,
            'Qty.location' => $queryParam['location'],
            'Products.id' => $id
        ];
        if (!empty($queryParam['asset_id'])) {
            $asset = RentMy::$Model['Assets']->find()->where(['id' => $queryParam['asset_id']])->first();
            $conditions = array_merge(array('VariantsProducts.id' => $asset['variants_products_id']), $conditions);
        } else {
            $conditions = array_merge(array('VariantsProducts.is_default' => 1), $conditions);
        }
        $product = $this->find()->select($prodFields)
            ->contain('VariantsProducts', function ($q) {
                return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain',
                    'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                    ->contain('Qty', function ($q) {
                        return $q->select(['Qty.id', 'Qty.quantity', 'Qty.available']);
                    });
            })
//            ->contain('Images', function ($q) {
//                return $q->select([
//                    'Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status',
//                    'Images.variants_products_id'
//                ]);
//            })

            ->where($conditions)
            ->first();

        if (empty($product)) {
            throw new \Exception('Product not found');
        }
        $product['images'] = RentMy::$Model['Images']->image_by_store_plan('product_details', ['product_id' => $product->id])->toArray();
        if ($product) {

            $options = empty($product->options) ? [] : json_decode($product->options, true);

            // Step 2 :  exact date feature - start date & end date calculations
            if (!empty($options['exact_date'])) {
                $exactDates = RentMy::extactDates($product->id);
                $queryParam['start_date'] = !empty($exactDates['start_date']) ? $exactDates['start_date'] : $queryParam['start_date'];
                $queryParam['end_date'] = !empty($exactDates['end_date']) ? $exactDates['end_date'] : $queryParam['end_date'];

                if (!empty($exactDates)) {
                    $product->rent_start = $queryParam['start_date'];
                    $product->rent_end = $queryParam['end_date'];
                    $product->exact_date = true;
                }
            }

            // Step 3 : exact time feature - fetch durations and stat time for those duration
            $product->extact_durations = [];
            if (!empty($options['exact_time'])) {
                $product->extact_durations = RentMy::exactTimes('', $product->id);
                if (!empty($product->extact_durations) && !empty($product->extact_durations['times'])){
                    $product->exact_time = true;
                }

            }elseif (!empty($options['exact_time_with_days'])){
                $product['exact_time_with_days'] = true;
            }

            $product['booking'] = !empty($options['booking']);
            if ($product['booking']){
                $bookingConfig = Configure::read('booking');
                $product['booking_config'] = $bookingConfig[RentMy::$store->id];
                if (empty($product['booking_config']))
                    $product['booking_config'] = ['parent' => 1, 'child' => 1];
            }



            // Step 4 : format variant sets & attribute list
            $variant_sets = json_decode($product->variant_set);
            if (empty($product->variant_set) || $variant_sets[0] == 1) {
                $product->variant_list = [];
                $product->variant_set_list = [];
            } else {
                $product->variant_list = $this->_getParentAttributeList($location, $product->id, $variant_sets, $queryParam);
                $product->variant_set_list = $this->_getVariantsets($variant_sets);
            }

            // Step 5 : get and format price list
            $product->prices = RentMy::$Model['ProductPrices']->getPriceDetails($product->variants_product);

            $i = 0;
            $isBuy = false;
            $recurring = [];
            $priceType = 0;
            foreach ($product->prices[0] as $key => $allPrices) {

                if ($key == 'base') {
                    $isBuy = !empty($allPrices['id']) ? true : false;
                } elseif ($key == 'fixed'){
                    $initial['rent_start'] = $allPrices['rent_start'];
                    $initial['rent_end'] = $allPrices['rent_end'];
                }else {
                    foreach ($allPrices as $j => $allPrice) {
                        $priceType = $allPrice['price_type'];
                        if ($i == 0 & $j == 0) {
                            $initial['rent_start'] = $allPrice['rent_start'];
                            $initial['rent_end'] = $allPrice['rent_end'];
                        }
                    }
                    $i++;
                }
            }

            $product->enduring_rental = !empty($options['enduring_rental']) && $priceType == 5;
            // Step 6 : get recurring pricing if config is activated
            if ($product->enduring_rental) {
                $recurring = RentMy::$Model['ProductPrices']->getRecurringPriceList($product->prices[0]);
            }

            if (!empty($recurring)) {
                $product->recurring_prices = $recurring;
            }


            // Step 7 : format default variant product details
            $defaultAttribute = empty($product->variants_product) == true ? [] : $this->_getDefaultAttribute($product->variants_product->id);

            // Step 8 : Product availabilities

            // cart availability
            $product->cart_available = TableRegistry::getTableLocator()->get('CartItems')->available($queryParam['token'], $product->variants_product->qty->id);
            if (!empty($product->variants_product)) {
                if ($isBuy) { // buy availabilty
                    $buy_start_date = RentMy::toStoreTimeZone(Time::now(), 'Y-m-d H:i:00');
                    $buy_end_date = RentMy::toStoreTimeZone(Time::now()->addDays(14), 'Y-m-d H:i:00');
                    $product->available_for_sale = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($product->id, $product->variants_product->id, $location, $buy_start_date, $buy_end_date);
                    $product->available_for_sale = $product->available_for_sale - $product->cart_available;
                }
                $defaultAttribute['quantity'] = empty($product->variants_product->qty) ? ['quantity' => 0] : $product->variants_product->qty;

                if (!empty($queryParam['start_date']) && !empty($queryParam['end_date'])) { // exact date & token start & end date
                    $rentalDates = RentMy::formatRentalDates($queryParam['start_date'], $queryParam['end_date'], $product);
                    $product->rental_price = RentMy::$Model['ProductPrices']->getRentalPriceByDates($product->id, $product->variants_product->id, $rentalDates['start_date'], $rentalDates['end_date']);
                    $product->available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($product->id, $product->variants_product->id, $location, RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i'), RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i'));
                } else { // default
                    $rent_start = RentMy::toUTC($initial['rent_start'], 'Y-m-d H:i') ?? Time::now()->format('Y-m-d H:i');
                    $rent_end = RentMy::toUTC($initial['rent_end'], 'Y-m-d H:i') ?? Time::now()->addDays(14)->format('Y-m-d H:i');

                    $product->available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($product->id, $product->variants_product->id, $location, $rent_start, $rent_end);
                }
                $product->available = $product->available - $product->cart_available;

            }
            unset($product->variants_product->qty->available);
            $product->default_variant = $defaultAttribute;

            // Step 9  : Product images
            $images = array();
            foreach ($product->images as $img) {
                if ($img->variants_products_id == $product->variants_product['id']) {
                    $images[] = array('image_large' => $img->image_large, 'image_small' => $img->image_small, 'status' => $img->status);
                }
            }
            if (empty($images)) {
                foreach ($product->images as $img) {
                    if (empty($images) && (!empty($img->image_large) || !empty($img->image_small))) {
                        $images[] = array('image_large' => $img->image_large, 'image_small' => $img->image_small, 'status' => $img->status);
                    }
                }
            }
            $product->images = $images;
            $product->client_specific_id = !empty($product->client_specific_id) ? $product->client_specific_id : $product->id;
            $product = $product->toArray();
            unset($product['variants']);
            unset($product['variants_product']);

            if (!empty($options['seo'])){
                $product["seo"] = $options['seo'];
                if (!empty($product['seo']['meta_title'])){
                    RentMy::addModel(['Stores']);
                    $product['seo']['meta_title'] = RentMy::$Model['Stores']->parsingSiteName(RentMy::$store->slug, $product['seo']['meta_title']);
                }
            }

            if (!empty($options['exact_time_with_days'])){
                RentMy::addModel(['ReferenceProducts']);
                $options['exact_time_ids'] = RentMy::$Model['ReferenceProducts']->find('list', [
                    'valueField' => 'reference_id'
                ])->where([
                    'reference_type' => 'ExactTime',
                    'product_id' => $product['id'],
                ])->toList();

            }
            if (!empty($options['exact_time_ids'])){
                RentMy::addModel(['ExactTimes']);
                $dayObj = RentMy::$Model['ExactTimes']
                    ->find()
                    ->select(['days', 'start_date', 'end_date'])
                    ->where(['id IN' => $options['exact_time_ids']]);
                $days = $dayObj->reduce(function ($days, $exactTime) {
                        if (!empty($exactTime['days'])) {
                            return array_merge($days, json_decode($exactTime['days']));
                        }
                        return $days;
                    }, []);

                $validDate = $dayObj->map(function ($day){
                    if (!empty($day['start_date'])){
                        return [
                            'start_date' => Time::parse($day['start_date'])->format("Y-m-d"),
                            'end_date' => Time::parse($day['end_date'])->format("Y-m-d"),
                        ];
                    }
                    return null;

                })->filter()->toArray();
                $product['est_available_days'] = array_values(array_unique($days));
                $product['est_valid_dates'] = $validDate;
            }

            $product['band_pricing'] = RentMy::$Model['Holidays']->getBandPricing($product['id']);

            return $product;
        } else {
            return false;
        }

    }


    /**
     * Get Product Availability for single tracked and untracked items. We need to delete this function and use
     * productavailabilities original one.
     * @param $product object
     * @param $quantity object
     * @param null $dateTime start time
     * @return int - available quantity
     *
     * @deprecated
     */
    public function _quantityAvailable($product, $quantity, $dateTime = null)
    {
        $data = array(
            'product_id' => $product->id,
            'quantity_id' => $quantity->id,
        );

        if (empty($dateTime)) {
            $date = RentMy::toUTC(RentMy::getDefaultStartDate(Time::parse('now')->format('Y-m-d')), 'Y-m-d H:i');
        } else {
            $date = $dateTime;
        }
        $productAvObj = TableRegistry::get('ProductsAvailabilities');
        if ($product->is_tracked == 0) { // untracked items
            $rentalQuantity = $productAvObj->_getAvailableQuantity($data, $date);
            $p_quantity = $quantity->quantity;
            $quantityAvailable = $p_quantity - $rentalQuantity;
        } else {  // asset
            $assignedAssets = $productAvObj->_getAssignedAssets($data, $date);
            $p_quantity = $quantity->available;
            $quantityAvailable = $p_quantity - $assignedAssets;
        }
        $quantityAvailable = (is_numeric($quantityAvailable) && ($quantityAvailable > 0)) ? $quantityAvailable : 0;
        return $quantityAvailable;
    }

    public function priceFormatting($prices)
    {
        $data = array();
        $hourly_price = array();
        $daily_price = array();
        $weekly_price = array();
        foreach ($prices as $i => $price) {
            if ($i == 0) {
                $data[$price['variants_products_id']]['price'] = $price['price'];
            }
            if ($price['hourly_price'] != null) {
                $pData = array();
                $pData['price'] = $price['hourly_price'];
                $pData['duration'] = $price['hourly_duration'];
                $data[$price['variants_products_id']]['hourly'][] = $hourly_price[$price['variants_products_id']]['hourly'][] = $pData;
            }
            if ($price['daily_price'] != null) {
                $pData = array();
                $pData['price'] = $price['daily_price'];
                $pData['duration'] = $price['daily_duration'];
                $data[$price['variants_products_id']]['daily'][] = $daily_price[$price['variants_products_id']]['daily'][] = $pData;
            }
            if ($price['weekly_price'] != null) {
                $pData = array();
                $pData['price'] = $price['weekly_price'];
                $pData['duration'] = $price['weekly_duration'];
                $data[$price['variants_products_id']]['weekly'][] = $weekly_price[$price['variants_products_id']]['weekly'][] = $pData;
            }
        }
        $allPrice = array();
        foreach ($data as $k => $v) {
            $productPrice = array();
            $productPrice['variants_products_id'] = $k;
            $productPrice['price'] = empty($v['price']) ? 0 : $v['price'];
            $productPrice['hourly'] = empty($v['hourly']) ? [] : $v['hourly'];
            $productPrice['daily'] = empty($v['daily']) ? [] : $v['daily'];
            $productPrice['weekly'] = empty($v['weekly']) ? [] : $v['weekly'];
            $allPrice[] = $productPrice;
        }
        return $allPrice;
    }

    /**
     * Get variant default change data
     * @param $variants_products_id
     * @return array
     */
    public function _getDefaultAttribute($variants_products_id)
    {
        RentMy::addModel(['Variants', 'VariantsProducts']);
        $variant = RentMy::$Model['VariantsProducts']->get($variants_products_id);

        $attrName = array();
        if (!empty($variant->chain)) {
            $chain = explode('-', $variant->chain);
            foreach ($chain as $v) {
                $attrName[] = RentMy::$Model['Variants']->get($v)->name;
            }
        }
        $attrName = implode('-', $attrName);
        $data = array();
        $data['barcode'] = $variant->barcode;
        $data['price_type'] = $variant->price_type;
        $data['variant_chain_id'] = $variant->chain;
        $data['variant_chain_name'] = $attrName;
        $data['variants_products_id'] = $variants_products_id;
        return $data;
    }

    public function _getParentAttributeList($location, $pId, $setIds, $queryParam = [])
    {
        $attrProdObj = TableRegistry::get('VariantsProducts');
        $condPVariants = array();
        // $condPVariants = array_merge(array('location' => $location), $condPVariants);
        $condPVariants = array_merge(array('product_id' => $pId), $condPVariants);
        if (!empty($queryParam['asset_id'])) {
            $asset = RentMy::$Model['Assets']->find()->where(['id' => $queryParam['asset_id']])->first();
            $condPVariants = array_merge(array('id' => $asset['variants_products_id']), $condPVariants);
        } else {
            $condPVariants = array_merge(array('is_default' => 1), $condPVariants);
        }
        $condPVariants = array_merge(array('is_last' => 1), $condPVariants);
        $defaultAttribute = $attrProdObj->find()
            ->where($condPVariants)
            ->first();
        $chain = explode('-', $defaultAttribute->chain);
        //array_shift($chain);
        $attribteArr = array();
        $t = 0;
        $chainMatch = array();
        foreach ($setIds as $setId) {
            $options = array();
            $options = array_merge(array('product_id' => $pId), $options);
            $options = array_merge(array('set_id' => $setId), $options);

            if ($t > 0) {
                $chainMatch[] = $chain[$t - 1];
                $chainArr = implode('-', $chainMatch);
                $options = array_merge(array('chain LIKE' => '%' . $chainArr . '%'), $options);
            }

            $variants = $attrProdObj->find()
                ->select(['VariantsProducts.id', 'VariantsProducts.barcode', 'VariantsProducts.chain', 'VariantsProducts.sequence_no'])
                ->contain([
                    'VariantSets' => function ($q) {
                        return $q->select(['VariantSets.id', 'VariantSets.name']);
                    },
                    'Variants' => function ($q) {
                        return $q->select(['Variants.id', 'Variants.name']);
                    }
                ])
                ->where($options)
                ->order(['VariantsProducts.sequence_no' => 'ASC'])
                ->toArray();

//            if (!empty($location)) {
//                $variantList = array();
//                foreach ($variants as $variant) {
//                    $a_chain = explode('-', $variant->chain);
//                    if ($location == $a_chain[0]) {
//                        $variantList[] = $variant;
//                    }
//                }
//                $variants = $variantList;
//            }
            $attrs = array();
            $check = array();
            foreach ($variants as $variant) {
                if (!in_array($variant->variant->id, $check)) {
                    $aData = array();
                    $check[] = $aData['id'] = $variant->variant->id;
                    $aData['name'] = $variant->variant->name;
                    $aData['barcode'] = $variant->barcode;
                    $aData['index'] = $t;
                    $aData['selected'] = in_array($variant->variant->id, $chain) == true ? true : false;
                    $aData['location'] = $variant->location;
                    $aData['variant_set_id'] = $variant->variant_set->id;
                    $aData['sequence_no'] = $variant->sequence_no;
                    $attribteArr[] = $aData;
                }
            }
            //$attribteArr[] = $attrs;
            $t++;
        }
        return $attribteArr;
    }

    public function _getVariantsets($sets)
    {
        $attrSetObj = TableRegistry::get('VariantSets');
        $data = array();
        foreach ($sets as $set) {
            $attrSet = $attrSetObj->get($set);
            $data[] = array('id' => $attrSet->id, 'name' => $attrSet->name);

        }
        return $data;
    }

    /**
     * @desc Products listing for front end/user section
     * @param type $page
     * @param type $limit
     * @param type $conditionsCategory
     * @param type $conditionsProduct
     * @param type $conditionsVariants
     * @return type
     */
    public function userGetProducts($page = 1, $limit = 10, $conditionsCategory = null, $conditionsTag = null, $conditionsProduct = null, $conditionsVariants = null)
    {
        $products = [];
        $productIds = $this->_getProductIds($page, $limit, $conditionsCategory, $conditionsTag, $conditionsProduct, $conditionsVariants);

        if (!empty($productIds)) {
            $notInCacheProductIds = $this->_productsNotInCache($productIds);
            $newlyFetchedProducts = [];
            if (!empty($notInCacheProductIds)) {
                $newlyFetchedProducts = $this->_fetchShortDetails($notInCacheProductIds);
            }
            $products = array_merge($products, $newlyFetchedProducts);
            $inCacheProductIds = array_diff($productIds, $notInCacheProductIds);
            if (!empty($inCacheProductIds)) {
                $fromCache = $this->_getShortDetailsFromCache($inCacheProductIds);
                $products = array_merge($products, $fromCache);
            }
        }

        return $products;
    }

    /**
     * @desc Get product ids by various filtering options for product listing in front section
     * @param type $page
     * @param type $limit
     * @param type $conditionsCategory
     * @param type $conditionsProduct
     * @param type $conditionsVariants
     * @return type
     */
    protected function _getProductIds($page = 1, $limit = 10, $conditionsCategory = null, $conditionsTag = null, $conditionsProduct = null, $conditionsVariants = null)
    {
        $offset = ($page * $limit) - $limit;
        $productIds = [];
        if ($conditionsVariants && $conditionsCategory && $conditionsTag) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->matching('Categories', function ($q) use ($conditionsCategory) {
                    return $q->where($conditionsCategory);
                })
                ->matching('Tags', function ($q) use ($conditionsTag) {
                    return $q->where($conditionsTag);
                })
                ->matching('Variants', function ($q) use ($conditionsVariants) {
                    return $q->where($conditionsVariants);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else if ($conditionsVariants && $conditionsCategory) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->matching('Categories', function ($q) use ($conditionsCategory) {
                    return $q->where($conditionsCategory);
                })
                ->matching('Variants', function ($q) use ($conditionsVariants) {
                    return $q->where($conditionsVariants);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else if ($conditionsVariants && $conditionsTag) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->matching('Tags', function ($q) use ($conditionsTag) {
                    return $q->where($conditionsTag);
                })
                ->matching('Variants', function ($q) use ($conditionsVariants) {
                    return $q->where($conditionsVariants);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else if ($conditionsCategory && $conditionsTag) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->matching('Categories', function ($q) use ($conditionsCategory) {
                    return $q->where($conditionsCategory);
                })
                ->matching('Tags', function ($q) use ($conditionsTag) {
                    return $q->where($conditionsTag);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else if ($conditionsCategory) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->matching('Categories', function ($q) use ($conditionsCategory) {
                    return $q->where($conditionsCategory);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else if ($conditionsTag) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->matching('Tags', function ($q) use ($conditionsTag) {
                    return $q->where($conditionsTag);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else if ($conditionsVariants) {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain(['BasePrice'])
                ->matching('Variants', function ($q) use ($conditionsVariants) {
                    return $q->where($conditionsVariants);
                })
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        } else {
            $productIds = $this->find('list', ['keyField' => 'id', 'valueField' => 'id'])
                ->contain('BasePrice')
                ->where($conditionsProduct)
                ->offset($offset)
                ->limit($limit)
                ->order(['Products.created'])
                ->toList();
        }
        return $productIds;
    }

    /**
     * @desc Check whether the given
     * @param type $products
     * @return array
     */
    protected function _productsNotInCache($productIds = array())
    {
        $notInCacheProductIds = [];
        if (!empty($productIds)) {
            foreach ($productIds as $id) {
                $found = \Cake\Cache\Cache::read('Product.short_details.' . $id, 'Boot');
                if (!$found) {
                    $notInCacheProductIds[] = $id;
                }
            }
        }
        return $notInCacheProductIds;
    }

    /**
     * @desc Fetch products short details from db and store it in cache
     * @param type $productIds
     * @return type
     */
    protected function _fetchShortDetails($productIds = array())
    {
        $products = $this->find('all')
            ->select(['Products.id', 'Products.available', 'Products.sales_tax', 'Products.deposit_amount', 'Products.store_id', 'Products.url', 'Products.uuid', 'Products.name', 'Products.variants_products_id'])
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.variants_products_id']);
            })
            ->contain('Prices', function ($q) {
                return $q->select(['Prices.id', 'Prices.product_id', 'Prices.price', 'Prices.variants_products_id']);
            })
            ->contain('ProductsAvailabilities', function ($q) {
                return $q->select(['ProductsAvailabilities.id', 'ProductsAvailabilities.product_id', 'ProductsAvailabilities.start_date', 'ProductsAvailabilities.end_date', 'ProductsAvailabilities.quantity']);
            })
            ->where(['Products.id IN' => $productIds])
            ->toArray();

        foreach ($products as $product) {
            $a_price = array();
            $a_image = array();
            $defaultAttribute = array();
            if (!empty($product->variants_products_id)) {
                // get default prices
                foreach ($product->prices as $price) {
                    if ($product->variants_products_id == $price->variants_products_id) {
                        $a_price[] = $price;
                    }
                }
                // get default images
                foreach ($product->images as $image) {
                    if ($product->variants_products_id == $image->variants_products_id) {
                        $a_image[] = $image;
                    }
                }
                $defaultAttribute = $this->_getDefaultAttribute($product);
            }
            $product->prices = $a_price;
            $product->images = $a_image;
            $product->default_attribute = $defaultAttribute;
        }

        if (!empty($products)) {
            foreach ($products as $product) {
                $product->sales_tax = empty($product->sales_tax) == true ? Configure::read('taxValue') : $product->sales_tax;
                $encoded = json_encode($product);
                if ($encoded) {
                    \Cake\Cache\Cache::write('Product.short_details.' . $product->id, $encoded, 'Boot');
                }
            }
        }

        return $products;
    }

    public function getProductsPriceImage($products = array())
    {
        foreach ($products as $product) {
            $images = array();
            $defaultAttribute = array();
            if (!empty($product->variants_product->id)) {
                // get default images
                foreach ($product->images as $img) {
                    if ($img->variants_products_id == $product->variants_product->id) {
                        $images[] = $img;
                    }
                }
                if (empty($images)) {
                    $noImage = true;
                    foreach ($product->images as $img) {
                        if ($img->status == 2 && $noImage) {
                            $images[] = $img;
                            $noImage = false;
                        }
                    }
                }
                $defaultAttribute = $this->_getDefaultAttribute($product->variants_product->id);
                $defaultAttribute['quantity'] = $product->variants_product->qty;
            }
            $product->prices = TableRegistry::get('ProductPrices')->priceDetails($product->variants_product);
            $product->images = $images;
            $product->default_variant = $defaultAttribute;
        }
        return $products;
    }

    /**
     *
     * @param type $inCacheProductIds
     * @return type
     */
    protected function _getShortDetailsFromCache($inCacheProductIds = array())
    {
        $products = [];
        foreach ($inCacheProductIds as $id) {
            $found = \Cake\Cache\Cache::read('Product.short_details.' . $id, 'Boot');
            if ($found) {
                $products[] = json_decode($found);
            }
        }
        return $products;
    }

    /*
     * \Get All Products
     */
    public function getProductIds($options = null, $cOptions = null, $sOptions = null, $orderBy, $order)
    {
        if ($cOptions != null && $sOptions != null) {
            $productIds = $this->searchBySupplierAndCategory($options, $cOptions, $sOptions, $orderBy, $order);
        } else if ($cOptions != null && $sOptions == null) {
            $productIds = $this->searchByCategory($options, $cOptions, $orderBy, $order);
        } else if ($cOptions == null && $sOptions != null) {
            $productIds = $this->searchBySupplier($options, $sOptions, $orderBy, $order);
        } else {
            $productIds = $this->searchByProduct($options, $orderBy, $order);
        }
        return $productIds;
    }

    /*
     * \Get All Products
     * Admin inventory Listing page .
     */
    public function getProducts($options = null, $pageNo = null, $limit = null, $model = null, $cOptions = null, $sOptions = null, $orderBy, $order)
    {
        if ($cOptions != null && $sOptions != null) {
            $productIds = $this->searchBySupplierAndCategory($options, $cOptions, $sOptions, $orderBy, $order);
            $total = count($productIds);
            $products = $total == 0 ? 0 : $this->productList($model, $productIds, $pageNo, $limit, $orderBy, $order);
            $data = array(
                'total' => $total,
                'products' => $products,
            );
        } else if ($cOptions != null && $sOptions == null) {
            $productIds = $this->searchByCategory($options, $cOptions, $orderBy, $order);
            $total = count($productIds);
            $products = $total == 0 ? 0 : $this->productList($model, $productIds, $pageNo, $limit, $orderBy, $order);
            $data = array(
                'total' => $total,
                'products' => $products,
            );
        } else if ($cOptions == null && $sOptions != null) {
            $productIds = $this->searchBySupplier($options, $sOptions, $orderBy, $order);
            $total = count($productIds);
            $products = $total == 0 ? 0 : $this->productList($model, $productIds, $pageNo, $limit, $orderBy, $order);
            $data = array(
                'total' => $total,
                'products' => $products,
            );
        } else {
            $products = $this->searchByProduct($options, $orderBy, $order, $pageNo, $limit);

            $data = array(
                'total' => $products['total'],
                'products' => $products['data'],
            );
        }


        return $data;
    }

    private function productList($model, $productIds = array(), $pageNo, $limit, $orderBy, $order)
    {
        $offset = ($pageNo - 1) * $limit;
        $options = array();
        pr($productIds);
        if (!empty($productIds)) {
            $options = array_merge(array('Products.id IN' => $productIds), $options);
        }

        $containArr = array('Users');
        $selectedArr = array('Products.id', 'Products.uuid', 'Products.store_id', 'Products.name', 'Products.status', 'Products.url',  'Products.variants_products_id');
        $userArr = array('Users.id', 'Users.first_name', 'Users.last_name', 'Users.email');
        $priceArr = array('Prices.product_id', 'Prices.price', 'Prices.hourly_price', 'Prices.daily_price', 'Prices.weekly_price', 'Prices.hourly_duration', 'Prices.daily_duration', 'Prices.weekly_duration');
        $imageArr = array('Images.id', 'Images.product_id', 'Images.image_small', 'Images.variants_products_id');
        if (!empty($model)) {
            $results = $this->getModelName($model, $selectedArr, $userArr, $priceArr);
            $containArr = $results['containArr'];
            $selectedArr = $results['selectedArr'];
        }
        $results = $this->find()
            ->select($selectedArr)
            //->contain($containArr)
//            ->contain([
//                    'Prices' => function ($q) use ($priceArr) {
//                        return $q->select($priceArr);
//                    }]
//            )
//            ->contain(
//                'Images', function ($q) use ($imageArr) {
//                return $q->select($imageArr)->where(['Images.status' => 2]);
//            }
//            )
//            ->contain(
//                'Users', function ($q) use ($userArr) {
//                return $q->select($userArr);
//            }
//            )
//            ->contain(
//                'Categories', function ($q) {
//                return $q->select(['Categories.id', 'Categories.name']);
//            }
//            )
            ->contain(
                'Suppliers', function ($q) {
                return $q->select(['Suppliers.id', 'Suppliers.name', 'ProductsSuppliers.supply_id']);
            }
            )
//            ->contain('Tags', function ($q) {
//                return $q->select(['Tags.id', 'Tags.name']);
//            })
            ->contain('AttributeChain', function ($q) {
                return $q
                    ->contain([
                        // 'Images',
                        // 'BasePrice'
                    ]);
                // ->where(['is_default' => 1, 'is_last' => 1]);
                // ->limit(1);
            })
            ->where($options)
            // ->group(['Products.id'])
            ->offset($offset)
            ->limit($limit)
            ->order([$orderBy => $order])
            ->toArray();
        $data = array();
//        pr(ConnectionManager::get('default')->getLogger());
//        exit();
        foreach ($results as $row) {
            $formated = array();
            $price = array();
            $base_price = empty($row->attribute_chain[0]['base_price']) ? array() : $row->attribute_chain[0]['base_price'];
            if (!empty($base_price)) {
                $price = array(
                    'hourly_price' => $base_price['hourly_price'],
                    'daily_price' => $base_price['daily_price'],
                    'weekly_price' => $base_price['weekly_price'],
                    'hourly_duration' => $base_price['hourly_duration'],
                    'daily_duration' => $base_price['daily_duration'],
                    'weekly_duration' => $base_price['weekly_duration'],
                    'base_price' => $base_price['price']
                );
            }
            $image = empty($row->attribute_chain[0]['image']['image_small']) ? '' : $row->attribute_chain[0]['image']['image_small'];
            $tags = array();
//            if (!empty($row->tags)) {
//                foreach ($row->tags as $tag) {
//                    $tagData = array();
//                    $tagData['id'] = $tag->id;
//                    $tagData['tag_name'] = $tag->name;
//                    $tags[] = $tagData;
//                }
//            }
            $formated['id'] = $row->id;
            $formated['uuid'] = $row->uuid;
            $formated['store_id'] = $row->store_id;
            $formated['name'] = $row->name;
            $formated['url'] = $row->url;

            $formated['status'] = $row->status;
            $formated['price'] = $price;
            //$image = '';
//            foreach ($row->images as $img) {
//                if ($img->a == 1) {
//                    $image = $img->image_small;
//                }
//            }
            $barcode = 0;
            //  foreach ($row->attribute_chain as $chain) {
            //  if ($row->variants_products_id == $chain->id) {
            $quantity = empty($row->attribute_chain[0]['quantity']) ? 0 : $row->attribute_chain[0]['quantity'];
            $barcode = empty($row->attribute_chain[0]['barcode']) ? '' : $row->attribute_chain[0]['barcode'];
            //   }
            // }
            $formated['image'] = $image;
            $formated['quantity'] = $quantity;
            $formated['barcode'] = $barcode;
            //$formated['image'] = empty($row->images) == true ? '' : $row->images[0]['image_small'];
            $formated['supplier']['name'] = empty($row->suppliers) == true ? '' : $row->suppliers[0]->name;
            $formated['category']['name'] = empty($row->categories) == true ? '' : $row->categories[0]->name;
            $formated['tag'] = $tags;

            $data[] = $formated;
        }
        return $data;
    }

    /*
     * search By Category
     */
    public function searchByCategory($options, $cOptions, $orderBy, $order)
    {
        $results = $this->find()
            ->select(['id'])
            ->contain(['BasePrice', 'Images', 'Suppliers', 'Tags'])
            ->matching(
                'Categories', function ($q) use ($cOptions) {
                return $q->select(['Categories.id', 'Categories.name'])->where($cOptions);
            }
            )
            ->where($options)
            ->order([$orderBy => $order])
            ->toArray();
        $data = array();
        foreach ($results as $row) {
            $data[] = $row->id;
        }
        return $data;
    }

    /*
     * search By Category
     */
    public function searchByProduct($options, $orderBy, $order, $page_no = 1, $limit = 10)
    {

        $offset = ($page_no - 1) * $limit;
        $selectedArr = array('Products.id', 'Products.uuid', 'Products.store_id', 'Products.name', 'Products.status', 'Products.url', 'Products.variants_products_id');
        $priceArr = array('Prices.product_id', 'Prices.price', 'Prices.hourly_price', 'Prices.daily_price', 'Prices.weekly_price', 'Prices.hourly_duration', 'Prices.daily_duration', 'Prices.weekly_duration', 'Prices.variants_products_id');
        $imageArr = array('Images.id', 'Images.product_id', 'Images.image_small', 'Images.variants_products_id');
        if (!empty($model)) {
            $results = $this->getModelName($model, $selectedArr, $priceArr);
            $containArr = $results['containArr'];
            $selectedArr = $results['selectedArr'];
        }
        $options = array_merge(array('AttributeChain.is_default' => 1, 'AttributeChain.is_last' => 1), $options);
        $pResults = $this->find()
            //   ->select($selectedArr)
            ->contain(['ProductsSuppliers'])
            ->contain('AttributeChain')
            //, function ($q) {
            //return $q
            //->contain(['Images','BasePrice'])
            //})
            ->where($options)
            ->group(['Products.id']);
        $results = $pResults
            ->offset($offset)
            ->limit($limit)
            ->order([$orderBy => $order])
            ->toArray();
        $counts = $pResults->count();
        $data = array();
        //  pr(ConnectionManager::get('default')->getLogger());
        //pr($results);
        foreach ($results as $i => $row) {
            $attr_ids[] = $row['attribute_chain']['id'];
        }
        $imagesObj = TableRegistry::get('Images');
        $pricesObj = TableRegistry::get('Prices');
        $images = $imagesObj->find()->select()->where(['variants_products_id IN' => $attr_ids])->toArray();
        $prices = $pricesObj->find()->select()->where(['variants_products_id IN' => $attr_ids])->toArray();
        //pr($prices);
        foreach ($results as $row) {
            $formated = array();
            $price = array();
            foreach ($prices as $mprice) {
                if ($row['attribute_chain']['id'] == $mprice['variants_products_id']) {
                    $base_price = $mprice;
                }
            }
            $image = '';
            foreach ($images as $mimage) {
                if ($row['attribute_chain']['id'] == $mimage['variants_products_id']) {
                    $image = $mimage['image_small'];
                }
            }
            //$base_price = empty($row->attribute_chain['base_price']) ? array() : $row->attribute_chain['base_price'];
            if (!empty($base_price)) {
                $price = array(
                    'daily_price' => $base_price['daily_price'],
                    'daily_duration' => $base_price['daily_duration'],
                    'hourly_duration' => $base_price['hourly_duration'],
                    'hourly_price' => $base_price['hourly_price'],
                    'weekly_duration' => $base_price['weekly_duration'],
                    'weekly_price' => $base_price['weekly_price'],
                    'price' => $base_price['price']
                );
            }
            $tags = array();
            $formated['id'] = $row->id;
            $formated['uuid'] = $row->uuid;
            $formated['store_id'] = $row->store_id;
            $formated['name'] = $row->name;
            $formated['url'] = $row->url;

            $formated['status'] = $row->status;
            $formated['price'] = $price;
            //$image = '';
//            foreach ($row->images as $img) {
//                if ($img->a == 1) {
//                    $image = $img->image_small;
//                }
//            }
            $barcode = 0;
            //  foreach ($row->attribute_chain as $chain) {
            //  if ($row->variants_products_id == $chain->id) {
            $quantity = empty($row->attribute_chain['quantity']) ? 0 : $row->attribute_chain['quantity'];
            $barcode = empty($row->attribute_chain['barcode']) ? '' : $row->attribute_chain['barcode'];
            //   }
            // }
            $formated['image'] = $image;
            $formated['quantity'] = $quantity;
            $formated['barcode'] = $barcode;
            //$formated['image'] = empty($row->images) == true ? '' : $row->images[0]['image_small'];
            $formated['supplier']['name'] = empty($row->suppliers) == true ? '' : $row->suppliers[0]->name;
            $formated['category']['name'] = empty($row->categories) == true ? '' : $row->categories[0]->name;
            $formated['tag'] = $tags;

            $data[] = $formated;
        }
        return array('data' => $data, 'total' => $counts);
    }

    /*
     * search By Supplier
     */
    private function searchBySupplier($options, $sOptions, $orderBy, $order)
    {
        $results = $this->find()
            ->select(['id'])
            ->matching(
                'Suppliers', function ($q) use ($sOptions) {
                return $q->select(['Suppliers.id', 'Suppliers.name', 'ProductsSuppliers.supplier_id'])->where($sOptions);
            }
            )
            ->where($options)
            ->order([$orderBy => $order])
            ->toArray();
        $data = array();
        foreach ($results as $row) {
            $data[] = $row->id;
        }
        return $data;
    }

    /*
     * search By Supplier And Category
     */
    private function searchBySupplierAndCategory($options, $cOptions, $sOptions, $orderBy, $order)
    {
        $results = $this->find()
            ->select(['id'])
            ->matching(
                'Categories', function ($q) use ($cOptions) {
                return $q->select(['Categories.id', 'Categories.name'])->where($cOptions);
            }
            )
            ->matching(
                'Suppliers', function ($q) use ($sOptions) {
                return $q->select(['Suppliers.id', 'Suppliers.name', 'ProductsSuppliers.supplier_id'])->where($sOptions);
            }
            )
            ->where($options)
            ->order([$orderBy => $order])
            ->toArray();
        $data = array();
        foreach ($results as $row) {
            $data[] = $row->id;
        }
        return $data;
    }

    /*
     * front section search By Category,searh params
     */
    public function searchByName($location, $selectedArr, $priceArr, $imageArr, $containArr, $options, $cOptions, $sOptions, $offset, $limit, $orderBy, $order, $search='')
    {
        $results = $this->find()
            ->select($selectedArr)
            ->contain($containArr)
            ->contain('VariantsProducts', function ($q) {
                return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode'])
                    ->contain('Qty', function ($q) {
                        return $q->select(['Qty.id', 'Qty.variants_products_id', 'Qty.location', 'Qty.quantity']);
                    });

                })
                ->contain(
                    'ProductPrices', function ($q) use ($priceArr) {
                    return $q->select($priceArr);
                }
                )
                ->contain(
                    'Images', function ($q) use ($imageArr) {
                    return $q->select($imageArr)->where(['Images.status' => 2]);
                }

                )
//            ->contain('Categories', function ($q) use ($cOptions) {
//                return $q->select(['Categories.id', 'Categories.name'])->where($cOptions);
//            }
//            )
            ->where($options)
            ->where(['VariantsProducts.is_default' => 1, 'VariantsProducts.is_last' => 1])
            ->where(['Qty.location' => $location])
            ->group(['Products.id'])
            ->offset($offset)
            ->limit($limit)
            ->order([$orderBy => $order])
            ->toArray();

        $data = array();

        foreach ($results as $row) {
            $formated = array();
            $a_price = array();
            $a_image = array();
            $defaultAttribute = array();
            if (!empty($row->variants_product->id)) {
                foreach ($row->product_prices as $price) {
                    if ($row->variants_product->id == $price->variants_products_id) {
                        $a_price[] = $price;
                    }
                }
                foreach ($row->images as $image) {
                    if ($row->variants_product->id == $image->variants_products_id) {
                        $a_image[] = $image;
                    }
                }
                $defaultAttribute = $this->_getDefaultAttribute($row->variants_product->id);
                $defaultAttribute['quantity'] = $row->variants_product->qty;
            }
            $formated['id'] = $row->id;
            $formated['url'] = $row->url;
            $formated['uuid'] = $row->uuid;
            $formated['store_id'] = $row->store_id;
            $formated['name'] = $row->name;
            $formated['type'] = $row->type;
            $formated['status'] = $row->status;
            $formated['price'] = empty($a_price) == true ? 0 : $a_price[0];
            $formated['buy'] = empty($a_price) == true ? false : true;
            $rent = true;
            if (!empty($a_price) && empty($a_price[0]['hourly_price']) && empty($a_price[0]['daily_price']) && empty($a_price[0]['weekly_price'])) {
                $rent = false;
            }
            $formated['rent'] = $rent;
            $formated['image'] = empty($a_image) == true ? '' : $a_image[0]['image_small'];
            $formated['default_variant'] = $defaultAttribute;
            $data[] = $formated;
        }
        return $data;
    }

    public function searchByString($location, $search, $selectedArr, $priceArr, $options, $offset, $limit, $orderBy, $order)
    {
        $attrProductsObj = TableRegistry::get('VariantsProducts');
        $results = $attrProductsObj->find()
            ->select(['VariantsProducts.barcode', 'VariantsProducts.chain', 'VariantsProducts.id', 'VariantsProducts.price_type'])
            ->contain('Products', function ($q) use ($selectedArr, $search) {
                return $q->select($selectedArr);

            })
            ->contain(
                'BasePrice', function ($q) use ($priceArr) {
                return $q->select($priceArr)
                    ->where(["BasePrice.duration_type LIKE '%" . 'base' . "%'"]);
            })
            ->contain('Qty', function ($q) use ($location) {
                return $q->select(['Qty.id', 'Qty.location', 'Qty.quantity', 'Qty.available', 'Qty.variants_products_id']);
            })
            ->group(['VariantsProducts.id'])
            ->where($options)
            ->offset($offset)
            ->limit($limit)
            ->order([$orderBy => $order])
            ->toArray();
        $data = array();

        foreach ($results as $row) {
            $priceData = TableRegistry::get('ProductPrices')->find('all')
                ->where(['variants_products_id' => $row->id])
                ->where(['price_type' => $row->price_type])
                ->where(['duration_type IN ' => ['hourly', 'daily', 'weekly', 'monthly', 'fixed']])
                ->first();
            $formated = array();
            $price = array();
            if (!empty($row->base_price)) {
                $price = array(
                    'base_price' => $row->base_price['price']
                );
            }
            $formated['id'] = $row->product->id;
            $formated['deposit_amount'] = !empty($row->product->deposit_amount) ? $row->product->deposit_amount : 0;
            $formated['rent'] = empty($priceData) ? false : true;
            $formated['url'] = $row->product->url;
            $formated['type'] = $row->product->type;
            $formated['uuid'] = $row->product->uuid;
            $formated['store_id'] = $row->product->store_id;
            $formated['name'] = $row->product->name;
            $formated['status'] = $row->product->status;
            $formated['price'] = $price;
            $formated['barcode'] = $row->barcode;
            $formated['quantity'] = $row->qty;
            $formated['set_id'] = $row->product->variant_set;
            $formated['set_name'] = TableRegistry::getTableLocator()->get('VariantSets')->getChainAttributeSetName($row->product->variant_set);
            if ($row->chain != '-') {
                $formated['chain_id'] = $row->chain;

                $formated['chain_name'] = TableRegistry::getTableLocator()->get('Variants')->getChainAttributeName($row->chain);
                if ($row->chain == 1) {
                    $row->product->variant_set = json_encode([1]);
                }
                $formated['variant_chain'] = TableRegistry::get('VariantsProducts')->getVariantChain(json_decode($row->product->variant_set, true), $row->chain, ' -> ');
            }
            $formated['attr_id'] = $row->id;
            $data[] = $formated;
        }
        return $data;
    }

    public function searchForAsset($options, $offset, $limit, $orderBy, $order)
    {
        $attrProductsObj = TableRegistry::get('VariantsProducts');
        $results = $attrProductsObj->find()
            ->select(['VariantsProducts.barcode', 'VariantsProducts.chain', 'VariantsProducts.id', 'VariantsProducts.price_type'])
            ->contain('Products', function ($q) {
                return $q->select(['Products.id', 'Products.store_id', 'Products.uuid', 'Products.name', 'Products.url', 'Products.variant_set']);
            })
            ->contain(['Qty'])
            ->group(['VariantsProducts.id'])
            ->where($options)
            ->offset($offset)
            ->limit($limit)
            ->order([$orderBy => $order])
            ->toArray();
        $data = array();
        foreach ($results as $row) {

            $formated = array();
            $formated['id'] = $row->product->id;
            $formated['url'] = $row->product->url;
            $formated['uuid'] = $row->product->uuid;
            $formated['name'] = $row->product->name;
            $formated['status'] = $row->product->status;
            $formated['barcode'] = $row->barcode;
            $formated['set_id'] = $row->product->variant_set;
            $formated['set_name'] = TableRegistry::get('VariantSets')->getChainAttributeSetName($row->product->variant_set);
            $formated['chain_id'] = $row->chain;
            $formated['chain_name'] = TableRegistry::get('Variants')->getChainAttributeName($row->chain);
            if ($row->chain == 1) {
                $row->product->variant_set = json_encode([1]);
            }
            $formated['variant_chain'] = TableRegistry::get('VariantsProducts')->getVariantChain(json_decode($row->product->variant_set, true), $row->chain, ' -> ');
            $formated['attr_id'] = $row->id;
            $data[] = $formated;
        }
        return $data;
    }

    /**
     * @desc Related products for user/front end
     * @param type $id
     * @param type $requestFor
     * @return boolean
     */
    public function userGetRelatedProducts($id = null)
    {
        $relatedProducts = \Cake\Cache\Cache::read('Product.related.' . $id, 'Boot');
        if ($relatedProducts) {
            $relatedProducts = json_decode($relatedProducts);
        }
        if ($relatedProducts) {
            return $relatedProducts;
        } else {
            $tableRelatedProducts = TableRegistry::get('RelatedProducts');
            $relatedProducts = $tableRelatedProducts->find('all')
                ->select(['RelatedProducts.id', 'RelatedProducts.related_product_id'])
                ->contain('Products', function ($q) use ($id) {
                    return $q->select(['Products.id', 'Products.available', 'Products.sales_tax', 'Products.deposit_amount', 'Products.store_id', 'Products.url', 'Products.uuid', 'Products.name', 'Products.average_rating'])->where(['Products.id' => $id]);
                })
                ->contain('Products.Images', function ($q) use ($id) {
                    return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small'])
                        ->where(['Images.product_id' => $id, 'Images.status' => 2]);
                })
                ->contain('Products.Prices', function ($q) use ($id) {
                    return $q->select(['Prices.id', 'Prices.product_id', 'Prices.price'])->where(['Prices.product_id' => $id]);
                })
                ->where(['RelatedProducts.product_id' => $id])
                ->order(['RelatedProducts.product_id'])
                ->toArray();

            if ($relatedProducts) {
                $encoded = json_encode($relatedProducts);
                if ($encoded) {
                    \Cake\Cache\Cache::write('Product.related.' . $id, $encoded, 'Boot');
                }
                return $relatedProducts;
            } else {
                return false;
            }
        }
    }


    /*
     * Get All Related Products
     */
    public function getRelatedProducts($id = null)
    {
        $relatedProducts = TableRegistry::get('RelatedProducts');
        $r_product = $relatedProducts->find('all')->select(['RelatedProducts.related_product_id'])->where(['RelatedProducts.product_id' => $id])->toArray();
        $results = array();
        if (!empty($r_product)) {
            $ids = array();
            foreach ($r_product as $product) {
                $ids[] = $product->related_product_id;
            }
            $options = array('Products.id IN' => $ids);
            $commonArr = array('Products.id', 'Products.name', 'Products.type');
            $imageArr = array('Images.product_id', 'Images.image_original');
            $results = $this->find()
                ->select($commonArr)
                ->contain(
                    'Images', function ($q) use ($imageArr) {
                    return $q->select($imageArr)->where(['Images.status' => 2]);
                }
                )
                ->where($options)
                ->order(['Products.created' => 'DESC'])
                ->toArray();
        }
        return $results;
    }

    /*
     * Get All Related Products
     */
    public function getRelatedInfo($id = null)
    {
        $commonArr = array('Products.id', 'Products.name', 'Products.type');
        $imageArr = array('Images.product_id', 'Images.image_original');
        $results = $this->find()
            ->select($commonArr)
            ->contain(
                'Images', function ($q) use ($imageArr) {
                return $q->select($imageArr)->where(['Images.status' => 2]);
            }
            )
            ->where(['Products.id' => $id])
            ->order(['Products.created' => 'DESC'])
            ->first();
        return $results;
    }

    /*
     * Get Model Names for association
     */
    private function getModelName($model = array(), $selectedArr, $userArr, $priceArr)
    {
        $containArr = array();
        $modelData = explode(',', $model);
        for ($i = 0; $i < count($modelData); $i++) {
            $v = $modelData[$i];
            if ($v == 'Users') {
                $containArr[] = 'Users';
                $selectedArr = array_merge($userArr, $selectedArr);
            }
            if ($v == 'Prices') {
                $containArr[] = 'Prices';
                $selectedArr = array_merge($priceArr, $selectedArr);
            }
        }

        $result = array(
            'containArr' => $containArr,
            'selectedArr' => $selectedArr
        );

        return $result;
    }

    /*
     * Get formatted data from array of product data
     */
    // Not used
    private function getFormattedData_old($data = array())
    {
        $results = array();
        foreach ($data as $row) {
            $product = $this->getData($row);
            $results[] = $product;
        }
        return $results;
    }


    /*
     * View details for admin site
     */
    public function view($vp_id, $location = null, $options = array())
    {
        $attrProductsObj = TableRegistry::getTableLocator()->get('VariantsProducts');
        RentMy::addModel(['ProductPrices', 'Images']);
        $attrProduct = $attrProductsObj->find()
            ->select(['VariantsProducts.id', 'VariantsProducts.barcode', 'VariantsProducts.chain', 'VariantsProducts.price_type'])
            ->contain('Products', function ($q) {
                return $q->select(['id', 'name', 'store_id', 'sales_tax', 'Products.variant_set' , 'Products.type', 'deposit_amount', 'driving_license', 'Products.url', 'Products.uuid', 'Products.options', 'Products.status']);
            })
            ->contain('Qty', function ($q) use ($location) {
                return $q->select(['Qty.id', 'Qty.location', 'Qty.quantity', 'Qty.variants_products_id'])
                    ->where(['Qty.location' => $location]);
            })
//            ->contain('Images', function ($q) {
//                return $q->select(['Images.image_large', 'Images.image_small'])->where(['Images.status' => 2]);
//            }
//            )
            ->where(['VariantsProducts.id' => $vp_id])
            ->first();
        $attrProduct['images'] = RentMy::$Model['Images']->image_by_store_plan('view', ['variants_products_id' => $vp_id])->toArray();
        $productOptions = empty($attrProduct->product->options) ? [] : json_decode($attrProduct->product->options, true);
        $formated = [];
        // Step 2 :  exact date feature - start date & end date calculations
        if (!empty($productOptions['exact_date'])) {
            $exactDates = RentMy::extactDates($attrProduct->product->id);
            $options['start_date'] = !empty($exactDates['start_date']) ? $exactDates['start_date'] : $options['start_date'];
            $options['end_date'] = !empty($exactDates['end_date']) ? $exactDates['end_date'] : $options['end_date'];
            if (!empty($exactDates)) {
                $formated['rent_start'] = $options['start_date'];
                $formated['rent_end'] = $options['end_date'];
                $formated['exact_date'] = true;
            }
        }
        if (!empty($productOptions['exact_time'])) {
            $formated['extact_durations'] = RentMy::exactTimes('', $attrProduct->product->id);
            if (!empty($formated['extact_durations']) && !empty($formated['extact_durations']['times'])){
                $formated['exact_time'] = true;
            }

        }

        $row = $attrProduct;
        $formated['id'] = $row->product->id;
        $formated['url'] = $row->product->url;
        $formated['uuid'] = $row->product->uuid;
        $formated['store_id'] = $row->product->store_id;
        $formated['deposit_amount'] = empty($row->product->deposit_amount) ? 0 : $row->product->deposit_amount;
//        $formated['sales_tax'] = empty($row->product->sales_tax) == true ? Configure::read('taxValue') : $row->product->sales_tax;
        $formated['sales_tax'] = $this->getSalesTaxClass($row->product->id);
        $formated['deposite_tax'] = empty($row->product->deposite_tax) == true ? 'false' : $row->product->deposite_tax['value'];
        $formated['name'] = $row->product->name;
        $formated['status'] = $row->product->status;
        $formated['prices'] = RentMy::$Model['ProductPrices']->getPriceDetails($row);
        $formated['images'] = $row['images'];
        $formated['barcode'] = $row->barcode;
        $formated['quantity'] = $row->qty;
        $formated['driving_license'] = $row->product->driving_license;
        //$formated['set_id'] = $row->product->variant_set;
        $formated['variant_sets'] = TableRegistry::get('VariantSets')->getChainAttributeSetName($row->product->variant_set);
        $formated['chain_id'] = $row->chain;
        $formated['chain_name'] = TableRegistry::get('Variants')->getChainAttributeName($row->chain);
        $formated['attr_id'] = $row->id;

        if ($row->product->type  == 2){
            RentMy::addModel(['ProductPackages']);
            $productPackages = RentMy::$Model['ProductPackages']->find()
                ->where(['ProductPackages.store_id' => RentMy::$store->id])
                ->where(['ProductPackages.package_id' => $formated['id']])
                ->toArray();

            if (!empty($productPackages)) {
                $productIds = Hash::extract($productPackages, '{n}.product_id');
                $packageProducts = RentMy::$Model['ProductPackages']->productDetails($productIds, $productPackages, $location);
                $products = [];
                foreach ($productIds as $productId) {
                    foreach ($packageProducts as $packageProduct) {
                        if ($productId == $packageProduct['id']) {
                            $products[] = $packageProduct;
                        }
                    }
                }
            }
            $formated['products'] = $products;
        }

        if ($options['type'] == 'cart' && (!empty($options['token']))) {
            $cart = TableRegistry::getTableLocator()->get('Carts')->find()->where(['uid' => $options['token']])->first();
            if (!empty($cart)) {
                $formated['rental_price'] = RentMy::$Model['ProductPrices']->getRentalPriceByDates($row->product->id,
                    $vp_id, RentMy::toStoreTimeZone($cart['rent_start'], 'Y-m-d H:i:00'), RentMy::toStoreTimeZone($cart['rent_end'], 'Y-m-d H:i:00'));
            }
        }

        if ($options['type'] == 'order' && (!empty($options['order_id']))) {
            $order = TableRegistry::getTableLocator()->get('Orders')->find()->where(['id' => $options['order_id']])->first();
            if (!empty($order)) {
                $formated['rental_price'] = RentMy::$Model['ProductPrices']->getRentalPriceByDates($row->product->id,
                    $vp_id, RentMy::toStoreTimeZone($order['rent_start'], 'Y-m-d H:i:00'), RentMy::toStoreTimeZone($order['rent_end'], 'Y-m-d H:i:00'));
            }
        } else if (!empty($options['start_date']) && !empty($options['end_date'])) {
            $rentalDates = RentMy::formatRentalDates($options['start_date'], $options['end_date']);
            $formated['rental_price'] = RentMy::$Model['ProductPrices']->getRentalPriceByDates($row->product->id, $vp_id, $rentalDates['start_date'], $rentalDates['end_date']);
        }


        // recurring pricing.
        $recurring = RentMy::$Model['ProductPrices']->getRecurringPriceList($formated['prices'][0]);
        if (!empty($recurring)) {
            $formated['recurring_prices'] = $recurring;
        }

        return $formated;
    }

    public function saveProduct($data, $supplier_id)
    {

        if (!empty($data['name'])) {
            $productCount = $id = 0;
            $pdata = array();
            $result = array();
            $product = $this->find()
                ->where(['id' => $data['product_id']])
                ->first();
            if (empty($product)) {
                $product = $this->newEntity();
                $pdata['user_id'] = $data['user_id'];
                $pdata['store_id'] = $data['store_id'];
                $pdata['name'] = $data['name'];
                $pdata['sales_tax'] = $data['sales_tax'];
                $pdata['status'] = 1;
                $pdata['description'] = $data['description'];
                $pdata['keyword'] = $data['keyword'];
                $pdata['deposit_amount'] = $data['deposit_amount'];
                $pdata['supply_id'] = $data['supply_id'];
                $pdata['supplier_id'] = $supplier_id;
                $pdata['variant_set'] = $data['variant_set'];
                $product = $this->patchEntity($product, $pdata);
                if ($this->save($product)) {
                    $id = $product['id'];
                    $productCount++;
                }
            } else {
                $product['user_id'] = $data['user_id'];
                $pdata['status'] = 1;
                $product['store_id'] = $data['store_id'];
                $product['name'] = $data['name'];
                $pdata['sales_tax'] = $data['sales_tax'];
                $product['description'] = $data['description'];
                $product['keyword'] = $data['keyword'];
                $pdata['deposit_amount'] = $data['deposit_amount'];
                $pdata['supply_id'] = $data['supply_id'];
                $pdata['variant_set'] = $data['variant_set'];
                $pdata['supplier_id'] = $supplier_id;
                if ($this->save($product)) {
                    $productCount++;
                }
            }
            $result ['id'] = $id;
            $result ['count'] = $productCount;
            return $product;
        }

        return 0;

    }

    /**
     * Check the product has any Default Image
     * @param $productId
     */
    public function saveHasImageToProduct($productId)
    {
        RentMy::addModel(['Images']);
        $image = RentMy::$Model['Images']->find()->where(['product_id' => $productId, 'status' => 2])->first();
        $product = $this->get($productId);
        $product->has_default_image = 0;
        if (!empty($image)) {
            $product->has_default_image = 1;
        }
        $this->save($product);
    }

    /*
     * @depricated
    * seoUrl
    */
    function seoUrl($string)
    {
        //Unwanted:  {UPPERCASE} ; / ? : @ & = + $ , . ! ~ * ' ( )
        $string = strtolower(trim($string));
        //Strip any unwanted characters
        $string = preg_replace("/[^a-z0-9_\s-]/", "", $string);
        //Clean multiple dashes or whitespaces
        $string = preg_replace("/[\s-]+/", " ", $string);
        //Convert whitespaces and underscore to dash
        $string = preg_replace("/[\s_]/", "-", $string);
        return trim($string);
    }

    function getProductwithallVariants($productIds, $location)
    {
        if (empty($productIds)) {
            return [];
        }
        RentMy::addModel(['VariantsProducts']);
        $conditions = ['pVariants.is_last' => 1, 'pVariants.status' => 1];
        $products = $this->find()
            ->select(['Products.id', 'Products.name', 'Products.variant_set', 'Products.is_tracked', 'Products.variants_products_id'])
            ->contain('pVariants', function ($q) use ($conditions, $location) {
                return $q->select(['pVariants.id', 'pVariants.product_id', 'pVariants.chain', 'pVariants.barcode', 'pVariants.price_type'])
                    ->contain('Qty', function ($q) use ($location) {
                        return $q->select(['Qty.id', 'Qty.quantity', 'Qty.available'])
                            ->where(['Qty.location' => $location]);
                    })
                    ->where($conditions);
            })
            ->where(['Products.id IN' => $productIds])
            ->toArray();
        $data = array();

        $collection = new Collection($products);
        $defaultVariants = $collection->extract('variants_products_id')->toArray();
        if (!empty($defaultVariants)) {
            $images = TableRegistry::getTableLocator()->get('Images')->find()
                ->select(['variants_products_id', 'image_small'])
                ->where(['variants_products_id IN' => $defaultVariants]);

        }

        foreach ($products as $product) {
            $variants = [];
            if (!empty($images)) {
                $imageCollection = new Collection($images);
                $image = $imageCollection->match(['variants_products_id' => $product['variants_products_id']])->first();
            }
            foreach ($product->p_variants as $variant) {
                if ($variant->chain == 1) {
                    $product->variant_set = json_encode([1]);
                }
                $variant_chain = RentMy::$Model['VariantsProducts']->getVariantChain(json_decode($product->variant_set, true), $variant->chain);

                $variants[] = [
                    'id' => $variant->id,
                    'name' => $variant_chain,
                    //'chain'=> json_decode($product->variant_set,true),
                    'quantity_id' => $variant->qty->id
                ];
            }
            $product->variants = $variants;
            $aData['id'] = $product->id;
            $aData['name'] = $product->name;
            $aData['image'] = $image['image_small'] ?? '';
            $aData['variants'] = $product->variants;
            $data[] = $aData;
        }
        return $data;
    }

    /**
     * Save sales tax value of a product
     *
     * @param $productId
     * @param $location
     * @param $salesTax
     * @return bool
     */
    public function saveSalesTax($productId, $location, $salesTax)
    {
        RentMy::addModel(['ProductsSettings']);
        $productSettings = RentMy::$Model['ProductsSettings']->find()
            ->where([
                'store_id' => RentMy::$store->id,
                'product_id' => $productId,
                'location' => $location,
                'p_key' => 'sales_tax'
            ])
            ->first();

        if (empty($productSettings)) {
            $productSettings = RentMy::$Model['ProductsSettings']->newEntity();
        }
        $productSettings = RentMy::$Model['ProductsSettings']->patchEntity($productSettings, [
            'store_id' => RentMy::$store->id,
            'product_id' => $productId,
            'location' => $location,
            'p_key' => 'sales_tax',
            'value' => $salesTax
        ]);

        return RentMy::$Model['ProductsSettings']->save($productSettings);
    }

    /**
     * this function will return a product tax class
     * @param $productId
     * @return string
     */
    public function getSalesTaxClass($productId){
        RentMy::addModel(['ProductsSettings']);
        $productSettings = RentMy::$Model['ProductsSettings']->find()
            ->where([
                'store_id' => RentMy::$store->id,
                'product_id' => $productId,
                'location' => RentMy::$token->location,
                'p_key' => 'sales_tax'
            ])
            ->first();
        return !empty($productSettings)?$productSettings->value:'';
    }

    /**
     * Find the sales tax value of a product
     *
     * @param int $productId
     * @param string $saleType
     * @param string $location
     * @return float
     */
    public function getSalesTax($productId, $saleType, $location = null)
    {
        RentMy::addModel(['ProductsSettings']);

        $product = $this->get($productId);
        if (!$location) {
            $location = RentMy::$token->location;
        }

        $storeConfig = RentMy::$storeConfig;
        $taxConfig = $storeConfig['tax'] ?? Configure::read('tax');
        $taxApply = true;
        if ($taxConfig['tax_apply_on'] == 0) {
            $taxApply = false;
        }

        if (!$taxApply) {
            return 0.00;
        }

        $productSettings = RentMy::$Model['ProductsSettings']->find()
            ->where(['p_key' => 'sales_tax', 'product_id' => $productId, 'location' => $location])
            ->first();

        if ($productSettings && isset($productSettings->value)) {
            $taxValue = $productSettings->value;
        } else {
            if (!empty($product->sales_tax)) {
                $taxValue = $product->sales_tax;
            } else {
                if ($saleType == 'buy') {
                    $taxValue = $taxConfig['buy_tax'] ?? Configure::read('tax.buy_tax');
                } else {
                    $taxValue = $taxConfig['rent_tax'] ?? Configure::read('tax.rent_tax');
                }
            }
        }

        return $taxValue;
    }



    /**
     * @param $product_ids array - required
     * @param $tags array - not required
     * @param $tax int - not required [tax class id]
     * @param $vendor int - not required
     * @param $status int - not required
     * @param $featured int - not required
     * @return bool
     */
    public function bulkEdit($data)
    {
        RentMy::addModel(['ProductsTags']);

        //if update for all product button is triggered get all products list and override products id
        if (isset($data['update_all']) && $data['update_all']) {
            $products = $this->find()->where(['store_id' => RentMy::$store->id])->select(['id'])->toArray();
            foreach ($products as $product)
                $data['product_ids'][] = $product->id;
        }

        //
        $fillable = [];
        if (isset($data['status']) && is_numeric($data['status']))
            $fillable['status'] = $data['status'];

        if (isset($data['featured']) && !is_null($data['featured']))
            $fillable['featured'] = $data['featured'] == 2 ? 0 : $data['featured'];

        if (isset($data['vendor']) && !is_null($data['vendor']))
            $fillable['supplier_id'] = $data['vendor'];

        if (isset($data['tax']) && !is_null($data['tax']))
            $fillable['sales_tax'] = $data['tax'];

        if (isset($data['sequence_no']) && !is_null($data['sequence_no']))
            $fillable['sequence_no'] = $data['sequence_no'];

        $options = [];
        if (!empty($data['exact_date_time']) && $data['exact_date_time']=='exact_date'){
            $options['exact_date'] = true;
            if (isset($options['exact_time'])){
                unset($options['exact_time']);
            }
        }
        if (!empty($data['exact_date_time']) && $data['exact_date_time']=='exact_time'){
            $options['exact_time'] = true;
            if (isset($options['exact_date'])){
                unset($options['exact_date']);
            }
        }
        if (!empty($data['exact_date_time']) && $data['exact_date_time']=='none'){
            if (isset($options['exact_date'])){
                unset($options['exact_date']);
            }
            if (isset($options['exact_time'])){
                unset($options['exact_time']);
            }
        }
        $fillable['options'] = json_encode($options);
        if (empty($data['exact_date_time'])){
            unset($fillable['options']);
        }

        if (empty($fillable) && empty($data['tags']))
            return false;

        //format data for products,tags and logs
        $tagData = $logs = [];
        $tags = $data['tags'] ?? [];
        foreach ($data['product_ids'] as $productId) {
            $tagLogs = [];
            if (is_array($tags) && !empty($tags)) {
                RentMy::$Model['ProductsTags']->deleteAll(['product_id' => $productId]);
                foreach ($tags as $tagId) {
                    $tagData[] = [
                        'product_id' => $productId,
                        'tag_id' => $tagId
                    ];
                    $tagLogs[] = $tagId;
                }
            }

            if (!empty($fillable['sales_tax'])){
                $this->saveSalesTax($productId, $data['location'], $fillable['sales_tax']);
            }

            $_fillable = $fillable;

            if (isset($_fillable['sales_tax'])) unset($_fillable['sales_tax']);

            if (!empty($tagLogs))
                $_fillable['tags'] = $tagLogs;

            $logs[] = [
                'product_id' => $productId,
                'store_id' => RentMy::$store->id,
                'content' => 'Products',
                'value' => json_encode($_fillable),
            ];

        }

        //update product rows
        if (!empty($fillable))
            $this->updateAll($fillable, ['id IN' => $data['product_ids']]);

        //add product tags if not empty
        if (!empty($tagData)) {
            $tagEntities = RentMy::$Model['ProductsTags']->newEntities($tagData);
            RentMy::$Model['ProductsTags']->saveMany($tagEntities);
        }

        //add logs
        if (!empty($logs)) {
            RentMy::addModel(['ProductsLog']);
            RentMy::$Model['ProductsLog']->createLogs($logs);
        }

        return true;
    }


    /**
     * @param string $slug
     * @param int $storeId
     * @param int $locationId
     * @param int|null $assetId
     * @return array|EntityInterface|null
     */
    public function getProductWithDefaultVariant(string $slug, int $storeId, int $locationId, ?int $assetId = null, $options = [])
    {

        $query = $this->find();
        if (!empty($options['product_fields']) && is_array($options['product_fields'])){
            $query = $query->select($options['product_fields']);
        }

        $conditions = [
            'Products.url' => $slug,
            'Products.store_id' => $storeId,
            'Products.status' => 1,
            'Qty.location' => $locationId,
        ];
        if (!empty($queryParam['asset_id'])) {
            $asset = RentMy::$Model['Assets']->find()->where(['id' => $queryParam['asset_id']])->first();
            $conditions = array_merge(array('VariantsProducts.id' => $asset['variants_products_id']), $conditions);
        } else {
            $conditions = array_merge(array('VariantsProducts.is_default' => 1), $conditions);
        }
            $query = $query->where($conditions)
                ->contain('VariantsProducts', function ($q) {
                    return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain',
                        'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                        ->contain('Qty', function ($q) {
                            return $q->select(['Qty.id', 'Qty.quantity', 'Qty.available']);
                        });
                });

        if ($assetId) {
            $query->matching('ItemAssets', function ($q) use ($assetId) {
                return $q->where(['ItemAssets.asset_id' => $assetId]);
            });
        }

        $product = $query->first();

        // Assign the default variant manually (if needed)
        if ($product && !empty($product->variants_products)) {
            $product->variants_product = $product->variants_products[0]; // assuming first one is default
        }

        return $product;
    }

    public function getPackageWithDefaultVariant(string $slug, int $storeId, int $locationId, $options = [])
    {
        return RentMy::$Model['Products']->find('all')
            ->select(['id', 'name', 'description', 'type', 'uid' => 'uuid', 'variants_products_id', 'client_specific_id', 'options','deposit_amount', 'Products.status'])
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status', 'Images.variants_products_id']);
            })
            ->contain('VariantsProducts', function ($q) {
                return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type']);
            })
            ->where(['Products.url' => $slug, 'Products.store_id' => $storeId])->first();
    }

}
