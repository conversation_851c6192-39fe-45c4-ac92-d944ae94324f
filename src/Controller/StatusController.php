<?php

namespace App\Controller;

use App\Lib\RentMy\RentMy;
use Cake\Core\Configure;
use Exception;

class StatusController extends AppController
{


    /**
     * Get list of status with first level of sub-category
     * @API  - /settings/status/:type
     */
    public function index($type)
    {
        $this->request->allowMethod('get');
        RentMy::addModel(['SystemStatus']);
        $systemStatuses = RentMy::$Model['SystemStatus']->find('threaded')
            ->where(['store_id IN' => [RentMy::$store->id, 'store_id' => 0]])
            ->where(['status' => 1, 'content_type' => $type])
            ->where(['reference_id IS NULL'])
            ->order(['sequence' => 'ASC'], true)
            ->map(function ($statuses) {
                $status = RentMy::$Model['SystemStatus']->getStoreWiseStatus($statuses);
                $items = [
                    'id' =>  $status['id'],
                    'name' => $status['name'],
                    'enabled' => $status['enabled'],
                    'sequence' => $status['sequence'],
                    'color_code' => $status['color_code'],
                    'can_delete' => $status['store_id'] != 0
                ];

                foreach ($statuses['children'] as $status) {
                    $status = RentMy::$Model['SystemStatus']->getStoreWiseStatus($status);
                    $items['child'][] = [
                        'id' => $status['id'],
                        'name' => $status['name'],
                        'enabled' => $status['enabled'],
                        'sequence' => $status['sequence'],
                        'color_code' => $status['color_code'],
                        'can_delete' => $status['store_id'] != 0
                    ];
                }
                return $items;
            })
            ->toArray();

        $this->apiResponse['data'] = $systemStatuses;
    }

    /**
     * update or add new status
     * @POST  - /settings/status/update
     */
    public function update()
    {
        $this->request->allowMethod('post');
        $data = $this->request->getData();
        RentMy::addModel(['SystemStatus']);

        if (empty($data['id'])) //when null id sent from frontend unset it
            unset($data['id']);

        if ($this->request->is('post')) {
            if (empty($data['id'])) {
                $systemStatus = RentMy::$Model['SystemStatus']->newEntity();
            } else {
                $systemStatus = RentMy::$Model['SystemStatus']->find()->where(['id' => $data['id']])->first();
                if ($systemStatus->store_id == 0){
                    $data['reference_id'] = $systemStatus->id;
                    $data['content_type_id'] = $systemStatus->content_type_id;
                    $data['content_type'] = $systemStatus->content_type;
                    $data['status'] = $systemStatus->status;

                    if (empty($data['sequence']) && !empty($systemStatus->sequence))
                        $data['sequence'] = $systemStatus->sequence;

                    if (empty($data['color_code']) && !empty($systemStatus->color_code))
                        $data['color_code'] = $systemStatus->color_code;

                    if (empty($data['parent_id']))
                        $data['parent_id'] = $systemStatus->parent_id;

                    unset($data['id']);
                    $systemStatus = RentMy::$Model['SystemStatus']->newEntity();
                }
            }

            if (!is_numeric($data['parent_id']))
                unset($data['parent_id']);

            if (isset($data['parent_id'])){
                $parentStatus = RentMy::$Model['SystemStatus']->find()->where(['id' => $data['parent_id']])->first();
                if (!empty($parentStatus->store_id) && !empty($parentStatus->reference_id)){
                    $data['parent_id'] = $parentStatus->reference_id;
                }
            }

            if (empty($data['sequence']))
                $data['sequence'] = 1;

            if (empty($data['content_type_id']))
                unset($data['content_type_id']);

            $data['store_id'] = RentMy::$store->id;

            $systemStatus = RentMy::$Model['SystemStatus']->patchEntity($systemStatus, $data);

            if (RentMy::$Model['SystemStatus']->save($systemStatus)) {
                $this->apiResponse = ['id' => $systemStatus['id'], 'name' => $systemStatus['name'], 'enabled' => $systemStatus['enabled']];
            } else {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = "Status can't by updated";
            }
        }
    }

    /**
     * delete status
     * @DELETE  - /settings/status/delete/:id
     */
    public function delete($id)
    {
        $this->request->allowMethod('delete');

        RentMy::addModel(['SystemStatus']);
        $systemStatus = RentMy::$Model['SystemStatus']->find()->where(['id'=> $id])->first();

        if (empty($systemStatus)){
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = "Status can't find";
            return;
        }

        // skip when global status delete request
        if ($systemStatus->store_id == 0){
            $this->httpStatusCode = 403;
            $this->apiResponse['error'] = "This status can't be deleted";
            return;
        }
        $systemStatus->status = 2; //delete status
        $systemStatus->enabled = 0;
        RentMy::$Model['SystemStatus']->save($systemStatus);
        $this->apiResponse['message'] = "Status has been deleted successfully";
    }



    /**
     * get order status list
     */
    public function orderStatus()
    {
        $orderStatus = RentMy::getOrderStatus();
        if (is_numeric(RentMy::$token->user_type_id) && !in_array(RentMy::$token->user_type_id, [1,2,3]))
            $orderStatus = RentMy::getOrderStatus(RentMy::$token->user_type_id);

        $this->apiResponse['data'] = $orderStatus;

    }
}
