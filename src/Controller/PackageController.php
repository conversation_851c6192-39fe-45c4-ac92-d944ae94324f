<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\Event\Event;
use Cake\I18n\Time;
use Cake\Network\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Exception;
use DateTime;
use Cake\Core\Configure;
use App\Lib\ProductAsset;

class PackageController extends AppController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * Get all package
     */
    public function index()
    {
        $this->request->allowMethod('get');
        $this->add_model(array('VariantsProducts', 'Suppliers', 'Images', 'Prices', 'Stores', 'ProductPrices', 'ProductPackages'));
        $orderBy = 'Products.id';
        $order = 'DESC';
        $data = $this->request->getQuery();
        //$location_id = empty($data['location']) ? 0 : $data['location'];
        $pageNo = empty($data['page_no']) ? 1 : $data['page_no'];
        $limit = empty($data['limit']) ? 10 : $data['limit'];
        $offset = ($pageNo - 1) * $limit;

        $conditions = array(
            'VariantsProducts.is_last' => 1,
            'Products.store_id' => $this->parseToken->store_id,
            'Products.type' => 2,
            //'Qty.location' => $location_id
        );

        // Advanced Search
        if (!empty($data['avd_search'])) {
            if (isset($data['category_id']) && !empty($data['category_id'])) {
                $categories = explode(',', $data['category_id']);
                $categorySearch = ' (';
                foreach ($categories as $i => $category) {
                    if ($i == 0) {
                        $categorySearch .= "(Products.category_chain_id LIKE '%#" . $category . "#%')";
                    } else {
                        $categorySearch .= "OR (Products.category_chain_id LIKE '%#" . $category . "#%')";
                    }
                }
                $categorySearch .= ') ';
            }
            if (!empty($categorySearch)) {
                $conditions = array_merge(array($categorySearch), $conditions);
            }
            if (!empty($data['product_id'])) {
                $conditions = array_merge(array("Products.id" => $data['product_id']), $conditions);
            }
            if (!empty($data['name'])) {
                $conditions = array_merge(array("Products.name LIKE " => "%" . $data['name'] . "%"), $conditions);
            }
            if (!empty($data['status'])) {
                $conditions = array_merge(array("Products.status" => $data['status']), $conditions);
            } else {
                $conditions = array_merge(array("Products.status !=" => 5), $conditions);
            }
            if (!empty($data['supplier_name'])) {
                $conditions = array_merge(array("Products.supplier_id" => $data['supplier_name']), $conditions);
            }
            if (!empty($data['supplier_id'])) {
                $conditions = array_merge(array("Products.supply_id LIKE '%" . $data['supplier_id'] . "%'"), $conditions);
            }
            if (!empty($data['barcode'])) {
                $conditions = array_merge(array("VariantsProducts.barcode LIKE '%" . $data['barcode'] . "%'"), $conditions);
            }
            if (!empty($data['rental_type'])) {
                $conditions = array_merge(array("Products.purchase_type LIKE '%" . $data['rental_type'] . "%'"), $conditions);
            }
            if (!empty($data['image'])) {
                $conditions = array_merge(array("Products.has_default_image" => 0), $conditions);
            }
        } // Quick searrch
        elseif (!empty($data['search'])) {
            $search = $data['search'];
            $search = strtolower($search);
            $str = "%" . $search . "%";
            $conditions = array_merge(array(
                'OR' => array(
                    "Products.id" => intval($search),
                    "Products.name LIKE " => $str,
                    "Products.supply_id LIKE " => $str,
                    "Products.keyword LIKE " => $str,
                )

            ), $conditions);
        } else {
            $conditions = array_merge(array("VariantsProducts.is_default" => 1), $conditions);
        }
        if (!empty($data['order_by']) && !empty($data['order'])) {
            $order = $data['order'];
            $orderBy = 'Products.' . $data['order_by'];
        }
        $attrArr = ['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'];
        $productsArr = array('Products.deposit_amount', 'Products.id', 'Products.name', 'Products.url', 'Products.sales_tax', 'Products.uuid', 'Products.status');
        $productsObj = $this->VariantsProducts->find()
            ->select($attrArr)
            ->contain([
                'Products' => function ($q) use ($productsArr) {
                    return $q->select($productsArr);
                }
            ])
            ->where($conditions)
            ->group('VariantsProducts.product_id');
        $products = $productsObj
            ->order([$orderBy => $order])
            ->offset($offset)
            ->limit($limit)
            ->toArray();
        $total = $productsObj->count();
        foreach ($products as $i => $row) {
            $attr_ids[] = $row['id'];
        }
        if (!empty($attr_ids)) {
            $images = $this->Images->find()->select()->where(['variants_products_id IN' => $attr_ids])->toArray();
        }
        foreach ($products as $row) {
            $productPackage = $this->ProductPackages->find()->where(['package_id' => $row['product']['id']])->toArray();
            $formated = array();
            $image = '';
            foreach ($images as $mimage) {
                if ($row['id'] == $mimage['variants_products_id']) {
                    if (!empty($mimage['image_small'])) {
                        //$directoryPath = WWW_ROOT . DS . 'img' . DS . 'upload' . DS . 'products' . DS . $this->parseToken->store_id . DS . $row['product']['id'] . DS;
                        //if (file_exists($directoryPath . $mimage['image_small']))
                        $image = $mimage['image_small'];
                    }
                }
            }
            $tags = array();
            $formated['id'] = $row['product']['id'];
            $formated['uuid'] = $row['product']['uuid'];
            $formated['store_id'] = $row['product']['store_id'];
            $formated['name'] = $row['product']['name'];
            $formated['url'] = $row['product']['url'];
            $formated['status'] = $row['product']['status'];
            $formated['price'] = $this->ProductPrices->priceDetails($row);
            $formated['image'] = $image;
            //$formated['barcode'] = empty($row['barcode']) ? '' : $row['barcode'];
            //$formated['supplier']['name'] = empty($row['products_supplier']['supplier_id']) ? '' : $row['products_supplier']['supplier_id'];
            //$formated['supplier']['product_supplier_id'] = empty($row['products_supplier']['supply_id']) ? '' : $row['products_supplier']['supply_id'];
            $formated['tag'] = $tags;
            $formated['quantity'] = empty($productPackage) ? 0 : count($productPackage);

            $result[] = $formated;
        }
        if (!empty($result)) {
            $this->apiResponse['data'] = $result;
            $this->apiResponse['page_no'] = $pageNo;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = $total;
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    /**
     * Product add to package
     */
    public function add()
    {
        $data = $this->request->getData();
        RentMy::addModel(['ProductPackages']);
        $productPackage = RentMy::$Model['ProductPackages']->newEntity();
        $data['store_id'] = RentMy::$store->id;
        $data['max_quantity'] = $data['max_quantity'] ?? 1;
        $productPackage = RentMy::$Model['ProductPackages']->patchEntity($productPackage, $data);
        if (RentMy::$Model['ProductPackages']->save($productPackage)) {
            $productPackages = RentMy::$Model['ProductPackages']->find()
                ->select(['ProductPackages.id', 'ProductPackages.quantity', 'Products.id', 'Products.name'])
                ->contain('Products')
                ->where(['ProductPackages.id' => $productPackage->id])
                ->first();

            $this->apiResponse['data'] = $productPackages;
        } else {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'Something went wrong!';
        }
    }

    /**
     * Product update to package
     */
    public function edit($packageId)
    {
        try {
            $data = $this->request->getData();
            RentMy::addModel(['ProductPackages']);
            $productPackage = RentMy::$Model['ProductPackages']->get($packageId);
            if (RentMy::$store->id != $productPackage->store_id) {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = 'Access denied.';
                return;
            }
            $productPackage->quantity = $data['quantity'];
            $productPackage->max_quantity = $data['max_quantity'] ?? 2;

            if (RentMy::$Model['ProductPackages']->save($productPackage)) {
                $productPackages = RentMy::$Model['ProductPackages']->find()
                    ->select(['ProductPackages.id', 'ProductPackages.quantity', 'Products.id', 'Products.name'])
                    ->contain('Products')
                    ->where(['ProductPackages.id' => $productPackage->id])
                    ->first();

                $this->apiResponse['data'] = $productPackages;
            } else {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = 'Something went wrong!';
            }
        } catch (Exception $exception) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = $exception->getMessage();
        }
    }

    /**
     * Product delete to package
     */
    public function delete($packageId)
    {
        try {
            $this->add_model(array('ProductPackages'));
            $productPackage = $this->ProductPackages->get($packageId);
            if ($this->parseToken->store_id != $productPackage->store_id) {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = 'Access denied.';
                return;
            }
            if ($this->ProductPackages->delete($productPackage)) {
                $this->apiResponse['message'] = 'Product Successfully removed from the package';
            } else {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = 'Something went wrong!';
            }
        } catch (Exception $exception) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = $exception->getMessage();
        }
    }

    /**
     * Get all product of a package
     * @param $packageId
     */
    public function productPackages($packageId)
    {
        $this->add_model(array('ProductPackages'));
        $productPackages = $this->ProductPackages->find()
            ->select(['ProductPackages.id', 'ProductPackages.quantity', 'ProductPackages.max_quantity', 'Products.id', 'Products.name'])
            ->contain('Products')
            ->where(['ProductPackages.store_id' => $this->parseToken->store_id])
            ->where(['ProductPackages.package_id' => $packageId])
            ->toArray();

        $this->apiResponse['data'] = $productPackages;
    }

    /**
     * get package details for admin
     */

    public function packageDetails()
    {
        $this->add_model(['Products', 'VariantsProducts', 'ProductPackages', 'ProductPrices', 'Images']);
        RentMy::addModel(['ProductsSettings']);
        $result = array();
        $data = $this->request->getQueryParams();
        if (!empty($this->request->getQuery('location'))) {
            $location = $this->request->getQuery('location');
        } else {
            $header = $this->request->getHeaders();
            $location = empty($header['Location']) ? $data['location'] : $header['Location'][0];
        }
        $uId = $this->request->getParam('uid');
        $timestamp = $this->request->getParam('time');

        $package = $this->Products->find('all')
            ->select(['id', 'description', 'name', 'deposit_amount', 'type', 'variants_products_id', 'client_specific_id', 'options'])
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status', 'Images.variants_products_id']);
            })
            ->contain('VariantsProducts', function ($q) {
                return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type']);
            })
            ->where(['uuid' => $uId])->first();
        if (empty($package) || $package->type != 2) {
            $this->httpStatusCode = 403;
            $this->apiResponse['error'] = 'Invalid UID!';
            return;
        }

        $productPackages = $this->ProductPackages->find()
            ->where(['ProductPackages.store_id' => $this->parseToken->store_id])
            ->where(['ProductPackages.package_id' => $package->id])
            ->toArray();
        $products = array();
        $t_quantities = 0;
        if (!empty($productPackages)) {
            $productIds = Hash::extract($productPackages, '{n}.product_id');
            $quantities = Hash::extract($productPackages, '{n}.quantity');
            $t_quantities = array_sum($quantities);

            $packageProducts = $this->ProductPackages->packageDetails($productIds, $productPackages, $location, $timestamp);
            $products = [];
            foreach ($productIds as $productId) {
                foreach ($packageProducts as $packageProduct) {
                    if ($productId == $packageProduct['id']) {
                        $products[] = $packageProduct;
                    }
                }
            }

        }

        $packagePrice = $this->ProductPrices->getPriceDetails($package->variants_product);
        $recurring = $this->ProductPrices->getRecurringPriceList($packagePrice[0]);
        if (!empty($recurring)) {
            $result['recurring_prices'] = $recurring;
        }
        $rental_price = 0;
//        $exactDates = RentMy::extactDates($package->id);
//        $data['start_date'] = !empty($exactDates['start_date']) ? $exactDates['start_date'] : $data['start_date'];
//        $data['end_date'] = !empty($exactDates['end_date']) ? $exactDates['end_date'] : $data['end_date'];
        $options = empty($package->options) ? [] : json_decode($package->options, true);
        if (!empty($options['exact_date'])) {
            $exactDates = RentMy::extactDates($package->id);
            $data['start_date'] = !empty($exactDates['start_date']) ? $exactDates['start_date'] : $data['start_date'];
            $data['end_date'] = !empty($exactDates['end_date']) ? $exactDates['end_date'] : $data['end_date'];

        }

        if (!empty($data['start_date']) && !(empty($data['end_date']))) {
            $rentalDates = RentMy::formatRentalDates($data['start_date'], $data['end_date']);
            $rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($package->id, $package->variants_product->id, $rentalDates['start_date'], $rentalDates['end_date']);
            //$packageTerm = $this->ProductPackages->getAvailablePackage($productPackages, $variantIds, $location,$rentalDates['start_date']);
        } else {
            //$packageTerm = $this->ProductPackages->getAvailablePackage($productPackages, $variantIds, $location);
        }
        if (!empty($token)) {
            //$cart_available=TableRegistry::getTableLocator()->get('CartItems')->available($data['token'], $package->variants_product->id);
        }

        $packageKeys = ['package_content', 'package_dynamic_bundle_builder', 'package_max_item_count', 'package_item_max_quantity'];
        $packageContents = RentMy::$Model['ProductsSettings']->find()->where(['product_id' => $package->id, 'p_key IN' => $packageKeys])->toArray();
        foreach($packageContents as $packageContent){
            $result[$packageContent['p_key']] = $packageContent['value'];
        }

        $result['id'] = $package->id;
        $result['name'] = $package->name;
        $result['deposit_amount'] = !empty($package->deposit_amount) ? $package->deposit_amount : 0;
        $result['description'] = $package->description;
        $result['variants_products_id'] = $package->variants_products_id;
        $result['price'] = $packagePrice;
        $result['quantity'] = $t_quantities;
        $result['images'] = $package->images;
        $result['rental_price'] = $rental_price;
        $result['client_specific_id'] = !empty($package->client_specific_id) ? $package->client_specific_id : $package->id;
        $result['products'] = $products;
        if (!empty($exactDates)) {
            $result['rent_start'] = $data['start_date'];
            $result['rent_end'] = $data['end_date'];
            $result['exact_date'] = true;
        }
        $result['extact_durations'] = [];
        if (!empty($options['exact_time'])) {
            $result['extact_durations'] = RentMy::exactTimes('', $package->id);
            if (!empty($result['extact_durations']) && !empty($result['extact_durations']['times'])){
                $result['exact_time'] = true;
            }
        }
        $result['enduring_rental'] = !empty($options['enduring_rental']);

        $this->apiResponse['data'] = $result;
    }

    /**
     * get package details
     */
    public function getPackageDetails()
    {
        RentMy::addModel(['ProductsSettings', 'ProductsAvailabilities', 'CartItems', 'Quantities', 'Products', 'ProductPackages', 'ProductPrices']);
        $result = array();
        if (!empty($this->request->getQuery('location'))) {
            $location = $this->request->getQuery('location');
        } else {
            $headers = $this->request->getHeaders();
            $location = empty($headers['Location']) ? 0 : $headers['Location'][0];
        }

        if (!empty(RentMy::$token->location)){
            $data['location'] = RentMy::$token->location;
        }

        $uId = $this->request->getParam('uid');
        $timestamp = $this->request->getParam('time');
        $data = $this->request->getQueryParams();
        // @start - for package details with product id
       $view_type=$data['view_type'];
        if(!empty($view_type) && ($view_type== 'id')){
            $uIdExp= explode('RentMy-',$uId); // this api can accept only uid. ( as a sting ). So we make id as string from wp
            $productFromId = RentMy::$Model['Products']->find()->select(['uuid'])->where(['id' => $uIdExp[1], 'store_id' => RentMy::$store->id])->first();
            $uId= $productFromId->uuid;
        }elseif (!empty($view_type) && ($view_type== 'slug')){
            $productFromSlug = RentMy::$Model['Products']->find()->select(['uuid'])->where(['url' => $uId, 'store_id' => RentMy::$store->id])->first();
            $uId = $productFromSlug->uuid;
        }
        // @end
        $package = RentMy::$Model['Products']->find('all')
            ->select(['id', 'name', 'description', 'type', 'variants_products_id', 'client_specific_id', 'options','deposit_amount', 'Products.status'])
            ->contain('Images', function ($q) {
                return $q->select(['Images.id', 'Images.product_id', 'Images.image_large', 'Images.image_small', 'Images.status', 'Images.variants_products_id']);
            })
            ->contain('VariantsProducts', function ($q) {
                return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type']);
            })
            ->where(['uuid' => $uId])->first();
        if (empty($package) || $package->type != 2) {
            $this->httpStatusCode = 403;
            $this->apiResponse['error'] = 'Invalid request';
            return;
        }
        $productPackages = RentMy::$Model['ProductPackages']->find()
            ->where(['ProductPackages.store_id' => $this->parseToken->store_id])
            ->where(['ProductPackages.package_id' => $package->id])
            ->toArray();

        if (!empty($productPackages)) {
            $productIds = Hash::extract($productPackages, '{n}.product_id');
            $quantities = Hash::extract($productPackages, '{n}.quantity');
            $packageProducts = RentMy::$Model['ProductPackages']->productDetails($productIds, $productPackages, $location);
            $products = [];
            foreach ($productIds as $productId) {
                foreach ($packageProducts as $packageProduct) {
                    if ($productId == $packageProduct['id']) {
                        $products[] = $packageProduct;
                    }
                }
            }

            $variantIds = Hash::extract($products, '{n}.variants.0.id');
            $packagePrice = RentMy::$Model['ProductPrices']->getPriceDetails($package->variants_product);
            $i = 0;
            $isBuy = false;
            $recurring = [];
            foreach ($packagePrice[0] as $key => $allPrices) {
                if ($key == 'base') {
                    $isBuy = !empty($allPrices['id']) ? true : false;
                } else {
                    foreach ($allPrices as $j => $allPrice) {
                        if ($i == 0 & $j == 0) {
                            $initial['rent_start'] = $allPrice['rent_start'];
                            $initial['rent_end'] = $allPrice['rent_end'];
                        }
                    }
                    $i++;
                }
            }
            $recurring = RentMy::$Model['ProductPrices']->getRecurringPriceList($packagePrice[0]);
            if (!empty($recurring)) {
                $result['recurring_prices'] = $recurring;
            }
        }
        $rental_price = 0;

        // Step 2 :  exact date feature - start date & end date calculations
        $options = empty($package->options) ? [] : json_decode($package->options, true);
        if (!empty($options['exact_date'])) {
            $exactDates = RentMy::extactDates($package->id);
            $data['start_date'] = !empty($exactDates['start_date']) ? $exactDates['start_date'] : $data['start_date'];
            $data['end_date'] = !empty($exactDates['end_date']) ? $exactDates['end_date'] : $data['end_date'];

        }

        $result['enduring_rental'] = !empty($options['enduring_rental']);

        if (!empty($data['start_date']) && !(empty($data['end_date']))) {
            $rentalDates = RentMy::formatRentalDates($data['start_date'], $data['end_date']);
            $rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($package->id, $package->variants_product->id, $rentalDates['start_date'], $rentalDates['end_date']);
            $_packageTerms = RentMy::$Model['ProductsAvailabilities']->getPackageAvailability($productPackages, $variantIds, $location, RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i'), RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i'));
        } else {
            $rent_start = !empty($initial['rent_start']) ? RentMy::toUTC($initial['rent_start'], 'Y-m-d H:i') : Time::now()->format('Y-m-d H:i');
            $rent_end = !empty($initial['rent_end']) ? RentMy::toUTC($initial['rent_end'], 'Y-m-d H:i') : Time::now()->addDays(14)->format('Y-m-d H:i');
            $_packageTerms = RentMy::$Model['ProductsAvailabilities']->getPackageAvailability($productPackages, $variantIds, $location, $rent_start, $rent_end);
        }
        $packageTerm = $_packageTerms[0]['term'];
        if (!empty($data['token'])) {
            RentMy::$Model['Quantities']->find()->where([''])->first();
            $quantity = RentMy::$Model['Quantities']->find()->where(['product_id' => $package->id, 'location' => $location])->first();
            if (!empty($quantity)) {
                $cart_available = RentMy::$Model['CartItems']->available($data['token'], $quantity->id);
                $packageTerm = $packageTerm - $cart_available;
            }
        }

        if ($packageTerm < 1){
            $messages = [];
            foreach ($_packageTerms as $_packageTerm) {
                if ($_packageTerm['variant_chain'] != 'Unassigned: Unassigned') {
                    $message = $_packageTerm['product_name'] . ' (' . $_packageTerm['variant_chain'] . ')';
                }else {
                    $message = $_packageTerm['product_name'];
                }

                if (isset($cart_available)){
                    if (($_packageTerm['term'] - $cart_available) < 1){
                        $messages[] = $message;
                    }
                } else {
                    if ($_packageTerm['term'] < 1)
                        $messages[] = $message;
                }
            }
            $this->apiResponse['errors'] = implode(', ', $messages).' not available';
        }

        $packageContent = RentMy::$Model['ProductsSettings']->find()->where(['product_id' => $package->id, 'p_key' => 'package_content'])->first();

        $result['id'] = $package->id;
        $result['uid'] = $uId;
        $result['name'] = $package->name;
        $result['description'] = $package->description;
        $result['deposit_amount'] = $package->deposit_amount;
        $result['variants_products_id'] = $package->variants_products_id;
        $result['price'] = $packagePrice;
        $result['rental_price'] = $rental_price;
        $result['quantity'] = array_sum($quantities);
        $result['cart_available'] = $cart_available ?? 0;
        $result['images'] = $package->images;
        $result['products'] = $products;
        $result['term'] = $packageTerm;
        $result['available'] = $packageTerm;
        $result['client_specific_id'] = !empty($package->client_specific_id) ? $package->client_specific_id : $package->id;
        $result['package_content'] = $packageContent['value'];
        if (!empty($exactDates)) {
            $result['rent_start'] = $data['start_date'];
            $result['rent_end'] = $data['end_date'];
            $result['exact_date'] = true;
        }
        $result['extact_durations'] = [];
        $result['status'] = $package->status;
        if (!empty($options['exact_time'])) {
            $result['extact_durations'] = RentMy::exactTimes('', $package->id);
            if (!empty($result['extact_durations']) && !empty($result['extact_durations']['times'])){
                $result['exact_time'] = true;
            }
        }

        if (!empty($options['seo']))
            $result['seo'] = $options['seo'];

        RentMy::addModel(['Holidays']);
        $result['band_pricing'] = RentMy::$Model['Holidays']->getBandPricing($result['id']);
        $this->apiResponse['data'] = $result;
    }

    /**
     * Get number of term a package can rent
     * @param variantids, location, uid
     * @API GET {{host}}package/1cca17f3af2f11eaba8274d435ed1826/term/360?start_date=2020-07-02%2012:00&end_date=2020-07-03%2012:00&location=647&variants[]=161972&variants[]=161974&token=
     * @return term
     */
    public function getPackageTerm()
    {
        $this->add_model(['Products', 'VariantsProducts', 'ProductPackages', 'ProductPrices', 'ProductsAvailabilities', 'CartItems', 'Quantities']);
        $params = $this->request->getQueryParams();
        if (!empty($params['location'])) {
            $location = $params['location'];
        } else {
            $data = $this->request->getHeaders();
            $location = empty($data['Location']) ? 0 : $data['Location'][0];
        }
        $uId = $this->request->getParam('uid');
        $timestamp = $params['time'];
        $variants = $params['variants'];
        $package = $this->Products->find('all')
            ->select(['id'])
            ->where(['uuid' => $uId])->first();

        $productPackages = $this->ProductPackages->find()
            ->where(['ProductPackages.store_id' => $this->parseToken->store_id])
            ->where(['ProductPackages.package_id' => $package->id])
            ->toArray();
        $rentalDates = RentMy::formatRentalDates($params['start_date'], $params['end_date']);
        $_packageTerms = $this->ProductsAvailabilities->getPackageAvailability($productPackages, $variants, $location, RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i'), RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i'));
        $packageTerm = $_packageTerms[0]['term'];
        if (!empty($params['token'])) {
            $quantity = RentMy::$Model['Quantities']->find()->where(['product_id' => $package->id, 'location' => $location])->first();
            if (!empty($quantity)) {
                $cart_available = $this->CartItems->available($params['token'], $quantity->id);
                $packageTerm = $packageTerm - $cart_available;
            }
        }
        if ($packageTerm < 1){
            $messages = [];
            foreach ($_packageTerms as $_packageTerm) {
                $flug = false;
                if (isset($cart_available)){
                    if (($_packageTerm['term'] - $cart_available) < 1){
                      $flug = true;
                    }
                } else {
                    if ($_packageTerm['term'] < 1)
                        $flug = true;
                }

                if ($flug){
                    if ($_packageTerm['variant_chain'] != 'Unassigned: Unassigned')
                        $messages[] = $_packageTerm['product_name'].' ('.$_packageTerm['variant_chain'] . ')';
                    else
                        $messages[] = $_packageTerm['product_name'];
                }
            }
            $this->apiResponse['errors'] = implode(', ', $messages).' not available';
        }
        // $packageTerm = $this->ProductPackages->getAvailablePackage($productPackages, $variants, $location);

        $this->apiResponse['data'] = $packageTerm;

    }

}
