<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Exception\MissingParamsException;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\Datasource\ConnectionManager;
use Cake\Http\Exception\NotFoundException;
use Cake\Http\Exception\UnauthorizedException;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use function Clue\StreamFilter\fun;

/**
 * Class ProductsAvailabilitiesController
 * @package App\Controller
 */
class ProductsAvailabilitiesController extends AppController
{
    /**
     * View method
     * @param null $id
     */
    public function index($id = null)
    {
        $this->add_model(array('ProductsAvailabilities', 'Products'));
        $producsAavailabilities = [];
        if ($id != null) {
            $producsAavailabilities = $this->ProductsAvailabilities->find()->select(['id', 'product_id', 'order_id', 'start_date', 'end_date', 'quantity'])->where(['product_id' => $id])->toArray();
            $product = $this->Products->find()->select(['id', 'quantity'])->where(['id' => $id])->first();

        }

        $this->apiResponse['data'] = $producsAavailabilities;
        $this->apiResponse['quantity'] = $product['quantity'];
    }


    /**
     * View list of product reservation
     */
    public function view()
    {
        $this->add_model(array('ProductsAvailabilities', 'Products', 'Orders'));
        $param = $this->request->getQuery();
        $pageNo = empty($param['page_no']) == true ? 1 : $param['page_no'];
        $limit = empty($param['limit']) == true ? 10 : $param['limit'];
        $offset = ($pageNo - 1) * $limit;
        $data = array();
        $producsAavailabilities = $this->ProductsAvailabilities->find()
            ->select(['id', 'start_date', 'end_date', 'order_id', 'quantity'])
            ->offset($offset)->limit($limit)
            ->contain('Products', function ($q) {
                return $q->select(['Products.id', 'Products.name']);
            })
            ->leftJoinWith('Orders', function ($q) {
                return $q->select(['Orders.id', 'Orders.status']);
            })
            ->order(['ProductsAvailabilities.created' => 'DESC'])
            ->toArray();

        $total = $this->ProductsAvailabilities->find()
            ->count();
        $orderid = array();
        foreach ($producsAavailabilities as $p) {
            $orderid[] = $p['order_id'];
        }
        foreach ($producsAavailabilities as $r) {
            $a_data = array();
            $a_data['id'] = $r->id;
            $a_data['start_date'] = $r->start_date;
            $a_data['end_date'] = $r->end_date;
            $a_data['quantity'] = $r->quantity;
            $a_data['product'] = $r->product;
            $a_data['order'] = $r['_matchingData']['Orders'];
            $data[] = $a_data;
        }
        $this->apiResponse['data'] = $data;
        $this->apiResponse['page'] = $pageNo;
        $this->apiResponse['limit'] = $limit;
        $this->apiResponse['total'] = $total;
    }

    /*
     * Available quantity for block
     */
    public function unavailability()
    {
        $data = $this->request->getData();
        $this->add_model(array('ProductsAvailabilities', 'VariantsProducts', 'Quantities'));
        if (!isset($data['variants_products_id'])) {
            $default = $this->VariantsProducts->find()
                ->where(['product_id' => $data['product_id']])
                ->where(['is_default' => 1])
                ->where(['status' => 1])
                ->first();
            $data['variants_products_id'] = $default->id;
        }
        $stock = $this->Quantities->find()
            ->where(['Quantities.location' => $data['location']])
            ->where(['Quantities.product_id' => $data['product_id']])
            ->where(['Quantities.variants_products_id' => $data['variants_products_id']])
            ->first();
        $data['quantity_id'] = $stock->id;
        $date = $this->Timezones->_dateGlobal($data['date']);

        $unavailability = $this->ProductsAvailabilities->_unavailableQuantity($data, $date);
        $rentalQuantity = $this->ProductsAvailabilities->_getAvailableQuantity($data, $date);
        $this->apiResponse['quantity'] = empty($unavailability->quantity) ? $stock->quantity - $rentalQuantity : $unavailability->quantity;
    }

    /**
     *
     */
    public function remove()
    {
        $data = $this->request->getData();
        $requiredParam = ['product_id', 'variants_products_id', 'location'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->apiResponse['error'] = Configure::read('message.permission');
            return;
        }
        $this->add_model(array('ProductsAvailabilities', 'Quantities'));
        /* get quantity_id */
        $stock = $this->Quantities->find()
            ->where(['Quantities.location' => $data['location']])
            ->where(['Quantities.product_id' => $data['product_id']])
            ->where(['Quantities.variants_products_id' => $data['variants_products_id']])
            ->first();
        $data['quantity_id'] = $stock->id;

        /* date convert to UTC */
        //$date = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']), 'Y-m-d');
        $date = RentMy::toUTC($data['date'], 'Y-m-d');


        if ($this->ProductsAvailabilities->remove($data, $date)) {
            $this->apiResponse['message'] = 'Update saved.';
        } else {
            $this->apiResponse['error'] = Configure::read('message.wrong');
        }
    }

    /**
     * Add Unavailability
     *  POST /products-availabilities/add-unavailability
     */
    public function addUnavailability()
    {
        $data = $this->request->getData();
        $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->apiResponse['error'] = Configure::read('message.permission');
            return;
        }
        $this->add_model(array('ProductsAvailabilities', 'VariantsProducts', 'Quantities'));
        /* get quantity_id */
        $stock = $this->Quantities->find()
            ->where(['Quantities.location' => $data['location']])
            ->where(['Quantities.product_id' => $data['product_id']])
            ->where(['Quantities.variants_products_id' => $data['variants_products_id']])
            ->first();
        $data['quantity_id'] = $stock->id;

        /* date convert to UTC */
        // echo     $date = $this->Timezones->_dateGlobal($data['date']);
        //  if (empty(RentMy::$storeConfig['show_start_time'])) {
        $start = Time::parse($data['date'])->format('Y-m-d 00:00:00');
        $end = Time::parse($data['date'])->format('Y-m-d 23:59:59');
        //   $rent_start = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']));
        //  $rent_start = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']));
        $rent_start = RentMy::toUTC($start);
        $rent_end = RentMy::toUTC($end);
        // }
        /* Check available quantity */
        $rentalQuantity = $this->ProductsAvailabilities->getProductAvailability($data['product_id'], $data['quantity_id'], $data['location'], $rent_start, $rent_end);
//        $unavailability = $this->ProductsAvailabilities->_unavailableQuantity($data, $rent_start);
        $available = $stock->quantity - $rentalQuantity;//+ $unavailability;
        if ($available < $data['quantity']) {
            $this->apiResponse['error'] = 'Quantity must less than or equal to the available quantity';
            return;
        }

        /* Save Unavailable Quantity (edit if exist) */
        $options = array();
        $options = array_merge(array("product_id" => $data['product_id']), $options);
        $options = array_merge(array("quantity_id" => $data['quantity_id']), $options);
        $options = array_merge(array("order_id" => 0), $options);
        $options = array_merge(array("DATE_FORMAT(start_date,'%Y-%m-%d')" => $rent_start), $options);
        $unavailableQuery = $this->ProductsAvailabilities->find()
            ->where($options)
            ->first();
        if ($unavailableQuery) {
            $availableproduct = $this->ProductsAvailabilities->get($unavailableQuery->id);
        } else {
            $availableproduct = $this->ProductsAvailabilities->newEntity();
        }
        $data['start_date'] = $rent_start;
        $data['end_date'] = $rent_end;
        $data['actual_start_date'] = $rent_start;
        $data['actual_end_date'] = $rent_end;
        $availableproduct = $this->ProductsAvailabilities->patchEntity($availableproduct, $data);
        if ($this->ProductsAvailabilities->save($availableproduct)) {
            $this->apiResponse['message'] = 'Update saved.';
        } else {
            $this->apiResponse['error'] = 'Not saved!';
        }
    }

    /**
     * this function will make unavailable for a specific date range
     * @POST /products-availabilities/add-unavailability/by-date-range
     * @return void
     */

    public function addUnavailabilityByDateRange()
    {
        $connection = ConnectionManager::get('default');
        $data = $this->request->getData();
        $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->apiResponse['error'] = Configure::read('message.permission');
            return;
        }
        RentMy::addModel(array('ProductsAvailabilities', 'VariantsProducts', 'Quantities'));
        /* get quantity_id */
        $stock = RentMy::$Model['Quantities']->find()
            ->where(['Quantities.location' => $data['location']])
            ->where(['Quantities.product_id' => $data['product_id']])
            ->where(['Quantities.variants_products_id' => $data['variants_products_id']])
            ->first();
        $data['quantity_id'] = $stock->id;

        $startDate = Time::parse($data['start_date'])->format('Y-m-d');
        $endDate = Time::parse($data['end_date'])->format('Y-m-d');
        //   $rent_start = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']));
        //  $rent_start = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']));
        $connection->begin();
        while ($startDate <= $endDate) {
            $start = Time::parse($startDate)->format('Y-m-d 00:00:00');
            $end = Time::parse($startDate)->format('Y-m-d 23:59:59');
            //   $rent_start = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']));
            //  $rent_start = RentMy::toUTC(RentMy::getDefaultStartDate($data['date']));
            $rent_start = RentMy::toUTC($start);
            $rent_end = RentMy::toUTC($end);
            $rentalQuantity = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['quantity_id'], $data['location'], $rent_start, $rent_end);
            $available = $stock->quantity - $rentalQuantity;

            if ($available < $data['quantity']) {
                $connection->rollback();
                $this->apiResponse['error'] = 'Quantity must less than or equal to the available quantity';
                return;
            }

            /* Save Unavailable Quantity (edit if exist) */
            $options = array();
            $options = array_merge(array("product_id" => $data['product_id']), $options);
            $options = array_merge(array("quantity_id" => $data['quantity_id']), $options);
            $options = array_merge(array("order_id" => 0), $options);
            $options = array_merge(array("DATE_FORMAT(start_date,'%Y-%m-%d')" => Time::parse($rent_start)->format('Y-m-d')), $options);

            $unavailableQuery = RentMy::$Model['ProductsAvailabilities']->find()
                ->where($options)
                ->first();

            if ($unavailableQuery) {
                $availableProduct = RentMy::$Model['ProductsAvailabilities']->get($unavailableQuery->id);
            } else {
                $availableProduct = RentMy::$Model['ProductsAvailabilities']->newEntity();
            }
            $data['start_date'] = $rent_start;
            $data['end_date'] = $rent_end;
            $data['actual_start_date'] = $rent_start;
            $data['actual_end_date'] = $rent_end;
            $availableProduct = RentMy::$Model['ProductsAvailabilities']->patchEntity($availableProduct, $data);
            RentMy::$Model['ProductsAvailabilities']->save($availableProduct);
            $startDate = Time::parse($startDate)->addDay()->format('Y-m-d');
        }
        $connection->commit();
        $this->apiResponse['message'] = 'Update saved.';
    }
    /**
     * Add method
     */
    public function add()
    {
        $this->add_model(array('Products'));
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (!empty($data['product_id'])) {
                $query = $this->ProductsAvailabilities->find();
                $check = $query->select([
                    'quantity' => $query->func()->sum('quantity')
                ])
                    ->where(['product_id' => $data['product_id']])
                    ->where(['end_date >=' => date('Y-m-d')])
                    ->where(['order_id' => 0])
                    ->first();
                $product = $this->Products->get($data['product_id']);
                $available = $product['quantity'] - $check['quantity'];
                if ($available >= $data['quantity']) {
                    $availableproduct = $this->ProductsAvailabilities->newEntity();
                    $availableproduct = $this->ProductsAvailabilities->patchEntity($availableproduct, $data);
                    $this->ProductsAvailabilities->save($availableproduct);
                    $a_product = $this->ProductsAvailabilities->find('all')->select(['id', 'product_id', 'quantity', 'start_date', 'end_date', 'order_id'])->where(['product_id' => $data['product_id']])->toArray();
                    $this->apiResponse['data'] = array('success' => true, 'reserve' => $a_product, 'quantity' => $product['quantity'], 'available' => $available);
                } else {
                    $this->apiResponse['data'] = array('success' => false, 'reserve' => [], 'quantity' => $product['quantity'], 'available' => $available);
                }
            } else {
                $this->apiResponse['message'] = 'Method not allowed';
            }
        } else {
            $this->apiResponse['message'] = 'Method not allowed';
        }
    }

    /**
     * Edit method
     */
    public function edit()
    {
        $this->add_model(array('Products'));
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (!empty($data['id'])) {
                $aAvailableData = $this->ProductsAvailabilities->get($data['id']);
                $query = $this->ProductsAvailabilities->find();
                $check = $query->select([
                    'quantity' => $query->func()->sum('quantity')
                ])
                    ->where(['product_id' => $data['product_id']])
                    ->where(['end_date >=' => date('Y-m-d')])
                    ->where(['order_id' => 0])
                    ->first();
                $product = $this->Products->get($data['product_id']);
                $available = $product['quantity'] - ($check['quantity'] - $aAvailableData->quantity);
                if ($available >= $data['quantity']) {
                    $availableproduct = $this->ProductsAvailabilities->newEntity();
                    $availableproduct = $this->ProductsAvailabilities->patchEntity($availableproduct, $data);
                    $this->ProductsAvailabilities->save($availableproduct);
                    $a_product = $this->ProductsAvailabilities->find('all')->select(['id', 'product_id', 'quantity', 'start_date', 'end_date', 'order_id'])->where(['product_id' => $data['product_id']])->toArray();
                    $this->apiResponse['data'] = array('success' => true, 'reserve' => $a_product, 'quantity' => $product['quantity'], 'available' => $available);
                } else {
                    $this->apiResponse['data'] = array('success' => false, 'reserve' => [], 'quantity' => $product['quantity'], 'available' => $available);
                }
            } else {
                $this->apiResponse['message'] = 'Method not allowed';
            }
        } else {
            $this->apiResponse['message'] = 'Method not allowed';
        }
    }

    /**
     * Delete method
     */
    public function delete($id = null)
    {
        $this->add_model('Orders');
        if (!empty($id)) {
            $product = $this->ProductsAvailabilities->get($id);
            $orderid = $this->ProductsAvailabilities->find()->select(['order_id'])->where(['id' => $id])->first();

            if ($this->ProductsAvailabilities->delete($product)) {
                if (!empty($orderid)) {
                    $order = $this->Orders->get($id);
                    $this->Orders->delete($order);
                }
                $product = $this->ProductsAvailabilities->find('all')->where(['product_id' => $product['product_id']])->toArray();
                $this->apiResponse['data'] = $product;
            } else {
                $this->httpStatusCode = 405;
                $this->apiResponse['error'] = 'Product could not be deleted. Please try again.';
            }
        } else {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'No data has found. Please enter Product id';
        }
    }


    /**
     * This function returns multiple products - default variants availability
     * @param $type = cart/product
     * @param $token [cart token] when type = cart , token must be provided
     * @param $ids product ids - when type = product , ids must be provided
     * <AUTHOR> Kabir
     */
    public function products()
    {
        $data = $this->request->getData();
        if (empty($data['type'])) {
            $this->apiResponse['message'] = 'Invalid Request';
            return;
        }
        RentMy::addModel(['Carts']);
        $_data = $data;
        if (!empty($data['start_date']) && !empty($data['end_date'])){
            $_data['rent_start'] = $_data['start_date'];
            $_data['rental_type'] = 'rent';

            if (strtotime($data['start_date']) > strtotime($data['end_date'])){
                $this->httpStatusCode = 400;
                $this->apiResponse['error'] = 'To ensure a valid booking, the end date needs to be after the start date. Please adjust your selection.';
                return;
            }
        }
        if (!RentMy::$Model['Carts']->sameDateBooking($_data)){
            $this->apiResponse['same_day_booking'] = false;
            $this->apiResponse['error'] = 'Same-day booking is not permitted. Select another day. Please call for more options';
            return;
        }
        if (!empty($data['exact_times_id'])){
            $data['rent_start'] = $data['start_date'];
            $data = RentMy::exactTimeItemChecker($data);
            if (isset($data['exact_times_limit']) && !$data['exact_times_limit']['canAdd']){
                $this->httpStatusCode = 400;
                $this->apiResponse = ['message' => $data['exact_times_limit']['message']];
                return;
            }
        }

        if (!empty($data['fullfilment_option']) && $data['fullfilment_option'] == 'delivery') {
            RentMy::addModel(['DeliveryDetails']);
            $data['rent_start'] = $data['start_date'];
            $data = RentMy::$Model['DeliveryDetails']->maxDeliveryChecker($data);
            if (!empty($data['delivery_limit'])) {
                if(!$data['delivery_limit']['canAdd']) {
                    $this->apiResponse['error'] = $data['delivery_limit']['message'];
                    return;
                }
                $this->apiResponse['warning'] = $data['delivery_limit']['message'];
            }
        }

        RentMy::addModel(['Holidays']);
        $holiday = RentMy::$Model['Holidays']->checkHoliday(RentMy::$store->id, $data['start_date'], $data['end_date']);
        if ($holiday['success'] && !in_array(RentMy::$token->source, ['admin'])) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = "We're closed for " . $holiday['data']->description . " for the date you selected.";
            return;

        }

        if ($data['type'] == 'cart') { // check availability for cart items
            if (empty($data['token'])) {
                if($data['source'] == 'nano') {
                    $this->apiResponse['data'] =[
                        'rent_start'=> $data['start_date'],
                        'rent_end'=> $data['end_date']
                    ];
                    return;
                }
                $this->apiResponse['message'] = 'Invalid Request';
                return;
            }
            $this->availabilityFromCart($data);


        } elseif ($data['type'] == 'order') { // check availability for all ordered items
            if (empty($data['order_id'])) {
                $this->apiResponse['message'] = 'Invalid Request';
                return;
            }
            $this->availabilityFromOrder($data);
        } elseif ($data['type'] == 'product') { // check availability for product ids
            if (empty($data['ids'])) {
                $this->apiResponse['message'] = 'Invalid Request';
                return;
            }
        }


    }

    /**
     * Cart items /order items availability and update date
     * @param $data
     */
    function availabilityFromCart($data)
    {
        RentMy::addModel(['Carts', 'CartItems', 'ProductsAvailabilities', 'Products', 'Quantities', 'OrderProductOptions', 'Holidays']);
        $cart = RentMy::$Model['Carts']->find()->where(['uid' => $data['token']])->first();
        // when end date is disabled and update date range  by only start date
        // then start date = provided input , end date = rational end date from cart db and provide start date
        if (empty(RentMy::$storeConfig['show_end_date'])) {
            if (!empty($cart['rent_start'])) {
                // get old start date .
                $old_start_date = RentMy::toStoreTimeZone($cart['rent_start'], 'Y-m-d H:i:s');
                $start_date = Time::parse($data['start_date'])->format('Y-m-d H:i:s');
                $cartEndDate = Time::parse($cart['rent_end'])->format('Y-m-d H:i:s');
                // get difference
                $diff = strtotime($start_date) - strtotime($old_start_date);
                // add the difference with the new end date
                $end_date = RentMy::toStoreTimeZone(strtotime($cartEndDate) + $diff, 'Y-m-d H:i:s');
                $data['start_date'] = $start_date;
                $data['end_date'] = $end_date;
            }
        }

        $storeConfig = RentMy::$storeConfig;
        $isForceRentalAdjustment = isset($storeConfig['checkout']['force_rent_time'])?$storeConfig['checkout']['force_rent_time']:true;
        // allow rental return
        $isAllowRentalReturn= isset($storeConfig['checkout']['show_separate_date_picker'])?$storeConfig['checkout']['show_separate_date_picker']:false;

        if ((!empty($data['start_date']) && !empty($data['end_date'])) && ($isForceRentalAdjustment && !$isAllowRentalReturn)){
            $holiday = RentMy::$Model['Holidays']->checkStoreCloseTime($data['start_date'], $data['end_date']);
            $data['start_date'] = $holiday['start_date'];
            $data['end_date'] = $holiday['end_date'];
            $this->apiResponse['message'] = $holiday['message'];
        }


        $rentalDates = RentMy::formatRentalDates($data['start_date'], $data['end_date']);

        $siteLinkContent = RentMy::getSiteLinkContent();
        // holiday checking
        RentMy::addModel(['Holidays']);
        $storeTime=RentMy::$Model['Holidays']->checkStoreTime(RentMy::$store->id, $rentalDates['start_date'],[]);
        if (!$storeTime  && !in_array(RentMy::$token->source, ['admin'])) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = $siteLinkContent['message']['error_store_close'];
            return;
        }


        $cartItems = RentMy::$Model['CartItems']->find()->where(['cart_id' => $cart->id, 'rental_type !=' => 'buy', 'parent_id' => 0])
            ->toArray();

        if(empty($cartItems)){
            if($data['source'] == 'nano') { // when cart items empty and from sdk
                $cart->rent_start = RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i:00');
                $cart->rent_end = RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i:00');
                RentMy::$Model['Carts']->save($cart);
                $cartDetails = $this->Carts->getCartDetails($cart->id);
                $this->apiResponse['data'] = $cartDetails;
                $this->apiResponse['message'] = 'Rental dates updated.';
                return;
            }else{
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'There is no items in the cart.';
            }
        }
        $changedCartItems = [];
        foreach ($cartItems as $i => $cartItem) {
            $itemOptions = !empty($cartItem->options) ? json_decode($cartItem->options, true) : [];

            $options = [];
            if (!empty($itemOptions['recurring']['price_id'])){
                $options['price_id'] = $itemOptions['recurring']['price_id'];
            }
            if ($cartItem->product_type == 1) {
                $cartItem->rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($cartItem->product_id, $cartItem->variants_products_id, $rentalDates['start_date'], $rentalDates['end_date'], $cartItem, 'cart', [], $options);
                $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($cartItem->product_id, $cartItem->variants_products_id, $cartItem->location, RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i'), RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i'));
                $changedCartItems[$i] = ['rental_price' => $cartItem->rental_price, 'available' => $available, 'id' => $cartItem->id, 'quantity' => $cartItem->quantity];
            } elseif ($cartItem->product_type == 2) {
                $cartItem->rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($cartItem->product_id, $cartItem->variants_products_id, $rentalDates['start_date'], $rentalDates['end_date'], $cartItem, 'cart', [], $options);
                $available = RentMy::$Model['ProductsAvailabilities']->getAvailableForPackage('cart', $cartItem->product_id, $cartItem->location, RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i'), RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i'), ['cart_item_id' => $cartItem->id]);
                $changedCartItems[$i] = ['rental_price' => $cartItem->rental_price, 'available' => $available, 'id' => $cartItem->id, 'quantity' => $cartItem->quantity];
            }
        }
        $collection = (new Collection($changedCartItems))->filter(function ($item, $key) {
            return ($item['quantity'] <= $item['available']) ? $item['available'] : 0;
        });

        if ($collection->isEmpty()) {
            if (!in_array($data['source'],['admin','nano'])) {
                $this->httpStatusCode = 400;
                $this->apiResponse = ['message' => 'Some items are not available within the selected rental period', 'error' => $changedCartItems];
                return;
            }
            if($data['source'] == 'nano'){  // for project nano, update cart with the provided dates, although item not available.
                $this->apiResponse = ['message' => 'Some items are not available within the selected rental period', 'error' => $changedCartItems];
            }
        }

        if (!empty($collection->toArray()) || ($data['source'] == 'nano')) { // update cart items
            foreach ($cartItems as $i => $cartItem) {
                $cartItem['membership_checkout'] = !empty($data['membership_checkout']);
                RentMy::$Model['CartItems']->updateProductPrice($cart, $cartItem);
            }

            // update date for cart
            $cart->rent_start = RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i:00');
            $cart->rent_end = RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i:00');
            //update date for all cart items
            RentMy::$Model['CartItems']->updateAll(['rent_start' => RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i:00'), 'rent_end' => RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i:00')],
                ['cart_id' => $cart->id]);
            $this->Carts->_updatePriceNQty($cart);
            $cartDetails = $this->Carts->getCartDetails($cart->id);
            if (RentMy::$storeConfig['tax']['address'] == 'store') {
                $cartDetails = $this->Carts->addTaxLookup($cartDetails);
            }
            $allCartItemsCollection = new Collection($changedCartItems);
            foreach($cartDetails['cart_items'] as $i=>$items){
             $items['available'] = $allCartItemsCollection->match(['id' => $items['id']])
                    ->map(function ($mitem) {
                        //echo $mitem['available'].'....';
                        return  $mitem['available'];
                    })
                    ->first();

            }

            $this->apiResponse['data'] = $cartDetails;
        }
    }

    /**
     * Generic function for getting availability by cart token, cart item ids, products ids , order id
     * @param
     */
    function getAvailability()
    {
        RentMy::addModel(['Carts', 'CartItems', 'ProductsAvailabilities', 'Products', 'Quantities']);
        $data = $this->request->getQueryParams();
        $changedCartItems = [];
        switch ($data['type']) {
            case 'cart' :
                $cart = RentMy::$Model['Carts']->find()->where(['uid' => $data['token']])->first();

                // when end date is disabled and update date range  by only start date
                // then start date = provided input , end date = rational end date from cart db and provide start date
                $rentalDates['start_date'] = $cart->rent_start;
                $rentalDates['end_date'] = $cart->rent_end;
                $cartItems = RentMy::$Model['CartItems']->find()->where(['cart_id' => $cart->id, 'rental_type !=' => 'buy', 'parent_id' => 0])
                    ->toArray();
                if(empty($cartItems)) {
                    throw new UnauthorizedException('Invalid cart id or there is no products added in the cart');
                }

                $changedCartItems = [];
                foreach ($cartItems as $i => $cartItem) {
                    if ($cartItem->product_type == 1) {
                        $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($cartItem->product_id, $cartItem->variants_products_id, $cartItem->location, $rentalDates['start_date'], $rentalDates['end_date']);
                        $changedCartItems[$i] = ['available' => $available, 'cart_item_id' => $cartItem->id, 'quantity' => $cartItem->quantity, 'product_id' => $cartItem->product_id, 'variants_products_id' => $cartItem->variants_products_id];
                    } elseif ($cartItem->product_type == 2) {
                        $available = RentMy::$Model['ProductsAvailabilities']->getAvailableForPackage('cart', $cartItem->product_id, $cartItem->location, $rentalDates['start_date'], $rentalDates['end_date'], ['cart_item_id' => $cartItem->id]);
                        $changedCartItems[$i] = ['rental_price' => $cartItem->rental_price, 'available' => $available, 'cart_item_id' => $cartItem->id, 'quantity' => $cartItem->quantity, 'product_id' => $cartItem->product_id, 'variants_products_id' => $cartItem->variants_products_id];
                    }
                }

                break;
            default :
                break;
        }

        $this->apiResponse['data'] = $changedCartItems;

    }

    /**
     * Cart items availability chaecking
     * @param $data
     */
    function availabilityFromOrder($data)
    {
        $rentalDates = RentMy::formatRentalDates($data['start_date'], $data['end_date']);
        $rent_start = RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i');
        $rent_end = RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i');
        RentMy::addModel(['Orders', 'OrderItems', 'ProductsAvailabilities', 'Products', 'Quantities']);
        $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
        $orderOptions = !empty($order->options) ? json_decode($order->options, true) : [];
        $order['vendor_id'] = $orderOptions['vendor_id'] ?? '';
        $orderItems = RentMy::$Model['OrderItems']->find()->where(['order_id' => $order->id, 'rental_type !=' => 'buy', 'parent_id' => 0])
            ->contain(['Products' => function  ($query) {
                return $query->select(['id', 'name']);
            }])
            ->toArray();

        $changedOrderItems = [];
        foreach ($orderItems as $i => $orderItem) {
            $itemOptions = !empty($orderItem->options) ? json_decode($orderItem->options, true) : [];

            $options = [];
            if (!empty($itemOptions['recurring']['price_id'])){
                $options['price_id'] = $itemOptions['recurring']['price_id'];
            }
            if ($orderItem->product_type == 1) {
                $orderItem->rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($orderItem->product_id, $orderItem->variants_products_id, $rentalDates['start_date'], $rentalDates['end_date'], $orderItem, 'order', [], $options);
                $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($orderItem->product_id, $orderItem->variants_products_id, $orderItem->location, $rent_start, $rent_end,['order_id'=> $orderItem->order_id,'order_item_id'=> $orderItem->id]);
                $changedOrderItems[$i] = ['rental_price' => $orderItem->rental_price, 'available' => $available, 'id' => $orderItem->id, 'quantity' => $orderItem->quantity, 'product_id'=>$orderItem->product_id, 'product_name' => $orderItem->product['name'], 'variants_products_id' => $orderItem->variants_products_id];
            } elseif ($orderItem->product_type == 2) {
                $orderItem->rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($orderItem->product_id, $orderItem->variants_products_id, $rentalDates['start_date'], $rentalDates['end_date'], $orderItem, 'order', [], $options);
                $available = RentMy::$Model['ProductsAvailabilities']->getAvailableForPackage('order', $orderItem->product_id, $orderItem->location, $rent_start, $rent_end, ['order_item_id' => $orderItem->id,'order_id'=> $orderItem->order_id]);
                $changedOrderItems[$i] = ['rental_price' => $orderItem->rental_price, 'available' => $available, 'id' => $orderItem->id, 'quantity' => $orderItem->quantity, 'product_id'=>$orderItem->product_id, 'product_name' => $orderItem->product['name']];
            }
        }


        $unavailableItems = (new Collection($changedOrderItems))->filter(function ($item) {
            // Consider unavailable if product_id is 0 or not enough stock
            if ($item['product_id'] == 0) {
                return true;
            }
            return $item['quantity'] > $item['available'];
        });

        if (!$unavailableItems->isEmpty()) {
            //if ($data['source'] != 'admin') {
            $productName = implode(', ', array_column($unavailableItems->toArray(), 'product_name'));
            $isAre =$unavailableItems->count() > 1 ? 'are': 'is';
                $this->httpStatusCode = 400;
                $this->apiResponse = ['message' => $productName . ' ' . $isAre.' not available within the selected rental period', 'error' => $changedOrderItems];
                return;
            //}
        }
        $collection = (new Collection($changedOrderItems))->filter(function ($item, $key) {
            if ($item['product_id'] == 0)
                return $item['quantity'];

            return ($item['quantity'] <= $item['available']) ? $item['available'] : 0;
        });
    //    RentMy::dbg($collection->toArray());exit();
        $options = !empty($order->options) ? json_decode($order->options, true): [];
        if (!empty($collection->toArray())) { // update order items price for the date range
            foreach ($orderItems as $i => $orderItem) {
                $orderItem['membership_checkout'] = !empty($options['membership_checkout']);
                RentMy::$Model['OrderItems']->updateProductPrice($order, $orderItem);
            }

            // update date for cart
            $order->rent_start = RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i:00');
            $order->rent_end = RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i:00');
            //update date for all cart items
            RentMy::$Model['OrderItems']->updateAll(['rent_start' => RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i:00'), 'rent_end' => RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i:00')],
                ['order_id' => $order->id]);
            $this->Orders->_updatePriceNQty($order);
            RentMy::$Model['ProductsAvailabilities']->updateDate($data['order_id'], $order->rent_start, $order->rent_end);

            RentMy::addModel(['DeliveryDetails']);
            $deliveryDetails = RentMy::$Model['DeliveryDetails']->find()->where(['order_id' => $order->id])->first();
            $deliveryDetails->delivery_date = $order->rent_start;
            RentMy::$Model['DeliveryDetails']->save($deliveryDetails);

            RentMy::$Model['Orders']->updateOrderTaxLookup($order->id);
            $orderDetails = $this->Orders->view($order->id);
            $this->apiResponse['data'] = $orderDetails;
        }
    }


    /**
     * Get Product availablity by any range type and duration
     * @param $rangeType ='' hour, month, day , week
     * @param $start_date
     * @param $end_date
     * @param @location
     * @param $variants_products_id
     *
     *
     */
    function getAvailabilityByRange()
    {
        $data = $this->request->getQueryParams();
        RentMy::addModel(['ProductsAvailabilities', 'ProductPrices']);
        if (!empty($data['rangeType'])) {
            if ($data['rangeType'] == 'month') {
                $start = Time::parse($data['start_date'])->format('n');
                $end = Time::parse($data['end_date'])->format('n');
                for ($i = $start; $i <= $end; $i++) {
                    $days = cal_days_in_month(CAL_GREGORIAN, $i, 2020);
                    $month = ($i > 9) ? $i : '0' . $i;
                    $startDate = Time::parse($data['start_date'])->format('Y-' . $month . '-01');
                    $endDate = Time::parse($data['start_date'])->format('Y-' . $month . '-' . $days);
                    $price = RentMy::$Model['ProductPrices']->getRentalPriceByDates($data['product_id'], $data['variants_products_id'], $startDate, $endDate);
                    $available = RentMy::$Model['ProductsAvailabilities']->getAvailableForProduct($data['product_id'], $data['variants_products_id'], $data['location'], $startDate);
                    $price =
                    $response[] = [
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'available' => $available,
                        'price' => $price
                    ];
                }
            }
        }
        $this->apiResponse['data'] = $response;

    }

    /**
     * Get Product wise total inventory quantity and avilability
     * @POST /products-availabilities
     * @param $data ['start_date'] date time
     * @param $data ['end_date'] - date time
     * @param $data ['location_id'] - int
     * @param $data ['product_ids'] - array
     */
    public
    function productWiseQuantityAndAvilability()
    {
        $data = $this->request->getData();
        $requiredParam = ['start_date', 'end_date', 'product_ids', 'location_id'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = 'Invalid request params';
            return;
        }

        RentMy::addModel(['Quantities', 'ProductsAvailabilities', 'Products']);
        $defaultVariantsProductsIds = RentMy::$Model['Products']->find('list', [
            'valueField' => 'variants_products_id',
        ])->where(['id IN' => $data['product_ids']])->toList();
        // total quantity for products for any location
        $where = [
            'Quantities.product_id IN' => $data['product_ids'],
        ];
        if (empty(RentMy::$storeConfig['inventory']['affiliate']['active'])){
            $where['Quantities.location'] = $data['location_id'];
        }
        $pQuantities = [];
        $productQuantites = RentMy::$Model['Quantities']->find()
            ->select(['Quantities.product_id', 'Quantities.quantity', 'Quantities.available'])
            ->contain(['VariantsProducts'])
            ->where($where)
            ->where(['VariantsProducts.status' => 1])
            ->groupBy('product_id')
            ->each(function ($products, $key) use (&$pQuantities) {
                $pCollection = (new Collection($products))->sumOf('quantity');
                $pCollectionAvailable = (new Collection($products))->sumOf('available');
                $pQuantities[] = ['product_id' => $key, 'quantity' => $pCollection, 'available' => $pCollectionAvailable];
            })
            ->toArray();

        //   RentMy::dbg($pQuantities);
        $data['start_date'] = Time::parse($data['start_date'])->format('Y-m-d 00:00');
        $data['start_date'] = RentMy::toUTC($data['start_date']);
        $data['end_date'] = Time::parse($data['end_date'])->format('Y-m-d 23:59');
        $data['end_date'] = RentMy::toUTC($data['end_date']);
        $rentalQuantity = RentMy::$Model['ProductsAvailabilities']->getRentalQuantityWithoutVariant($data['product_ids'], $data['location_id'], $data['start_date'], $data['end_date']);
        // total availability for products for any location during a date range

        // RentMy::dbg($rentalQuantity);
        foreach ($data['product_ids'] as $i => $id) {

            $products[$i] = [
                'product_id' => $id,
                'quantity' => 0,
                'available' => 0
            ];
            foreach ($pQuantities as $q) {

                if ($q['product_id'] == $id) {
                    $products[$i]['quantity'] = $q['quantity'];
                    $products[$i]['available'] = $q['quantity'];
                    $products[$i]['track'] = $q['available'];
                }
            }
            foreach ($rentalQuantity as $rent) {
                if ($id == $rent['product_id'])
                    if ($rent['is_tracked']) {
                        $products[$i]['available'] = $products[$i]['quantity'] - $rent['rental'];

                    } else {
                        $products[$i]['available'] = $products[$i]['quantity'] - $rent['rental'];
                    }
                //   unset($products[$i]['track']);
            }
        }

        $this->apiResponse['data'] = $products;
    }

    /**
     * Update stock alert
     * @param data[quantity_id]
     * @param data[quantity]
     * @param data[all_variants] true|false
     * @API POST /stock/alert/update
     */
    function updateStockAlert()
    {
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();
        $requiredParams = ['quantity', 'quantity_id'];
        if (RentMy::requiredKeyExist($data, $requiredParams)) {
            throw new NotFoundException('Required data missing');
            return;
        }

        RentMy::addModel(['Quantities']);
        $quantity = RentMy::$Model['Quantities']->find()
            ->where([
                'id' => $data['quantity_id']
            ])
            ->first();
        if ($quantity['store_id'] != RentMy::$store->id) {
            throw new UnauthorizedException('You are not authorized for this action');
            return;
        }
        if ($data['all_variants']) { // update all variants for this product
            RentMy::$Model['Quantities']->updateAll(['stock_alert' => $data['quantity']],
                ['product_id' => $quantity['product_id']]);
        } else { // update only this variant
            RentMy::$Model['Quantities']->updateAll(['stock_alert' => $data['quantity']],
                ['variants_products_id' => $quantity['variants_products_id']]);
        }
        $this->apiResponse['message'] = 'Stock alert quantity updated.';

    }


    /**
     * Available time
     *
     * this function will return all available time of a day
     *
     * @param id
     */

    public function checkAvailability(){
        RentMy::addModel(['Holidays', 'ProductsAvailabilities']);
        $data = $this->request->getData();
        $storeId = RentMy::$store->id;
        $startTime = '08:00';
        $endTime = '18:00';
        $day = $data['day'];
        $interval = 60;
        $available = [];


        $week_day = Time::parse($day)->format('N');
        $opeCloseHours = RentMy::$Model['Holidays']->find()
            ->where([
                'store_id' => $storeId,
                'location' => RentMy::$token->location,
                'day' => $week_day,
                'type' => 'day',
                'is_open' => 1
            ])
            ->first();
        if (!empty($opeCloseHours)){
            $startTime = Time::parse(RentMy::toStoreTimeZone($opeCloseHours->start_time))->format('H:i');
            $endTime = Time::parse(RentMy::toStoreTimeZone($opeCloseHours->end_time))->format('H:i');
        }

        $day = Time::parse($day)->format('Y-m-d');
        $closingDateTime = Time::parse($day)->format('Y-m-d') . ' ' . $endTime;
        $dayTime = Time::parse($day)->format('Y-m-d').' '.$startTime;
        $i = 0;
        while (strtotime($dayTime) <= strtotime($closingDateTime)){

            $ren_start = Time::parse($dayTime)->addMinutes(1)->format('Y-m-d H:i');
            $rent_end = Time::parse($dayTime)->addMinutes($interval)->format('Y-m-d H:i');
            $data['rent_start'] = RentMy::toUTC($ren_start, 'Y-m-d H:i');
            $data['rent_end'] = RentMy::toUTC($rent_end, 'Y-m-d H:i');
            $availableQty = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $data['rent_start'],  $data['rent_end']);

            $available[$i]['time'] = Time::parse($dayTime)->format('H:s');
            $available[$i]['is_available'] = $availableQty>0;
            $available[$i]['available'] = $availableQty;
            $available[$i]['rent_start'] = $ren_start;
            $available[$i]['rent_end'] = $rent_end;

            $dayTime = Time::parse($dayTime)->addMinutes($interval)->format('Y-m-d H:s');
            $i++;
        }

        $this->apiResponse['data'] = $available;
        $this->apiResponse['date'] = $day;
    }
}
