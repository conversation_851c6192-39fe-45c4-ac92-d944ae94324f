<?php

namespace App\Controller;

use App\Lib\ProductContainer;
use App\Lib\MatrixCodeGenerator;
use App\Lib\RentMy\RentMy;
use App\Lib\S3;
use App\Services\Cache\CacheService;
use App\Services\Captcha\NumberImageCaptcha;
use App\Services\Receipt\OrderReceipt;
use Cake\Cache\Cache;
use Cake\Collection\Collection;
use Cake\Http\Exception\BadRequestException;
use Cake\Http\Exception\NotFoundException;
use Cake\I18n\Number;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Hash;
use RestApi\Utility\JwtToken;
use Throwable;

/**
 * Class PagesController
 * @package App\Controller
 */
class PagesController extends AppController
{
    private $productContainer;

    public function initialize()
    {
        parent::initialize();
        $this->productContainer = new ProductContainer();
    }

    /**
     * get payment method list
     */

    public function getPaymentMethod()
    {
        $paymentType = Configure::read('paymentType');
        $onlinePaymentMethod = Configure::read('onlinePaymentMethod');
        $offlinePaymentMethod = Configure::read('offlinePaymentMethod');
        $data = array(
            'paymentType' => $paymentType,
            'onlinePaymentMethod' => $onlinePaymentMethod,
            'offlinePaymentMethod' => $offlinePaymentMethod
        );
        $this->apiResponse['data'] = $data;

    }

    /*
     * Settings of payment gateways
     */
    public function paymentGatewaySettings()
    {
        $paymentGateways = Configure::read('paymentGateways');
        $this->apiResponse['data'] = $paymentGateways;
    }

    /*
     * List of payment gateways
     */
    public function paymentGatewayList()
    {
        $paymentGateways = Configure::read('paymentGatewayList');
        $this->apiResponse['data'] = $paymentGateways;
    }


    /**
     * get order status list
     * @deprecated
     */
    public function shippingMethod()
    {
        $shippingMethod = Configure::read('shippingMethod');
        $this->apiResponse['data'] = $shippingMethod;

    }

    /**
     * get tax Value
     */
    public function taxValue()
    {
        $taxValue = Configure::read('taxValue');
        $this->apiResponse['data'] = $taxValue;

    }

    public function orderpdf()
    {
        RentMy::addModel(array('Orders', 'OrderItems', 'Payments', 'OrderAddresses', 'OrderCharge', 'OrderProductOptions', 'Locations', 'Timezones', 'Templates', 'Contents', 'DeliveryDetails', 'StoresUsers'));

        $this->request->allowMethod('get');
        $params = $this->request->getQuery();
        $orderId = $params['order_id'];

        $orderReceiptObj = new OrderReceipt();
        $event = $_options['event'] ?? '';
        $orderReceiptObj->setOrder($orderId)->setOrderItem()->setFulfillment()->setEventSpecificData($event)->setPaymentInfo()->setStoreInfo();
        $order = $orderReceiptObj->getOrder();

        RentMy::addModel(['Templates']);
        $template = RentMy::$Model['Templates']->find()
            ->where(['store_id' => $order->store_id, 'location'=>$order->location])
            ->where(['type' => 1, 'is_downloadable' => 1, 'status' => 1])
            ->first();

        $template = $orderReceiptObj->setTemplate($template->id)->getTemplate();
        $contents = $orderReceiptObj->getContent();
        $store_text = $contents['store_text'];
        $store_config = $orderReceiptObj->getStoreConfig();
        $content = $contents['contents'];

        $orderShowId = $orderReceiptObj->getOrderShowableId();

        $pdfTemplate = 'order';
        if ($template->content_type == 2){
            $pdfTemplate = 'default';
            $content = $template['content_body'];
        }
        if ($params['type'] == 'receipt') {
            $pdfTemplate = 'print_receipt';
        }

        $this->viewBuilder()->className('Dompdf.Pdf')
            ->layout('Dompdf.pdf/default')
            ->template('Pages/pdf/' . $pdfTemplate)
            ->options(['config' => [
                'filename' => $orderShowId,
                'render' => 'download'
            ]]);
        $view = $this->viewBuilder()->build();

        $view->set(compact('template', 'order', 'content', 'store_config', 'store_text'));
        $view->render();

    }


    /*
     * Clear Cache
     */
    public function clearCache()
    {
        if (\Cake\Cache\Cache::clearAll()) {
            $this->apiResponse['message'] = 'Successfully deleted.';
        } else {
            $this->apiResponse['message'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
        }
    }

    public function payments()
    {

    }

    /*
     * Image upload in temp directory
    */
    public function imageUpload()
    {
        $data = $this->request->getData();
        if (!empty($data)) {
            foreach ($data['file'] as $file) {
                if (!empty($file['name'])) {
                    $dir = WWW_ROOT . 'img' . DS . 'temp' . DS;
                    $file_name = $file['name'];
                    move_uploaded_file($file['tmp_name'], $dir . $file_name);
                }
            }
            $this->apiResponse['message'] = 'Successfully uploaded.';
        } else {
            $this->apiResponse['message'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
        }
    }

    /*
     * Get active locations list for admin upldate quantity
     * GET /products/locations
     */
    public function locations()
    {
        $storeId = RentMy::$store->id;
        RentMy::addModel(['Locations']);
        $locations = RentMy::$Model['Locations']->find()->select(['id', 'name', 'is_online'])->where(['store_id' => $storeId, 'status' => 1, 'is_virtual !=' => 1])->toArray();
        $this->apiResponse['data'] = $locations;
    }

    /**
     *  This function used for getting initial configuration for online store.
     * @param store_name | string
     * @return json
     *      location [ id, name, country code ]
     *      store [ id, layout[colorSetting, layout_id, section[]], logo, token ]
     * @api get-setting?store_name = xxxx
     */
    public function defaultLocation()
    {
        $this->add_model(array('Stores', 'AccessLogs'));
        $data = $this->request->getQueryParams();
        if (empty($data['store_name'])) {
            $this->apiResponse['error'] = 'Missing store name.';
            return;
        }
        $store = $this->Stores->find()->select(['id', 'uid', 'name', 'slug', 'config', 'logo', 'settings', 'store_type', 'plan_id'])->where(['name' => $data['store_name'], 'status' => 1])->first();
        if (empty($store)) {
            $store = $this->Stores->find()->select(['id', 'uid', 'name', 'slug', 'config', 'logo', 'settings', 'store_type', 'plan_id'])->where(['domain' => $data['store_name'], 'status' => 1])->first();
        }
        if (empty($store)) {
            $this->apiResponse['error'] = 'Not Found!';
            return;
        }

        if (!empty($store['logo'])) {
            $store['logo'] = RentMy::makeS3Url('/store-logo/' . $store['id'] . '/' . $store['logo']);
        }
        if (!empty($store)) {
            $storeId = $store['id'];
        } else {
            $storeId = 0;
        }
        $this->add_model(array('Locations', 'Contents'));
        $locations = $this->Locations->find()->select(['id', 'name', 'country'])->where(['is_online' => 1, 'status' => 1])->where(['store_id' => $storeId])->order(['is_virtual' => 'DESC'])->first();
        $location = $locations['id'];

        if (!empty($data['location'])){
            $locationCheck = $this->Locations->find()->select(['id', 'name', 'country'])->where(['is_online' => 1, 'status' => 1])->where(['store_id' => $storeId, 'id'=>$data['location']])->order(['is_virtual' => 'DESC'])->first();
            if (!empty($locationCheck)){
                $locations = $locationCheck;
                $location = $data['location'];
            }
        }

        RentMy::getStore($storeId, $location);
        $token_data = $this->AccessLogs->find()->where(['store_id' => $store['id'], 'location' => $location, 'status' => 1, 'is_online' => 1])->first();

        if (!empty($store) && !empty($locations)) {
            $layout = !empty($store->settings) ? (!empty(json_decode($store->settings, true)[$location]['layout']) ? json_decode($store->settings, true)[$location]['layout'] : 0) : 0;
            $locations->country = empty($locations->country) ? 'US' : $locations->country;

            $storeConfig = json_decode($store['config'], true)[$location];

            //if ($storeConfig['checkout']['multiple_location']) {
                $allOnlineLocations = $this->Locations->find()->select(['id', 'name', 'country'])->where(['is_online' => 1, 'status' => 1])->where(['store_id' => $storeId])->order(['name' => 'ASC'])->toArray();
            //}

            // Custom CSS
            $contents = $this->Contents->find()
                ->select(['contents'])
                ->where(['store_id' => $storeId, 'location'=>$location, 'type' => 'custom-css'])
                ->first();

            // Custom CSS
            $contentsJs = $this->Contents->find()
                ->select(['contents'])
                ->where(['store_id' => $storeId, 'location'=>$location, 'type' => 'custom-js'])
                ->first();
            $customCss = '';
            $customJs = '';
            if (!empty($contents)) {
                $customCss = $contents->contents;
            }

            if (!empty($contentsJs)) {
                $customJs = $contentsJs->contents;
            }
            //Custom JS
            $custom_js = false;
            if (file_exists(WWW_ROOT . 'js' . DS . 'upload' . DS . $store['uid'] . '.js')) {
                $custom_js = true;
            }

            $this->apiResponse['location'] = $locations;
            //if (!empty($allOnlineLocations)) {
                $this->apiResponse['locations'] = $allOnlineLocations;
            //}

            $token = $token_data['token'];

            if (!empty($data['customer_id'])){
                RentMy::addModel(['Customers']);
                $customer = RentMy::$Model['Customers']->find()->where(['id' => $data['customer_id'], 'store_id' => $storeId])->first();
                if (!empty($customer)){
                    $options = !empty($customer->optional) ? json_decode($customer->optional, true) : [];
                    $token_salt = [
                        'id' => $customer['id'],
                        'customer_id' => $customer['id'],
                        'email' => $customer['email'],
                        'date' => date('Y-m-d h:i:s'),
                        'store_id' => $customer['store_id'],
                        'status' => $customer['status'],
                        'customer_type' => $customer['type'],
                        'source' => 'online',
                        'location'=> $location,
                        'tax_exempt' => !empty($options['tax_exempt'])
                    ];
                    if (!empty(RentMy::$storeConfig['client']['active'])){
                        if (($customer['type'] == 2) && !empty($customer['username'])){
                            $token_salt['vendor_id'] = $customer['id'];
                        }
                    }

                   $token = JwtToken::generateToken($token_salt);
                }
            }

            $this->apiResponse['store'] = ['id' => $store['id'], 'uid' => $store['uid'], 'name' => $store['name'], 'slug' => $store['slug'],
                'logo' => $store['logo'], 'custom-css' => $customCss,
                'custom-js' => $custom_js,
                'custom_js_code' => $customJs,
                'layout' => $layout,
                'plan_type' => RentMy::checkPaidStoreType($store),
                'account_type' => strtoupper($store->store_type),
                'token' => $token];
        } else {
            throw new NotFoundException();
        }

    }

    /**
     *  This function used for getting initial configuration for online store.
     * @param store_name | string
     * @return json
     *      location [ id, name, country code ]
     *      store [ id, layout[colorSetting, layout_id, section[]], logo, token ]
     * @api get-setting?store_name = xxxx
     */
    public function guestToken()
    {
        $this->add_model(array('Stores', 'AccessLogs'));
        $data = $this->request->getQueryParams();
        if (empty($data['store_name'])) {
            $this->apiResponse['error'] = 'Missing store name.';
            return;
        }
        $store = $this->Stores->find()->select(['id', 'uid', 'name', 'logo', 'settings', 'store_type'])->where(['name' => $data['store_name'], 'status' => 1])->first();
        if (empty($store)) {
            $store = $this->Stores->find()->select(['id', 'uid', 'name', 'logo', 'settings'])->where(['domain' => $data['store_name'], 'status' => 1])->first();
        }
        if (empty($store)) {
            $this->apiResponse['error'] = 'Not Found!';
            return;
        }

        $token_data = $this->AccessLogs->find()->where(['store_id' => $store['id'], 'status' => 1, 'is_online' => 1])->first();
        if (!empty($store['logo'])) {
            $store['logo'] = Router::url('img/upload/store-logo/' . $store['id'] . '/' . $store['logo'], true);
        }
        if (!empty($store)) {
            $storeId = $store['id'];
        } else {
            $storeId = 0;
        }
        $this->add_model(array('Locations', 'Contents'));
        $locations = $this->Locations->find()->select(['id', 'name', 'country'])->where(['status' => 1, 'is_online' => 1])->where(['store_id' => $storeId])->first();
        if (empty($locations)) {
            $locations = $this->Locations->find()->select(['id', 'name', 'country'])->where(['status' => 1])->where(['store_id' => $storeId])->first();
        }
        $location = $locations['id'];
        if (!empty($store) && !empty($locations)) {
            $layout = !empty($store->settings) ? (!empty(json_decode($store->settings, true)[$location]['layout']) ? json_decode($store->settings, true)[$location]['layout'] : 0) : 0;
            $locations->country = empty($locations->country) ? 'US' : $locations->country;

            // Custom CSS
            $contents = $this->Contents->find()
                ->select(['contents'])
                ->where(['store_id' => $storeId, 'type' => 'custom-css'])
                ->first();
            $customCss = '';
            if (!empty($contents)) {
                $customCss = $contents->contents;
            }
            //Custom JS
            $custom_js = false;
            if (file_exists(WWW_ROOT . 'js' . DS . 'upload' . DS . $store['uid'] . '.js')) {
                $custom_js = true;
            }

            $this->apiResponse['location'] = $locations;
            $this->apiResponse['store'] = ['id' => $store['id'], 'uid' => $store['uid'], 'name' => $store['name'],
                'logo' => $store['logo'], 'custom-css' => $customCss,
                'custom-js' => $custom_js,
                'layout' => $layout,
                'plan_type' => RentMy::checkPaidStoreType($store),
                'token' => $token_data['token']];
        } else {
            throw new NotFoundException();
        }

    }

    /*
     * Contact us from online store
     * For type = newsletter , saving data into newsletter ( from the footer of newsletter section)
     * For type = customer saving into customers ( contact us page )
     * @API - POST /contactus
     */
    public function contactus()
    {

        if (!empty($this->request->getData())) {
            $data = $this->request->getData();
            RentMy::addModel(['Users', 'Newsletters', 'StoresUsers', 'Captcha']);

            if ($data['type'] != 'newsletter') {
                // Get captcha using token to match with the answer
                if (!empty($data['token']) && !empty($data['answer'])){
                    $getCaptcha = RentMy::$Model['Captcha']->find()->where(['token' => $data['token']])->first();

                    if(empty($getCaptcha) || ($getCaptcha['correct_answer'] != $data['answer'])){
                        // Delete captcha if answer doesn't matches
                        (new NumberImageCaptcha())->deleteCaptcha($data['token']);
                        //recreate new catcha
                        $data = (new NumberImageCaptcha())->createCaptcha();

                        $this->httpStatusCode = 400;
                        if(!empty($data)){
                            $this->apiResponse['data'] = $data;
                            $this->apiResponse['message'] = 'Answer didn\'t match. Please try again';

                        } else {
                            $this->apiResponse['error'] = 'Error generating captcha.';
                        }

                        return;
                    }

                    // Delete captcha if answer matches
                    (new NumberImageCaptcha())->deleteCaptcha($data['token']);
                } else {
                    $this->apiResponse['error'] = 'Please answer the captcha.';
                    return;
                }
            }

            $user_email = RentMy::$Model['StoresUsers']->getReceiptEmailsByType('email', 'contact');
            if (empty($user_email)) {
                $user = RentMy::$Model['Users']->find()->where(['id' => RentMy::$store->user_id])->first();
                $user_email = $user->email;
            }
            // get mobile no for alert sms
            $mobiles = RentMy::$Model['StoresUsers']->getReceiptEmailsByType('sms', 'contact');
            if (!empty($mobiles)) {
                $storeName = RentMy::$store->name;
                $msg = 'New Contact from ' . RentMy::$store->slug . '::';
                $msg .= 'Name:' . $data['first_name'] . ' ' . $data['last_name'] . ', Email:' . $data['email'] . ', Phone:' . $data['phone'] . ', Message: ' . $data['message'];
                $smsObj = new \App\Lib\Sms();
                foreach ($mobiles as $mobile) {
                    $smsObj->send($mobile, $msg);
                }
            }
            $options = !empty($data['options']) ? $data['options'] : [];

            RentMy::$Model['Newsletters']->add(['email' => $data['email'], 'options' => $options]);
            if ($data['type'] == 'newsletter') {
                $data['type'] = 'newsletter';
            } elseif ($data['type'] == 'customer') {
                $data['type'] = 'customer';
                RentMy::addModel(['Customers']);
                RentMy::$Model['Customers']->create(['first_name' => $data['first_name'], 'last_name' => $data['last_name'], 'email' => $data['email']]);
            } else {
                $data['type'] = '';
            }

            // Store attachment temporarily in local machine and Add attachment only if it exists and is a valid uploaded file or path
            $attachments = [];
            if (!empty($data['attachment']['tmp_name']) && is_uploaded_file($data['attachment']['tmp_name'])) {
                $dir = WWW_ROOT . DS . 'upload' . DS;
                $file_name = RentMy::$store->id . '_' . time() . '_' . RentMy::randomNum(7) . '.png';
                move_uploaded_file($data['attachment']['tmp_name'], $dir . $file_name);

                $attachments = $dir . $file_name;
            }

            $data['store'] = RentMy::$store;
            $options = array('template' => 'contactus',
                //'to' => Configure::read('admin_email.to'),
                'to' => $user_email,
                'data' => $data, 'attachment' => $attachments, 'subject' => RentMy::$store->slug . ':: Contact Us');
            if (!RentMy::Email($options)) {
                $this->apiResponse['message'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
            }

            // Delete attachment from local machine
            RentMy::deleteFile($dir, $file_name);
        }
    }


    /*
     * send feedback to store admin
     */
    public function sendFeedback()
    {

        $this->request->allowMethod('post');
        $data = $this->request->getData();

        //Required fields
        $requiredFields = ['message'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        $data['store'] = RentMy::$store;
        RentMy::addModel(['StoresUsers']);
        $emails = RentMy::$Model['StoresUsers']->getReceiptEmailsByType('email', 'receive_customer_message');
//        $user = RentMy::$Model['Users']->find()->select(['email'])->where(['id' => RentMy::$store->user_id])->first();
//        if (empty($user['email'])) {
//            $this->httpStatusCode = 400;
//            $this->apiResponse = ['message' => 'Something wrong.', 'error' => $required];
//            return;
//        }

        if (empty($emails)){
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong'), 'error' => $required];
            return;
        }

        RentMy::addModel(['Customers']);
        $data['sender'] = RentMy::$Model['Customers']->get(RentMy::$token->customer_id);

        if (empty($data['sender'])) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'No data found for this customer.';
            return;
        }

        $data['sender']['message'] = trim($data['message']);

        $options = [
            'template' => 'feedback',
            'to' => $emails,
            'data' => $data,
            'subject' => RentMy::$store->slug . ':: Customer Feedback'
        ];

        if ($data['order_id']){
            $options['subject']  = $options['subject'] .' - '. 'Order#'.$data['order_id'];
        }

        if (!empty($data['sender']['email'])){
            $options['replyTo']['email'] = $data['sender']['email'];
            $options['replyTo']['name'] = $data['sender']['first_name'] . ' ' . $data['sender']['last_name'];
        }

        try {
            if (RentMy::Email($options)) {
                $this->apiResponse['message'] = 'Your message has been sent.';
            }

        } catch (\Exception $e) {
            $this->httpStatusCode = 423;
            $this->apiResponse['message'] = 'Something went wrong.';
        }
    }

    /*get all pages for a store*/
    public function index()
    {
        $this->request->allowMethod('get');
        $this->add_model(array("Pages"));
        $pages = $this->Pages->find()
            ->select(['id', 'store_id', 'location', 'name', 'slug', 'meta_description', 'meta_keyword', 'status'])
            ->where(['store_id' => RentMy::$store->id, 'location'=>RentMy::$token->location, 'type' => 'page', 'parent_id IS NULL'])->toArray();
        if ($pages) {
            $this->apiResponse['data'] = $pages;
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    /*get all blogs for a store*/
    public function blogList()
    {
        try {

        $this->request->allowMethod('get');
        RentMy::addModel(['Pages']);
        $queryParams = $this->request->getQueryParams();
        $limit = $queryParams['limit'] ?? 20;
        $page = $queryParams['page_no'] ?? 1;
        $offset = ($page - 1) * $limit;
        $tagIds = $queryParams['tag_ids'] ?? '';
        if (!empty($tagIds)) {
            $tagIds = explode(',', $tagIds);
        }
        $shortDescriptionLength = 250;

        if (!empty($queryParams['short_description_length'])){
            $shortDescriptionLength = $queryParams['short_description_length'];
        }

            $tags = $queryParams['tags'] ?? '';
            if (!empty($tags)) {
                RentMy::addModel(['Tags']);
                $tags = explode(',', $tags);
                $tagIds = RentMy::$Model['Tags']->find('list', [
                    'valueField' => 'id',
                ])->where(['name IN' => $tags])->where(['store_id' => RentMy::$store->id, 'type' => 'blog'])->toList();
            }

        $pageQuery = RentMy::$Model['Pages']->find()
            ->select(['Pages.id', 'store_id', 'Pages.location', 'Pages.name', 'Pages.contents', 'Pages.slug', 'Pages.meta_description', 'Pages.meta_keyword', 'Pages.status', 'Pages.thumbnail_image', 'Pages.featured_image', 'Pages.created', 'Pages.modified'])
            ->where(['Pages.store_id' => RentMy::$store->id, 'Pages.location'=>RentMy::$token->location, 'Pages.type' => 'blog', 'Pages.parent_id IS NULL']);

            if (!empty($queryParams['name'])) {
                $pageQuery =  $pageQuery->where(['Pages.name LIKE' => '%' . $queryParams['name'] . '%']);
            }
        if (!empty($tagIds)) {
            $pageQuery
                ->matching('Tags', function ($q) use ($tagIds) {
                    return $q->where(['Tags.id IN' => $tagIds]);
                })
                ->distinct(['Pages.id']);
        }

        $pageQuery = $pageQuery->order(['Pages.created' => 'DESC']);

        $total = $pageQuery->count();
        $pages =  $pageQuery->limit($limit)
        ->offset($offset)
            ->map(function ($page) use($shortDescriptionLength){
                if (!empty($page['featured_image'])) {
                    $page['featured_image'] = RentMy::makeS3Url('pages/'.$page['store_id'].'/'.$page['featured_image']);
                }

                if (!empty($page['thumbnail_image'])) {
                    $page['thumbnail_image'] = RentMy::makeS3Url('pages/'.$page['store_id'].'/'.$page['thumbnail_image']);
                }
                $contents = !empty($page['contents']) ? json_decode($page['contents'], true) : [];
                $content = strip_tags($contents['content'] ?? '');

                $cleanText = preg_replace('/\s+/', ' ', trim($content));

                $page['short_description'] = mb_substr($cleanText, 0, $shortDescriptionLength); // Use mb_substr for UTF-8 support

                $page['created'] = RentMy::toStoreTimeZone($page['created']);
                $page['modified'] = RentMy::toStoreTimeZone($page['modified']);
                unset($page['contents']);
                return $page;
            })
        ->toArray();
        $this->apiResponse['data'] = $pages;
        $this->apiResponse['total'] = $total;
        $this->apiResponse['page_no'] = $page;
        $this->apiResponse['limit'] = $limit;
        }catch (\Throwable $th) {
            $this->apiResponse['message'] = $th->getMessage();
        }
    }

    /*get a single  page for a store*/
    public function view($slug)
    {
        $this->request->allowMethod('get');

        RentMy::addModel(['Pages']);
        $page = RentMy::$Model['Pages']->find()
            ->where([
                'OR' => [
                    'slug' => $slug,
                    'canonical_url' => $slug,
                ],
                'status' => 1,
                'store_id' => RentMy::$store->id,
                'location' => RentMy::$token->location,
            ])->map(function ($page) {
                if (!empty($page['featured_image'])) {
                    $page['featured_image'] = RentMy::makeS3Url('pages/'.$page['store_id'].'/'.$page['featured_image']);
                }

                if (!empty($page['thumbnail_image'])) {
                    $page['thumbnail_image'] = RentMy::makeS3Url('pages/'.$page['store_id'].'/'.$page['thumbnail_image']);
                }
                return $page;
            })->first();
        if (!$page) {
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'No page found for this slug.';
            return;
        }

        if (RentMy::$store->id != $page['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Access denied.';
            return;
        }

        RentMy::addModel(['Stores']);
        $page['meta_title'] = RentMy::$Model['Stores']->parsingSiteName(RentMy::$store->slug, $page['meta_title']);
        RentMy::addModel(['Pages', 'ReferenceProducts']);
        $page['children'] = RentMy::$Model['Pages']->find()
            ->select(['id', 'name', 'slug'])
            ->where([
                'store_id' => RentMy::$store->id,
                'location' => RentMy::$token->location,
                'parent_id' => $page->id,
            ])->toArray();

        $productIds = RentMy::$Model['ReferenceProducts']->find('list', [
            'valueField' => 'id',
        ])
            ->where([
                'reference_id' => $page->id,
                'reference_type' => 'Pages',
            ])->toList();
        if (!empty($productIds)){
            $page['products'] = RentMy::$Model['Products']->find()->select([
                'id', 'name'
            ])->where([
                'id IN' => $productIds,
            ])->toArray();
        }

        $page->contents = json_decode($page->contents, true);


        RentMy::addModel(['Contents']);
        $content = RentMy::$Model['Contents']->find('all')
            ->select(['id', 'type', 'tag', 'contents', 'status', 'label'])
            ->where(['store_id' => RentMy::$store->id, 'location'=>RentMy::$token->location,'status' => 1, 'type'=>'site-links'])
            ->first();
        $storeContent = !empty($content['contents'])?json_decode($content['contents'], true):[];
        /** Parsing HTML tag*/

        $rentMyContents = $page->contents['content'];

        /** parse contents to have plain value. */
        $start = '[RentMy:{';
        $end = '}';
        $string = $rentMyContents;
        $string = " " . $string;
        $ini = strpos($string, $start);
        $shortcode_data = [];

        if ($ini != 0) {
            $ini += strlen($start);
            $len = strpos($string, $end, $ini) - $ini;
            $parsedString = substr($string, $ini, $len);
            $explodedParentString = explode(';', $parsedString);

            foreach ($explodedParentString as $value) {
                $explodedChildString = explode('=', $value);
                $shortcode_data[$explodedChildString[0]] = $explodedChildString[1];
            }
        }

        if ($shortcode_data['type'] == 'category' && !empty($shortcode_data['id'])) {
            // get parameter values and search as the short-code wants to fetch values from
            RentMy::addModel(['Categories']);
            // fetch query by provided data and boom
            $category_id = RentMy::$Model['Categories']
                ->find()
                ->where([
                    'uuid' => $shortcode_data['id']
                ])->first()->id;

            $categories = RentMy::$Model['Categories']
                ->find('threaded')
                ->order(['Categories.sequence_no' => 'ASC'], true)
                ->where(['Categories.store_id' => RentMy::$store->id])
                ->map(function ($category) use ($category_id) {
                    if ($category->id == $category_id) {
                        return $category;
                    }
                })->toArray();

            // rendering the html category block here from the output data or shortcode data
            $html = $this->menuTemplate($categories);
            //replace the menu with the returned html with the shortcode provided
            $html = str_replace('[RentMy:{type=' . $shortcode_data['type'] . ';id=' . $shortcode_data['id'] . '}]', $html, $page->contents['content']);
            $mobileHtml = $this->menuMobileTemplate($categories);
            $page->contents['content'] = $mobileHtml . $html;
            // ends

            $this->apiResponse['data'] = $page;
            $this->apiResponse['custom'] = [
                'short_code' => $shortcode_data,
                'data' => array_values(array_filter($categories)),
            ];
        } else if ($shortcode_data['type'] == 'customer_membership' && !empty($shortcode_data['id'])) {
           RentMy::addModel(['PagesContents', 'CustomerPlans', 'Customers']);
           $pageContent = RentMy::$Model['PagesContents']
                       ->find()
                       ->where(['id'=>$shortcode_data['id']])
                       ->first();
           $CustomerPlans = RentMy::$Model['CustomerPlans']
                       ->find()
                       ->where(['store_id'=>RentMy::$store->id, 'status'=>1])
                       ->order(['amount'=>'ASC'])
                       ->toArray();
           $customer = [];
           if (!empty($this->jwtPayload->customer_id)) {
               $customer = RentMy::$Model['Customers']->find()->contain(['CustomerPlans'])->where([
                   'Customers.id' => $this->jwtPayload->customer_id,
                   'Customers.store_id' => $this->jwtPayload->store_id,
                   'Customers.status' => 1,
               ])->map(function ($customer){
                   $customer->optional = json_decode($customer->optional, true);
                   return $customer;
               })->first();
           }
           $newHtml = '';
            $get_the_plan_button_text = !empty($storeContent['billing_payment']['btn_get_the_plan'])?$storeContent['billing_payment']['btn_get_the_plan']:'Get The Plan';
            $class = 'rentMy-getMembership rm-get-the-plan-btn';
           foreach($CustomerPlans as $plan){
               $content = $pageContent->content;
               if (!empty($customer['customer_plan']) && (!empty($customer['optional']) && !$customer['optional']['is_subscription_canceled'])){
                   $get_the_plan_button_text = !empty($storeContent['billing_payment']['btn_upgrade'])?$storeContent['billing_payment']['btn_upgrade']:'Upgrade';
                   $class = 'rentMy-getMembership rm-upgrade-btn';
                   if ($plan['plan_id'] == $customer['customer_plan']['plan_id']) {
                       $get_the_plan_button_text = !empty($storeContent['billing_payment']['btn_selected'])?$storeContent['billing_payment']['btn_selected']:'Selected';
                       $cancel_btn_txt = !empty($storeContent['billing_payment']['btn_cancel'])?$storeContent['billing_payment']['btn_cancel']:'Cancel';
                       $class = 'rentMy-getMembership';
                       $content = preg_replace('/'.preg_quote('<!-- placeholder_cancel_button -->', '/').'/', '
                        <a href="#" rentmy-data="{{mermbership_json}}"  class="rm-plan-cancel rm-cancel-btn">'. $cancel_btn_txt .'</a>
                       ', $content);
                   }
               }

               $content  =str_replace('rentMy-getMembership', $class, $content);

               $content = preg_replace('/'.preg_quote("{{get_the_plan_button_text}}", '/').'/', $get_the_plan_button_text, $content);
               $durationType = ngettext(('Per '.$plan['duration_type']), ($plan['duration']." ".$plan['duration_type'].'s'), $plan['duration']);
               $content = preg_replace('/'.preg_quote("{{membership_number_of_inventory}}", '/').'/', $plan['number_of_inventory'], $content);
               $content = preg_replace('/'.preg_quote("{{membership_plan_name}}", '/').'/', $plan['name'], $content);
               $content = preg_replace('/'.preg_quote("{{membership_amount}}", '/').'/', number_format($plan['amount'],2), $content);
               $content = preg_replace('/'.preg_quote("{{membership_additional_charge}}", '/').'/', number_format($plan['additional_charge'],2), $content);
               $content = preg_replace('/'.preg_quote("{{membership_duration_type}}", '/').'/', $durationType, $content);
               $content = preg_replace('/'.preg_quote("{{membership_description}}", '/').'/', $plan['description'], $content);
               $content = preg_replace('/'.preg_quote("{{membership_id}}", '/').'/', $plan['id'], $content);

               $tokenLabel = !empty(RentMy::$storeConfig['currency_format']['point_unit_name'])?RentMy::$storeConfig['currency_format']['point_unit_name']:"Tokens";

               $content = preg_replace('/'.preg_quote("{{token_label}}", '/').'/', $tokenLabel, $content);

               $plan_json="{'id':".$plan['id'].",'name':'".$plan['name']."','number_of_inventory':".$plan['number_of_inventory'].",'plan_id':'".$plan['plan_id']."','amount':'".$plan['amount']."','additional_charge':'".$plan['additional_charge']."'}";
               //$plan_json=json_encode(['id'=>$plan['id'],'name'=>$plan['name'],'number_of_inventory'=>$plan['number_of_inventory'],'plan_id'=>$plan['plan_id']],JSON_UNESCAPED_SLASHES);
               $content = preg_replace('/'.preg_quote("{{mermbership_json}}", '/').'/', $plan_json, $content);
               $newHtml .= $content;
           }
           $finalHtml = str_replace('[RentMy:{type=' . $shortcode_data['type'] . ';id=' . $shortcode_data['id'] . '}]', $newHtml, $page->contents['content']);
           $page->contents['content'] = $finalHtml;
           $this->apiResponse['data'] = $page;
           $this->apiResponse['custom'] = [
               'short_code' => $shortcode_data,
               'data' => [],
           ];
       }
        else {
            // return as usual empty data if nothing found from shortcode
            $checkboxInputField = '<label class="m-checkbox rentmy_checkbox_label"><input type="checkbox" name="rentmy_checkbox" class="rentmy_checkbox" tabindex="0"><span>&nbsp;&nbsp;</span></label>';
            $signaturePad = '<div class="rentmySignaturePad" style="display: none;"></div>';
            $content = $page->contents['content'];
            $page->contents['checkbox_count'] = substr_count($content,"{{tos_checkbox}}");
            $content = preg_replace('/'.preg_quote("{{tos_checkbox}}", '/').'/', $checkboxInputField, $content);

            $page->contents['checkbox_count'] += substr_count($content,"{{input_checkbox}}");
            $content = preg_replace('/'.preg_quote("{{input_checkbox}}", '/').'/', $checkboxInputField, $content);

            $page->contents['signature_count'] += substr_count($content,"{{customer_initial}}");
            $content = preg_replace('/'.preg_quote("{{customer_initial}}", '/').'/', $signaturePad, $content);

            $page->contents['content'] = $content;
            $this->apiResponse['data'] = $page;
            $this->apiResponse['custom'] = [
                'short_code' => null,
                'data' => [],
            ];
        }
    }

    public function menuTemplate($categories)
    {
        $html = '';
        $content = '';
        $threshold = 9;
        $count = 0;
        foreach ($categories as $category) {
            if (empty($category->children)) {
                continue;
            }
            foreach ($category->children as $children) {
                if ($count == $threshold) {
                    break;
                }

                if (empty($children->children)) {
                    $content .= '<li>
                                    <a href="/category/' . $children->uuid . '/' . $children->url . '">' . $children->name . '
                                        <span class="vm-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                    </a>
                                </li>';
                } else {
                    $content .= '<li>
                                    <a href="/category/' . $children->uuid . '/' . $children->url . '">' . $children->name . '
                                        <span class="vm-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                    </a>
                                    <ul class="vertical-submenu">
                                        <div class="submenu-inner">
                                            <h5>' . $children->name . '</h5>
                                            <div class="row vertical-submenu-list">
                                                <ol class="col-12">';

                    foreach ($children->children as $sub_children) {
                        $content .= '<li><a href="/category/' . $sub_children->uuid . '/' . $sub_children->url . '">' . $sub_children->name . '</a></li>';
                    }
                    $content .= '
                                                </ol>
                                            </div>
                                        </div>
                                    </ul>
                                </li>';
                }

                $count++;
            }
        }

        $html .= '<div class="vertical-menu homevertical-menu">
					<div class="vertical-menu-title">
						<span>
							<i class="fa fa-bars"></i> Categories</span>
					</div>
					<nav>
						<ul class="mcd-menu">
							' . $content . '
						</ul>
					</nav>
				</div>';
        return $html;
    }

    /**
     * Shortcode content mobile menu
     * @param $categories
     * @return string
     */
    public function menuMobileTemplate($categories)
    {
        $htmlMobile = $contentMobile = '';
        $threshold = 9;
        $count = 0;
        foreach ($categories as $category) {
            if (empty($category->children)) {
                continue;
            }
            foreach ($category->children as $children) {
                if ($count == $threshold) {
                    break;
                }

                if (empty($children->children)) {
                    $contentMobile .= '<li><a href="/category/' . $children->uuid . '/' . $children->url . '">' . $children->name . ' </a></li>';
                } else {
//                    $contentMobile .='<li><a href="/category/' . $children->uuid . '/' . $children->url . '">' . $children->name . '<i class="fa fa-plus"></i></a>
//                                        <ul class="mobile-category-submenu">';
                    $contentMobile .= '<li><a href="javascript:;">' . $children->name . '<i class="fa fa-plus"></i></a>
                                        <ul class="mobile-category-submenu">';

                    foreach ($children->children as $sub_children) {
                        $contentMobile .= '<li><a href="/category/' . $sub_children->uuid . '/' . $sub_children->url . '">' . $sub_children->name . '</a></li>';
                    }
                    $contentMobile .= '</ul></li>';
                }

                $count++;
            }
        }

        $htmlMobile .= '<section class="mobile-category-section">
                            <div class="container">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mobile-category-titlebar"><h1>Categories</h1></div>
                                            <div class="mobilecategory-box" id="mobilecategory-menubar" style="display: none;">
                                                <ul class="mobile-category-togglemenu">' . $contentMobile . '
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                           ';
        return $htmlMobile;
    }

    /**
     * Add new pages
     * @throws \ImagickException
     */
    public function add()
    {
        $this->request->allowMethod('post');
        RentMy::addModel(['Pages']);

        $data = $this->request->getData();
        $location = RentMy::$token->location;

        if (!empty($data['product_ids']) && !empty($data['parent_id'])){
            $pages = RentMy::$Model['Pages']->find()->where([
                'store_id' => RentMy::$store->id,
                'location' => RentMy::$token->location,
            ])->innerJoinWith('ReferenceProducts', function ($query) use ($data){
                return $query->where(['product_id IN' => $data['product_ids']]);
            })->contain(['ReferenceProducts'])->first();

            if (!empty($pages)){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = "Some products are already associated with different terms and conditions.";
                return;
            }
        }

        if (!empty($data['slug'])) {
            if ($data['status'] == 1) {
                $pre_pages = RentMy::$Model['Pages']->find()->where(['slug' => $data['slug'], 'store_id' => RentMy::$store->id, 'location'=>$location])->toArray();
                foreach ($pre_pages as $p_page) {
                    $p_page->status = 0;
                    $this->Pages->save($p_page);
                }
            }
            $page = RentMy::$Model['Pages']->newEntity();
            $data['store_id'] = RentMy::$store->id;
            $data['location'] = $location;

            if (empty($data['meta_title']))
                $data['meta_title'] = $data['name'];

            $data['type'] = !empty($data['type']) ? $data['type'] : 'page';

            if (!empty($data['thumbnail_image'])){
                $fileLocalLink = WWW_ROOT . 'upload' . DS . 'tmp' . DS . $data['thumbnail_image'];
                if (file_exists($fileLocalLink)) {
                    $s3 = new S3();
                    $s3->upload([
                        ['path' => $fileLocalLink, 'dir' => 'pages/' . RentMy::$store->id .'/'.$data['thumbnail_image']],
                    ]);

                    unlink($fileLocalLink);
                }

            }

            if (!empty($data['featured_image'])){
                $fileLocalLink = WWW_ROOT . 'upload' . DS . 'tmp' . DS . $data['featured_image'];
                if (file_exists($fileLocalLink)){
                    $s3 = new S3();
                    $s3->upload([
                        ['path' => $fileLocalLink, 'dir' => 'pages/' . RentMy::$store->id .'/'. $data['featured_image']],
                    ]);

                    unlink($fileLocalLink);
                }
            }

            $page = RentMy::$Model['Pages']->patchEntity($page, $data);


            if (RentMy::$Model['Pages']->save($page)) {
                if (!empty($data['product_ids'])){
                    RentMy::addModel(['ReferenceProducts']);
                    RentMy::$Model['ReferenceProducts']->addOrUpdate($page->id, 'Pages', $data['product_ids']);
                }

                if (!empty($data['tags'])) {
                    RentMy::addModel(['PageTags']);
                    foreach ($data['tags'] as $tagId) {
                        $tagData = array();
                        $tagObj = RentMy::$Model['PageTags']->newEntity();
                        $tagData['page_id'] = $page->id;
                        $tagData['tag_id'] = $tagId;
                        $tagObj = RentMy::$Model['PageTags']->patchEntity($tagObj, $tagData);
                        RentMy::$Model['PageTags']->save($tagObj);
                    }
                }

                $this->apiResponse['message'] = 'Update saved.';
                $this->apiResponse['data'] = ['id' => $page['id'], 'label' => $page['name'], 'slug' => $page['slug']];
            } else {
                $this->httpStatusCode = 400;
                if ($page->errors()) {
                    foreach ($page->errors() as $field => $validationMessage) {
                        $this->apiResponse['error'] = $validationMessage[key($validationMessage)];
                    }
                }
            }
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['error'] = 'Please complete the required fields to continue.';
        }
    }

    private function setDefaultPdfTemplate(){
        RentMy::addModel(['Templates']);
        $template = [];
        $template['store_id'] = RentMy::$store->id;
        $template['location'] = RentMy::$token->location;
        $template['title'] = '';
        $template['type'] = 1;
        $template['content_type'] = 1;
        $template['content_body'] = '';

        if ($template['content_type'] == 1) {
            $template['general'] = '<div style="padding: 20px"><p style="margin:0px">Receipt | No: {{order.order_id}}</p><p style="margin:0px">Customer Name: {{customer.name}}</p><p style="margin:0px">Email: {{customer.email}}</p><p style="margin:0px">Contact Number: {{customer.mobile}}</p><p style="margin:0px">Order Date: {{order.date}}</p><p style="margin:0px">Order Status: {{order.status}}</p><p style="margin:0px">Pickup Location: {{order.delivery}}</p></div>';
            $template['store_address'] = '<div style="padding: 20px"><p style="margin:0px; text-align: right;">{{store.name}}</p><p style="margin:0px; text-align: right;">{{store.address}}</p></div>';

            $template['options'] = '{"report_title":"Sales Report","sub_total_text":"Sub total:","sub_total":true,"delivery_charge_text":"Delivery charge:","delivery_charge":true,"delivery_tax_text":"Delivery tax:","delivery_tax":true,"discount_text":"Discount:","discount":true,"sales_tax_text":"Sales tax:","sales_tax":true,"deposite_amount_text":"Deposit amount:","deposite_amount":true,"additional_charge_text":"Additional charge:","additional_charge":true,"total_text":"Total:","total":true,"customer_info_text":"Customer Info","show_customer_info":true,"store_name_text":"{{store.name}}","show_store_name":true,"amount_tendered_text":"Tendered amount:","amount_tendered":true,"amount_changed_text":"Changed amount:","amount_changed":true}';
            $template['address_body'] = '<table><tr><td><b>Billing Address<b/></td></tr><tr><td>{{customer.address}}</td></tr></table>';
        }
        $template = RentMy::$Model['Templates']->patchEntity(RentMy::$Model['Templates']->newEntity(), $template);

        RentMy::$Model['Templates']->save($template);
    }

    /**
     * update a pages
     * @param page_id
     */
    public function edit($id)
    {
        $this->request->allowMethod('post');
        RentMy::addModel(['Pages','Templates']);
        $location = RentMy::$token->location;
        $page = RentMy::$Model['Pages']->find()->where(['id' => $id])->first();


        if (!$page) {
            throw new NotFoundException();
        }
        if (RentMy::$store->id != $page['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'Access denied.';
            return;
        }
        $data = $this->request->getData();

        if (!empty($data['product_ids']) && !empty($data['parent_id'])) {
            $pages = RentMy::$Model['Pages']->find()->where([
                'store_id' => RentMy::$store->id,
                'location' => RentMy::$token->location,
                'Pages.id !=' => $page->id
            ])->innerJoinWith('ReferenceProducts', function ($query) use ($data) {
                return $query->where(['product_id IN' => $data['product_ids']]);
            })->contain(['ReferenceProducts'])->first();

            if (!empty($pages)) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = "Some products are already associated with different terms and conditions.";
                return;
            }
        }



        if (!empty($data['slug'])) {
            if ($data['status'] == 1) {
                $pre_pages = RentMy::$Model['Pages']->find()->where(['slug' => $data['slug'], 'store_id' => RentMy::$store->id, 'location'=>$location])->toArray();
                if (count($pre_pages) > 1) {
                    foreach ($pre_pages as $p_page) {
                        $p_page->status = 0;
                        RentMy::$Model['Pages']->save($p_page);
                    }
                }
            }
            $data['location'] = $location;
            $data['status'] = isset($data['status']) ? $data['status'] : 1;

            if (!empty($data['thumbnail_image'])){
                $s3 = new S3();
                // check if there is existing and remove if exists
                if (!empty($page['thumbnail_image']) && $s3->exist('pages/' . RentMy::$store->id .'/'.$page['thumbnail_image'])){
                    $s3->deleteFiles([
                        'pages/' . RentMy::$store->id .'/'.$page['thumbnail_image']
                    ]);
                }

                $fileLocalLink = WWW_ROOT . 'upload' . DS . 'tmp' . DS . $data['thumbnail_image'];
                if (file_exists($fileLocalLink)){
                    $s3->upload([
                        ['path' => $fileLocalLink, 'dir' => 'pages/' . RentMy::$store->id .'/'. $data['thumbnail_image']],
                    ]);

                    unlink($fileLocalLink);
                }
            }

            if (!empty($data['featured_image'])){
                $s3 = new S3();

                // check if there is existing and remove if exists
                if (!empty($page['featured_image']) && $s3->exist('pages/' . RentMy::$store->id .'/'.$page['featured_image'])){
                    $s3->deleteFiles([
                        'pages/' . RentMy::$store->id .'/'.$page['featured_image']
                    ]);
                }

                $fileLocalLink = WWW_ROOT . 'upload' . DS . 'tmp' . DS . $data['featured_image'];
                if (file_exists($fileLocalLink)){
                    $s3->upload([
                        ['path' => $fileLocalLink, 'dir' => 'pages/' . RentMy::$store->id .'/'. $data['featured_image']],
                    ]);

                    unlink($fileLocalLink);
                }
            }

            $page = RentMy::$Model['Pages']->patchEntity($page, $data);

            if (RentMy::$Model['Pages']->save($page)) {
                if ($page['slug']=='terms-and-conditions' && (isset($data['update_receipt']) && ($data['update_receipt']==true))){
                    $pdfTemplate = RentMy::$Model['Templates']->find()->where(['store_id' => RentMy::$store->id, 'location' => RentMy::$token->location, 'type'=>1, 'content_type'=>1])->first();

                    if (empty($pdfTemplate))
                        $this->setDefaultPdfTemplate();


                    $templates = RentMy::$Model['Templates']->find()->where(['store_id' => RentMy::$store->id, 'location' => RentMy::$token->location])->toArray();
                    foreach ($templates as $template){
                        $contents = json_decode($data['contents'], true);
                        $templateData = [
                            'terms_conditions'=>$contents['content']??''
                        ];
                        $template = RentMy::$Model['Templates']->patchEntity($template, $templateData);
                        RentMy::$Model['Templates']->save($template);
                    }
                }

                if (!empty($data['product_ids'])){
                    RentMy::addModel(['ReferenceProducts']);
                    RentMy::$Model['ReferenceProducts']->addOrUpdate($page->id, 'Pages', $data['product_ids']);
                }
                RentMy::addModel(['PageTags']);
                RentMy::$Model['PageTags']->deleteAll(['page_id' => $page->id]);
                if (!empty($data['tags'])) {

                    foreach ($data['tags'] as $tagId) {
                        $tagData = array();
                        $tagObj = RentMy::$Model['PageTags']->newEntity();
                        $tagData['page_id'] = $page->id;
                        $tagData['tag_id'] = $tagId;
                        $tagObj = RentMy::$Model['PageTags']->patchEntity($tagObj, $tagData);
                        RentMy::$Model['PageTags']->save($tagObj);
                    }
                }
                $this->apiResponse['message'] = 'Update saved.';
                $this->apiResponse['data'] = ['id' => $page['id'], 'label' => $page['name'], 'slug' => $page['slug']];
            } else {
                $this->httpStatusCode = 400;
                if ($page->errors()) {
                    foreach ($page->errors() as $field => $validationMessage) {
                        $this->apiResponse['error'] = $validationMessage[key($validationMessage)];
                    }
                }
            }
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['error'] = 'Please complete the required fields to continue.';
        }
    }

    /**
     * Get Pages details
     * @param $id
     * @api /pages/:id
     */
    public function details($id)
    {
        $this->request->allowMethod('get');
        $this->add_model(array("Pages"));
        $page = $this->Pages->find()->where(['id' => $id, 'store_id' => RentMy::$store->id])
            ->map(function ($page) {
                if (!empty($page['featured_image'])) {
                    $page['featured_image'] = RentMy::makeS3Url('pages/'.$page['store_id'].'/'.$page['featured_image']);
                }

                if (!empty($page['thumbnail_image'])) {
                    $page['thumbnail_image'] = RentMy::makeS3Url('pages/'.$page['store_id'].'/'.$page['thumbnail_image']);
                }
                return $page;
            })
            ->first();

        RentMy::addModel(['Pages', 'ReferenceProducts', 'Products']);
        $page['children'] = RentMy::$Model['Pages']->find()
            ->select(['id', 'name', 'slug'])
            ->where([
                'store_id' => RentMy::$store->id,
                'location' => RentMy::$token->location,
                'parent_id' => $page->id,
            ])->toArray();

        $productIds = RentMy::$Model['ReferenceProducts']->find('list', [
            'valueField' => 'product_id',
        ])
            ->where([
                'reference_id' => $page->id,
                'reference_type' => 'Pages',
            ])->toList();

        if (!empty($productIds)){
            $page['products'] = RentMy::$Model['Products']->find()->select([
                'id', 'name'
            ])->where([
                'id IN' => $productIds,
            ])->toArray();

        }
        RentMy::addModel(['PageTags']);
        $page['tags'] = RentMy::$Model['PageTags']->getTags($id);

        $this->apiResponse['data'] = $page;
    }

    public function delete($id)
    {
        $this->request->allowMethod('delete');
        $this->add_model(array('Pages'));
        $page = $this->Pages->find()->where(['id' => $id])->first();
        if (!$page) {
            throw new NotFoundException();
        }
        if ($this->parseToken->store_id != $page['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'Access denied.';
            return;
        }
        if ($this->Pages->delete($page)) {
            RentMy::addModel(['ReferenceProducts']);
            RentMy::$Model['ReferenceProducts']->deleteAll(['reference_id' => $id, 'reference_type' => 'Pages']);
            $this->apiResponse['message'] = 'This page has been deleted.';
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'The page could not be deleted. Please try again.';
        }
    }

    public function test()
    {
        $this->loadModel('ProductPrices');
        $productPrices = $this->ProductPrices->find()
            ->where(['price_type' => 3])
            ->distinct(['variants_products_id'])
            ->toArray();
        $variants_products_ids = array();
        foreach ($productPrices as $productPrice) {
            $query = $this->ProductPrices->find();
            $p_productPrices = $query->select([
                'price_type',
                'duration_type',
                'variants_products_id',
                'id',
                'count' => $query->func()->count('duration_type')
            ])
                ->group('duration_type')
                ->having(['count >' => 1])
                ->where(['price_type' => 3])
                ->where(['variants_products_id' => $productPrice->variants_products_id])
                ->toArray();

            if (!empty($p_productPrices)) {
                $variants_products_ids = array_merge(Hash::extract($p_productPrices, '{n}.variants_products_id'), $variants_products_ids);
            }
        }
        pr($variants_products_ids);
        exit;
    }

    public function serviceRequest()
    {
        $data = $this->request->getData();
        if (!empty($data['service'])) {
            $this->add_model(array('Users', 'Stores'));
            $store = $this->Stores->get($this->parseToken->store_id);
            $user = $this->Users->get($store->user_id);
            $data['store'] = $store->slug;
            $data['name'] = $user->first_name . ' ' . $user->last_name;
            $data['email'] = $user->email;
            $options = array('template' => 'service', 'to' => Configure::read('admin_email.to'), 'email' => Configure::read('admin_email.to'),
                'client_smtp'=> false,
                'data' => $data, 'subject' => 'Service Request');
            if (RentMy::Email($options)) {
                $this->apiResponse['message'] = "Thanks! We'll be in touch shortly to arrange for the requested service(s).";
            } else {
                $this->httpStatusCode = 400;
                $this->apiResponse['error'] = 'The email could not be sent. Please try again';
            }
        } else {
            $this->apiResponse['error'] = 'Empty request!';
        }
    }

    public function stateByCountry($country)
    {
        $this->add_model('States');
        $states = $this->States->getAll($country);
        $this->apiResponse['data'] = empty($states) ? null : $states;
    }

    public function genericData($type)
    {
        $this->loadModel('StoreOptions');
        $quickbookTmpStore = $this->StoreOptions->find()->where(['type' => 'quickbook_store'])->first();
        $this->apiResponse['data'] = $quickbookTmpStore['value'] ?? '';
    }

    /**
     *
     */
    function setQuestionaries()
    {
        $this->add_model(array('StoreOptions', 'Products'));
        $data = $this->request->getData();
        $tmp['first_login'] = \Cake\I18n\Time::parse('now')->format('Y-m-d H:i:s');
        if ($data['questionaries_skip'] == true) {
            $tmp['questionaries_skip'] = true;
        } else {
            $tmp['questionaries_skip'] = false;
        }

        //$cart = false;
        $addLogo = false;
        if (!empty($data['questionaries'])) {
            $tmp['questionaries'] = json_encode($data['questionaries']);
            foreach ($data['questionaries'] as $i => $questionary) {
                if (!empty($questionary['answer']['product'][0]['name'])) {
                    $product = $questionary['answer']['product'][0];
                }
//                if ($questionary['question'] == 'Want to place a test order?') {
//                    $cart = true;
//                }
                if (!empty($data['questionaries'][12]['answer'])) {
                    $addLogo = true;
                }
            }
        }

        // add product
        if (!empty($product)) {
            $url = $this->seoUrl($product['name']);
            $pdata['name'] = $product['name'];
            $pdata['url'] = $url;
            $pdata['status'] = 1;
            $pdata['variant_set'] = json_encode([1]);
            $pdata['store_id'] = $this->parseToken->store_id;
            $pdata['user_id'] = $this->parseToken->id;
            $newproduct = $this->Products->newEntity();
            $newproduct = $this->Products->patchEntity($newproduct, $pdata);
            if ($this->Products->save($newproduct)) {

                $this->add_model(array('VariantsProducts', 'Locations', 'ProductPrices', 'Images'));
                $location = array();
                $locations = $this->Locations->find()->where(['store_id' => $this->parseToken->store_id])->toArray();
                foreach ($locations as $aLocation) {
                    $aData = array(
                        'id' => $aLocation->id,
                        'store_id' => $this->parseToken->store_id,
                        'quantity' => $product['qty'],
                        'available' => $product['qty']
                    );
                    $location[] = $aData;
                }
                $attrData = array(
                    'variant_id' => [1], 'barcode' => '',
                    'cost' => '', 'purchase_date' => '', 'location' => $location, 'default' => false,
                    'product_id' => $newproduct->id,
                    'store_id' => $newproduct->store_id,
                    'set_id' => [1],

                );
                $this->VariantsProducts->add($newproduct->id, $attrData);
                if (!empty($product['price_term'])) {
                    $vp = $this->VariantsProducts->find()->where(['product_id' => $newproduct->id])->first();
                    $vp->price_type = 3;
                    $this->VariantsProducts->save($vp);
                    if ($product['price_term'] == 'hourly') {
                        $label = 'hour';
                    } elseif ($product['price_term'] == 'daily') {
                        $label = 'day';
                    } elseif ($product['price_term'] == 'weekly') {
                        $label = 'week';
                    } elseif ($product['price_term'] == 'monthly') {
                        $label = 'month';
                    }

                    $priceData = [
                        'store_id' => $this->parseToken->store_id,
                        'product_id' => $newproduct->id,
                        'price_type' => 3,
                        'variants_products_id' => $vp->id,
                        'duration_type' => $product['price_term'],
                        'flex_duration' => 1,
                        'label' => $label,
                        'price' => $product['price'],
                        'duration' => 1,
                        'max_range' => 1
                    ];
                    $productPrice = $this->ProductPrices->newEntity();
                    $productPrice = $this->ProductPrices->patchEntity($productPrice, $priceData);
                    $this->ProductPrices->save($productPrice);

                    if (!empty($product['image'])) {
                        $image = $this->initialfileUpload($product['image'], $newproduct->id);

                        $imagesProducts = [
                            'product_id' => $newproduct->id,
                            'store_id' => RentMy::$store->id, 'status' => 2,
                            'image_original' => $image['image_original'],
                            'image_large' => $image['image_large'],
                            'image_small' => $image['image_small'],
                            'variants_products_id' => $vp->id
                        ];
                        $imageObj = $this->Images->newEntity($imagesProducts);
                        $this->Images->save($imageObj);

                    }


                }
            }
        }

        $storeLogoUrl = '';
        if ($addLogo) {
            $image = $this->resizeImage(WWW_ROOT . 'upload', $data['questionaries'][12]['answer'], 250);
            $directoryPath = WWW_ROOT . 'upload';
            $file = file_get_contents(WWW_ROOT . 'upload' . DS . $image);
            file_put_contents($directoryPath . DS . $image, $file);

            $s3 = new S3();
            $s3->upload([
                ['path' => $directoryPath . DS . $image, 'dir' => 'store-logo/' . RentMy::$store->id . '/' . $image],
            ]);
            unlink(WWW_ROOT . 'upload' . DS . $data['questionaries'][12]['answer']);
            unlink(WWW_ROOT . 'upload' . DS . $image);
            $storeLogoUrl = RentMy::makeS3Url('/store-logo/' . RentMy::$store->id . "/" . $image);
            $store = $this->Stores->find()->where(['id' => RentMy::$store->id])->first();
            $store->logo = $image;
            $this->Stores->save($store);
        }
        //if (!empty($product['image'])) {
        //    unlink(WWW_ROOT . 'upload' . DS . $product['image']);
        // }
        foreach ($tmp as $i => $t) {
            $dbData = [
                'store_id' => $this->parseToken->store_id,
                'type' => $i,
                'value' => $t
            ];

            $tmpQuestions = $this->StoreOptions->find()
                ->where(['store_id' => $this->parseToken->store_id,
                    'type' => $i])
                ->first();
            if (empty($tmpQuestions)) {
                $tmpQuestions = $this->StoreOptions->newEntity();
            }
            $tmpQuestions = $this->StoreOptions->patchEntity($tmpQuestions, $dbData);
            $this->StoreOptions->save($tmpQuestions);
        }
        RentMy::getStore(RentMy::$store->id, RentMy::$token->location);
        $tmp['store'] = RentMy::$store;
        $order = $tmp;

        if (!$tmp['questionaries_skip']) {
            $options = [
                'template' => 'questionarie', 'to' => '<EMAIL>', 'client_smtp'=>false,
                'bcc' => '<EMAIL>', 'order' => $order, 'subject' => 'New Store Questionnaire - ' . RentMy::$store->slug];
            if (in_array($this->request->getEnv('SERVER_ADDR'), Configure::read('SERVER_IP'))) {
               RentMy::Email($options);
            }
        }
        $this->apiResponse['data'] = ['logo' => $storeLogoUrl];
    }

    /**
     * Get Questionaries data & and inital dashboard data
     */
    function getQuestionaries()
    {
        $this->add_model(array('StoreOptions'));
        $questionaries = $this->StoreOptions->find()
            ->where(['store_id' => $this->parseToken->store_id,
                'type IN' => ['first_login', 'questionaries', 'questionaries_skip', 'hide_start_buttons']])
            ->toArray();
        $questions = [];
        foreach ($questionaries as $i => $questionary) {
            if ($questionary['type'] == 'questionaries') {
                $questions[$questionary['type']] = json_decode($questionary['value']);
            } else {
                $questions[$questionary['type']] = $questionary['value'];
            }

        }
        $this->apiResponse['data'] = $questions;
    }

    /**
     * Save store options.
     * This will save store specific options in store_options db table
     * $_POST[type] - for hiding dashboard buttons 'hide_start_buttons'
     * $_POST[value]
     * @API POST /save-store-options
     */
    public function saveStoreOptions()
    {
        $data = $this->request->getData();
        RentMy::addModel(['StoreOptions']);
        $storeOptions = RentMy::$Model['StoreOptions']->find()->where(['store_id' => RentMy::$store->id, 'type' => $data['type']])->first();
        if (empty($storeOptions)) {
            $storeOptions = RentMy::$Model['StoreOptions']->newEntity();
            $storeOptions->type = $data['type'];
            $storeOptions->store_id = RentMy::$store->id;
        }
        $storeOptions->value = $data['value'];
        RentMy::$Model['StoreOptions']->save($storeOptions);

    }

    public function initialfileUpload($file, $id)
    {
        if (!empty($file)) {
            $directoryPath = WWW_ROOT . 'upload';
            $fileContent = file_get_contents(WWW_ROOT . 'upload' . DS . $file);
            file_put_contents($directoryPath . DS . $file, $fileContent);

            $largeImage = $this->resizeImage($directoryPath, $file, 600);
            $smallImage = $this->resizeImage($directoryPath, $file, 300);

            // upload s3
            $s3 = new S3();
            $s3->upload([
                ['path' => $directoryPath . DS . $file, 'dir' => 'products/' . RentMy::$store->id . '/' . $id . '/' . $file],
                ['path' => $directoryPath . DS . $largeImage, 'dir' => 'products/' . RentMy::$store->id . '/' . $id . '/' . $largeImage],
                ['path' => $directoryPath . DS . $smallImage, 'dir' => 'products/' . RentMy::$store->id . '/' . $id . '/' . $smallImage],
            ]);
            // remove file from server .
            RentMy::deleteFile($directoryPath, $file);
            RentMy::deleteFile($directoryPath, $largeImage);
            RentMy::deleteFile($directoryPath, $smallImage);

            $imgName = array(
                'image_original' => $file,
                'image_large' => $largeImage,
                'image_small' => $smallImage
            );
            return $imgName;
        }
        return false;
    }

    public function customFormSubmit($type){
        $rawData = $data = $this->request->getData();
        try {
            RentMy::addModel(['Users', 'StoresUsers']);
            $user_email = RentMy::$Model['StoresUsers']->getReceiptEmailsByType('email', 'contact');
            if (empty($user_email)) {
                $user = RentMy::$Model['Users']->find()->where(['id' => RentMy::$store->user_id])->first();
                $user_email = $user->email;
            }

            $data['store'] = RentMy::$store;
            $data['raw_data'] = $rawData;
            $options = array('template' => 'custom_form',
                //'to' => Configure::read('admin_email.to'),
                'to' => $user_email,
                'data' => $data, 'subject' => RentMy::$store->slug . '::' . str_replace("_", " ", $type));
            RentMy::Email($options);

            RentMy::saveLogDB('custom_page', $data, [], ['store_id'=>RentMy::$store->id, 'location'=>RentMy::$token->location]);
            RentMy::saveLogFile('alert', 'store_id-------=>'.RentMy::$store->id .'---type => ' . $type . '---data =>  '. json_encode($data), ['queriesLog']);
            $this->apiResponse['message'] = "Data has been submitted";
        }catch (\Exception $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
        }

    }

    /**
     * @POST /pages/:id/duplicate-check/:type
     * @return mixed
     */
    public function duplicateChecking($id, $type)
    {
        RentMy::addModel(['Pages']);
        $data = $this->request->getData();

        $duplicate = false;
        $message = "";
        switch ($type){
            case 'url':
                if (empty($data['url'])){
                    $this->httpStatusCode = 400;
                    $this->apiResponse['message'] = "required field missing";
                    return;
                }
                if (!empty($id) && $id != 'new'){
                    $page = RentMy::$Model['Pages']->find()->select(['id', 'slug', 'name'])->where(['slug' => $data['url'], 'id !=' => $id, 'store_id' => RentMy::$store->id, 'location' => RentMy::$token->location])->first();
                }else{
                    $page = RentMy::$Model['Pages']->find()->select(['id', 'slug', 'name'])->where(['slug' => $data['url'], 'store_id' => RentMy::$store->id, 'location' => RentMy::$token->location])->first();
                }

                if (!empty($page)){
                    $duplicate = true;
                    $message = "Same url exists with ". $page->name;
                }

                break;
            default:
                break;

        }

        $this->apiResponse["data"] = [
            "duplicate" => $duplicate,
            "message" => $message
        ];
    }

    /**
     * this API will return Terms and conditions for a store alongside it will return item specific TAC
     * @GET /terms-and-condition
     * @return void
     */
    public function termsAndCondition()
    {
        $termsAndConditions = [];
        $data = $this->request->getQueryParams();
        RentMy::addModel(['Pages']);
        $termsAndCondition = RentMy::$Model['Pages']->find()->where([
            'slug' => 'terms-and-conditions',
            'store_id' => RentMy::$store->id,
            'location' => RentMy::$token->location,
            'status' => 1,
        ])->first();
        $contents = !empty($termsAndCondition->contents) ? json_decode($termsAndCondition->contents, true) : [];

        $termsAndConditions[] = array_merge([
            "parent" => true,], $this->parsseTermsAndCondition($contents['content']));

        if (!empty($data['product_ids'])){
            $productIds = explode(',', $data['product_ids']);
        }elseif (!empty($data['type']) && $data['type'] == 'cart' && !empty($data['cart_id'])){
            RentMy::addModel(['CartItems']);
            $productIds = RentMy::$Model['CartItems']->find('list', [
                'valueField' => 'product_id',
            ])->where(['cart_id' => $data['cart_id']])->toList();
        }elseif (!empty($data['type']) && $data['type'] == 'order' && !empty($data['order_id'])){
            RentMy::addModel(['OrderItems']);
            $productIds = RentMy::$Model['OrderItems']->find('list', [
                'valueField' => 'product_id',
            ])->where(['order_id' => $data['order_id']])->toList();
        }


        if (!empty($productIds)){
            $productSpecificTermsAndConditions = $this->termsAndConditionByProductIds($productIds);
            $termsAndConditions = array_merge($termsAndConditions, $productSpecificTermsAndConditions);

        }

        $this->apiResponse['data'] = $termsAndConditions;

    }

    private function termsAndConditionByProductIds($productIds)
    {
        $termsAndConditions = [];
        RentMy::addModel(['Products']);
        $products = RentMy::$Model['Products']->find('list', [
            'keyField' => 'id',
            'valueField' => 'name',
        ])->where(['id IN' => $productIds])->toArray();

        RentMy::addModel(['Pages']);
        $pages = RentMy::$Model['Pages']->find()->where([
            'store_id' => RentMy::$store->id,
            'location' => RentMy::$token->location,
        ])->innerJoinWith('ReferenceProducts', function ($query) use ($productIds){
            return $query->where(['product_id IN' => $productIds]);
        })->contain(['ReferenceProducts' => function ($query) use ($productIds) {
            return $query->where(['product_id IN' => $productIds]);
        }])->toArray();

        foreach ($pages as $page){
            $contents = !empty($page->contents) ? json_decode($page->contents, true) : [];
            foreach ($page->reference_products as $referenceProduct){
                $termsAndConditions[] = array_merge([
                    "parent" => false,
                    "page_name" => $page->name,
                    "product_id" => $referenceProduct->product_id,
                    "product_name" => $products[$referenceProduct->product_id],
                ], $this->parsseTermsAndCondition($contents['content']));
            }
        }
        return $termsAndConditions;
    }

    /**
     * this function will parse checkbox and signature placeholder to actual field
     * @param $contentData
     * @return array
     */
    private function parsseTermsAndCondition($contentData)
    {
        $checkboxInputField = '<label class="m-checkbox rentmy_checkbox_label"><input type="checkbox" name="rentmy_checkbox" class="rentmy_checkbox"><span>&nbsp;&nbsp;</span></label>';
        $signaturePad = '<div class="rentmySignaturePad" style="display: none;"></div>';
        $content = $contentData;
        $signatureCount = 0;
        $checkboxCount = substr_count($content,"{{tos_checkbox}}");
        $content = preg_replace('/'.preg_quote("{{tos_checkbox}}", '/').'/', $checkboxInputField, $content);
        $checkboxCount += substr_count($content,"{{input_checkbox}}");
        $content = preg_replace('/'.preg_quote("{{input_checkbox}}", '/').'/', $checkboxInputField, $content);

        $signatureCount += substr_count($content,"{{customer_initial}}");
        $content = preg_replace('/'.preg_quote("{{customer_initial}}", '/').'/', $signaturePad, $content);
        return [
            'content' => $content,
            'checkbox_count' => $checkboxCount,
            'signature_count' => $signatureCount,
        ];
    }

}
