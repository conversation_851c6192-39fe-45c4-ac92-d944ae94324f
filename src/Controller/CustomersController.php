<?php

namespace App\Controller;

use App\Lib\RentMy\PosCustomerView;
use App\Lib\RentMy\RentMy;
use App\Lib\S3;
use Cake\Auth\AbstractPasswordHasher;
use Cake\I18n\Time;
use Cake\Network\Exception\HttpException\NotFoundException;
use RestApi\Utility\JwtToken;
use Cake\Auth\DefaultPasswordHasher;
use Cake\Routing\Router;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;

class CustomersController extends AppController
{

    /**
     * Customer Profile View. Get method
     */
    public function viewProfile()
    {

        $this->request->allowMethod('get');
        $this->add_model(array('Customers'));
        RentMy::addModel(['Orders', 'CustomerAddresses']);
        $customer = $this->Customers->find()->contain(['CustomerPlans'])->where([
            'Customers.id' => $this->jwtPayload->customer_id,
            'Customers.store_id' => $this->jwtPayload->store_id,
            'Customers.status' => 1,
        ])->first();

        if (empty($customer)):
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'Customer not found.';
            return;
        endif;

        $customer['address'] = RentMy::$Model['CustomerAddresses']->find()->where(['customer_id'=>$customer->id, 'store_id'=>RentMy::$store->id])->order(['is_primary'=>'DESC'])->first();
        $customer->optional = !empty($customer->optional)?json_decode($customer->optional, true):[];
        $order = RentMy::$Model['Orders']->find()->where(['store_id'=>RentMy::$store->id, 'customer_id'=>$customer->id, 'status !='=>1])->order(['created'=>'DESC'])->first();
        $customer->optional['order_id'] = '';

        $customer['in_hand_quantity'] =  $this->Customers->customerOngoingOrdersQuantityCount($customer->id);

        RentMy::addModel(['WishList']);
        $wishList = RentMy::$Model['WishList']->find()->where(['customer_id'=>$customer->id])->first();
        if (!empty($wishList)){
            $customer['wishlist_token'] = $wishList->uid;
        }

        if (!empty($order))
            $customer->optional['order_id'] = $order->id;
        $this->apiResponse['data'] = $customer;
    }

    /**
     * Customer Profile Edit. Post method
     */
    public function editProfile()
    {

        $this->request->allowMethod('post');
        $this->add_model(array('Customers'));
        $data = $this->request->getData();

        //Required fields
        $requiredFields = ['email'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        $customer = $this->Customers->find()->where([
            'id' => $this->jwtPayload->customer_id,
            'store_id' => $this->jwtPayload->store_id,
            'status' => 1,
        ])->first();

        if (empty($customer)):
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'No customers found for this request.';
            return;
        endif;

        // check for existing customer
        $customerExists = $this->Customers->find()->where([
            'id !=' => $this->jwtPayload->customer_id,
            'store_id' => $this->jwtPayload->store_id,
            'email' => trim($data['email']),
        ])->first();

        if (empty($customerExists)):
            // update the customer
            $customer = $this->Customers->patchEntity($customer, $data);
            if ($this->Customers->save($customer)):
                $this->apiResponse['data'] = $customer;
                $this->apiResponse['message'] = 'Profile updated successfully.';
                return;
            else:
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Profile update failed. Please try again.';
                return;
            endif;

        else:
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Profile update failed. Please try again.';
            return;
        endif;
    }

    /**
     * Customer password change. Post method
     */
    // /api/customers/change-password
    // parameters password, old_password, confirm_password
    public function changePassword()
    {

        $this->request->allowMethod('post');
        $this->add_model(array('Customers'));
        $data = $this->request->getData();

        //Required fields
        $requiredFields = ['password', 'old_password', 'confirm_password'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        $customer = $this->Customers->find()->where([
            'id' => $this->jwtPayload->customer_id,
            'store_id' => $this->jwtPayload->store_id,
            'status' => 1,
        ])->first();

        if (empty($customer)):
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'No customers found for this request.';
            return;
        endif;

        if ((new DefaultPasswordHasher)->check($data['old_password'], $customer->password) == false):
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'The password you entered for your account is incorrect. Please try again.';
            return;
        endif;

        if ($data['password'] != $data['confirm_password']):
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'A mismatch between the entered passwords, try entering your passwords again.';
            return;
        endif;

        // update the customer
        $customer->password = (new \Cake\Auth\DefaultPasswordHasher)->hash($data['password']);
        if ($this->Customers->save($customer)):
            $this->apiResponse['data'] = $customer;
            $this->apiResponse['message'] = 'Update saved.';
            return;
        else:
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Password update failed. Please try again.';
            return;
        endif;
    }

    /**
     * Customer password change. Post method it is used by admin to change a specific customers password
     */
    // /api/customers/auth/change-password
    // parameters password, old_password, confirm_password
    public function changePasswordAdmin($id)
    {

        $this->request->allowMethod('post');
        $this->add_model(array('Customers'));
        $data = $this->request->getData();

        //Required fields
        $requiredFields = ['password'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        $customer = $this->Customers->find()->where([
            'id' => $id,
            'store_id' => RentMy::$token->store_id
        ])->first();

        if (empty($customer)) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'No customers found for this request.';
            return;
        }

        // update the customer
        $customer->password = (new \Cake\Auth\DefaultPasswordHasher)->hash($data['password']);
        if ($this->Customers->save($customer)) {
            $this->apiResponse['message'] = 'Update saved.';
            return;
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Password update failed. Please try again.';
            return;
        }
    }


    /**
     * Customer login
     */
    public function login()
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Customers', 'Stores', 'Jobs'));
        $data = $this->request->getData();

        if (empty($data)):
            $this->httpStatusCode = 400; //forbidden , 404 : not found, 401 : unauthorized, 405 : method not allow (get/post)
            $this->apiResponse['message'] = 'Please provide the credentials.';
            return;
        endif;

        try {

            if (empty($data['email']) || empty($data['password'])):
                $this->httpStatusCode = 400;// bad request
                $this->apiResponse['message'] = 'Please enter email and password.';
                if( isset($data['password']) ) {
                    $data['password'] = '*********';
                }
                RentMy::logToGenius([
                      "event" => "customer_login",
                      "status" => "fail",
                      "description" => $this->apiResponse['message'],
                      "value" => 'Email: ' . $data['email'],
                      "custom_content" => json_encode([
                        'data' => $data,
                      ])
                ]);
                return;
            endif;

            $customerFinder = $this->Customers->find();
            $customerFinder->where(['email' => $data['email'],
                'store_id' => RentMy::$store->id,
                'status' => 1]);
            if ($data['is_client']) {
                $customerFinder->where(['type' => 2]);
            } else {
                $customerFinder->where(['type'=> 1]);
            }

            $customer = $customerFinder->first();
            if (empty($customer)):
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Incorrect email or password.';
                if( isset($data['password']) ) {
                    $data['password'] = '*********';
                }
                RentMy::logToGenius([
                      "event" => "customer_login",
                      "status" => "fail",
                      "description" => $this->apiResponse['message'],
                      "value" => $this->apiResponse['message'],
                      "custom_content" => json_encode([
                        'data' => $data,
                      ])
                ]);
                return;
            endif;

            if ((new DefaultPasswordHasher)->check($data['password'], $customer['password']) == false):
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Incorrect email or password.';
                if( isset($data['password']) ) {
                    $data['password'] = '*********';
                }
                RentMy::logToGenius([
                      "event" => "customer_login",
                      "status" => "fail",
                      "description" => $this->apiResponse['message'],
                      "value" => $this->apiResponse['message'],
                      "custom_content" => json_encode([
                        'data' => $data,
                      ])
                ]);
                return;
            endif;

            $customer_token_data = $this->makeToken($customer);
            $this->saveToken($customer, $customer_token_data['token']);
            if (RentMy::$storeConfig['client']['active']) {
                $job = $this->Jobs->find()->where(['client_id' => $customer['id'], 'store_id' => RentMy::$store->id, 'is_default' => 1])
                    ->first();
                if (!empty($job)) {
                    $customer_token_data['job_id'] = $job->id;
                }
            }

            if (!empty($data['wishlist_token'])){
                RentMy::addModel(['WishList']);
                $wishList = RentMy::$Model['WishList']->find()->where(['uid' => $data['wishlist_token']])->first();
                if (!empty($wishList)) {
                    $wishList->customer_id = $customer['id'];
                    RentMy::$Model['WishList']->save($wishList);
                }
            }

            RentMy::logToGenius([
                  "event" => "customer_logged_in",
                  "status" => "success",
                  "description" => 'Customer logged in.',
                  "value" => 'Customer email: ' . $data['email'],
                  "custom_content" => json_encode([
                    'customer' => $customer
                  ])
            ]);


            $this->apiResponse['data'] = $customer_token_data;
            $this->apiResponse['message'] = 'Congrats, Login Successful.';
            return;

        } catch (\Exception $e) {
            $this->httpStatusCode = 400;// bad request
            $this->apiResponse['message'] = $e->getMessage();
            RentMy::logToGenius([
                  "event" => "customer_login",
                  "status" => "fail",
                  "description" => $this->apiResponse['message'],
                  "value" => $this->apiResponse['message'],
                  "custom_content" => json_encode([
                    'exception' => json_encode($e),
                  ])
            ]);
            return;
        }
    }



    /**
     * Customer logout
     * /api/customer/logout?store_name=test
     */
    public function logout()
    {
        $this->add_model(array('Stores', 'AccessLogs'));
        $data = $this->request->getQueryParams();
        if (empty($data['store_name'])):
            $this->apiResponse['message'] = 'Missing store name.';
            return;
        endif;

        $store = $this->Stores->find()->select(['id', 'uid', 'name', 'slug', 'logo', 'settings', 'store_type'])->where(['name' => $data['store_name'], 'status' => 1])->first();
        if (empty($store)):
            $store = $this->Stores->find()->select(['id', 'uid', 'name', 'slug', 'logo', 'settings', 'store_type'])->where(['domain' => $data['store_name'], 'status' => 1])->first();
        endif;
        if (empty($store)):
            $this->apiResponse['message'] = 'Store Not Found!';
            return;
        endif;

        $token_data = $this->AccessLogs->find()->where(['store_id' => $store['id'], 'status' => 1, 'is_online' => 1])->first();
        if (!empty($store['logo'])):
            $store['logo'] = Router::url('img/upload/store-logo/' . $store['id'] . '/' . $store['logo'], true);
        endif;
        if (!empty($store)):
            $storeId = $store['id'];
        else:
            $storeId = 0;
        endif;

        $this->add_model(array('Locations', 'Contents'));
        $locations = $this->Locations->find()->select(['id', 'name', 'country'])->where(['is_online' => 1, 'status' => 1])->where(['store_id' => $storeId])->first();
        if (empty($store) || empty($locations)):
            throw new NotFoundException();
        endif;

        $layout = !empty($store->settings) ? (!empty(json_decode($store->settings, true)[$location]['layout']) ? json_decode($store->settings, true)[$location]['layout'] : 0) : 0;
        $locations->country = empty($locations->country) ? 'US' : $locations->country;

        // Custom CSS
        $contents = $this->Contents->find()
            ->select(['contents'])
            ->where(['store_id' => $storeId, 'type' => 'custom-css'])
            ->first();
        $customCss = '';
        if (!empty($contents)):
            $customCss = $contents->contents;
        endif;
        //Custom JS
        $custom_js = false;
        if (file_exists(WWW_ROOT . 'js' . DS . 'upload' . DS . $store['uid'] . '.js')):
            $custom_js = true;
        endif;

        $this->apiResponse['location'] = $locations;
        $this->apiResponse['store'] = [
            'id' => $store['id'],
            'uid' => $store['uid'],
            'name' => $store['name'],
            'slug' => $store['slug'],
            'logo' => $store['logo'],
            'custom-css' => $customCss,
            'custom-js' => $custom_js,
            'layout' => $layout,
            'plan_type' => RentMy::checkPaidStoreType($store),
            'account_type' => strtoupper($store->store_type),
            'token' => $token_data['token']
        ];
    }

    /** Customer Forgot Password & send email to change password
     * /api/customers/forgot-password
     * @param $email
     */
    public function forgotPassword()
    {
        $this->request->allowMethod('post');
        $this->loadModel('Customers');
        $data = $this->request->getData();
        $params = $this->request->getQuery();

        //Required fields
        $requiredFields = ['email'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        $active_customer = $this->Customers->find('all')->where(['email' => trim($data['email']), 'store_id' => RentMy::$store->id, 'status' => 1])->first();
        if (empty($active_customer)):
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'The email address is not valid or activated. Please check the address and try again.';
            return;
        endif;

        $activation = $six_digit_random_number = mt_rand(100000, 999999);//md5($this->randomnum(6));
        $active_customer->activation_key = $activation;
        //after one day activation code will expire
        $date = strtotime("+1 day");
        $active_customer['activation_expire'] = date('Y-m-d', $date);
        if ($this->Customers->save($active_customer)):

            try {

                if (!empty($data['source'])) { // used for WP & api
                    $host = $data['source'];
                } else {
                    $host =RentMy::storeDomain();
//                    if (!empty(RentMy::$store->domain)) {
//                        $host = 'https://' . RentMy::$store->domain;
//                    } else {
//                        $host = Configure::read('STORE_HOST')[0] . RentMy::$store->name . Configure::read('STORE_HOST')[1];
//                    }
                }
                $storeLogoUrl = RentMy::makeS3Url('/store-logo/' . RentMy::$store->id . "/" . RentMy::$store->logo);
                RentMy::$store->store_logo = !empty(RentMy::$store->logo) ? $storeLogoUrl :
                    Configure::read('CLIENT_HOST') . '/assets/img/home/<USER>';

                // if admin sends type activation then it sends the activation mail. else it will send a forgot password mail.
                if ($params['type'] == 'activation') {

                    $activation = ($active_customer->type == 1) ? $host . '/customer/activation?activation_key=' . $active_customer->activation_key
                        : $host . '/client/activation?activation_key=' . $active_customer->activation_key;

                    if ($data['password_reset_link'])
                        $activation = $data['password_reset_link'] . '?activation_key=' . $active_customer->activation_key;

                    $options = array('template' => 'customer_activation', 'to' => trim($data['email']), 'activation' => $activation,
                        'subject' => 'Welcome to ' . RentMy::$store->slug,
                        'store' => RentMy::$store
                    );

                } else {

                    $activation = $host . '/customer-reset-password/?activation_key=' . $active_customer->activation_key;

                    if ($data['password_reset_link']){
                        // Use a ternary operator to dynamically choose '?' or '&'
                        $separator = (strpos($data['password_reset_link'], '?') === false) ? '?' : '&';
                        // Append activation key
                        $activation = $data['password_reset_link'] . $separator . 'activation_key=' . $active_customer->activation_key;
                    }


                    $options = array('template' => 'customer_forgot_password', 'to' => trim($data['email']), 'activation' => $activation,
                        'subject' => 'Forgot Password',
                        'store' => RentMy::$store
                    );

                }

                $options['location'] = RentMy::$token->location;
                RentMy::addNotificationQueue('customer_forgot_password', RentMy::$store->id, $options);

//                RentMy::addModel(['Templates', 'EmailNotifications']);
//                $notificationConfig = RentMy::$Model['EmailNotifications']->find()
//                    ->where(['store_id' => RentMy::$store->id])
//                    ->where(['event' => 'customer_forgot_password'])
//                    ->where(['status' => 1])
//                    ->first();
//                if (!empty($notificationConfig)){
//                    $template = RentMy::$Model['Templates']->find()
//                        ->where(['store_id' => RentMy::$store->id])
//                        ->where(['id' => $notificationConfig->email_template])
//                       // ->where(['content_type' => 3])
//                        ->first();
//                    if ($template) {
//                        $options['subject'] = $template->title;
//                        $options['email_body'] = $template->content_body;
//                    }
//                    RentMy::Email($options);
//                }

            } catch (\Exception $e) {

            }

            $this->apiResponse['activation'] = true;
            $this->apiResponse['message'] = 'An activation email sent successfully. Please check email.';
        else:
            $this->httpStatusCode = 400;
            $this->apiResponse['activation'] = false;
            $this->apiResponse['error'] = 'This email could not be sent. Please try again.';
        endif;
    }

    /* after clicking activation account link, user can reset his password
     *
     *@param  $activation_key, $password
     */
    public function resetPassword($activation_key)
    {
        $this->request->allowMethod('post');
        $this->loadModel('Customers');
        $data = $this->request->getData();

        //Required fields
        $requiredFields = ['password'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)):
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        endif;

        $check_customer = $this->Customers->find()->where(['activation_key' => $activation_key, 'store_id' => $this->jwtPayload->store_id])->first();
        if (empty($check_customer)){
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'The reset password link expired';
            return;
        }

        $check_customer['password'] = (new \Cake\Auth\DefaultPasswordHasher)->hash($data['password']);
        $check_customer['activation_key'] = '';
        $check_customer['activation_expire'] = NULL;

        if ($this->Customers->save($check_customer)):
            $this->apiResponse['message'] = 'Password has been changed successfully.';
            return;
        endif;

        $this->httpStatusCode = 400;
        $this->apiResponse['error'] = 'Password could not be changed. Please try again.';
    }

    /**
     * this function will check activation for customers token if found reset it. and make status to 1.
     */
    public function checkToken()
    {
        $this->request->allowMethod('get');
        $this->loadModel('Customers');
        $params = $this->request->getQuery();

        if (empty($params['activation_key'])):
            $this->httpStatusCode = 400;
            $this->apiResponse['data'] = ['validation_status' => false];
            $this->apiResponse['message'] = 'Please provide activation code.';
            RentMy::logToGenius([
                  "event" => "customer_activation",
                  "status" => "fail",
                  "description" => $this->apiResponse['message'],
                  "value" => $this->apiResponse['message'],
                  "custom_content" => json_encode([
                    'params' => $params,
                  ])
            ]);
            return;
        endif;

        $check_customer = $this->Customers->find()->where(['activation_key' => $params['activation_key'], 'store_id' => $this->jwtPayload->store_id])->first();

        if (empty($check_customer)):
            $this->httpStatusCode = 404;
            $this->apiResponse['data'] = ['validation_status' => false];
            $this->apiResponse['message'] = 'No activation code found.';
            RentMy::logToGenius([
                  "event" => "customer_activation",
                  "status" => "fail",
                  "description" => $this->apiResponse['message'],
                  "value" => $this->apiResponse['message'],
                  "custom_content" => json_encode([
                    'params' => $params,
                    'check_customer' => $check_customer,
                  ])
            ]);
            return;
        endif;

        if (date('Y-m-d', strtotime($check_customer['activation_expire'])) <= date('Y-m-d')):
            $this->httpStatusCode = 404;
            $this->apiResponse['data'] = ['validation_status' => false];
            $this->apiResponse['message'] = 'Account activation date is already expired.';
            RentMy::logToGenius([
                  "event" => "customer_activation",
                  "status" => "fail",
                  "description" => $this->apiResponse['message'],
                  "value" => $this->apiResponse['message'],
                  "custom_content" => json_encode([
                    'params' => $params,
                    'check_customer' => $check_customer,
                  ])
            ]);
            return;
        endif;

        $check_customer->status = 1;
        $this->Customers->save($check_customer);

        RentMy::logToGenius([
              "event" => "customer_activated",
              "status" => "success",
              "description" => 'Customer activated.',
              "value" => 'ID: ' . $check_customer->id . ', Email: ' . $check_customer->email,
              "custom_content" => json_encode([
                'customer' => $check_customer,
              ])
        ]);

        $this->apiResponse['data'] = ['validation_status' => true];
        $this->apiResponse['message'] = 'Provided activation key is valid.';// . $message;
    }

    /**
     * this function will activate or change customers password
     */
    public function activateCustomerPassword()
    {
        $this->request->allowMethod('post');
        $this->loadModel('Customers');
        $params = $this->request->getQuery();
        $post_data = $this->request->getData();

        if (empty($params['activation_key'])):
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Please provide activation code.';
            return;
        endif;

        if (empty($post_data['password'])):
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Please provide password.';
            return;
        endif;

        $check_customer = $this->Customers->find()->where(['activation_key' => $params['activation_key'], 'store_id' => $this->jwtPayload->store_id])->first();

        if (empty($check_customer)):
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'No activation code found.';
            return;
        endif;

        if (date('Y-m-d', strtotime($check_customer['activation_expire'])) <= date('Y-m-d')):
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'Account activation date is already expired.';
            return;
        endif;

        $check_customer->status = 1;
        $check_customer->password = (new DefaultPasswordHasher)->hash(trim($post_data['password']));
        $check_customer->activation_key = '';
        $check_customer->activation_expire = null;

        if ($this->Customers->save($check_customer)):
            $this->apiResponse['message'] = 'Congrats, user password saved successfully.';
        else:
            $this->httpStatusCode = 403;
            $this->apiResponse['message'] = 'User password could not updated. Please try again.';
        endif;

    }

    /**
     * this function will generate token for user with stores, company
     */
    private function makeToken($customer)
    {
        $location = RentMy::$token->location;

        if (!empty(RentMy::$storeConfig['client']['active'])){
            $vendorId = RentMy::$token->vendor_id ?? '';
            if (($customer['type'] == 2) && !empty($customer['username'])){
                $vendorId = $customer['id'];
            }
        }

        $options = !empty($customer->optional) ? json_decode($customer->optional, true) : [];

        $token_salt = [
            'id' => $customer['id'],
            'customer_id' => $customer['id'],
            'email' => $customer['email'],
            'date' => date('Y-m-d h:i:s'),
            'store_id' => $customer['store_id'],
            'status' => $customer['status'],
            'customer_type' => $customer['type'],
            'source' => RentMy::$token->source,
            'location'=> $location,
            'tax_exempt' => !empty($options['tax_exempt'])
        ];

        if (!empty($vendorId)){
            $token_salt['vendor_id'] = $vendorId;
        }

        $token_data = array(
            'token' => JwtToken::generateToken($token_salt),
            'customer_id' => $customer['id'],
            'first_name' => $customer['first_name'],
            'last_name' => $customer['last_name'],
            'email' => $customer['email'],
            'mobile' => $customer['mobile'],
            'company' => $customer['company'],
            'logo' => !empty($customer['image']) ? $customer['image'] : null,
            'customer_type' => $customer['type'],
            'tax_exempt' => !empty($options['tax_exempt'])
        );
        if (!empty($vendorId)){
            $token_data['vendor_id'] = $vendorId;
        }
        return $token_data;
    }

    /*
     * This function will save and activate the current token and inactive previous token
     *
     **/
    private function saveToken($customer, $token)
    {
        $this->add_model(array('AccessLogs'));
        $access_data = [
            'user_id' => $customer['id'],
            'token' => $token,
            'store_id' => RentMy::$store->id,
            'type' => 'customer', 'status' => 1,
            'location'=> RentMy::$token->location
        ];

        $this->AccessLogs->updateAll(['status' => 0], ['user_id' => $access_data['user_id'], 'status' => 1, 'type' => 'customer']);
        $token = $this->AccessLogs->newEntity();
        $token = $this->AccessLogs->patchEntity($token, $access_data);
        $this->AccessLogs->save($token);
    }

    /**
     * register user
     * account activation date will expire after 1 days from registration date
     *
     * @param $activation code and password
     * @return sent activation email
     * @API - POST /customers/register
     * */
    public function register()
    {
        $this->request->allowMethod('post');
        RentMy::addModel(['Customers', 'CustomerAddresses']);
        $data = $this->request->getData();

        if (!empty($data['email']) && !empty($data['password'])) {
            try {
                // check existing customers
                $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($data['email']), 'store_id' => RentMy::$store->id])->first();
                if (!empty($customer)) {
                    $this->httpStatusCode = 400;
                    $this->apiResponse['message'] = 'Email Already exists. Please use another email.';
                    RentMy::logToGenius([
                          "event" => "customer_register",
                          "status" => "fail",
                          "description" => $this->apiResponse['message'],
                          "value" => 'Email: ' . $data['email'],
                          "custom_content" => json_encode([
                            'data' => $data,
                            'customer' => $customer,
                          ])
                    ]);
                    return;
                }

                $activation = mt_rand(********, ********); //md5($this->randomnum(8));
                $data['activation_key'] = $activation;
                $date = strtotime("+1 day");
                $data['activation_expire'] = date('Y-m-d', $date);
                $data['status'] = 1;
                $data['store_id'] = RentMy::$store->id;
                $data['type'] = 1;
                $data['password'] = (new DefaultPasswordHasher)->hash(trim($data['password']));
                $user = RentMy::$Model['Customers']->newEntity();
                $user = RentMy::$Model['Customers']->patchEntity($user, $data);
                if (RentMy::$Model['Customers']->save($user)) {
                    if (!empty($data['city']) && !empty($data['state']) && !empty($data['country'])) {
                        $addressData = [
                            'customer_id' => $user->id,
                            'store_id' => RentMy::$store->id,
                            'is_primary' => 1,
                            'type' => 'Primary',
                            'city' => $data['city'] ?? '',
                            'zipcode' => $data['zipcode'] ?? '',
                            'state' => $data['state'] ?? '',
                            'country' => strtoupper($data['country']) ?? '',
                            'mobile' => $data['mobile'] ?? '',
                            'address_line1' => $data['address_line1'] ?? '',
                            'address_line2' => $data['address_line2'] ?? ''
                        ];
                        $address = RentMy::$Model['CustomerAddresses']->newEntity($addressData);
                        RentMy::$Model['CustomerAddresses']->save($address);

                    }



                    if (!empty(RentMy::$store->domain)) {
                        $host = 'https://' . RentMy::$store->domain;
                    } else {
                        $host = Configure::read('STORE_HOST')[0] . RentMy::$store->name . Configure::read('STORE_HOST')[1];
                    }
                    if ($user->type == 1){
                        $host =RentMy::storeDomain();
                    }

                    $activation = ($user->type == 1) ? $host . '/customer/activation?activation_key=' . $user->activation_key
                        : $host . '/client/activation?activation_key=' . $user->activation_key;

                    RentMy::$store->store_logo = !empty(RentMy::$store->logo) ? RentMy::makeS3Url('/store-logo/' . RentMy::$store->id . '/' . RentMy::$store->logo) :
                        Configure::read('CLIENT_HOST') . '/assets/img/home/<USER>';

                    $options = array('template' => 'customer_activation', 'to' => trim($data['email']), 'activation' => $activation,
                        'subject' => 'Welcome to ' . RentMy::$store->slug,
                        'store' => json_decode(json_encode(RentMy::$store), true)
                    );
                    // RentMy::Email($options);
                    $options['location'] = RentMy::$token->location;
                    RentMy::addNotificationQueue('customer_register', RentMy::$store->id, $options);

                    RentMy::logToGenius([
                          "event" => "customer_registered",
                          "status" => "success",
                          "description" => 'Customer registered.',
                          "value" => 'ID: ' . $user->id . ', Email: ' . $user->email,
                          "custom_content" => json_encode([
                            'customer' => $user,
                            'email_data' => $options
                          ])
                    ]);


                    if (!empty($data['wishlist_token'])){
                        RentMy::addModel(['WishList']);
                        $wishList = RentMy::$Model['WishList']->find()->where(['uid' => $data['wishlist_token']])->first();
                        if (!empty($wishList)) {
                            $wishList->customer_id = $user['id'];
                            RentMy::$Model['WishList']->save($wishList);
                        }
                    }
                    $customer_token_data = $this->makeToken($user);
                    $this->saveToken($user, $customer_token_data['token']);
                    $this->apiResponse['data'] = $customer_token_data;
                    $options = array('template' => 'register', 'to' => $user['email'], 'user' => 'company', 'activation' => $activation, 'subject' => 'Registration');
                    //$this->apiResponse['message'] = 'Customer has been saved successfully. An email is sent to your email address. Please check your email and activate account.';
                    // echo '.........................';
//
//                    RentMy::dbg($customer_token_data);
//                    echo 'kkkkkkkkkkkkkk';
//
//                    echo '-----------------';
//                    $this->apiResponse['data'] = $customer_token_data;
                    //  $this->apiResponse['customer'] = ['id' => $user['id'], 'email' => $user['email'], 'first_name' => $user['first_name'], 'last_name' => $user['last_name']];
                   } else {
                    $this->httpStatusCode = 400;
                    $errors = $user->errors();
                    $error_msg = $this->errorMessage($errors);
                    $this->apiResponse['message'] = $error_msg;
                    RentMy::logToGenius([
                          "event" => "customer_register",
                          "status" => "fail",
                          "description" => $this->apiResponse['message'],
                          "value" => $this->apiResponse['message'],
                          "custom_content" => ''
                    ]);
                }
            } catch (\Exception $e) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = $e->getMessage();
                RentMy::logToGenius([
                      "event" => "customer_register",
                      "status" => "fail",
                      "description" => $this->apiResponse['message'],
                      "value" => $this->apiResponse['message'],
                      "custom_content" => json_encode([
                          "exception" => json_encode($e->getMessage())
                      ])
                ]);
            }
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Please enter customer information';
            RentMy::logToGenius([
                  "event" => "customer_register",
                  "status" => "fail",
                  "description" => $this->apiResponse['message'],
                  "value" => $this->apiResponse['message'],
                  "custom_content" => ''
            ]);
        }
    }

    /**
     * Add new customer
     *
     * @param basic: first_name, last_name, email etc..
     * @param primary address: state, city, country etc
     * @param shipping address: shipping_state, shipping_city, shipping_country etc..
     * */
    public function add()
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Customers'));

        $data = $this->request->getData();
        $source = $this->request->getQuery('source');
        if (!empty($source) && ($source == 'admin')) {  // for admin
            $requiredFields = ['first_name', 'last_name', 'email'];
            $required = RentMy::requiredKeyExist($data, $requiredFields);
            if (!empty($required)) {
                $this->httpStatusCode = 400;
                $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
                return;
            }

            $customer = $this->Customers->find()->where([
                'email' => trim($data['email']),
                'store_id' => Rentmy::$token->store_id,
                'status' => 1,
            ])->first();

            if (!empty($customer)) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = 'User already exists with this email. Please provide a new one.';
                return;
            }

            $data['store_id'] = RentMy::$store->id;
            $data['location'] = !empty($data['location']) ? $data['location'] : '';
            $activation = mt_rand(********, ********); //md5($this->randomnum(8));
            $data['activation_key'] = $activation;
            $date = strtotime("+1 day");
            $data['activation_expire'] = date('Y-m-d', $date);
            $data['status'] = 1;
            $optional = !empty($data['optional'])?$data['optional']:[];
            $data['optional']  = json_encode($optional);

            $data['password'] = (new \Cake\Auth\DefaultPasswordHasher)->hash(RentMy::randomNum(5));
            $customer = $this->Customers->create($data);
            if (empty($customer->id)) {
                $this->httpStatusCode = 401;
                $errors = $customer->errors();
                $error_msg = $this->errorMessage($errors);
                $this->apiResponse['message'] = $error_msg;
                return;
            }

            $this->loadModel('CustomerAddresses');
            $data['customer_id'] = $customer->id;
            $customer['address'] = $this->CustomerAddresses->_customerAddress($data);

            if ($data['send_email'] == 'true') {
                try {
                    if (!empty($data['source'])) {
                        $host = $data['source'];
                    }else{
                        $host =RentMy::storeDomain();
                    }

//                    if (!empty(RentMy::$store->domain)) {
//                        $host = 'https://' . RentMy::$store->domain;
//                    } else {
//                        $host = Configure::read('STORE_HOST')[0] . RentMy::$store->name . Configure::read('STORE_HOST')[1];
//                    }

                    $activation = ($customer->type == 1) ? $host . '/customer/activation?activation_key=' . $customer->activation_key
                        : $host . '/client/activation?activation_key=' . $customer->activation_key;
                    RentMy::$store->store_logo = !empty(RentMy::$store->logo) ? RentMy::makeS3Url('/store-logo/' . RentMy::$store->id . '/' . RentMy::$store->logo) :
                        Configure::read('CLIENT_HOST') . '/assets/img/home/<USER>';

                    $options = array('template' => 'customer_activation', 'to' => trim($data['email']), 'activation' => $activation,
                        'subject' => 'Welcome to ' . RentMy::$store->slug,
                        'store' => json_decode(json_encode(RentMy::$store), true)
                    );
                    $options['location'] = RentMy::$token->location;
                    RentMy::addNotificationQueue('customer_register', RentMy::$store->id, $options);
                    //RentMy::Email($options);

                } catch (\Exception $e) {

                }
            }
            $this->apiResponse['data'] = $customer;
            $this->apiResponse['message'] = 'Customer has been saved successfully.';
        } else {
            $data['store_id'] = RentMy::$store->id;
            $user = $this->Customers->create($data);
            if (!empty($user->id)) {
                $this->loadModel('CustomerAddresses');
                $data['customer_id'] = $user->id;
                $user['address'] = $this->CustomerAddresses->_customerAddress($data);
                $this->apiResponse['data'] = $user;
                if (!empty($data['view_token'])) { // pos screen customer view
                    $customer = $this->Customers->getDetails($data['customer_id']);
                    (new PosCustomerView($data['view_token']))->save('customer', ['customer' => $customer]);
                }
                $this->apiResponse['message'] = 'Customer has been saved successfully.';

            } else {
                $this->httpStatusCode = 401;
                $errors = $user->errors();
                $error_msg = $this->errorMessage($errors);
                $this->apiResponse['message'] = $error_msg;
            }
        }

    }


    /**
     * Update customer info
     *
     * @param customer id
     *
     * */
    public function edit($id)
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Customers', 'CustomerAddresses'));
        $data = $this->request->getData();
        $source = $this->request->getQuery('source');
        if (!empty($source) && ($source == 'admin')) {  // for admin
            //Required fields
            $requiredFields = ['first_name', 'last_name', 'email'];
            $required = RentMy::requiredKeyExist($data, $requiredFields);
            if (!empty($required)) {
                $this->httpStatusCode = 400;
                $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
                return;
            }

            $user = $this->Customers->get($id);
            if (empty($user)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['message'] = 'No data has found. Please enter customer information.';
                return;
            }

            $customer = $this->Customers->find()->where([
                'email' => trim($data['email']),
                'store_id' => Rentmy::$token->store_id,
                'id !=' => $id
            ])->first();

            if (!empty($customer)) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = 'User already exists with this email. Please provide a new one.';
                return;
            }

            if (!empty($data['username'])){
                $customer = $this->Customers->find()->where([
                    'username' => trim($data['username']),
                    'store_id' => Rentmy::$token->store_id,
                    'id !=' => $id
                ])->first();
                if (!empty($customer)) {
                    $this->httpStatusCode = 403;
                    $this->apiResponse['message'] = 'User already exists with this username. Please provide a new one.';
                    return;
                }
            }

            $optional = !empty($data['optional'])?$data['optional']:[];
            $data['optional']  = json_encode($optional);
//            $previousStatus = $user->status;
            $data['status'] = !isset($data['status']) ? $user->status : $data['status'];
            $user = $this->Customers->patchEntity($user, $data);
            if ($this->Customers->save($user)) {
                $data['store_id'] = RentMy::$store->id;
                $data['customer_id'] = $user->id;
                $this->CustomerAddresses->_customerAddress($data);

                $this->apiResponse['data'] = $user;

                $this->apiResponse['message'] = 'Customer has been updated successfully.';
            } else {
                $this->httpStatusCode = 404;
                $this->apiResponse['data'] = [];
                $this->apiResponse['message'] = 'Customer could not be updated. Please try again.';
            }
        } else {
            if (empty($id)) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = 'No data has found. Please enter customer id';
            }
            if (empty($data)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['message'] = 'No data has found. Please enter customer information.';
            }
            $user = $this->Customers->get($id);
            $user = $this->Customers->patchEntity($user, $data);
            if ($this->Customers->save($user)) {
                $data['store_id'] = $this->parseToken->store_id;
                $data['customer_id'] = $user->id;
                $this->CustomerAddresses->_customerAddress($data);


                if (!empty($data['view_token'])) { // pos screen customer view
                    $customer = $this->Customers->getDetails($id);
                    (new PosCustomerView($data['view_token']))->save('customer', ['customer' => $customer]);
                }
                $this->apiResponse['message'] = 'Customer has been updated successfully.';
            } else {
                $this->httpStatusCode = 404;
                $this->apiResponse['message'] = 'Customer could not be updated. Please try again.';
            }


        }
    }

    /**
     * delete a customer
     *
     * @param customer id
     * */
    public function delete($id = null)
    {
        $this->request->allowMethod('delete');
        $this->add_model(array('Customers', 'CustomerAddresses', 'Orders'));
        if (!empty($id)) {
            try {
                $user = $this->Customers->get($id);
                if (!empty($user)) {
                    if ($this->Customers->delete($user)) {
                        try {
                            $this->CustomerAddresses->deleteAll(['id' => $id]);
                            $this->Orders->updateAll(['customer_id' => null], ['customer_id' => $id, 'store_id' => RentMy::$store->id]);
                        } catch (\Exception $e) {

                        }

                        $this->apiResponse['message'] = 'This Customer has been deleted.';
                    } else {
                        $this->httpStatusCode = 405;
                        $this->apiResponse['message'] = 'Customer could not be deleted. Please try again.';
                    }
                } else {
                    throw new NotFoundException();
                }
            } catch (\Exception $e) {
                $this->httpStatusCode = 405;
                $this->apiResponse['message'] = 'Customer could not be deleted. Please try again.';
            }
        } else {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'No data has found. Please enter customer id';
        }
    }

    /** Get a customer details
     *
     * @param @customer_id,
     * @return $basic info,
     * @return $primary address,
     * @return $shipping address
     * */
    public function view($id = null)
    {

        $this->loadModel('Customers');
        if ($id != null) {
            $data = $this->request->getQueryParams();
            $customer = $this->Customers->getDetails($id, $data);
            if (!empty($this->request->getQueryParams()['view_token'])) { // pos screen customer view
                (new PosCustomerView($this->request->getQueryParams()['view_token']))->save('customer', ['customer' => $customer]);
            }
            $this->apiResponse['data'] = $customer;
        }

    }

    /**
     * List of all customer && search customers for admin
     * @API - GET- /customers?page_no=1&limit=10
     *
     */
    public function index()
    {
        $this->request->allowMethod('get');
        $orderBy = 'Customers.id';
        $order = 'DESC';
        $queryParams = $this->request->getQueryParams();
        $pageNo = empty($queryParams['page_no']) ? 1 : $queryParams['page_no'];
        $limit = empty($queryParams['limit']) ? 40 : $queryParams['limit'];
        $type = empty($queryParams['type']) ? 1 : $queryParams['type'];
        $isSubscription = !empty($queryParams['subscription']);
        $all = empty($queryParams['all']) ? false : $queryParams['all'];

        $offset = ($pageNo - 1) * $limit;
        $where = ['Customers.store_id' => RentMy::$store->id];

        if ($all){
            $where = array_merge($where, ['type IN' => [1, 2]]);
        }else{
            $where = array_merge($where, ['type' => $type]);
        }
        if ($isSubscription){
            $where = array_merge($where, ['subscription_id !=' => '']);
            if (!empty($queryParams['start_date']) && !empty($queryParams['end_date'])){
                $start_date = Time::parse($queryParams['start_date'])->format('Y-m-d 00:00');
                $end_date = Time::parse($queryParams['end_date'])->format('Y-m-d 23:59');
                $where = array_merge($where, ['subscription_date >=' => $start_date]);
                $where = array_merge($where, ['subscription_date <=' => $end_date]);
            }

        }

        $search = ['OR' => []];
        if (!empty($queryParams['email'])) {
            $search['OR'] += ["Customers.email LIKE " => '%' . strtolower(trim($queryParams['email'])) . '%'];
        }
        if (!empty($queryParams['first_name'])) {
            $search['OR'] += ["Customers.first_name LIKE " => '%' . strtolower(trim($queryParams['first_name'])) . '%'];
        }
        if (!empty($queryParams['last_name'])) {
            $search['OR'] += ["Customers.last_name LIKE " => '%' . strtolower(trim($queryParams['last_name'])) . '%'];
        }
        if (!empty($queryParams['mobile'])) {
            $search['OR'] += ["Customers.mobile LIKE " => '%' . strtolower(trim($queryParams['mobile'])) . '%'];
        }

        if (!empty($search['OR'])) {
            $where = array_merge($search, $where);
        }


        $this->add_model(array('Customers'));
        $customersFinder = $this->Customers->find();
        $customersFinder->where($where);

        $customersFinder = $customersFinder->contain(['CustomerPlans' => function($q){
            return $q->select(['id', 'name', 'duration_type', 'duration', 'number_of_inventory', 'description']);
        }]);

        $customers = $customersFinder
            ->order([$orderBy => $order])
            ->offset($offset)
            ->limit($limit)
            ->map(function ($customer) {
                $options = !empty($customer['optional']) ? json_decode($customer['optional'], true) : [];
                $return = [
                    'id' => $customer['id'],
                    "first_name" => $customer['first_name'],
                    "last_name" => $customer['last_name'],
                    "mobile" => $customer['mobile'],
                    "phone" => $customer['phone'],
                    "type" => $customer['type'],
                    "company" => $customer['company'],
                    "email" => $customer['email'],
                    "social_type" => $customer['social_type'],
                    "social_id" => $customer['social_id'],
                    "image" => $customer['image'],
                    "status" => $customer['status'],
                    "plan_id" => $customer['plan_id'],
                    "balance" => $customer['balance'],
                    "total_paid" => $customer['total_paid'],
                    "gateway_customer_id" => $customer['gateway_customer_id'],
                    "customer_plan" => $customer['customer_plan']??[],
                    "created" => RentMy::toStoreTimeZone($customer['created'], 'm-d-Y h:i A'),
                    'optional' => $options
                ];
                return $return;
            })
            ->toArray();

        if ($customers) {
            $this->apiResponse['page_no'] = $pageNo;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = $customersFinder->count();
            $this->apiResponse['data'] = $customers;
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    public function changeAvatar()
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Customers'));
        $data = $this->request->getData();
        if (empty($data)) {
            $this->httpStatusCode = 403;
            $this->apiResponse = ['message' => 'Please upload avatar'];
            return;
        }
        try {
            // $user = $this->Customers->find()->where(['id' => 1, 'store_id' => 2 ])->first();
            $user = $this->Customers->find()->where(['id' => RentMy::$token->customer_id, 'store_id' => RentMy::$store->id])->first();
            if (empty($user)) {
                $this->httpStatusCode = 403;
                $this->apiResponse = ['message' => 'Not allowed.'];
                return;
            }
            $type = 'customers' . DS . RentMy::$store->id;
            $name = $this->imgUpload($type, 400);
            if (empty($name)) {
                $this->httpStatusCode = 405;
                $this->apiResponse['message'] = 'Something wrong to upload avatar.';
                return;
            }
            $fileDir = getcwd() . DS . 'img' . DS . 'upload' . DS . $type . DS;
            if (!empty($user['image']) && file_exists($fileDir . $user['image'])) {
                unlink($fileDir . $user['image']);
            }

            $user->image = $name;
            if ($this->Customers->save($user)) {
                $this->apiResponse['data'] = $name;
            } else {
                $this->httpStatusCode = 405;
                $this->apiResponse = ['message' => 'Avatar could not be updated. Please try again.'];
            }
        } catch (Exception $e) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'Not Found';
        }

    }

    public function changeAvatarAdmin($id)
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Customers'));
        $data = $this->request->getData();
        if (empty($data)) {
            $this->httpStatusCode = 403;
            $this->apiResponse = ['message' => 'Please upload avatar'];
            return;
        }
        try {
            $user = $this->Customers->find()->where(['id' => $id, 'store_id' => RentMy::$store->id])->first();
            if (empty($user)) {
                $this->httpStatusCode = 403;
                $this->apiResponse = ['message' => 'Not allowed.'];
                return;
            }
            $type = 'customers' . DS . RentMy::$store->id;
            $name = $this->imgUpload($type, 400);
            if (empty($name)) {
                $this->httpStatusCode = 405;
                $this->apiResponse['message'] = 'Something wrong to upload avatar.';
                return;
            }
            $fileDir = getcwd() . DS . 'img' . DS . 'upload' . DS . $type . DS;
            if (!empty($user['image']) && file_exists($fileDir . $user['image'])) {
                unlink($fileDir . $user['image']);
            }

            $user->image = $name;
            if ($this->Customers->save($user)) {
                $this->apiResponse['data'] = $name;
            } else {
                $this->httpStatusCode = 405;
                $this->apiResponse = ['message' => 'Avatar could not be updated. Please try again.'];
            }
        } catch (Exception $e) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'Not Found';
        }

    }

    /**
     * @desc Gives the details of a order of a customer.
     */
    public function getOrderDetails($id)
    {
        try {
            if ($id != null) {
                RentMy::addModel(['Orders', 'Products', 'Customers', 'Jobs', 'JobItems']);
                $details = RentMy::$Model['Orders']->view($id);
                if (RentMy::$storeConfig['client']['active']) {
                    $jobs = RentMy::$Model['Jobs']->find()
                        ->select(['id', 'name'])
                        ->where(['store_id' => RentMy::$store->id, 'client_id' => RentMy::$token->customer_id])
                        ->toArray();
//                    RentMy::dbg($jobs);
                    $jobItems = RentMy::$Model['JobItems']->find()
                        ->where(['client_id' => RentMy::$token->customer_id, 'content_type' => 'order', 'content_id' => $id])
                        ->toArray();


                    //$details->jobs = $jobs;
                    foreach ($jobItems as $jobItem) {
                        foreach ($jobs as $i => $job) {
                            if ($job['id'] == $jobItem['job_id']) {
                                $selected_jobs[] = ['id' => $jobItem['job_id'], 'name' => $job['name']];
                            }
                        }

                    }
                    $details->jobs = $selected_jobs;
                }
//                $details = $this->Orders->find()
//                    ->contain(['OrderItems'])
//                    ->where([
//                        'customer_id' => $this->jwtPayload->customer_id,
//                        'store_id' => $this->jwtPayload->store_id,
//                        'id' => $id,
//                    ])
//                    ->map(function ($order) {
//                        foreach ($order['order_items'] as $items) {
//                            $items['product'] = $this->Products->find()->contain(['Images'])->where(['id' => $items['product_id']])->first();
//                        }
//                        $order['customer'] = $this->Customers->find()->select(['first_name', 'last_name', 'email', 'phone', 'mobile', 'company'])->where(['id' => $order['customer_id']])->first();
//                        return $order;
//                    })->toArray();

                if (!empty($details)) {
                    $this->httpStatusCode = 200;
                    $this->apiResponse['data'] = $details;
                } else {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['message'] = 'No data found for this order.';
                }
            } else {
                $this->httpStatusCode = 401;
                $this->apiResponse['message'] = 'Sorry! Invalid order.';
            }
        } catch (Exception $ex) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = $ex->getMessage();
        }
    }

    //get a customers all orders
    public function customerOrder()
    {
        $this->request->allowMethod('get');
        // $this->loadModel('Orders');
        RentMy::addModel(['Orders', 'JobItems', 'Jobs']);
        $where = array();
        $orderBy = 'Orders.created';
        $order = 'DESC';

        $queryParams = $this->request->getQueryParams();
        $pageNo = empty($queryParams['page_no']) ? 1 : $queryParams['page_no'];
        $limit = empty($queryParams['limit']) ? 10 : $queryParams['limit'];
        $offset = ($pageNo - 1) * $limit;

        $where = array_merge(array('Orders.customer_id' => $this->parseToken->customer_id), $where);
        $where = array_merge(array('Orders.store_id' => $this->parseToken->store_id), $where);

        if (!empty($queryParams['closet']))
            $where = array_merge(array('Orders.status !=' => 1), $where);

        $finder = RentMy::$Model['Orders']->find()
            // ->contain(['orderItems'])
            ->where($where);

        $order = $finder->offset($offset)
            ->limit($limit)
            ->order([$orderBy => $order])
            ->map(function ($item) {
                $orderReturn = [
                    'id' => $item['id'],
                    'store_order_id' => RentMy::getOrderPrefix() .$item['store_order_id'],
                    'type' => $item['type'],
                    'tax' => $item['tax'],
                    'delivery_charge' => $item['delivery_charge'],
                    'delivery_tax' => $item['delivery_tax'],
                    'created' => RentMy::toStoreTimeZone($item['created'], 'm-d-Y h:i A'),
                    'sub_total' => $item['sub_total'],
                    'total_price' => $item['total_price'],
                    'total_quantity' => $item['total_quantity'],
                    'name' => $item['first_name'] . ' ' . $item['last_name'],
                    'address' => $item['address_line1'],
                    'country' => $item['country_id'],
                    'state' => $item['state_id'],
                    'city' => $item['city'],
                    'zipcode' => $item['zipcode'],
                    'status' => $item['status'],
                    'total_discount' => $item['total_discount'],
                    'total_deposit' => $item['total_deposit'],
                    'total' => RentMy::$Model['Orders']->getOrderTotal($item)
                ];
//                $pickup = '';
//                if (!empty($row->pickup)) {
//                    $stores = TableRegistry::get('Locations');
//                    $store = $stores->get($row->pickup);
//                    $pickup = $store->location;
//                }
                // $a_data['pickup'] = $pickup;];
                return $orderReturn;
            })
            ->toArray();


        if (RentMy::$storeConfig['client']['active']) {
            foreach ($order as $row) {
                $orderIds[] = $row['id'];
            }
            if (!empty($orderIds)) {
                $jobs = RentMy::$Model['Jobs']->find('list')->where(['store_id' => RentMy::$store->id, 'client_id' => RentMy::$token->customer_id])->toArray();
                $jobItems = RentMy::$Model['JobItems']->find()
                    ->where(['client_id' => RentMy::$token->customer_id, 'content_type' => 'order', 'content_id IN' => $orderIds])
                    ->toArray();
                foreach ($order as $i => $item) {
                    foreach ($jobItems as $jobItem) {
                        if ($item['id'] == $jobItem['content_id']) {
                            $order[$i]['jobs'][] = ['id' => $jobItem['job_id'], 'name' => $jobs[$jobItem['job_id']]];
                        }
                    }
                }
            }
        }

        $this->apiResponse['data'] = $order;
        $this->apiResponse['page_no'] = $pageNo;
        $this->apiResponse['limit'] = $limit;
        $this->apiResponse['total'] = $finder->count();
    }

    /**
     * Search Customer by name, email , mobile, first or last name
     */
    public function search()
    {
        $this->loadModel('Customers');
        $data = $this->request->getQueryParams();

        if (!empty($data['search']) && !empty($data['from'])) {  // single customer lookup from email address
            $search = trim($data['search']);
            $customerSearch = $this->Customers->find()
                ->select(['id'])
                ->where(["store_id" => $this->parseToken->store_id, 'email' => $search])
                ->first();
            if (!empty($customerSearch)) {
                $customer = $this->Customers->getDetails($customerSearch->id, $data);
                $this->apiResponse['data'] = $customer;
            } else {
                $this->apiResponse['data'] = [];
            }
            return;
        }
        if (!empty($data['search'])) {
            $search = $this->request->getQuery('search');
            $search = strtolower($search);
            $options = array();
            $options = array_merge(array("Customers.store_id" => $this->parseToken->store_id), $options);
            $str = $search . "%";
            $options = array_merge(array(
                'OR' => array(
                    "Customers.email LIKE " => $str,
                    "Customers.first_name LIKE " => $str,
                    "Customers.last_name LIKE " => $str,
                    "Customers.mobile LIKE " => $str,
                    "Customers.company LIKE " => $str,
                )

            ), $options);
            $customers = $this->Customers->find()
                ->where($options)->distinct(['Customers.email'])
                // ->contain('primaryAddresses')
                // ->contain('shippingAddresses')
                ->order(['last_name' => 'ASC', 'first_name' => 'ASC'])
                ->toArray();
            $response = array();
            foreach ($customers as $customer) {
                $aCustomer = array();
                $aCustomer['id'] = $customer->id;
                $aCustomer['first_name'] = $customer->first_name;
                $aCustomer['last_name'] = $customer->last_name;
                $aCustomer['email'] = $customer->email;
                $aCustomer['mobile'] = $customer->mobile;
                $bind_address = [];
                // if (!empty($customer->primary_address)) {
                //     if (!empty($customer->primary_address->city)) {
                //         $aCustomer['address_line1'] = $customer->primary_address->address_line1;
                //     } else {
                //         $aCustomer['address_line1'] = '';
                //     }
                //     if (!empty($customer->primary_address->city)) {
                //         $aCustomer['address_line2'] = $customer->primary_address->address_line2;
                //     } else {
                //         $aCustomer['address_line2'] = '';
                //     }
                //     if (!empty($customer->primary_address->city)) {
                //         $bind_address[] = $aCustomer['city'] = $customer->primary_address->city;
                //     } else {
                //         $aCustomer['city'] = '';
                //     }
                //     if (!empty($customer->primary_address->state)) {
                //         $bind_address[] = $aCustomer['state'] = $customer->primary_address->state;
                //     } else {
                //         $aCustomer['state'] = '';
                //     }
                //     if (!empty($customer->primary_address->zipcode)) {
                //         $bind_address[] = $aCustomer['zipcode'] = $customer->primary_address->zipcode;
                //     } else {
                //         $aCustomer['zipcode'] = '';
                //     }
                //     if (!empty($customer->primary_address->country)) {
                //         $bind_address[] = $aCustomer['country'] = $customer->primary_address->country;
                //     } else {
                //         $aCustomer['country'] = '';
                //     }

                //     //$aCustomer['address_line2'] = implode(', ', $bind_address);
                // }

                // if (!empty($customer->shipping_address)) {
                //     $aCustomer['shipping_city'] = $customer->shipping_address->city ?? '';
                //     $aCustomer['shipping_state'] = $customer->shipping_address->state ?? '';
                //     $aCustomer['shipping_country'] = $customer->shipping_address->country ?? '';
                //     $aCustomer['shipping_zipcode'] = $customer->shipping_address->zipcode ?? '';
                //     $aCustomer['shipping_address1'] = $customer->shipping_address->address_line1 ?? '';
                //     $aCustomer['shipping_address2'] = $customer->shipping_address->address_line2 ?? '';
                // }
                $response[] = $aCustomer;
            }
          //  $this->apiResponse['data'] = $data;
        }
        $this->apiResponse['data'] = !empty($response) ? $response :  [];
    }

    /**
     * Update order customer details and
     * Update customer details / address
     * Update Order Address
     * @param null $id
     */
    public function updateOrderAddress($id = null)
    {
        if ($id != null) {
            $this->add_model(array('Orders', 'Customers', 'Addresses', 'CustomerAddresses', 'OrderAddresses'));
            $data = $this->request->getData();

            if (isset($data['store_order_id']))
                unset($data['store_order_id']);

            $data['company_name'] = $data['company_name'] ?? $data['company'];
            $data['company'] = $data['company'] ?? $data['company_name'];

            if (!empty($data)) {
                unset($data['order_address']);
                $order = $this->Orders->get($id);

                if (empty($order->customer_id)) {
                    $customer = $this->Customers->create($data);
                    $data['customer_id'] = $customer->id;
                }
                $data['custom_values'] = json_encode($data['custom_values']);

                if(!empty($data['site_id'])){
                    $options = [];
                    $options = json_decode($order['options'], true);
                    $options['site_id'] = $data['site_id'];
                    $data['options'] = json_encode($options);
                }

                $order = $this->Orders->patchEntity($order, $data);
                if ($this->Orders->save($order)) {
                    $data['store_id'] = $order->store_id;
                    $data['country'] = $order->country_id;
                    $data['customer_id'] = $order->customer_id;
                    $this->loadModel('CustomerAddresses');
                    $this->CustomerAddresses->_customerAddress($data);

                    $this->OrderAddresses->updateOrderAddress($id, $data);
                    $this->apiResponse['data'] = ['order_id' => $order->id, 'order_date' => $order->created];
                }

            }
        }
    }

    public function posView($token)
    {
        if (!empty($token)) {
            $data = (new PosCustomerView($token))->view();
        }
        $this->apiResponse['data'] = $data;
    }

    /** Save Signature and signature data */
    public function saveSignature()
    {
        $this->request->allowMethod('post');
        $data = $this->request->getData();
        $posViewObj = new PosCustomerView($data['view_token']);
        $posViewObj->save('signature', ['signature' => $data['signature']]);
        $options = $posViewObj->get(['options']);
        $options = !(empty($options['options'])) ? json_decode($options['options'], true) : [];
        $options['signature']['visible'] = false;
        $posViewObj->save('options', ['options' => $options]);

        // save order signature if order_id exist
        if (!empty($data['order_id'])) {
//            $directoryPath = WWW_ROOT . 'img' . DS . 'upload' . DS . 'orders' . DS;
//            file_put_contents($directoryPath . 'signature_' . $data['order_id'] . '.png', base64_decode(explode(',', $data['signature'])[1]));

            $directoryPath = WWW_ROOT . 'upload' . DS;
            file_put_contents($directoryPath . 'signature_' . $data['order_id'] . '.png', base64_decode(explode(',', $data['signature'])[1]));

            $s3 = new S3();
            $s3->upload([
                ['path' => $directoryPath . 'signature_' . $data['order_id'] . '.png', 'dir' => 'orders/' . 'signature_' . $data['order_id'] . '.png'],
            ]);
            RentMy::deleteFile($directoryPath, 'signature_' . $data['order_id'] . '.png');


            // terms and condition
            RentMy::addModel(['Orders']);
            $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
            if (!empty($order)){
                $options = !empty($order['options']) ? json_decode($order['options'], true): [];
                $options['terms_and_condition_accepted'] = true;
                if (!empty($data['intro_video_seen'])) $options['intro_video_seen'] = true;
                $order['options'] = json_encode($options);
                RentMy::$Model['Orders']->save($order);
            }

        }

    }


    /**
     * @POST /orders/signature
     * @return mixed
     */
    public function collectSignatureFromOnline()
    {

        try {
            $data = $this->request->getData();

            // save order signature if order_id exist
            if (!empty($data['order_id'])) {

                $directoryPath = WWW_ROOT . 'upload' . DS;
                file_put_contents($directoryPath . 'signature_' . $data['order_id'] . '.png', base64_decode(explode(',', $data['signature'])[1]));

                $s3 = new S3();
                if ($data['type'] == 'delivery'){
                    $s3name = 'orders/' . 'delivery_signature_' . $data['order_id'] . '.png';
                }else{
                    $s3name = 'orders/' . 'signature_' . $data['order_id'] . '.png';
                }
                $s3->upload([
                    ['path' => $directoryPath . 'signature_' . $data['order_id'] . '.png', 'dir' => $s3name],
                ]);
                RentMy::deleteFile($directoryPath, 'signature_' . $data['order_id'] . '.png');

                // terms and condition
                RentMy::addModel(['Orders']);
                $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
                if (!empty($order) && (empty($data['type']) && !in_array($data['type'], ['delivery']))){
                    $options = !empty($order['options']) ? json_decode($order['options'], true): [];
                    $options['terms_and_condition_accepted'] = true;
                    $options['intro_video_seen'] = !empty($data['intro_video_seen']);
                    $order['options'] = json_encode($options);
                    RentMy::$Model['Orders']->save($order);
                }elseif($data['type'] == 'delivery'){
                    RentMy::addModel(['OrderNotes']);
                    $orderNote = RentMy::$Model['OrderNotes']->find()->where([
                        'order_id' => $data['order_id'],
                        'store_id' => RentMy::$store->id,
                        'content_type' => 'DeliveryAcknowledgement'
                    ])->first();

                    if (empty($orderNote))
                        $orderNote = RentMy::$Model['OrderNotes']->newEntity();

                    $orderNote = RentMy::$Model['OrderNotes']->patchEntity($orderNote, [
                        'order_id' => $data['order_id'],
                        'store_id' => RentMy::$store->id,
                        'note' => '',
                        'file' => $s3name,
                        'content_type' => 'DeliveryAcknowledgement'
                    ]);
                    RentMy::$Model['OrderNotes']->save($orderNote);
                }

                $this->apiResponse['message'] = "Signature has been added";
            }
        }catch (\Exception $exception){
            $this->apiResponse['message'] = "Signature can not be added";
        }

    }
    /**
     * Collect signature for customers
     * Cashier or admin will click on this button &
     * system will save a params and customers display will show
     * signature collection portion
     */
    public function collectSignature()
    {
        $this->request->allowMethod('post');
        $data = $this->request->getData();
        $posViewObj = new PosCustomerView($data['view_token']);
        //RentMy::dbg($posViewObj);
        $posViewObj->clearAll();
        $posData = $posViewObj->get(['id', 'options', 'order_id', 'customer', 'cart', 'payment', 'status'], $data['order_id']);
        if (!empty($posData)) {
            $options = !(empty($options['options'])) ? json_decode($options['options'], true) : [];
            $options['signature']['visible'] = true;
            $posData->options = json_encode($options);
            $posData->status = 1;
            RentMy::$Model['CustomerView']->save($posData);

        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "This order details can't be shown in Customer Display";
        }
        // RentMy::dbg($options);
        // $options = !(empty($options['options'])) ? json_decode($options['options'], true) : [];
        ///  $options['signature']['visible'] = true;
        //  $posViewObj->save('options', ['options' => $options]);
    }

    public function posClear($token)
    {
        (new PosCustomerView($token))->clear();
    }

    /**
     * this api will get data from SSO platform and make token for that user
     * @API /customers/sso/:type
     * @param $type
     */
    public function ssoLogin($type){
        RentMy::addModel(['Customers']);
        $data = $this->request->getData();

        switch ($type){
            case 'cartdotcom':
                $data['social_id'] = $data['id'];
                break;
            default:
                break;
        }

        if (empty($data['email'])){
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Email is required for SSO';
            return;
        }

        try {
            $customer = RentMy::$Model['Customers']->find()->where(['store_id'=>RentMy::$store->id, 'email'=>$data['email']])->first();
            if (empty($customer))
                $customer = RentMy::$Model['Customers']->newEntity();

            $customerData = [
                'email' => $data['email'],
                'store_id' => RentMy::$store->id,
                'social_type' => $type,
            ];
            if (!empty($data['social_id'])) $customerData['social_id'] = $data['social_id'];
            if (!empty($data['first_name'])) $customerData['first_name'] = $data['first_name'];
            if (!empty($data['last_name'])) $customerData['last_name'] = $data['last_name'];
            if (!empty($data['mobile'])) $customerData['phone'] = $data['mobile'];
            if (!empty($data['mobile'])) $customerData['mobile'] = $data['mobile'];
            if (!empty($data['company'])) $customerData['company'] = $data['company'];
            if (isset($data['status'])) $customerData['status'] = $data['status'];

            $customer = RentMy::$Model['Customers']->patchEntity($customer, $customerData);
            if(RentMy::$Model['Customers']->save($customer)){
                $customer_token_data = $this->makeToken($customer);
                $this->apiResponse['data'] = $customer_token_data;
                $this->apiResponse['message'] = 'Congrats, Login Successful.';
                return;
            }
        }catch (\Exception $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
            return;
        }
    }

    /**
     * @param $id
     * @return void
     */
    public function loginWithId($id){
        RentMy::addModel(['Customers']);
        $customer = RentMy::$Model['Customers']->find()->where(['store_id' => RentMy::$store->id,'id'=>$id])->first();
        if (empty($customer)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Customer not found";
            return;
        }
        $customer_token_data = $this->makeToken($customer);
        $this->apiResponse['data'] = $customer_token_data;
    }
    /*
     * @POST /client/login
     * @return mixed
     */
    public function clientLogin()
    {
        RentMy::addModel(['Customers', 'Stores', 'Jobs']);
        $data = $this->request->getData();
        if (empty($data)):
            $this->httpStatusCode = 400; //forbidden , 404 : not found, 401 : unauthorized, 405 : method not allow (get/post)
            $this->apiResponse['message'] = 'Please provide the credentials.';
            return;
        endif;
        try {
            switch ($data['login_type']){
                case 'username':
                    if (empty($data['username'])) {
                        $this->httpStatusCode = 400;// bad request
                        $this->apiResponse['message'] = 'Username is required.';
                        return;
                    }
                    $client  = RentMy::$Model['Customers']->find()->where(['username' => $data['username'],
                        'status' => 1, 'type' => 2, 'store_id'])->first();
                    break;
                default:
                    break;
            }
            if (empty($client)){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Client not found.';
                return;
            }
            $customer_token_data = $this->makeToken($client);
            $this->saveToken($client, $customer_token_data['token']);
            if (RentMy::$storeConfig['client']['active']) {
                $job = RentMy::$Model['Jobs']->find()->where(['client_id' => $client['id'], 'store_id' => RentMy::$store->id, 'is_default' => 1])
                    ->first();
                if (!empty($job)) {
                    $customer_token_data['job_id'] = $job->id;
                }
            }
            $this->apiResponse['data'] = $customer_token_data;
            $this->apiResponse['message'] = 'Congrats, Login Successful.';
            return;
        } catch (\Exception $e) {
            $this->httpStatusCode = 400;// bad request
            $this->apiResponse['message'] = $e->getMessage();
            return;
        }
    }

    /**
     * @GET /vendor/list
     * @return mixed
     */
    public function vendorList()
    {
        RentMy::addModel(['Customers']);
        $data = $this->request->getQueryParams();
        $vendorObj = RentMy::$Model['Customers']->find()->select(['id', 'first_name', 'last_name'])->where(['type' => 2, 'store_id' => RentMy::$store->id]);
        if (!empty($data['search']))
            $vendorObj = $vendorObj->where([
                'OR' => [
                    'first_name LIKE' => '%'.$data['search'] . '%',
                    'last_name LIKE' => '%'.$data['search'] . '%',
                    'username LIKE' => '%'.$data['search'] . '%',
                ]
            ]);
        $vendors = $vendorObj->map(function ($customer){
            $name = $customer['first_name'];
            if (!empty($customer['last_name'])){
                $name .=  ' ' .$customer['last_name'];
            }
            $customer['name'] = $name;
            unset($customer['first_name']);
            unset($customer['last_name']);
            return $customer;
        })->toArray();
        $this->apiResponse['data'] = $vendors;
    }

    /**
     * register client
     * account activation date will expire after 1 days from registration date
     *
     * @API - POST /customers/register
     * */
    public function clientRegister()
    {
        RentMy::addModel(['Customers', 'CustomerAddresses']);
        $data = $this->request->getData();

        if (!empty($data['email']) && !empty($data['password'])) {
            try {
                // check existing customers
                $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($data['email']), 'store_id' => RentMy::$store->id])->first();
                if (!empty($customer)) {
                    $this->httpStatusCode = 400;
                    $this->apiResponse['message'] = 'Email Already exists. Please use another email.';
                    return;
                }

//                $activation = mt_rand(********, ********); //md5($this->randomnum(8));
//                $data['activation_key'] = $activation;
//                $date = strtotime("+1 day");
//                $data['activation_expire'] = date('Y-m-d', $date);
                $username = $this->generateUniqueUserName($data['first_name'] . $data['last_name']);

                $data['username'] = $username;
                $data['status'] = 0;
                $data['store_id'] = RentMy::$store->id;
                $data['type'] = 2;
                $data['password'] = (new DefaultPasswordHasher)->hash(trim($data['password']));
                $user = RentMy::$Model['Customers']->newEntity();
                $user = RentMy::$Model['Customers']->patchEntity($user, $data);
                if (RentMy::$Model['Customers']->save($user)) {
                    if (!empty($data['city']) && !empty($data['state']) && !empty($data['country'])) {
                        $addressData = [
                            'customer_id' => $user->id,
                            'store_id' => RentMy::$store->id,
                            'is_primary' => 1,
                            'type' => 'Primary',
                            'city' => $data['city'] ?? '',
                            'zipcode' => $data['zipcode'] ?? '',
                            'state' => $data['state'] ?? '',
                            'country' => strtoupper($data['country']) ?? '',
                            'mobile' => $data['mobile'] ?? '',
                            'address_line1' => $data['address_line1'] ?? '',
                            'address_line2' => $data['address_line2'] ?? ''
                        ];
                        $address = RentMy::$Model['CustomerAddresses']->newEntity($addressData);
                        RentMy::$Model['CustomerAddresses']->save($address);
                    }

                    // Bell Notification to admin
                    $name = $data['first_name'];
                    if (!empty($data['last_name']))
                        $name .= ' ' . $data['last_name'];

                    RentMy::addModel(['OrderNotes']);
                    $notes = RentMy::$Model['OrderNotes']->newEntity();
                    $noteData = [
                        "store_id" => RentMy::$store->id,
                        "order_id" => 0,
                        "content_type" => "ClientCreate",
                        "content_id" => $user->id,
                        "note" => $name . " has been joined as partner",
                        "source" => RentMy::$token->source
                    ];
                    $notes = RentMy::$Model['OrderNotes']->patchEntity($notes, $noteData);
                    RentMy::$Model['OrderNotes']->save($notes);

                    $this->apiResponse['message'] = "Successfully registered";
                } else {
                    $this->httpStatusCode = 400;
                    $errors = $user->errors();
                    $error_msg = $this->errorMessage($errors);
                    $this->apiResponse['message'] = $error_msg;
                }
            } catch (\Exception $e) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = $e->getMessage();
            }
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Please enter partner information';
        }
    }

    private function generateUniqueUserName($username)
    {
        $username = strtolower($username);
        $username = $this->removeSpecialCharacterAndSpaces($username);
        RentMy::addModel(['Customers']);
        $customer = RentMy::$Model['Customers']->find()->where([
            'username' => $username,
            'store_id' => RentMy::$store->id
        ])->first();

        if (!empty($customer)){
            $username .= random_int(100, 1000);
        }
        return $username;
    }

    private function removeSpecialCharacterAndSpaces($string) {
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

    /**
     * @POST /client/confirmation
     * @return mixed
     */
    public function clientConfirm()
    {
        // sending client confirmation email
        $data = $this->request->getData();

        if (empty($data['email'])){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Email is required";
            return;
        }

        RentMy::addModel(['Customers']);
        $client = RentMy::$Model['Customers']->find()->where([
            'store_id' => RentMy::$store->id,
            'email' => $data['email'],
            'type' => 2
        ])->first();

        if (empty($client)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Client does not exist";
            return;
        }

        $options = array('template' => 'default', 'to' => trim($client['email']),
            'store' => RentMy::$store
        );

        if (!empty($client['mobile'])){
            $options['mobile'] = $client['mobile'];
        }

        $options['location'] = RentMy::$token->location;
        RentMy::addNotificationQueue('partner_confirmation', RentMy::$store->id, $options);

        $this->apiResponse['message'] = "Client confirmation email has been sent";
    }

    /**
     * this api will use to settings a customer specific config like tax-exempt
     *@POST /customer/:id/settings
     * @return mixed
     */
    public function customerSettings($id)
    {
        RentMy::addModel(['Customers']);
        $data = $this->request->getData();
        $customer = RentMy::$Model['Customers']->find()->where(['id' => $id])->first();
        $options = !empty($customer->optional) ? json_decode($customer->optional, true) :[];
        if (isset($data['tax_exempt'])){
            $options['tax_exempt'] = !empty($data['tax_exempt']);
        }

        $customer->optional = json_encode($options);
        RentMy::$Model['Customers']->save($customer);

        $this->apiResponse['message'] = "Update saved";
    }

}
