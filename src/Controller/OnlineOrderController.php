<?php

namespace App\Controller;

use App\Lib\Payment\AuthorizeNet\AuthorizeNet;
use App\Lib\Payment\ConvenuPay;
use App\Lib\Payment\DeltaPay;
use App\Lib\Payment\Empyrean;
use App\Lib\Payment\FreedomPay;
use App\Lib\Payment\goMerchant\goMerchant;
use App\Lib\Payment\goMerchant\Vault;
use App\Lib\Payment\Midtrans;
use App\Lib\Payment\Nelnet;
use App\Lib\Payment\Payment;
use App\Lib\Payment\Square;
use App\Lib\Payment\Stripe\Charge;
use App\Lib\Payment\Stripe\Customer;
use App\Lib\Payment\Stripe\Price;
use App\Lib\Payment\Stripe\Products;
use App\Lib\Payment\Stripe\Stripe;
use App\Lib\Payment\Stripe\Subscription;
use App\Lib\Payment\Stripe\StripeIntent;
use App\Lib\Payment\Transafe;
use App\Lib\Payment\ValorPay\ValorPay;
use App\Lib\ProductContainer;
use App\Lib\RentMy\Download;
use App\Lib\RentMy\RentMy;
use App\Lib\RentMy\Shipping;
use App\Lib\S3;
use App\PaymentGateway\AuthorizeDOTNET;
use App\PaymentGateway\StripePayment;
use App\PaymentGateway\PayPalPayment;
use App\Services\Order\OrderAssetService;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\Event\Event;
use Cake\I18n\Time;
use Cake\Log\Log;
use Cake\Utility\Hash;
use Cake\I18n\Number;
use DateTime;
use Exception;
use Twilio\TwiML\Voice\Pay;

class OnlineOrderController extends AppController
{
    private $currency, $storeId, $userId;
    private $productContainer;

    public function initialize()
    {
        parent::initialize();
        $this->productContainer = new ProductContainer();
    }


    public function beforeFilter(Event $event)
    {

        parent::beforeFilter($event);
        $this->storeId = $this->parseToken->store_id;
        $this->userId = $this->parseToken->id ?? 0;
        $this->currency = Hash::get($this->getStoreSettings(), 'currency');

    }

    public function getStoreSettings()
    {
        $this->loadModel('Stores');
        $storeSettings = $this->Stores->get($this->parseToken->store_id);
        $data = array();
        $storeConfig = json_decode($storeSettings->config, true)[RentMy::$token->location];
        $data['currency'] = (!empty($storeConfig) && !empty($storeConfig['currency_format']['code'])) ? $storeConfig['currency_format']['code'] : 'USD';

        return $data;
    }

    private function _checkAvailability($cartId)
    {
        $this->add_model(array('VariantsProducts', 'Quantities'));
        $cartItem = $this->CartItems->find('all')->where(['cart_id' => $cartId])->where(['product_type' => 1])->where(['rental_type' => 'buy'])->toArray();
        $pCount = array();
        foreach ($cartItem as $item) {
            $pCount[$item->quantity_id] = 0;
        }

        $productMappingWithQuantityId = [];
        foreach ($cartItem as $item) {
            $pCount[$item->quantity_id] = empty($pCount[$item->quantity_id]) ? $item->quantity : ($pCount[$item->quantity_id] + $item->quantity);
            $productMappingWithQuantityId[$item->quantity_id] = [
                'product_id' => $item->product_id,
                'variants_products_id' => $item->variants_products_id,
            ];
        }
        if (!empty($pCount)) {
            foreach ($pCount as $k => $v) {


                if(!empty(RentMy::$storeConfig['inventory']['affiliate']['active'])){
                    $totalQuantity = $this->Quantities->find()->select(['total_quantity' => 'SUM(quantity)'])->where(['product_id' => $productMappingWithQuantityId[$k]['product_id'], 'variants_products_id' => $productMappingWithQuantityId[$k]['variants_products_id']])->first();
                    $quantity = $this->Quantities->get($k);
                    $quantity->quantity = $totalQuantity->total_quantity;

                }else{
                    $quantity = $this->Quantities->get($k);
                }

                if ($quantity->quantity < $v) {
                    $product = $this->Products->get($quantity->product_id);
                    $data = array();
                    $data['id'] = $product->id;
                    $data['name'] = $product->name;
                    $data['quantity'] = $quantity->quantity;
                    return array('success' => false, 'product' => $data);
                }
            }
        }
        return array('success' => true, 'product' => []);
    }

    private function paymentLog($order_id, $content_type, $content_id = null, $status, $options, $action_name = null, $response_text = null, $api_request = null)
    {
        $logData = array(
            'user_id' => $this->userId,
            'store_id' => $this->storeId,
            'order_id' => $order_id,
            'content_type' => $content_type,
            'content_id' => $content_id,
            'status' => $status,
            'options' => $options,
            'action_name' => $action_name,
            'response_text' => $response_text,
            'api_request' => $api_request,
        );

        $event = new Event('Model.PaymentLogs.PaymentLogs', $this, ['logData' => $logData]);
        $this->getEventManager()->dispatch($event);
    }

    private function _getPaymentType($data)
    {
        $payment_type = $data['type'] == 1 ? 1 : 6;
        return $payment_type;
    }

    /**
     * Add method
     */

    public function add()
    {
        try {


        $this->add_model(array('PaymentGateways', 'OrderItems', 'CartItems', 'Carts', 'Products', 'Coupons', 'Payments', 'Stores', 'StoresUsers', 'ProductsAvailabilities', 'Customers'));

        /** Requirement checking Start */

        if (!$this->request->is('post')) {
            $this->apiResponse['message'] = 'Method Not Allowed';
            return;
        }

        $data = $this->request->getData();
        if (empty($data['token'])) {
            $this->apiResponse['error'] = 'Cart Token is missing!';
            return;
        }

        $cart = $this->Carts->find()->where(['uid' => $data['token'], 'order_id IS NULL'])->contain(['CartItems'])->first();
        if (empty($cart) || $cart->total_quantity < 1) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Invalid or Empty Cart!';
            return;
        }

        /** Requirement checking End */

        Log::alert('-------store ID: ' . $this->parseToken->store_id . '--------- Request Data: ' . json_encode($data), ['scope' => ['online']]);

        $data['user_id'] = $this->userId;
        $data['store_id'] = $this->storeId;
        $data['currency'] = $this->currency;

        $data['cart_id'] = $cart->id;

        $checkAvailability = $this->_checkAvailability($data['cart_id']);

        if (!$checkAvailability['success']) {
            $this->apiResponse['data'] = ['availability' => $checkAvailability, 'payment' => []];
            return;
        }

        $total_amount = empty($data['amount']) ? (($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge + $cart->product_option_price)) : $data['amount'];

        if (isset($data['payment_amount'])) {
            if ($data['payment_amount'] === 'NaN') {
                //$data['amount'] = ($cart->sub_total + $cart->tax + $cart->total_discount_tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge) - $cart->total_discount;
                $data['amount'] = ($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge + $cart->product_option_price);
            } else {

                $data['amount'] = $data['payment_amount'];
            }
        } else {
            //$data['amount'] = empty($data['amount']) ? ( $cart->sub_total + $cart->tax + $cart->total_discount_tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax - $cart->total_discount) : $data['amount'];
            //$data['amount'] = empty($data['amount']) ? (($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge) - $cart->total_discount) : $data['amount'];
            $data['amount'] = empty($data['amount']) ? (($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge + $cart->product_option_price)) : $data['amount'];


            $recurringTotalAmount = 0;
            foreach ($cart['cart_items'] as $item) {
                $cart_options = !empty($item['options']) ? json_decode($item['options'], true) : [];
                if (!empty($cart_options['recurring']['duration_type'])) {
                    $recurringTotalAmount += $item['sub_total'] + $item['sales_tax'];
                }
            }
            if ($recurringTotalAmount > 0 && in_array($data['payment_gateway_name'], ['Stripe', 'Transafe']))
                $data['amount'] = $data['amount'] - $recurringTotalAmount;

        }

        $isZeroPayment = empty($recurringTotalAmount) && empty($data['amount']);

        if (!empty(RentMy::$storeConfig['payments']['hosted_payment']['active'])){
            $isZeroPayment = true;
            $this->apiResponse['hosted_payment'] = true;
            $data['status'] = 2;
            if (RentMy::$storeConfig['cart']['abandoned_cart']['active']){
                $data['status'] = 19;
            }

        }


        $data['cart'] = $cart;
        $data['cart']['options'] = $cart->options;

        /* Check Payment*/
        if (isset($data['email']) && !empty($data['email'])) {
            $data['customer_type'] = 1;
            $customer = $this->Customers->create($data);
            $data['customer_id'] = $customer->id;
            $data['customer'] = $customer;
        }

//        if (!empty($data['membership_checkout'])) // zero payment for membership checkout
//            $isZeroPayment = true;

        if ($data['type'] == 1 && (!isset($data['swipe']) || ($data['swipe'] == false)) && !$isZeroPayment) {
            $getPayment = [];
            $cart_options = json_decode($data['cart']['options'], true); // recurring options.

            if (($recurringTotalAmount > 0 && $data['amount'] <= 0) || !empty($data['account']['for_enduring'])) {
                if ($data['payment_gateway_name'] == 'Stripe' && !empty($data['account']['for_enduring'])) {
                    $data['real_amount'] = $data['amount'] = 0;
                    $config = (new Payment())->getStorePaymentConfig('Stripe');
                    $stripeIntent = new StripeIntent('PaymentIntent', $config['config']);
                    $data['stripe_payment_method'] = $data['account']['payment_method_id'];
                    $getPayment['customerRef'] = $stripeIntent->createCustomer(RentMy::$store->id, $data);
                    $getPayment['success'] = !empty($getPayment['customerRef']['ref']);
                }

                if ($data['payment_gateway_name'] == 'Transafe') {
                    $data['real_amount'] = $data['amount'] = 0;
                    $config = (new Payment())->getStorePaymentConfig('Transafe');
                    $transafe = new Transafe($config['config']);
                    $transafeVault = $transafe->createVault($data, true);
                    if ($transafeVault)
                        $getPayment['customerRef'] = $transafeVault;

                    $getPayment['success'] = !empty($transafeVault);
                }

            } else {
                $getPayment = $this->_getPayment($data);
            }
            if ($getPayment['success']) {
                $payment = $getPayment['data'];
                $data['customerRef'] = !empty($getPayment['customerRef']) ? $getPayment['customerRef'] : [];
                $this->Customers->create($data);
                $this->paymentLog(null, 'Payments', null, 1, json_encode($getPayment['data']));
            } else {
                $paymentData = array(
                    'availability' => array('success' => true),
                    'payment' => array('success' => false, 'message' => $getPayment['message']),
                );
                $this->paymentLog(null, 'Payments', null, 2, $getPayment['message']);

                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($paymentData), ['scope' => ['online']]);
                $this->apiResponse['data'] = $paymentData;
                return;
            }
        }

        // Get payment type
        $payment_type = $this->_getPaymentType($data);
        //$data['status'] = $this->_getOrderStatus($data);
        //exit();
        $order = $this->_makeOrder($data, $cart, $payment_type);

        if (isset($getPayment) && !empty($getPayment['stripe_payment_payment_intent_id'])) {
            $config = (new Payment())->getStorePaymentConfig('Stripe');
            $stripe = new StripeIntent('PaymentIntent', $config['config']);
            $stripe->updateIntent($getPayment['stripe_payment_payment_intent_id'], ['order_id' => $order['id']]);
        }

        $data['order_id'] = $order['id'];
        $queueData = ['type' => 'Create', 'order_id' => $order['id'], 'content_id' => $order['id'], 'user_id' => RentMy::$token->customer_id, 'user_type' => 'customer', 'source' => RentMy::$token->source];
        if ($order['rent_end'])
            $queueData['rent_end'] = $order['rent_end']->format('Y-m-d H:i:s');

        if ($order['rent_start'])
            $queueData['rent_start'] = $order['rent_start']->format('Y-m-d H:i:s');

        $queueData['mobile'] = $data['mobile'] ?? null;
        RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], $queueData);
        //RentMy::addQueue('Order', ['type' => 'Create', 'rent_start'=> $order['rent_start']->format('Y-m-d H:i:s'), 'rent_end'=> $order['rent_end']->format('Y-m-d H:i:s'), 'order_id' => $order['id'], 'content_id' => $order['id'], 'user_id' => RentMy::$token->customer_id, 'user_type' => 'customer', 'source' => RentMy::$token->source]);
            RentMy::addQueue('Order', ['type' => 'AfterCreate', 'order_id' => $order['id'], 'source' => RentMy::$token->source, 'location' => RentMy::$token->location, 'store_id' => RentMy::$store->id]);

        RentMy::logToGenius([
                "account" => @$order['event_location'],
                "event" => "order_created",
                "status" => "success",
                "description" => @$order['event_location'] . ' order created',
                "value" => 'Order ID: ' . $order['id'],
                "custom_content" => json_encode([
                    'data' => $queueData,
                ]),
                "ref2" => $order['customer_id']
        ]);

            if ($data['type'] == 1 || $data['type'] == "Online") {
            /* Start Authorized data insert to payment table */
            $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';//'Captured';
            if (!empty($getPayment['payment_method']))
                $data['payment_method'] = $getPayment['payment_method'];

            if ($data['payment_gateway_name'] == 'Stripe')
                $data['amount'] = !empty($getPayment['amount']) ? $getPayment['amount'] : $data['amount'];

            $data['transaction_id'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['retref'] : $getPayment['transaction_id'];
            $data['response_text'] = json_encode($getPayment['data']);
            $data['content_id'] = isset($data['is_admin']) ? 2 : 1;
            $data['action_name'] = 'card-entry';
            $option = $data['payment_gateway_name'] == 'CardConnect' ? $payment['resptext'] : 'Approval';

            $this->_payment($data, $option);

            if (!empty($getPayment['deposit_payment'])) {
                $data['amount'] = $getPayment['deposit_payment']['amount'];
                $data['transaction_id'] = $getPayment['deposit_payment']['transaction_id'];
                $data['response_text'] = json_encode($getPayment['deposit_payment']['data']);
                $data['payment_method'] = !empty($getPayment['deposit_payment']['payment_method']) ? $getPayment['deposit_payment']['payment_method'] : 'Authorized';
                $this->_payment($data, $option);
            }
        } elseif ($data['type'] == 3) {
            $uuid = $this->Orders->get($order['id'])->uuid;
            $data['uid'] = $uuid;
            $payment = self::redirectPayments($data);
            $response = array(
                'availability' => ['success' => true],
                'order' => ['success' => true, 'data' => ['uid' => $uuid]],
                'redirect_url' => $payment['redirect_url'],
                'payment' => ['success' => true, 'order_id' => $data['order_id'], 'quote' => true],
            );
            Log::warning('-------store ID: ' . RentMy::$store->id . '--------- Response Data: ' . json_encode($data), ['scope' => ['online']]);
            $this->apiResponse['data'] = $response;
        }elseif ($isZeroPayment){
            $uuid = $this->Orders->get($order['id'])->uuid;
            $data['uid'] = $uuid;
            $response = array(
                'availability' => ['success' => true],
                'order' => ['success' => true, 'data' => ['uid' => $uuid]],
                'payment' => ['success' => true, 'order_id' => $order['id'], 'quote' => false],
            );
            RentMy::addModel(['Payments']);
            RentMy::$Model['Payments']->updateOrderPaymentStatus($order['id']);

            if (RentMy::$store['id'] != 3497)
                RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);

            Log::warning('-------store ID: ' . RentMy::$store->id . '--------- Response Data: ' . json_encode($data), ['scope' => ['online']]);
            $this->apiResponse['data'] = $response;
        } else {
            if ($data['quote']) { // for quote order ignor payments and payment logs
                $uuid = $this->Orders->get($order['id'])->uuid;
                if (!empty($data['email'])) {
                    RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);

                    //RentMy::addQueue('Order', ['type' => 'OrderEmail', 'order_id' =>$data['order_id'], 'content_id' => $data['order_id'],  'source' => RentMy::$token->source]);
                }
                $quoteResponse = array(
                    'availability' => ['success' => true],
                    'order' => ['success' => true, 'data' => ['uid' => $uuid]],
                    'payment' => ['success' => true, 'order_id' => $data['order_id'], 'quote' => true],
                );
                Log::warning('-------store ID: ' . RentMy::$store->id . '--------- Response Data: ' . json_encode($data), ['scope' => ['online']]);
                $this->apiResponse['data'] = $quoteResponse;
            } else {
                if (!empty($data['gateway_id'])) {
                    $gateway = $this->PaymentGateways->get($data['gateway_id']);
                    if ($gateway) {
                        $data['type'] = 3;
                        $gatewayConfig = json_decode($gateway['config'], true);
                        if ($gatewayConfig['is_paid']) {
                            //$data['amount'] = $cart->sub_total + $cart->tax + $cart->total_discount_tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax - $cart->total_discount;
                            $payment_method = 'Paid';
                            $action_name = 'Additional' . '(' . $gateway->name . ')';
                        } else {
                            $data['amount'] = 0;
                            $action_name = 'Additional' . '(' . $gateway->name . ')';
                            $payment_method = 'Unpaid';
                        }

                    }

                    $data['payment_method'] = $payment_method;
                    $data['content_id'] = 6;
                    $data['action_name'] = $action_name;
                    $data['payment_gateway_details'] = $gateway;
                }


                $data['action_name'] = $action_name;
                $this->_payment($data, 'Online store – Additional');
            }
        }
            (new OrderAssetService($order['id']))->assignAssetsAutomaticallyToOrder();
        }catch (\Exception $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();

        }
    }


    /**
     * @param $data
     * @return int
     * @deprecated
     */
    private function _getOrderStatus($data)
    {
        if ($data['type'] == 1) {
            $status = 3; // Paid
        } else {
            $status = 9;
            if (!empty($data['gateway_id'])) {
                $gateway = $this->PaymentGateways->find()->where(['id' => $data['gateway_id']])->first();
                if ($gateway->name == 'Quote') {
                    $status = 10; // Quote  - this is not using now . so remove these codes
                }
            }

        }
        return $status;
    }

    public function redirectPayments($data)
    {
        RentMy::addModel(['Payments']);
        switch ($data['payment_gateway_name']) {
            case "nelnet":
                $config = (new Payment())->getStorePaymentConfig('nelnet');
                $nelNet = new Nelnet($config['config']);
                $response = $nelNet->paymentUrl([
                    'amount' => $data['amount'],
                    'orderNumber' => $data['order_id'],
                    'orderName' => $data['first_name'] . $data['last_name'],
                    'orderDescription' => RentMy::$store->id
                ]);
                $data['transaction_id'] = $response['hash'];
                $data['payment_method'] = 'Unpaid';

                $paymentData = [
                    'order_id' => $data['order_id'],
                    'store_id' => RentMy::$store->id,
                    'type' => 0,
                    'status' => 0,
                    'payment_amount' => $data['amount'],
                    'payment_method' => 'Unpaid',
                    'payment_gateway' => $data['payment_gateway_name'],
                    'gateway_id' => $data['gateway_id'],
                    'is_captured' => 0,
                    'note' => empty($data['note']) ? '' : $data['note'],
                    'transaction_id' => $response['hash']
                ];
                $payment = RentMy::$Model['Payments']->newEntity();
                $payment = RentMy::$Model['Payments']->patchEntity($payment, $paymentData);
                if (RentMy::$Model['Payments']->save($payment)) {
                    $this->_updateQuantity($data['cart_id']);
                    //now clearing the cart
                    $this->Carts->clearCart($data['cart_id']);
                    return ['success' => true, 'redirect_url' => $response['redirect_url']];
                }

                return ['success' => false];
                //https://clientapi.rentmy.co/api/payments/authenticate?transactionStatus=1&transactionId=5001600705&transactionDate=************&transactionTotalAmount=999&timestamp=1617225586940&hash=35832f8b13b0683c1714772c0439e4ae
                break;
            case "FreedomPay":
                return ['success' => true, 'redirect_url' => 'order/' . $data['uid'] . '/quote/payment?order_id=' . $data['order_id'] . '&customer_id=' . $data['customer_id'] . '&amount=' . $data['amount'] . '&gateway=FreedomPay'];
                break;
            case "Touchnet":
                return ['success' => true, 'redirect_url' => 'order/' . $data['uid'] . '/quote/payment?order_id=' . $data['order_id'] . '&customer_id=' . $data['customer_id'] . '&amount=' . $data['amount'] . '&gateway=Touchnet'];
                break;
            case "default":
                break;
        }
    }
    //TODO add a definition of this data
    public function _getPayment($data)
    {
        try {
            if ($data['payment_gateway_name'] != '' && $data['payment_gateway_name'] != 'CardConnect') {

                $param = [
                    'amount' => $data['amount'],
                    'store_id' => $data['store_id'],
                    'description' => '',
                    'currency' => $data['currency'],
                ];

                $card = !empty($data['card']) ? $data['card'] : [];

                $payment = [];
                switch ($data['payment_gateway_name']) {
                    case "Stripe":
                        $config = (new Payment())->getStorePaymentConfig('Stripe');

                        $stripe = new StripeIntent('PaymentIntent', $config['config']);
                        $paymentIntent = $stripe->getIntent($data['account']['id']);

                        $amountWithoutDeposit = $data['amount'] - $data['cart']['deposit_amount'];

                        if ((RentMy::$storeConfig['checkout_online_capture'] && RentMy::$storeConfig['checkout']['separate_deposit_amount']) && empty($data['account']['requires_action']) && ($amountWithoutDeposit < $data['amount'])) {
                            $stripe->updateIntent($data['account']['id'], ['amount' => $amountWithoutDeposit]);
                            $updatedAmount = $amountWithoutDeposit;
                        }
//                    RentMy::dbgAndEnd($paymentIntent);
                        if ($paymentIntent['status'] == 'requires_confirmation')
                            $paymentIntent = $stripe->confirmIntend($data['account']['id']);


                        $payment = [
                            'success' => true,
                            'transaction_id' => $paymentIntent->id,
                            'data' => $paymentIntent,
                            'stripe_payment_payment_intent_id' => $paymentIntent->id,
                        ];
                        if (!empty($updatedAmount))
                            $payment['amount'] = $updatedAmount;

                        if (isset($paymentIntent['charges']['data'][0]['captured']))
                            $payment['capture'] = !empty($paymentIntent['charges']['data']['captured']);

                        if (!empty($paymentIntent['charges']['data'][0]['captured']) && $paymentIntent['status'] != 'succeeded')
                            $payment['success'] = false;

                        $data['stripe_payment_method'] = $paymentIntent->payment_method;
                        $data['action_name'] = 'card-entry';
                        $data['store_id'] = RentMy::$store->id;
                        if (RentMy::$token->customer_id && (!empty($data['paytype']) && ($data['paytype'] == 'stripe_token'))) { //@todo payment intent update
                            $payment['customerRef'] = ['gateway' => 'Stripe', 'ref' => $data['account']['customer'], 'payment_method' => $data['stripe_payment_method']];
                        } else {
                            try {
                                $payment['customerRef'] = $stripe->createCustomer(RentMy::$store->id, $data);

                                (new Customer('PaymentIntent', $config['config']))->addSourceForCustomerIntent($payment['customerRef']['ref'], $paymentIntent->payment_method);
                            }catch (\Throwable $throwable){
                                RentMy::saveLogFile('alert', "storeId ==> " . RentMy::$store->id . " ==> ".$throwable->getMessage(), ['payment']);
                            }
                        }

                        if ((RentMy::$storeConfig['checkout_online_capture'] && RentMy::$storeConfig['checkout']['separate_deposit_amount']) && empty($data['account']['requires_action']) && ($amountWithoutDeposit < $data['amount'])) {
                            $depositIntent = $stripe->intent([
                                'payment_method_id' => $paymentIntent['payment_method'],
                                'amount' => $data['cart']['deposit_amount'],
                                'capture' => false,
                                'store_id' => RentMy::$store->id,
                                'customer' => !empty($payment['customerRef']['ref']) ? $payment['customerRef']['ref']: null,
                                'confirm' => true
                            ]);
                            $depositIntent = $stripe->getIntent($depositIntent['id']);
                            $payment['deposit_payment'] = [
                                'capture' => false,
                                'payment_method' => 'Authorized',
                                'success' => true,
                                'amount' => $data['cart']['deposit_amount'],
                                'transaction_id' => $depositIntent->id,
                                'data' => $depositIntent,
                                'stripe_payment_payment_intent_id' => $depositIntent->id,
                            ];
                        }
                        break;

                    case "goMerchant":
                        $config = (new Payment())->getStorePaymentConfig('goMerchant');
                        $goMerchantObj = new goMerchant($config['config']);
                        $cart_options = json_decode($data['cart']['options'], true);
                        $data['customer'] = ['first_name' => $data['first_name'],
                            'last_name' => $data['last_name'],
                            'primary_address' =>
                                [
                                    'address_line1' => $data['address_line1'],
                                    'city' => $data['city'],
                                    'state' => $data['state'],
                                    'zipcode' => $data['zipcode']
                                ]
                        ];
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';

                        if (!empty($cart_options['recurring']['duration_type'])) {
                            $vault = (new Vault($config['config']))->addCard($data);
                            $data['recurring'] = [
                                'duration_type' => $cart_options['recurring']['duration_type'],
                                'vaultKey' => $vault['vaultKey'],
                                'vaultId' => $vault['vaultId']
                            ];
                            if ($cart_options['recurring']['payment_type'] == 'before_rental') {
                                $payment = $goMerchantObj->saleUsingVault($data);
                            } elseif ($cart_options['recurring']['payment_type'] == 'after_rental') {
                                $payment = $goMerchantObj->authorizeUsingVault($data);
                            }
                        } else {
                            if (!empty($data['paytype']) && ($data['paytype'] == 'goMerchant_token')) {
                                RentMy::addModel(['Customers']);
                                $vaultKey = RentMy::$Model['Customers']->getGatewayRef($data['email'], 'goMerchant');
                                $data['recurring'] = [
                                    'vaultKey' => $vaultKey,
                                    'vaultId' => $data['account']
                                ];
                                if ($data['capture'])
                                    $payment = $goMerchantObj->saleUsingVault($data);
                                else
                                    $payment = $goMerchantObj->authorizeUsingVault($data);

                            } else {
                                $vault = (new Vault($config['config']))->addCard($data);
                                $data['vaultKey'] = $vault['vaultKey'] ?? '';
                                if ($data['capture']) {
                                    $payment = $goMerchantObj->sale($data);
                                } else {
                                    $payment = $goMerchantObj->authorize($data);
                                }
                            }
                        }

                        break;
                    case "PayPal":
                        $payPalPayment = new PayPalPayment($param, $card);
                        $data['payment_method'] = 'Authorized';
                        $data['action_name'] = 'card-entry';
                        $payment = $payPalPayment->authorize();

                        break;

                    case "Authorize.Net":
                        $config = (new Payment())->getStorePaymentConfig('Authorize.Net');
                        $authorizeNetObj = new AuthorizeNet($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        //$data['capture'] = true;
                        //$data['payment_method'] = 'Captured';
                        $payment = $authorizeNetObj->charge($data);
                        break;
                    case "Empyrean":
                        $config = (new Payment())->getStorePaymentConfig('Empyrean');
                        $paymentObj = new Empyrean($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        $payment = $paymentObj->sale($data);

                        break;
                    case "Square":
                        $config = (new Payment())->getStorePaymentConfig('Square');
                        $paymentObj = new Square($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        $data['cardToken'] = $data['account'];
                        if (isset($data['order_id']))
                            $data['note'] = 'Order: ' . $data['order_id'];

                        $payment = $paymentObj->sale($data);
                        break;
                    case "Midtrans":
                        $config = (new Payment())->getStorePaymentConfig('Midtrans');
                        $paymentObj = new Midtrans($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        $data['cardToken'] = $data['account'];
                        if (isset($data['order_id']))
                            $data['note'] = 'Order: ' . $data['order_id'];

                        $payment = $paymentObj->sale($data);
                        break;
                    case "FreedomPay":
                        $config = (new Payment())->getStorePaymentConfig('FreedomPay');
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        $paymentObj = new FreedomPay($config['config']);
                        $_data = $data;
                        $_data['cvv'] = $_data['cvv2'];
                        $_data['card_no'] = $data['account'];
                        $payment = $paymentObj->sale($_data);
                        break;

                    case "Transafe":
                        $config = (new Payment())->getStorePaymentConfig('Transafe');
                        $paymentObj = new Transafe($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        if (isset($data['order_id']))
                            $data['note'] = 'Order: ' . $data['order_id'];

                        $payment = $paymentObj->sale($data);
                        break;

                    case "ConvenuPay":
                        $type = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'authorize' : 'sale';
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        $data['transaction_type'] = $type;
                        $config = (new Payment())->getStorePaymentConfig('ConvenuPay');
                        $paymentObj = new ConvenuPay($config['config']);
                        $payment = $paymentObj->sale($data);
                        break;
                    case "DeltaPay":
                        $data['payment_token'] = $data['account'];
                        $data['capture'] = !empty(RentMy::$storeConfig['checkout_online_capture']);

                        $config = (new Payment())->getStorePaymentConfig("DeltaPay");
                        $paymentObj = new DeltaPay($config['config'], $data);
                        $payment = $paymentObj->createPayment();
                        break;
                    case 'ValorPay':
                        $data['payment_token'] = $data['account'];
                        $authOrSale = !empty(RentMy::$storeConfig['checkout_online_capture']) ? 'sale' : 'auth';
                        $config = (new Payment())->getStorePaymentConfig("ValorPay");
                        $paymentObj = new ValorPay($config['config']);
                        $payment = $paymentObj->processSale($data['payment_token'], $data['amount'], $authOrSale, $data);
                        if (!empty($payment['success'])) {
                            try {
                                if ((empty($data['paytype']) || ($data['paytype'] != 'ValorPay_token'))) {
                                    $paymentObj->createCustomer(RentMy::$store->id, $data);
                                }
                            }catch (\Exception $e) {

                            }
                        }
                        break;

                }
                if ($payment['success'] != 1 || isset($payment['redirect'])) {

                    return ['success' => false, 'message' => $payment['message']];

                } else {

                    return $payment;
                }

            } else {
                $payment = $this->onlinePayment($data);
                if (empty($payment['respstat']) || $payment['respstat'] != "A") {
                    $msg = empty($payment['resptext']) ? 'Server not found!' : $payment['resptext'];

                    return ['success' => false, 'message' => $msg];
                }
                return ['success' => true, 'data' => $payment];
            }
        }catch (Exception $exception){
            return ['success' => false, 'message' => $exception->getMessage()];
        }

    }

    /**
     * Get Customer info
     */
    public function customerInfo($data)
    {
        $this->add_model(array('Customers'));
        $data['customer_type'] = 1;
        $customer = $this->Customers->create($data);
        return $customer->id;
    }


    private function _makeOrder($data, $cart, $payment_type)
    {
        RentMy::addModel(['Products']);
//        if (!empty($data['email']) && empty($data['customer_id'])) {
//            $data['customer_id'] = $this->customerInfo($data);
//        }
        RentMy::addModel(['OrderRecurring']);
        if (!empty($data['delivery']) && empty($data['pickup'])) {
            if ($cart->shipping_method == 1) {
                $data['pickup'] = $data['delivery']['id'];
            }
        }

        $customValue = $data['custom_values'] ?? [];

        if (!empty($data['special_instructions'])) {
            $customValue[] = ['field_name' => 'special_instructions', 'field_label' => 'Special Instructions', 'field_values' => $data['special_instructions']];
        }
        if (!empty($data['special_requests'])) {
            $customValue[] = ['field_name' => 'special_requests', 'field_label' => 'Special Requests', 'field_values' => $data['special_requests']];
        }
        if (!empty($data['driving_license'])) {
            $customValue[] = ['field_name' => 'driving_license', 'field_label' => 'Driving License', 'field_values' => $data['driving_license']];
        }
        $data['custom_values'] = json_encode($customValue);

        $order = $this->Orders->newEntity();
        $data['coupon_id'] = $cart->coupon_id;
        $data['type'] = ($data['quote']) ? 2 : 1;
        $data['total_deposit'] = $cart->deposit_amount;
        // $data['total_discount_tax'] = $cart->total_discount_tax;
        $data['tax'] = $cart->tax;
        $data['total_discount'] = $cart->total_discount;
        $data['off_amount'] = $cart->off_amount;
        $data['vendor_discount_amount'] = $cart->vendor_discount_amount;
        $data['coupon_amount'] = $cart->coupon_amount;
        $data['sub_total'] = $cart->sub_total;
        $data['product_option_price'] = $cart->product_option_price;
        $data['delivery_tax'] = $cart->delivery_tax;
        $data['total_quantity'] = $cart->total_quantity;
        //  $data['shipping_method'] = $cart->shipping_method;
        $data['delivery_charge'] = $cart->delivery_charge;
        $data['total_price'] = $cart->sub_total + $data['tax'];
        //$data['payment_date'] = date('Y-m-d');
        $data['payment_type'] = $payment_type;
        $data['state_id'] = $data['state'] ?? null;
        $data['country_id'] = strtoupper($data['country']) ?? null;
        $data['event_location'] = $data['order_source'] ?? '';
        $data['rent_start'] = $cart->rent_start;
        $data['rent_end'] = $cart->rent_end;
        $data['tax_id'] = $cart->tax_id;
        $data['location'] = $cart->location;

        $cart_options = $cart['options'];
        if (!is_array($cart['options']))
            $cart_options = json_decode($cart->options, true);
        $cart_options['referrer'] = $data['referrer'];

        if (RentMy::$token->vendor_id){
            $cart_options['vendor_id'] = RentMy::$token->vendor_id;
        }
        $cart_options['membership_checkout'] = !empty($data['membership_checkout']);

        $cart_options['terms_and_condition_accepted'] = isset($data['terms_and_condition_accepted']) ? $data['terms_and_condition_accepted']: true;
        $cart_options['intro_video_seen'] = !empty($data['intro_video_seen']);
        $data['options'] = json_encode($cart_options);
        $order = $this->Orders->patchEntity($order, $data);
        if ($this->Orders->save($order)) {
            // update coupon used limit when coupon is applied
            if (!empty($cart->coupon_id)) {
                RentMy::addModel(['Coupons']);
                $coupon = RentMy::$Model['Coupons']->find()->where(['id' => $cart->coupon_id])->first();
                if (!empty($coupon)) {
                    $coupon->used_limit = $coupon->used_limit + 1;
                    RentMy::$Model['Coupons']->save($coupon);
                }
            }
            RentMy::addModel(['OrderCharge']);
            //check if cart has additional service charges then migrate them for order
            RentMy::$Model['OrderCharge']->updateAll(['order_id' => $order->id, 'cart_id' => null, 'status' => 1], ['cart_id' => $cart->id, 'store_id' => RentMy::$store->id, 'status' => 0]);
            RentMy::$Model['OrderCharge']->changesRecalculateListener($order->id);


            // assign cart to job for client portal
            if (RentMy::$storeConfig['client']['active']) {
                RentMy::addModel(['JobItems']);
                $job = RentMy::$Model['JobItems']->find()
                    ->where(['store_id' => RentMy::$store->id,
                        'client_id' => RentMy::$token->customer_id,
                        'content_type' => 'cart',
                        'content_id' => $cart->id])
                    ->first();
                if (!empty($job)) {
                    $job->content_type = 'order';
                    $job->content_id = $order->id;
                    RentMy::$Model['JobItems']->save($job);
                }
            }
            // update cart tax to order
            RentMy::addModel(['TaxLookup']);
            RentMy::$Model['TaxLookup']->updateAll(['type' => 'order', 'content_id' => $order->id], ['type' => 'cart', 'content_id' => $cart->id]);

            $orderId = $order->id;
            $cart->order_id = $orderId;
            $cart->customer_id = $order->customer_id;
            $this->Carts->save($cart);
            $data['order_id'] = $orderId;


            if (!empty($data['mobile'])) {
                $smsConfig = true;
                if (isset(RentMy::$storeConfig['checkout']['sms'])) {
                    $smsConfig = (RentMy::$storeConfig['checkout']['sms']) ? true : false;
                }

                if ($smsConfig) {
                    $storeName = RentMy::$store->name;
                    $orderUid = $this->randomnum(3) . $order->id;
                    // $domain = 'https://' . $storeName . '.rentmy.co';


//                    $domain = RentMy::storeDomain();
//                    if($order['event_location'] == 'WP Plugin'){
//                        $link = $domain . '/order?id=' . $orderUid;
//                    }else {
//                        $link = $domain . '/order/' . $orderUid;
//                    }
                    $domain = RentMy::storeDomain(true);
                    $link = $domain . '/order/' . $orderUid;
//                    if ($order['event_location'] == 'WP Plugin'){
//                        $domain = RentMy::storeDomain(true);
//                        $link = $domain . '/order?id=' . $orderUid;
//                    }

                    // this code is commented because, we implemented sms sent functionalities dynamically.
//                    $msg = ($order->type == 2) ? 'Here is your quote. ' : 'Thank you for your order.';
//                    $msg .= "Please click the link below to download your new document from " . RentMy::$store->slug . ": " . $link;
//                    $this->loadComponent('Sms');
//                    $this->Sms->sendingSms($data['mobile'], $msg);
                }
            }
            $cartData = $this->CartItems->find('all')->where(['cart_id' => $cart->id])->where(['parent_id' => 0, 'store_id' => RentMy::$store->id])->toArray();
            $totalCartItem = count($cartData);
            $orderProductOptions = [];

            $config = [];
            if (in_array($data['payment_gateway_name'], ['Stripe', 'Transafe']))
                $config = (new Payment())->getStorePaymentConfig($data['payment_gateway_name']);

            $recurringAmount = 0;
            $recurringItems = [];
            foreach ($cartData as $row) {
                $aData = array();
                $aData['product_id'] = $row->product_id;
                $aData['store_id'] = $row->store_id;
                $aData['quantity'] = $row->quantity;
                $aData['quantity_id'] = $row->quantity_id;
                $aData['price'] = $row->price;
                $aData['sales_tax'] = $row->sales_tax;
                $aData['sales_tax_price'] = $row->sales_tax_price;
                $aData['off_amount'] = $row->off_amount;
                $aData['vendor_discount_amount'] = $row->vendor_discount_amount;
                $aData['coupon_amount'] = $row->coupon_amount + $row->automatic_coupon_amount;
                $aData['product_option_price'] = $row->product_option_price;
                $aData['sub_total'] = $row->sub_total;
                $aData['substantive_price'] = $row->substantive_price;
                $aData['total'] = $row->total;
                $aData['product_type'] = $row->product_type;
                $aData['term'] = $row->term;
                $aData['deposit_amount'] = $row->deposit_amount;
                $aData['driving_license_required'] = $row->driving_license_required;
                $aData['rental_type'] = $row->rental_type;
                $aData['rental_duration'] = $row->rental_duration;
                $aData['rent_start'] = $row->rent_start;
                $aData['rent_end'] = $row->rent_end;
                $aData['variant_chain_id'] = $row->variant_chain_id;
                $aData['location'] = $row->location;
                $aData['variant_chain_name'] = $row->variant_chain_name;
                $aData['additional'] = $row->additional;
                $aData['options'] = $row->options;
                $aData['variants_products_id'] = $row->variants_products_id;
                $item = $this->OrderItems->newEntity();
                $aData['order_id'] = $orderId;

                $item = $this->OrderItems->patchEntity($item, $aData);
                $orderItem = $this->OrderItems->save($item);

                //                Recuring payment activate
                $options = json_decode($orderItem->options, true);
                $recurringData = [];
                if ($data['payment_gateway_name'] == 'Stripe' && !empty($options['recurring']) && !empty($data['customerRef'])) {
                    $recurringData = $orderItem->toArray();

                    $recurringData = array_merge($recurringData, $options['recurring']);
                    $recurringData['customer'] = $data['customerRef']['ref'];
                    $recurringData['stripe_payment_method'] = $data['customerRef']['payment_method'];
                    $recurringData['start_date'] = $recurringData['rent_start'];
                    $recurringData['end_date'] = $recurringData['rent_end'];
                    $recurringData['coupon_id'] = $order['coupon_id'];
                    $recurringData['coupon_amount'] = $order['coupon_amount'];

                    $recurringData['amount'] = (float)number_format((float)$recurringData['sub_total'] + (float)$recurringData['coupon_amount'], 2, '.', '');
                    if (empty(RentMy::$storeConfig['tax']['price_with_tax'])){
                        $recurringData['amount'] += (float)$recurringData['sales_tax'];
                        $recurringData['tax_amount'] = (float)$recurringData['sales_tax'];
                    }
                    $recurringData['quantity'] = 1;

                    $stripe = new Stripe('Recurring', $config['config']);
                    $response = $stripe->recurring_process($recurringData);

                    if ($response['subscription_schedule_id']) {
                        $options['recurring']['subscription'] = $response;
                        $options['recurring']['subscription']['price_id'] = $options['price_id'] ?? '';
                        $orderItem->options = json_encode($options);
                        $itemData = [];
                        $itemData['options'] = json_encode($options);
                        $oItem = $this->OrderItems->patchEntity($orderItem, $itemData);
                        $this->OrderItems->save($oItem);
                        RentMy::addModel(['OrderRecurring']);
                        $_oItem = $oItem;
                        $_oItem['rent_start'] = !empty($_oItem['rent_start']) ? $_oItem['rent_start'] : '';
                        RentMy::$Model['OrderRecurring']->addStripeAutomaticRecurring($_oItem, $response);
                    }
                }

                if ($data['payment_gateway_name'] == 'Transafe' && !empty($options['recurring']) && !empty($data['customerRef'])) {
                    $recurringData = $orderItem->toArray();

                    $recurringData = array_merge($recurringData, $options['recurring']);
                    $recurringData['customer'] = $data['customerRef']['ref'];
                    $recurringData['payment_method_token'] = $data['customerRef']['vaultKey'];
                    $recurringData['start_date'] = $recurringData['rent_start'];
                    $recurringData['end_date'] = $recurringData['rent_end'];

                    $amount = $orderItem->sub_total + $orderItem->sales_tax;

                    $amount *= $orderItem['quantity'];
                    $recurringItems[] = [
                        'amount' => $amount,
                        'order_item_id' => $recurringData['id'],
                        'product_id' => $recurringData['product_id'],
                        'term' => $recurringData['term'],
                        'rental_duration' => $recurringData['rental_duration'],
                        'rental_type' => $recurringData['rental_type'],
                    ];

                }

                $parent_id = $item->id;

                $orderProductOptions[] = [
                    'where' => [
                        'content_type' => 'cart',
                        'content_id' => $cart->id,
                        'content_item_id' => $row['id'],
                    ],
                    'data' => [
                        'content_type' => 'order',
                        'content_id' => $orderId,
                        'content_item_id' => $parent_id,
                    ]
                ];

                /** Package product && required addons */
                if (in_array($row->product_type, [1, 2])) {
                    if ($row->product_type == 1) {
                        /*Insert to Products Availabilities for normal products */
                        if ($row->rental_type != 'buy') {
                            $aItem = $this->ProductsAvailabilities->newEntity();
                            $aItem->product_id = $row->product_id;
                            $aItem->order_id = $data['order_id'];
                            $aItem->rental_type = $row->rental_type;
                            $aItem->variants_products_id = $row->variants_products_id;
                            $aItem->quantity_id = $row->quantity_id;
                            $aItem->quantity = $row->quantity;
                            $aItem->location = $row->location;
                            $aItem->order_item_id = $orderItem->id;
                            $aItem->status = ($data['quote']) ? 0 : 1;
                            $aItem->start_date = $row->rent_start;
                            $aItem->end_date = $row->rent_end;
                            $aItem->actual_start_date = $row->rent_start;
                            $aItem->actual_end_date = $this->Orders->_getLeadLagTime('lag', $row->rent_end);
                            if ((RentMy::$storeConfig['rental_day'] == 'calendar') && empty(RentMy::$storeConfig['end_date_calculations'])) { //for hotel booking
                                $aItem->actual_end_date = $this->ProductsAvailabilities->actualEndDateWithoutEndDateCalculation($row->rent_start, $row->rent_end, true);
                            }
                            if (!empty($data['membership_checkout']) || !empty($options['recurring']['subscription'])) {
                                $aItem->actual_end_date = Time::now()->addYears(99)->format('Y-m-d H:i:s');
                            }

                            $aItem->before_time = $this->Orders->_getLeadTime();
                            $aItem->after_time = $this->Orders->_getLagTime();

                            $this->ProductsAvailabilities->save($aItem);

                            if (in_array($data['payment_gateway_name'], ['Stripe', 'Transafe'])) {
                                RentMy::addModel(['OrderItems']);
                                RentMy::$Model['OrderItems']->updateEnduringQuantity($orderItem->id, $orderId, $recurringData);
                            }
                        }
                    }

                    $cartItems = $this->CartItems->find('children', ['for' => $row->id])
                        ->find('threaded')
                        ->where(['store_id' => RentMy::$store->id])
                        ->toArray();
                    foreach ($cartItems as $cartItem) {
                        $item = $this->OrderItems->newEntity();
                        $item->product_id = $cartItem->product_id;
                        $item->store_id = $cartItem->store_id;
                        $item->location = $cartItem->location;
                        $item->variants_products_id = $cartItem->variants_products_id;
                        $item->quantity_id = $cartItem->quantity_id;
                        $item->quantity = $cartItem->quantity;
                        $item->parent_id = $parent_id;
                        $item->order_id = $data['order_id'];
                        $item->product_type = $cartItem->product_type;
                        $item->rental_type = $cartItem->rental_type;
                        $item->rent_start = $cartItem->rent_start;
                        $item->rent_end = $cartItem->rent_end;
                        $item->price = $cartItem->price;
                        $item->variant_chain_id = $cartItem->variant_chain_id;
                        $item->variant_chain_name = $cartItem->variant_chain_name;

                        $this->OrderItems->save($item);

                        /*Insert to Products Availabilities for package items addons*/
                        if ($cartItem->rental_type != 'buy') {
                            $aItem = $this->ProductsAvailabilities->newEntity();
                            $aItem->product_id = $cartItem->product_id;
                            $aItem->order_id = $data['order_id'];
                            $aItem->rental_type = $cartItem->rental_type;
                            $aItem->variants_products_id = $cartItem->variants_products_id;
                            $aItem->quantity_id = $cartItem->quantity_id;
                            $aItem->quantity = $cartItem->quantity;//* $row->quantity;
                            $aItem->location = $cartItem->location;
                            $aItem->order_item_id = $item->id;//$orderItem->id;
                            $aItem->status = ($data['quote']) ? 0 : 1;
                            $aItem->start_date = $row->rent_start;
                            $aItem->end_date = $row->rent_end;
                            $aItem->actual_start_date = $row->rent_start;
                            $aItem->actual_end_date = $this->Orders->_getLeadLagTime('lag', $row->rent_end);
                            if ((RentMy::$storeConfig['rental_day'] == 'calendar') && empty(RentMy::$storeConfig['end_date_calculations'])) { // for hotel booking
                                $aItem->actual_end_date = $this->ProductsAvailabilities->actualEndDateWithoutEndDateCalculation($row->rent_start, $row->rent_end, true);
                            }
                            if (!empty($data['membership_checkout']) || !empty($options['recurring']['subscription'])) {
                                $aItem->actual_end_date = Time::now()->addYears(99)->format('Y-m-d H:i:s');
                            }

                            $aItem->before_time = $this->Orders->_getLeadTime();
                            $aItem->after_time = $this->Orders->_getLagTime();

                            $this->ProductsAvailabilities->save($aItem);

                            if (in_array($data['payment_gateway_name'], ['Stripe', 'Transafe'])) {
                                RentMy::addModel(['OrderItems']);
                                RentMy::$Model['OrderItems']->updateEnduringQuantity($orderItem->id, $orderId, $recurringData);
                            }
                        }
                    }

                } else {

                }
            }

            if ($data['payment_gateway_name'] == 'Transafe' && !empty($data['customerRef']) && !empty($recurringItems)) {
                $recurringData = $order->toArray();
                $recurringData['customer'] = $data['customerRef']['ref'];
                $recurringData['token'] = $recurringData['payment_method_token'] = $data['customerRef']['vaultKey'];
                $recurringData['start_date'] = $recurringData['rent_start'];
                $recurringData['end_date'] = $recurringData['rent_end'];
                $recurringData['order_id'] = $order->id;
                $recurringData['rental_type'] = $recurringItems[0]['rental_type'];

                $recurringData['amount'] = array_sum(array_column($recurringItems, 'amount'));

                $transafe = new Transafe($config['config']);
                $recurringData['gateway_id'] = $config['data']['id'];
                $response = $transafe->soloRecurringProcess($recurringData);
                if (!empty($response)) {
                    RentMy::addModel(['OrderRecurring']);
                    $enduringData = [
                        'order_id' => $order->id,
                        'duration' => $recurringItems[0]['rental_duration'],
                        'duration_type' => $recurringItems[0]['rental_type'],
                        'term' => $recurringItems[0]['term'],
                        'amount' => $recurringData['amount'],
                        'rent_start' => $order->rent_start,
                        'payment_gateway_name' => $data['payment_gateway_name'],
                    ];
                    $enduringData['options']['recurring']['subscription'] = $response;
                    $enduringData['options']['order_items'] = $recurringItems;

                    RentMy::$Model['OrderRecurring']->addAutomaticRecurring($enduringData);
                }

            }

            /* Delivery details start */
            $this->_addDeliveryDetails($data);
            /* Delivery details end */

            RentMy::addModel(['OrderProductOptions']);
            RentMy::$Model['OrderProductOptions']->migrateCartToOrder($orderProductOptions);

            /** Item log */

            if (isset($data['item_log']) && !empty($data['item_log'])) {
                $item_log = $data['item_log'];
                for ($i = 0; $i < count($item_log); $i++) {
                    $this->paymentLog($orderId, 'Products', $item_log[$i]['product_id'], null, $item_log[$i]['note'], 'discount', null, json_encode($item_log[$i]));
                }
            }

            $this->Orders->updatePurchaseType($orderId);
            /** *** Order address ****** */
            $this->loadModel('OrderAddresses');
            if (!empty($data['delivery_multi_store']))
                $data['multi_store_delivery'] = json_encode($data['delivery_multi_store']);


            $orderAddress = $this->OrderAddresses->newEntity();
            $orderAddress = $this->OrderAddresses->patchEntity($orderAddress, $data);
            $this->OrderAddresses->save($orderAddress);

            /** Customer Address */
            $this->activateCustomer($data);
            try {
                if (!empty($data['signature'])) {
                    $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
                    file_put_contents($directoryPath . 'signature_' . $orderId . '.png', base64_decode(explode(',', $data['signature'])[1]));

                    $s3 = new S3();
                    $s3->upload([
                        ['path' => $directoryPath . 'signature_' . $orderId . '.png', 'dir' => 'orders/' . 'signature_' . $orderId . '.png'],
                    ]);
                    RentMy::deleteFile($directoryPath, 'signature_' . $orderId . '.png');
                }

                $tosSignature = [];
                if (!empty($data['tos_signature'])){
                    foreach ($data['tos_signature'] as $signature){
                        $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
                        $name = $orderId . '_customer_initial_' .time() . RentMy::randomNum(5) . '.png';
                        file_put_contents($directoryPath . $name, base64_decode(explode(',', $signature)[1]));

                        $s3 = new S3();
                        $s3->upload([
                            ['path' => $directoryPath . $name, 'dir' => 'orders/' . $name],
                        ]);
                        $tosSignature[] = 'orders/' . $name;
                        RentMy::deleteFile($directoryPath, $name);
                    }
                }
            } catch (Exception $e) {

            }

            /** ***** ******** ***** */
            // saving tos signature in options
            if (!empty($tosSignature)){
                RentMy::addModel(['Orders']);
                $order = RentMy::$Model['Orders']->find()->where(['id' => $orderId])->first();
                $options = !empty($order->options) ? json_decode($order->options, true) : [];
                $options['tos_signature'] = $tosSignature;
                $order->options = json_encode($options);
                RentMy::$Model['Orders']->save($order);
            }

            if (!empty($data['custom_values'])) {
                $customValue = json_decode($data['custom_values'], true);
                RentMy::addModel(['OrderAdditionalInfo']);
                RentMy::$Model['OrderAdditionalInfo']->add($customValue, $orderId, 'order');
            }

        }

        return $order;
    }

    /** Send customer activation email when is_customer_account = true */
    function activateCustomer($data)
    {
        if ($data['is_customer_account'] && !empty($data['customer_id'])) {
            RentMy::addModel(['Customers']);
            $customer = RentMy::$Model['Customers']->find()->where(['id' => $data['customer_id']])->first();
            $customer->activation_key = RentMy::randomNum(8) . $data['customer_id'];
            $customer->activation_expire = Time::parse('now')->addDays(7);
            if (RentMy::$Model['Customers']->save($customer)) {
                $host = RentMy::storeDomain();
//                if (!empty(RentMy::$store->domain)) {
//                    $host = 'https://' . RentMy::$store->domain;
//                } else {
//                    $host = Configure::read('STORE_HOST')[0] . RentMy::$store->name . Configure::read('STORE_HOST')[1];
//                }

                $link = $host . '/customer/activation?activation_key=' . $customer->activation_key;
                RentMy::$store->store_logo = empty(RentMy::$store->logo) ? Configure::read('CLIENT_API') . '/img/upload/store-logo/' . RentMy::$store->id . '/' . RentMy::$store->logo :
                    Configure::read('CLIENT_HOST') . '/assets/img/home/<USER>';
                $options = array('template' => 'customer_activation', 'to' => $data['email'], 'activation' => $link,
                    'subject' => 'Welcome to ' . RentMy::$store->slug,
                    'store' => json_decode(json_encode(RentMy::$store), true)
                );
//                RentMy::addModel(['Templates']);
//                $template = RentMy::$Model['Templates']->find()
//                    ->where(['store_id' => RentMy::$store->id])
//                    ->where(['type' => 2])
//                    ->where(['content_type' => 2])
//                    ->first();
//                if ($template) {
//                    $options['subject'] = $template->title;
//                    $options['email_body'] = $template->content_body;
//                }
//                RentMy::Email($options);
                $option['options'] = RentMy::$token->location;
                RentMy::addNotificationQueue('customer_register', RentMy::$store->id, $options);
            }
        }
        //if (!$data['is_customer_account'] && empty($data['customer_id'])) {
        $this->loadModel('CustomerAddresses');
        $this->CustomerAddresses->_customerAddress($data);
        //}
    }

    private function _payment($data = array(), $options = null, $responseText = null)
    {
        $this->add_model(array('Payments', 'Carts'));
        if (!empty($data)) {
            $pData = array();
            $pData['status'] = $data['payment_method'] == 'Unpaid' ? 1 : in_array($data['payment_method'], ['Authorized']) ? 2 : 1;
            if ($data['status'] != 3) {
                // $pData['status'] = 0;
            }
            $pData['order_id'] = empty($data['order_id']) == true ? 0 : $data['order_id'];
            $pData['store_id'] = empty($data['store_id']) == true ? 0 : $data['store_id'];
            $pData['type'] = empty($data['type']) == true ? 0 : $data['type'];
            $pData['payment_amount'] = empty($data['amount']) == true ? 0 : $data['amount'];
            $pData['payment_method'] = empty($data['payment_method']) == true ? null : $data['payment_method'];
            $pData['is_captured'] = (!empty($data['payment_method']) && ($data['payment_method'] == 'Captured')) ? 1 : 0;
            $pData['payment_gateway'] = empty($data['payment_gateway_name']) == true ? null : $data['payment_gateway_name'];
            $pData['gateway_id'] = $data['gateway_id'] ?? '';
            $pData['transaction_id'] = empty($data['transaction_id']) == true ? 0 : $data['transaction_id'];
            $pData['note'] = empty($data['note']) == true ? 0 : $data['note'];
            $pData['response_text'] = empty($data['response_text']) == true ? 0 : $data['response_text'];
            $pData['terminal'] = empty($data['terminal']) == true ? '' : $data['terminal'];
            $pData['terminal_id'] = empty($data['terminal_id']) == true ? '' : $data['terminal_id'];
            $pData['content_id'] = empty($data['content_id']) == true ? '' : $data['content_id'];

            // this condition is for stripe recurring. real_amount 0 mean, there is no charges except recurring amount. for this situation we entry a payment table data, for payment verification
            if (isset($data['real_amount']) && $data['real_amount'] <= 0) {
                $pData['status'] = 0;
            }
            $payment = $this->Payments->newEntity();
            $payment = $this->Payments->patchEntity($payment, $pData);
            if ($this->Payments->save($payment)) {
                if ($payment['status'] != 0){
                    RentMy::addNotificationQueue('payment_received', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'PaymentReceived', 'mobile' => $data['mobile'] ?? null, 'order_id' => $payment['order_id'], 'content_id' => $payment['order_id'], 'source' => RentMy::$token->source, 'payment_count' => 1]);
                }

                // payment log
                $action_name = !empty($data['action_name']) ? $data['action_name'] : NULL;
                $api_request = json_encode(array('amount' => $data['amount'], 'order_id' => $data['order_id']));
                $response_text = empty($responseText) ? json_encode($payment) : $responseText;

                $this->paymentLog($data['order_id'], 'Payments', $payment->id, 1, $options, $action_name, $response_text, $api_request);

                $this->_updateQuantity($data['cart_id']);
                // recurring payments
                $cart_options = json_decode($data['cart']['options'], true); // recurring options.
                if (!empty($cart_options['recurring']['duration_type'])) {
                    $rental = ['before_rental', 'after_rental'];
                    if ((RentMy::$storeConfig['arb']['active']) && (in_array(RentMy::$storeConfig['arb']['store_active'], $rental))) {
                        RentMy::addModel(['OrderRecurring']);
                        $data['payment_id'] = $payment->id;
//                        RentMy::$Model['OrderRecurring']->addFromCart($data['order_id'], $data);
                        if (($data['payment_gateway_name'] == 'Stripe')) {
                            RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'Recurring', 'content_id' => $payment->id, 'user_id' => RentMy::$token->cutomer_id, 'user_type' => 'customer', 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]);

                            // RentMy::addQueue('Order', ['type' => 'Recurring', 'content_id' => $payment->id, 'user_id' => RentMy::$token->cutomer_id, 'user_type' => 'customer', 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]); // add recurring payment
                        }
                    }
                }
                RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$token->cutomer_id, 'user_type' => 'customer', 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]);
                // RentMy::addQueue('Order', ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$token->cutomer_id, 'user_type' => 'customer', 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]); // add order log queue


                //now clearing the cart
                $this->Carts->clearCart($data['cart_id']);
                $orderData = $this->Orders->get($data['order_id']);
                $this->Payments->updateOrderPaymentStatus($data['order_id']); // set order status

                if (!empty($data['email'])) {
                    RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);

                    //RentMy::addQueue('Order', ['type' => 'OrderEmail', 'order_id' =>$data['order_id'], 'content_id' =>$data['order_id'],  'source' => RentMy::$token->source]);
                }
                $payment = array('success' => true, 'order_id' => $data['order_id']);
                $order = array('success' => true, 'data' => ['uid' => $orderData->uuid]);


                $data = array(
                    'availability' => array('success' => true),
                    'order' => $order,
                    'payment' => $payment,
                );

                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['online']]);

                $this->apiResponse['data'] = $data;
            } else {
                // payment log
                $response_text = empty($responseText) ? json_encode($payment) : $responseText;
                $this->paymentLog($data['order_id'], 'Payments', null, 2, $options, null, $response_text);


                $payment = array('success' => false, 'message' => 'Payment submission problem.');
                $order = array('success' => true, 'data' => []);
                $data = array(
                    'availability' => array('success' => true),
                    'order' => $order,
                    'payment' => $payment,
                );

                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['online']]);

                $this->apiResponse['data'] = $data;
            }
        } else {
            $this->apiResponse['message'] = 'Method Not Allowed';
        }

    }

    private function _updateQuantity($cartId)
    {
        $this->add_model(array('VariantsProducts', 'Quantities'));
        $cartItem = $this->CartItems->find('all')->where(['cart_id' => $cartId])->toArray();
        $pCount = array();
        foreach ($cartItem as $item) {
            if ($item->rental_type == 'buy' && !empty($item->quantity_id)) {
                $pCount[$item->quantity_id] = 0;
            }
        }
        foreach ($cartItem as $item) {
            if ($item->rental_type == 'buy' && !empty($item->quantity_id)) {
                $pCount[$item->quantity_id] = empty($pCount[$item->quantity_id]) == true ? $item->quantity : ($pCount[$item->quantity_id] + $item->quantity);
            }
        }
        if (!empty($pCount)) {
            foreach ($pCount as $k => $v) {
                $quantity = $this->Quantities->get($k);
                if (!empty($quantity)) {
                    $newQuantity = $quantity->quantity - $v;
                    $quantity['quantity'] = !is_numeric($newQuantity) ? 0 : $newQuantity;
                    $this->Quantities->save($quantity);
                }
            }

        }
        return;
    }

    /**
     * Saving delivery details
     * @param $data
     */
    private function _addDeliveryDetails($data)
    {
        if (!empty($data['delivery']['boxes']))
            $boxes = $data['delivery']['boxes'];

//        if (empty($data['delivery']) && !empty($data['pickup'])) {
//            $this->loadModel('Locations');
//            $location = $this->Locations->get($data['pickup']);
//            $data['delivery'] = array(
//                'charge' => 0.00,
//                'tax' => 0.00,
//               // 'charge_by' => 'in-store',
////                'location' => [
////                    'id' => $location->id,
////                    'pickup_from' => $location->name,
////                    'delivery_to' => $location->name,
////                ]
//            );
//        } else {
        $shipping_response = [];
        //  RentMy::dbg($data);
        if (empty($data['shipping_method']) && !empty($data['pickup'])) {
            $data['shipping_method'] = 1;
        }
        if ($data['shipping_method'] == 1) { // instore pickup
            $data['delivery'] = array(
                'charge' => 0.00,
                'tax' => 0.00,
                'pickup' => $data['delivery']['id']
                // 'charge_by' => 'in-store',
//                'location' => [
//                    'id' => $location->id,
//                    'pickup_from' => $location->name,
//                    'delivery_to' => $location->name,
//                ]
            );
        } elseif ($data['shipping_method'] == 2) { // delivery
            //$data['delivery']['location']['delivery_to'] = $this->_getShippingAddress($data);
        } elseif ($data['shipping_method'] == 4) { // shipping
            $response = (new Shipping())->createShipmentFromOrder($data);
            if (!empty($response['shipments'][0])) {
                $data['delivery'] = [
                    "method" => 4,
                    'charge_by' => 'shipping',
                    "shipment_id" => $response['shipments'][0]['shipment_id'],
                    "service_code" => $response['shipments'][0]['service_code'],
                    "rate_id" => $data['delivery']['rate_id'],
                    "service_name" => $data['delivery']['service_name'],
                    "ship_date" => $data['delivery']['ship_date'],
                    "carrier_id" => $data['delivery']['carrier_id'],
                    'delivery_date' => $data['delivery']['delivery_date'],
                    'delivery_days' => $data['delivery']['delivery_days'],
                    'charge' => $data['delivery']['charge'],
                    'handling_amount' => $data['delivery']['handling_amount'],
                    'tax' => $data['delivery']['tax'],
                    'two_way' => $data['delivery']['two_way']

                ];
                $shipping_response = $response['shipments'][0];
                $carrier_id = $response['shipments'][0]['carrier_id'];
            }
        } elseif ($data['shipping_method'] == 5) { // Free shipping
            $data['delivery'] = [];
            $carrier_id = '';
            $data['delivery']['charge'] = 0;
            $data['delivery']['tax'] = 0;
            //$data['delivery']['two_way'] = $data['delivery']['two_way'];
        } elseif ($data['shipping_method'] == 6) { // standard shipping
            $carrier_id = '';
            $data['delivery'] = [
                "method" => 6,
                'charge_by' => 'Standard shipping',
                "service_name" => $data['delivery']['carrier_code'],
                'charge' => $data['delivery']['charge'],
                'handling_amount' => $data['delivery']['handling_amount'],
                'tax' => $data['delivery']['tax'],
                'two_way' => $data['delivery']['two_way']
            ];
        } elseif ($data['shipping_method'] == 7) { // flat shipping
            $carrier_id = '';
            $data['delivery'] = [
                "method" => 7,
                'charge_by' => 'Flat shipping',
                "service_name" => $data['delivery']['carrier_code'],
                'charge' => $data['delivery']['charge'],
                'handling_amount' => $data['delivery']['handling_amount'],
                'tax' => $data['delivery']['tax'],
                'two_way' => $data['delivery']['two_way']
            ];
        }

        if (isset($boxes))
            $data['delivery']['boxes'] = $boxes;

        // }
        $this->loadModel('DeliveryDetails');
        $deliveryDetails = $this->DeliveryDetails->newEntity();
        $deliveryData = [
            'order_id' => $data['order_id'],
            'config' => json_encode($data['delivery']),
            'shipment_response' => json_encode($shipping_response),
            'method' => $data['shipping_method'],
            'carrier' => $carrier_id,
            'charge_amount' => ($data['delivery']['charge'] + $data['delivery']['tax']),
            'store_id' => RentMy::$store->id
        ];

        if (!empty($data['cart']['rent_start']))
            $deliveryData['delivery_date'] = RentMy::toUTC($data['cart']['rent_start']);

        $deliveryDetails = $this->DeliveryDetails->patchEntity($deliveryDetails, $deliveryData);
        $this->DeliveryDetails->save($deliveryDetails);

        if ($data['shipping_method'] == 4) {
            $params['partial_shipment'] = [];
            $params['shipment_id'] = $deliveryDetails['id'];
            $params['order_id'] = $deliveryDetails['order_id'];
            RentMy::addModel(['OrderItems']);
            $items = RentMy::$Model['OrderItems']->find()->where(['order_id' => $data['order_id']])->toArray();
            if (!empty($items)) {
                foreach ($items as $item) {
                    if ($item->product_type == 2) continue; //skip package

                    $params['partial_shipment'][] = [
                        'order_item_id' => $item->id,
                        'package' => $data['delivery']['package_type'] ?? 'custom_package',
                        'product_id' => $item->product_id,
                        'variants_products_id' => $item->variants_products_id,
                        'quantity' => $item->quantity,
                    ];
                }
            }

            if (!empty($params['partial_shipment'])) {
                $get_weight = (new Shipping())->calculateItemsWeight(['type' => 'order', 'order_id' => $data['order_id']], $params['partial_shipment'] ?? []);
                if (!empty($get_weight['products'])) {
                    $productWeights = new Collection($get_weight['products']);
                }
                RentMy::addModel(['OrderPartialShipments']);
                foreach ($params['partial_shipment'] as $partialShipment) {
                    if (!empty($productWeights))
                        $weights = $productWeights->match(['id' => $partialShipment['product_id'], 'variants_products_id' => $partialShipment['variants_products_id']])->first();

                    $orderPartialShipment = RentMy::$Model['OrderPartialShipments']->find()->where([
                        'shipment_id' => $params['shipment_id'],
                        'order_id' => $params['order_id'],
                        //'order_item_id' => $params['order_item_id'],
                        'product_id' => $partialShipment['product_id'],
                        'variants_products_id' => $partialShipment['variants_products_id'],
                        'package_code' => $partialShipment['package'],

                    ])->first();

                    if (!empty($orderPartialShipment)) {
                        $orderPartialShipment->quantity += $partialShipment['quantity'];
                    } else {
                        $orderPartialShipment = RentMy::$Model['OrderPartialShipments']->patchEntity(RentMy::$Model['OrderPartialShipments']->newEntity(), [
                            'shipment_id' => $params['shipment_id'],
                            'order_id' => $params['order_id'],
                            'order_item_id' => $partialShipment['order_item_id'],
                            'product_id' => $partialShipment['product_id'],
                            'variants_products_id' => $partialShipment['variants_products_id'],
                            'package_code' => $partialShipment['package'],
                            'quantity' => $partialShipment['quantity'],
                            'weight' => $weights['weight'],
                            'unit' => $weights['unit'],
                        ]);
                    }
                    RentMy::$Model['OrderPartialShipments']->save($orderPartialShipment);
                }
            }
        }

        return;
    }

    private function _getShippingAddress($dAddress)
    {
        $delivery_to = array();
        if (!empty($dAddress['shipping_address1'])) {
            $delivery_to[] = $dAddress['shipping_address1'];
        }
        if (!empty($dAddress['shipping_zipcode'])) {
            $delivery_to[] = $dAddress['shipping_zipcode'];
        }
        if (!empty($dAddress['shipping_city'])) {
            $delivery_to[] = $dAddress['shipping_city'];
        }
        $delivery_to = implode(', ', $delivery_to);
        return $delivery_to;
    }


    private function onlinePayment($data)
    {
        $this->loadComponent('CardConnect');
        if (
            (isset($data['account']) && !empty($data['account'])) &&
            (isset($data['expiry']) && !empty($data['expiry'])) &&
            (isset($data['cvv2']) && !empty($data['cvv2'])) &&
            (isset($data['amount']) && !empty($data['amount'])) &&
            (isset($data['currency']) && !empty($data['currency']))
        ) {
            $cardData = array(
                'account' => $data['account'],
                'expiry' => $data['expiry'],
                'cvv2' => $data['cvv2'],
                'amount' => $data['amount'] * 100,
                'currency' => $data['currency']
            );
            $fName = empty($data['first_name']) ? '' : $data['first_name'];
            $lName = empty($data['last_name']) ? '' : $data['last_name'];
            $customerData = array(
                'name' => $fName . ' ' . $lName,
                'address' => empty($data['address_line1']) ? '' : $data['address_line1'],
                'city' => empty($data['city']) ? '' : $data['city'],
                'region' => empty($data['state_id']) ? '' : $data['state_id'],
                'country' => !empty($data['country_id']) ? $data['country_id'] : NULL
            );
            $response = $this->CardConnect->paymentRequest($cardData, $customerData);
            /*only testing purposes*/
            // return json_decode('{"gsacard":"N","amount":"0.01","resptext":"Approval","acctid":"1","cvvresp":"M","respcode":"000","avsresp":"N","defaultacct":"Y","merchid":"************","token":"****************","authcode":"260305","respproc":"RPCT","profileid":"15720676655846214181","retref":"************","respstat":"A","account":"55XXXXXXXXXX0387"}',true);
            return $response;
        }

        return false;
    }
}
