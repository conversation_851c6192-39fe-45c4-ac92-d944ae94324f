<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Exception\MissingParamsException;
use App\Lib\ProductContainer;
use App\Lib\RentMy\PosCustomerView;
use App\Lib\RentMy\Price;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\Event\Event;
use Cake\Http\Exception\UnauthorizedException;
use Cake\I18n\Date;
use Cake\I18n\FrozenTime;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Exception;

class CartsController extends AppController
{
    private $storeId, $userId, $productContainer, $cartToken;

    public function initialize()
    {
        parent::initialize();
        $this->productContainer = new ProductContainer();
    }

    public function beforeFilter(Event $event)
    {

        parent::beforeFilter($event);
        $this->storeId = $this->parseToken->store_id;
        $this->userId = $this->parseToken->id ?? 0;
        $this->cartToken = '';
    }

    /**
     * add delivery/shipping method to a order
     * @param cart token
     * @param shipping method
     * @param shipping cost
     * @param shipping tax
     */
    public function Delivery()
    {
        RentMy::addModel(['Coupons']);
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (!empty($data['shipping_method']) && !empty($data['token'])) {
                $this->add_model(array('Carts', 'CartItems'));
                $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                if (empty($cart)) {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['error'] = 'Not found!';
                    return;
                }
                $cart->shipping_method = $data['shipping_method'];
                $cart->delivery_charge = $data['shipping_cost'];
                $cart = RentMy::$Model['Coupons']->applyDeliveryDiscount($cart);
                $cart->delivery_tax = !empty($data['tax']) ? $data['tax'] : 0;

//                if ($data['shipping_cost'] == 0){
//                    $cart_options = json_decode($cart->options, true);
//                    if (isset($cart_options['multi_store_delivery'])){
//                        unset($cart_options['multi_store_delivery']);
//                        $cart->options = json_encode($cart_options);
//                    }
//                }

                if ($data['shipping_method'] == 1) { // remove tax when address is delivery / shipping but local pickup is selected
                    if (RentMy::$storeConfig['tax']['address'] != 'store') {
                        $cart['tax'] = 0;
                        RentMy::addModel(['TaxLookup']);
                        RentMy::$Model['TaxLookup']->removeExistingTax('cart', $cart->id);
                    }
                }
                if ($this->Carts->save($cart)) {
                    if ($data['shipping_method'] != 1) {
                        $data['address']['shipping_method'] = $data['shipping_method'];
                        $this->apiResponse['data'] = $this->Carts->getCartDetails($cart->id, $data['address']);
                    } else {
                        $this->apiResponse['data'] = $this->Carts->getCartDetails($cart->id);
                    }
                } else {
                    $this->apiResponse['error'] = Configure::read('message.save');
                }
            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
            }
        } else {
            $this->apiResponse['error'] = 'Method not allowed.';
        }
    }

    /**
     * Deprecated function (Not used now)
     *
     * add Shipping Method
     */
    public function addShipping()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (!empty($data['shipping_method']) && !empty($data['token'])) {
                $this->add_model(array('Carts'));
                $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                if (empty($cart)) {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['error'] = 'Not found!';
                    return;
                }

                foreach (Configure::read('shippingMethod') as $item) {
                    if (Hash::get($item, 'id') == $data['shipping_method']) {
                        $shippingCost = $item;
                    }
                }
                $cart->shipping_method = $data['shipping_method'];
                $cart->delivery_charge = $shippingCost['shipping_cost'];
                $this->Carts->save($cart);
                $this->apiResponse['data'] = $this->Carts->getCartDetails($cart->id);
            } else {
                $this->apiResponse['error'] = 'Method not allowed.';
            }
        } else {
            $this->apiResponse['error'] = 'Method not allowed.';
        }
    }

    /**
     * Get package price &
     * Availability checking for products of a package
     *
     * @param package_id, variants_products_id, location
     * @return price, available quantity
     */
    public function getPackagePrice()
    {
        if ($this->request->is('post')) {
            $this->add_model(array('ProductPackages', 'Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'CartItems', 'ProductPrices', 'VariantsProducts'));
            $data = $this->request->getData();
            $requiredParam = ['package_id', 'variants_products_id', 'location'];
            if ($this->array_keys_exist($data, $requiredParam)) {

                $storeConfig = RentMy::$storeConfig;
                $isForceRentalAdjustment = isset($storeConfig['checkout']['force_rent_time'])?$storeConfig['checkout']['force_rent_time']:true;
                $isAllowRentalReturn= isset($storeConfig['checkout']['show_separate_date_picker'])?$storeConfig['checkout']['show_separate_date_picker']:false;
                // if ((isset($data['is_apply']) && $data['is_apply']) && $isForceRentalAdjustment && (!empty(RentMy::$storeConfig['show_start_time']))){

                if ($data['rental_type'] == 'rent') {
                    if ($isForceRentalAdjustment && !$isAllowRentalReturn) {
                        RentMy::addModel(['Holidays']);
                        $options = [
                            'current_time' => $data['current_time'] ?? null,
                        ];
                        $holiday = RentMy::$Model['Holidays']->checkStoreCloseTime($data['rent_start'], $data['rent_end'], $data['price_id'], $options);
                        $this->apiResponse['start_date'] = $data['rent_start'] = $holiday['start_date'];
                        $this->apiResponse['end_date'] = $data['rent_end'] = $holiday['end_date'];
                        $this->apiResponse['message'] = $holiday['message'];
                    }


                    if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
                        $this->apiResponse['error'] = 'Invalid Rental Date';
                        return;
                    }

                    $product = $this->Products->get($data['package_id']);
                    if (!empty($product->options))
                        $product->options = json_decode($product->options, true);

                    $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end'], $product);
                    $data['rent_start'] = $rentalDates['start_date'];
                    $data['rent_end'] = $rentalDates['end_date'];
                }

                $productAvailable = array();

//                foreach ($data['products'] as $item) {
//                    $quantity = $this->Quantities->find()
//                        ->where(['variants_products_id' => $item['variants_products_id']])
//                        ->where(['location' => $data['location']])
//                        ->first();
//                    if (empty($quantity)) {
//                        $this->apiResponse['error'] = 'Out of Stock!';
//                        return;
//                    }
//
//                    $product = $this->Products->get($item['product_id']);
//                    $pAvailable = $this->Products->_quantityAvailable($product, $quantity, $data['rent_start']);
//                    $productAvailable[] = ['product_id' => $item['product_id'], 'variants_products_id' => $item['variants_products_id'], 'available' => $pAvailable];
//                    if ($pAvailable < $item['quantity']) {
//                        $this->apiResponse['error'] = 'Not available!';
//                        return;
//                    }
//                }

                $productPackages = $this->ProductPackages->find()
                    ->where(['ProductPackages.store_id' => $this->parseToken->store_id])
                    ->where(['ProductPackages.package_id' => $data['package_id']])
                    ->toArray();

                // dynamic bundle builder
                if (!empty($data['package_dynamic_bundle_builder'])) {
                    // Extract 'products' array from request data
                    $productItems = isset($data['products']) ? $data['products'] : [];

                    // Build lookup: variants_products_id => quantity
                    $requestItems = [];
                    foreach ($productItems as $item) {
                        if (isset($item['product_id'], $item['quantity'])) {
                            $requestItems[$item['product_id']] = $item['quantity'];
                        }
                    }

                    // Filter and update only matching packages
                    $filteredPackages = [];
                    foreach ($productPackages as $package) {

                        if (isset($requestItems[$package->product_id])) {
                            $package->quantity = $requestItems[$package->product_id];
                            $filteredPackages[] = $package;
                        }
                    }

                    // Replace original array with filtered one
                    $productPackages = $filteredPackages;
                }

                if (!empty($data['products'])){
                    $productIds = array_column($data['products'], 'product_id');
                    $variants = array_column($data['products'], 'variants_products_id');
                }else{
                    $productIds = Hash::extract($productPackages, '{n}.product_id');
                    $variants = Hash::extract($data['products'], '{n}.variants_products_id');
                }


                $products = $this->ProductPackages->productDetails($productIds, $productPackages, $data['location'], null, $data['rent_start']);
                // $packageTerm = $this->ProductPackages->getAvailablePackage($productPackages, $variants, $data['location'], $data['rent_start']);

                $rental_price = 0;
                if (!empty($data['rent_start']) && !(empty($data['rent_end'])) && ($data['rental_type']=='rent')) {
                    $rental_price = TableRegistry::getTableLocator()->get('ProductPrices')->getRentalPriceByDates($data['package_id'], $data['variants_products_id'], $rentalDates['start_date'], $rentalDates['end_date'],[], 'cart', $data['custom_fields'], $data);
                    //   $packageTerm = $this->ProductPackages->getAvailablePackage($productPackages, $variants, $data['location'], $rentalDates['start_date']);
                    $_packageTerms = $this->ProductsAvailabilities->getPackageAvailability($productPackages, $variants, $data['location'], RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i'), RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i'));
                } else {
                    $responseOption = RentMy::$storeConfig;
                    $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];
                    $price = $this->_getPrice($data, $optionPrice);
                    $rental_price = $price['price'];
                    $rent_start = Time::now()->format('Y-m-d H:i');
                    $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
                    $_packageTerms = $this->ProductsAvailabilities->getPackageAvailability($productPackages, $variants, $data['location'], RentMy::toUTC($rent_start, 'Y-m-d H:i'), RentMy::toUTC($rent_end, 'Y-m-d H:i'));
                    //$packageTerm = RentMy::$Model['ProductsAvailabilities']->getPackageAvailability($productPackages, $variantIds, $location, $rent_start, $rent_end);
                }

                $packageTerm = $_packageTerms[0]['term'];
                if ($packageTerm < 1){
                    $messages = [];
                    foreach ($_packageTerms as $_packageTerm) {
                        if ($_packageTerm['term'] < 1)
                            if ($_packageTerm['variant_chain'] != 'Unassigned: Unassigned') {
                                $messages[] = $_packageTerm['product_name'] . ' (' . $_packageTerm['variant_chain'] . ')';
                            }else {
                                $messages[] = $_packageTerm['product_name'];
                            }
                    }
                    $this->apiResponse['errors'] = implode(', ', $messages).' not available';
                }

                $this->apiResponse['data'] = $rental_price;
                $this->apiResponse['products'] = $products;
                $this->apiResponse['term'] = $packageTerm;

            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
            }
        } else {
            $this->apiResponse['error'] = Configure::read('message.request');
        }
    }

    /**
     * Used on package add to cart , need to fix  with global pricing
     * @param $data
     * @param $optionPrice
     */
    private function _getPrice($data, $optionPrice)
    {
        $priceType = $this->VariantsProducts->get($data['variants_products_id']);
        if ($data['rental_type'] == 'buy') {
            $price = $this->ProductPrices->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->toArray();
            $price = RentMy::formatPricing($price, $data);
            $data['price'] = $price[0]->price;
            if (RentMy::$storeConfig['inventory']['promo_price']) {
                $data['price'] = !empty($price[0]->promo_price) ? $price[0]->promo_price : $price[0]->price;
            }
        } else {
            $customFields = !empty($data['custom_fields'])?$data['custom_fields']:[];
            $data['price'] = $this->ProductPrices->getRentalPriceByDates($data['package_id'], $data['variants_products_id'], $data['rent_start'], $data['rent_end'], [], 'cart', $customFields, $data);

//            if ($priceType->price_type == 2) {
//                $price = $this->ProductPrices->find()->where(['duration_type' => 'fixed'])->where(['variants_products_id' => $data['variants_products_id']])->toArray();
//                $price = RentMy::formatPricing($price);
//                $data['price'] = $price[0]->price;
//                $data['rental_type'] = 'fixed';
//            } else if ($priceType->price_type == 3) {
//                $data['price'] = (new Price())->getFlatPrice($data);
//                $data['rental_type'] = $this->ProductPrices->getRentalType($data);
//            } else if ($priceType->price_type == 4) {
//                // $optionPrice = false;
//                if ($optionPrice) {
//                    $requiredParam = ['rent_start', 'rent_end'];
//                    if (!$this->array_keys_exist($data, $requiredParam)) {
//                        $this->apiResponse['error'] = Configure::read('message.missing_param');
//                        return;
//                    }
//                    $data['price'] = $this->ProductPrices->getPriceValueWithFlexOption($data, $priceType->price_type, null);
//                    $data['rental_type'] = $this->ProductPrices->getRentalType($data);
//
//                } else {
//                    $requiredParam = ['rent_start', 'rent_end'];
//                    if (!$this->array_keys_exist($data, $requiredParam)) {
//                        $this->apiResponse['error'] = Configure::read('message.missing_param');
//                        return;
//                    }
//                    $data['price'] = $this->ProductPrices->getPriceValue($data, $priceType->price_type, null);
//                    $data['rental_type'] = $this->ProductPrices->getRentalType($data);
//                }
//            }
            //$data['price'] = $this->ProductPrices->seasonalPrice($data['price'], RentMy::toStoreTimeZone($data['rent_start'], 'Y-m-d H:i'));
        }

        return $data;
    }

    /**
     * add Package To Cart
     * checking availability
     *
     * @param package_id, variants_products_id, location
     * @return cart details
     */
    public function addPackageToCart()
    {
        if ($this->request->is('post')) {
            $this->add_model(array('Carts', 'Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'CartItems', 'ProductPrices', 'VariantsProducts', 'Holidays'));
            RentMy::addModel(['Carts', 'ProductsAvailabilities']);
            $data = $this->request->getData();

            if (!RentMy::$Model['Carts']->sameDateBooking($data)){
                $this->apiResponse['same_day_booking'] = false;
                $this->apiResponse['error'] = 'Same-day booking is not permitted. Select another day. Please call for more options';
                RentMy::logToGenius([
                      "event" => "add_package_to_cart",
                      "status" => "fail",
                      "description" => $this->apiResponse['error'],
                      "value" => $this->apiResponse['error'],
                      "custom_content" => json_encode($data)
                ]);
                return;
            }

            if (!empty($data['fullfilment_option']) && $data['fullfilment_option'] == 'delivery') {
                RentMy::addModel(['DeliveryDetails']);
                $data = RentMy::$Model['DeliveryDetails']->maxDeliveryChecker($data);
                if (!empty($data['delivery_limit'])) {
                    if(!$data['delivery_limit']['canAdd']) {
                        $this->apiResponse['error'] = $data['delivery_limit']['message'];
                        RentMy::logToGenius([
                              "event" => "add_package_to_cart",
                              "status" => "fail",
                              "description" => $this->apiResponse['error'],
                              "value" => $this->apiResponse['error'],
                              "custom_content" => json_encode($data)
                        ]);
                        return;
                    }
                    $this->apiResponse['warning'] = $data['delivery_limit']['message'];
                }
            }

            $quantity = $this->Quantities->find()->where(['product_id' => $data['package_id'], 'location' => $data['location'], 'variants_products_id' => $data['variants_products_id']])->first();
            if (empty($quantity)) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Out of stock';
            }
            $data['quantity_id'] = $quantity->id;
            $data['store_id'] = $this->parseToken->store_id;
            $data['sales_tax'] = $this->productContainer->getSalesTax($data['package_id'], $this->storeId, $data['rental_type']);
            $requiredParam = ['package_id', 'variants_products_id', 'location'];
            if ($this->array_keys_exist($data, $requiredParam)) {
                if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
                    $this->apiResponse['error'] = 'Invalid Rental Date';
                    RentMy::logToGenius([
                          "event" => "add_package_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }

                $responseOption = RentMy::$storeConfig;//$this->ProductPrices->getOption($this->parseToken->store_id);
                $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];

                if (!empty($data['token'])) {
                    $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                    if ((!empty($cart->rent_start)) && (!empty($cart->rent_end))) {
                        $data['rent_start'] = RentMy::toStoreTimeZone($cart->rent_start, 'Y-m-d H:i');
                        $data['rent_end'] = RentMy::toStoreTimeZone($cart->rent_end, 'Y-m-d H:i');
                    }
                }

                RentMy::addModel(['VariantsProducts', 'Products', 'CartItems']);
                $product = RentMy::$Model['Products']->get($data['package_id']);
                $productOptions = [];
                if (!empty($product->options))
                   $productOptions = $product->options = json_decode($product->options, true);

                $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end'],  $product);
                $data['rent_start'] = $rentalDates['start_date'];
                $data['rent_end'] = $rentalDates['end_date'];

                $siteLinkContent = RentMy::getSiteLinkContent();
                $storeTime = $this->Holidays->checkStoreTime($this->parseToken->store_id, $data['rent_start'], $responseOption);
                if (!$storeTime && !in_array(RentMy::$token->source, ['admin'])) {
                    $this->apiResponse['error'] = $siteLinkContent['message']['error_store_close'];
                    RentMy::logToGenius([
                          "event" => "add_package_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }
                $holiday = $this->Holidays->checkHoliday($this->parseToken->store_id, $data['rent_start'], $data['rent_end']);
                if ($holiday['success'] && !in_array(RentMy::$token->source, ['admin'])) {
                    $this->apiResponse['error'] = "Package not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                    RentMy::logToGenius([
                          "event" => "add_package_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }
                $data = $this->_getPrice($data, $optionPrice);

                $data['rent_start'] = $this->Timezones->_dateGlobal($data['rent_start'], 'yyyy-MM-dd HH:mm');

                if (empty($data['rent_end'])) {
                    $data['rent_end'] = $this->CartItems->_getRentEndTime($data['rent_start'], $data['rental_duration'], $data['rental_type'], $data['price_duration']);
                }
                $data['rent_end'] = $this->Timezones->_dateGlobal($data['rent_end'], 'yyyy-MM-dd HH:mm');

                $productAvailable = array();
                foreach ($data['products'] as $item) {
                    $quantity = $this->Quantities->find()
                        ->where(['variants_products_id' => $item['variants_products_id']])
                        ->where(['location' => $data['location']])
                        ->first();
                    if (empty($quantity)) {
                        $this->apiResponse['error'] = 'Out of Stock!';
                        RentMy::logToGenius([
                              "event" => "add_package_to_cart",
                              "status" => "fail",
                              "description" => $this->apiResponse['error'],
                              "value" => $this->apiResponse['error'],
                              "custom_content" => json_encode($data)
                        ]);
                        return;
                    }

                     $product = $this->Products->get($item['product_id']);
                    $pAvailable = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($item['product_id'], $item['variants_products_id'], $data['location'], $data['rent_start'], $data['rent_end']);
                    $productAvailable[] = ['product_id' => $item['product_id'], 'variants_products_id' => $item['variants_products_id'], 'available' => $pAvailable];


                    $variant = RentMy::$Model['VariantsProducts']->get($item['variants_products_id']);
                    if (!empty($data['token'])) {
                        $cart_available = RentMy::$Model['CartItems']->available($data['token'], $quantity->id);
                        $pAvailable = $pAvailable - $cart_available;
                    }

                    if ( $data['quantity'] * $item['quantity'] > $pAvailable) {
                        if ($variant->chain == 1)
                            $product->variant_set = json_encode([1]);

                        $variant_chain = RentMy::$Model['VariantsProducts']->getVariantChain(json_decode($product->variant_set, true), $variant->chain);
                        $unavailableProducts[] = [ 'product_name' => $product->name, 'variant_chain' => $variant_chain];
                    }
                }


                if (isset($unavailableProducts) && ((in_array(RentMy::$token->source, ['admin', 'pos']) && empty($productOptions['booking'])) ||
                        !in_array(RentMy::$token->source, ['admin', 'pos']))){
                    $messages = [];
                    foreach ($unavailableProducts as $unavailableProduct)
                        if ($unavailableProduct['variant_chain'] != 'Unassigned: Unassigned')
                            $messages[] = $unavailableProduct['product_name'].' ('.$unavailableProduct['variant_chain'] . ')';
                        else
                            $messages[] = $unavailableProduct['product_name'];

                    $this->apiResponse['errors'] = implode(', ', $messages).' not available';
                    $this->apiResponse['error'] = 'Not available!';
                    RentMy::logToGenius([
                          "event" => "add_package_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }

                $result = $this->Carts->addPackageToCart($data);
                if ($result['success']) {
                    $this->apiResponse['data'] = $result['data'];
                    RentMy::logToGenius([
                          "event" => "add_package_to_cart",
                          "status" => "success",
                          "description" => $this->apiResponse['data'],
                          "value" => $this->apiResponse['data'],
                          "custom_content" => json_encode([
                            "data" => $data,
                            "result_data" => $result['data']
                          ])
                    ]);
                    return;
                } else {
                    $this->apiResponse['error'] = $result['message'];
                    RentMy::logToGenius([
                          "event" => "add_package_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }
            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
                RentMy::logToGenius([
                      "event" => "add_package_to_cart",
                      "status" => "fail",
                      "description" => $this->apiResponse['error'],
                      "value" => $this->apiResponse['error'],
                      "custom_content" => json_encode($data)
                ]);
            }
        } else {
            $this->apiResponse['error'] = Configure::read('message.request');
            RentMy::logToGenius([
                  "event" => "add_package_to_cart",
                  "status" => "fail",
                  "description" => $this->apiResponse['error'],
                  "value" => $this->apiResponse['error'],
                  "custom_content" => ''
            ]);
        }
    }

    /**
     * @desc Add item to cart
     * checking availability
     *
     * $data['rent_end'] = "2019-03-06 09:02"
     * $data['rent_start'] = "2019-03-03 07:02"
     */
    public function add()
    {
        if ($this->request->is('post')) {
            $this->add_model(array('Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'Carts', 'CartItems', 'ProductPrices', 'VariantsProducts', 'OrderProductOptions'));
            RentMy::addModel(['ProductsAvailabilities', 'CartItems', 'Products', 'ProductPrices', 'OrderProductOptions', 'Carts']);
            $data = $this->request->getData();
            $data['location'] = RentMy::$token->location;
            $content = RentMy::getSiteLinkContent();

            if (!RentMy::$Model['Carts']->sameDateBooking($data)){
                $this->apiResponse['same_day_booking'] = false;
                $this->apiResponse['error'] = !empty($content['custom']['msg_same_day_booking_error'])?$content['custom']['msg_same_day_booking_error']:'Same-day booking is not permitted. Select another day. Please call for more options';
                RentMy::logToGenius([
                      "event" => "add_to_cart",
                      "status" => "fail",
                      "description" => $this->apiResponse['error'],
                      "value" => $this->apiResponse['error'],
                      "custom_content" => json_encode($data)
                ]);
                return;
            }

            if (!empty($data['fullfilment_option']) && $data['fullfilment_option'] == 'delivery') {
                RentMy::addModel(['DeliveryDetails']);
                $data = RentMy::$Model['DeliveryDetails']->maxDeliveryChecker($data);
                if (!empty($data['delivery_limit'])) {
                    if(!$data['delivery_limit']['canAdd']) {
                        $this->apiResponse['error'] = $data['delivery_limit']['message'];
                        RentMy::logToGenius([
                              "event" => "add_to_cart",
                              "status" => "fail",
                              "description" => $this->apiResponse['error'],
                              "value" => $this->apiResponse['error'],
                              "custom_content" => json_encode($data)
                        ]);
                        return;
                    }
                    $this->apiResponse['warning'] = $data['delivery_limit']['message'];
                }
            }

            //$data['sales_tax'] = $this->productContainer->getSalesTax($data['product_id'], $this->storeId, $data['rental_type']);
            $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
            if ($this->array_keys_exist($data, $requiredParam)) {
                $product = $this->Products->find()->where(['id' => $data['product_id']])->map(function ($product){
                    if(!empty($product->options))
                        $product->options = json_decode($product->options, true);

                    return $product;
                })->first();
//                $data['sales_tax'] = $product['sales_tax'];
                if (isset($data['sales_tax']))
                    unset($data['sales_tax']);

                $quantity = $this->Quantities->find()
                    ->where(['variants_products_id' => $data['variants_products_id']])
                    ->where(['location' => $data['location']])
                    ->first();
                if (empty($quantity)) {
                    $this->apiResponse['error'] = !empty($content['custom']['msg_out_of_stock'])?$content['custom']['msg_out_of_stock']:'Out of Stock!';
                    return;
                }
                if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
                    $this->apiResponse['error'] = !empty($content['custom']['msg_invalid_rental_date'])?$content['custom']['msg_invalid_rental_date']:'Invalid Rental Date';
                    RentMy::logToGenius([
                          "event" => "add_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }
                if (!empty($data['token'])) {
                    $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                    if ((!empty($cart->rent_start)) && (!empty($cart->rent_end))) {
                        $data['rent_start'] = RentMy::toStoreTimeZone($cart->rent_start, 'Y-m-d H:i');
                        $data['rent_end'] = RentMy::toStoreTimeZone($cart->rent_end, 'Y-m-d H:i');
                    }
                }
                $responseOption = $this->ProductPrices->getOption($this->parseToken->store_id, RentMy::$token->location);
                $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];

                $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end'], $product);
                $data['rent_start'] = $rentalDates['start_date'];
                $data['rent_end'] = $rentalDates['end_date'];

                $priceType = RentMy::$Model['ProductPrices']->find()->where([
                    'product_id' => $data['product_id'],
                    'variants_products_id' => $data['variants_products_id'],
                    'location' => RentMy::$token->location,
                    'current' => 1
                ])->first();
                $data['rental_type'] = empty($data['rental_type']) ? 'buy' : $data['rental_type'];

                if ($data['rental_type'] == 'buy') {
                    $price = $this->ProductPrices->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->where(['location' => RentMy::$token->location])->toArray();
                    //RentMy::dbg($price);
                    $price = RentMy::formatPricing($price, $data);
                    $data['price'] = $price[0]->price;
                    if (RentMy::$storeConfig['inventory']['promo_price']) {
                        $data['price'] = !empty($price[0]->promo_price) ? $price[0]->promo_price : $price[0]->price;
                    }
                } else {
                    if ($priceType->price_type == 2) {
                        $price = $this->ProductPrices->find()->where(['duration_type' => 'fixed'])->where(['variants_products_id' => $data['variants_products_id']])->where(['location' => RentMy::$token->location])->toArray();
                        $price = RentMy::formatPricing($price, $data);
                        $data['price'] = $price[0]->price;
                        $data['rental_type'] = 'fixed';
                    } else if ($priceType->price_type == 3) {
                        $isSubscription = false;
                        $rental = ['before_rental', 'after_rental'];
                        if ((RentMy::$storeConfig['arb']['active']) && (in_array(RentMy::$storeConfig['arb']['store_active'], $rental)) && !empty($data['price_id'])) {
                            $selected_price = RentMy::$Model['ProductPrices']->find()->where(['id' => $data['price_id']])->first();
                            $selected_product = RentMy::$Model['Products']->find()->where(['store_id'=>RentMy::$store->id,'id' => $data['product_id']])->first();
                            $productOptions = !empty($selected_product['options'])?json_decode($selected_product['options'], true):[];
                            if (!empty($productOptions['enduring_rental']))
                            {
                                if (!empty($data['custom_fields'])){
                                    $productPrices = RentMy::$Model['ProductPrices']->find()
                                        ->where(['variants_products_id' => $data['variants_products_id']])
                                        ->where(['price_type' => 3])
                                        ->where(['id' => $data['price_id']])
                                        ->where(['duration_type !=' => 'base'])
                                        ->where(['location' => RentMy::$token->location])
                                        ->order(['price' => 'DESC'])
                                        ->toArray();
                                    $productPrices= RentMy::$Model['OrderProductOptions']->calculatePricing($productPrices, $data['custom_fields']);
                                    $selected_price = $productPrices[0];
                                }
                                $rental_price = $selected_price['price'];
                                $isSubscription  = true;
                            }
                        }

                        if (!$isSubscription)
                            $rental_price = (new Price())->getFlatPrice($data);

                        $data['price'] = $rental_price;
                        $data['rental_type'] = $this->ProductPrices->getRentalType($data);
                    } else if ($priceType->price_type == 4) {
                        $optionPrice = false;
                        if ($optionPrice) {
                            $priceData = $this->ProductPrices->get($data['price_id']);
                            $data['rental_type'] = $priceData->duration_type;
                            if (empty($data['rent_end'])) {
                                $price = $this->ProductPrices->get($data['price_id']);
                                $data['price'] = $price->price;
                            } else {
                                $data['price'] = $this->ProductPrices->getPriceValue($data, $priceType->price_type, $priceData);
                                /* get flex price according to immediate lowest
                                $data['price'] = $this->ProductPrices->getItemPrice($data, $priceType->price_type, $priceData);
                                */
                            }
                        } else {
                            $requiredParam = ['rent_start', 'rent_end'];
                            if (!$this->array_keys_exist($data, $requiredParam)) {
                                $this->apiResponse['error'] = Configure::read('message.missing_param');
                                return;
                            }
                            $data['price'] = (new Price())->getFlexPrice($data);
                            $data['rental_type'] = $this->ProductPrices->getRentalType($data);
                        }
                    }
                    // check seasonal price data here and update the price thoroughly and return
                    //$data['price'] = $this->ProductPrices->seasonalPrice($data['price'], RentMy::toStoreTimeZone($data['rent_start'], 'Y-m-d H:i'));
                    // ends of seasonal pricing
                }
                $data['quantity_id'] = $quantity->id;
                $data['rental_duration'] = empty($data['rental_duration']) ? 1 : $data['rental_duration'];

                if (in_array($data['rental_type'], ['hourly', 'daily', 'weekly', 'monthly', 'fixed'])) {
                    $this->loadModel('Holidays');

                    // checking for the season if it is in the season time or not
//                    $holidaySeason = $this->Holidays->checkStoreTime(RentMy::$store->id, $data['rent_start'], $responseOption, 'season');
//                    if (!empty($holidaySeason)) {
//                        $this->apiResponse['data'] = $holidaySeason;
//                        $this->apiResponse['message'] = 'You are in the season time now.';
//                        return;
//                    }

                    $siteLinkContent = RentMy::getSiteLinkContent();
                    $storeTime = $this->Holidays->checkStoreTime($this->parseToken->store_id, $data['rent_start'], $responseOption);
                    if (!$storeTime) {
                        $this->apiResponse['error'] = !empty($content['custom']['msg_store_close'])?$content['custom']['msg_store_close']:'Rentals can not start when store is closed.  Enter valid rental start date and time.';
                        return;
                    }
                    $data['rent_start'] = $this->Timezones->_dateGlobal($data['rent_start'], 'yyyy-MM-dd HH:mm:ss');
                    if (empty($data['rent_end'])) {
                        $data['rent_end'] = $this->CartItems->_getRentEndTime($data['rent_start'], $data['rental_duration'], $data['rental_type'], $priceData->duration);
                    }
                    $data['rent_end'] = $this->Timezones->_dateGlobal($data['rent_end'], 'yyyy-MM-dd HH:mm:ss');
                    $holiday = $this->Holidays->checkHoliday($this->parseToken->store_id, $data['rent_start'], $data['rent_end']);
                    if ($holiday['success'] && !in_array(RentMy::$token->source, ['admin'])) {
                        $this->apiResponse['error'] = "Product not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                        RentMy::logToGenius([
                              "event" => "add_to_cart",
                              "status" => "fail",
                              "description" => $this->apiResponse['error'],
                              "value" => $this->apiResponse['error'],
                              "custom_content" => json_encode($data)
                        ]);
                        return;
                    }
                    // availability for rent item
                    $rent_start = RentMy::toUTC($rentalDates['start_date'], 'Y-m-d H:i') ?? Time::now()->format('Y-m-d H:i');
                    $rent_end = RentMy::toUTC($rentalDates['end_date'], 'Y-m-d H:i') ?? Time::now()->addDays(14)->format('Y-m-d H:i');
                    $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);
                    //$available = $this->ProductsAvailabilities->getAvailableForProduct($data['product_id'], $data['variants_products_id'], $data['location'], $data['start_date']);
                    //$available = !empty($quantity->available) ? $quantity->available : $quantity->quantity - $notAvailableQuantity;
                } else { // availability for buy items
                    $rent_start = Time::now()->format('Y-m-d H:i');
                    $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
                    $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);

                    //$available = $this->ProductsAvailabilities->getAvailableForProduct($data['product_id'], $data['variants_products_id'], $data['location'], $data['start_date']);
                }
                $addedInCart = !empty($data['token']) ? RentMy::$Model['CartItems']->available($data['token'], $quantity->id) : 0;

                if (($available < ($data['quantity'] + $addedInCart)) && ((in_array(RentMy::$token->source, ['admin', 'pos']) && empty($product['options']['booking'])) ||
                        !in_array(RentMy::$token->source, ['admin', 'pos']))) {
                    $this->apiResponse['error'] = 'Not available.';
                    RentMy::logToGenius([
                          "event" => "add_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                    return;
                }
                $data['store_id'] = $this->parseToken->store_id;
                $data['user_id'] = isset($this->parseToken->id) ? $this->parseToken->id : 0;
                $variant = $this->Products->_getDefaultAttribute($data['variants_products_id']);
                $data['variant_chain_id'] = $variant['variant_chain_id'];
                $data['variant_chain_name'] = $variant['variant_chain_name'];

                // $data['rent_start'] = RentMy::toUTC($data['rent_start'], 'Y-m-d H:i');
                // $data['rent_end'] = RentMy::toUTC($data['rent_end'], 'Y-m-d H:i');

                // asset assignment from cart
                if (!empty($data['asset_id']) && !empty($data['serial_no'])) {

                    $assetResponse = $this->CartItems->updateAssetsItem(
                        [
                            'product_id' => $data['product_id'],
                            'variants_products_id' => $data['variants_products_id'],
                            'quantity_id' => $data['quantity_id'],
                            'rental_type' => $data['rental_type'],
                            'asset_id' => $data['asset_id'],
                            'serial_no' => $data['serial_no']
                        ]
                    );

                    if (!$assetResponse['success']) {
                        $this->httpStatusCode = 401;
                        $this->apiResponse['message'] = $data['serial_no'] . ' is already exists in the cart.';
                        RentMy::logToGenius([
                              "event" => "add_to_cart",
                              "status" => "fail",
                              "description" => $this->apiResponse['message'],
                              "value" => $this->apiResponse['message'],
                              "custom_content" => json_encode($data)
                        ]);
                        return;
                    }
                    $data['assets'] = $assetResponse['data'];
                }

                if (!empty($data['exact_times_id']) || !empty($data['exact_times']['id'])){
                    $data = RentMy::exactTimeItemChecker($data);

                    if (isset($data['exact_times_limit']) && !$data['exact_times_limit']['canAdd']){
                        $this->httpStatusCode = 400;
                        $this->apiResponse = ['message' => $data['exact_times_limit']['message']];
                        RentMy::logToGenius([
                              "event" => "add_to_cart",
                              "status" => "fail",
                              "description" => $this->apiResponse['message'],
                              "value" => $this->apiResponse['message'],
                              "custom_content" => json_encode($data)
                        ]);
                        return;
                    }
                }

                $details = $this->Carts->addToCart($data);
                if ($details) {
                    $this->apiResponse['data'] = $details;

                    $account = (isset($data['is_admin']) && $data['is_admin'] == true) ? 'Admin' : 'Customer';
                    RentMy::logToGenius([
                          "account" => $account,
                          "event" => "product_added_to_cart",
                          "status" => "success",
                          "description" => 'Product added to cart',
                          "value" => 'Product ID: ' . $data['product_id'],
                          "custom_content" => json_encode([
                            'data' => $data,
                            'details' => $details
                          ]),
                          "ref2" => @$data['customer_id'],
                    ]);

                } else {
                    $this->apiResponse['error'] = Configure::read('message.save');
                    RentMy::logToGenius([
                          "event" => "add_to_cart",
                          "status" => "fail",
                          "description" => $this->apiResponse['error'],
                          "value" => $this->apiResponse['error'],
                          "custom_content" => json_encode($data)
                    ]);
                }
            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
                RentMy::logToGenius([
                      "event" => "add_to_cart",
                      "status" => "fail",
                      "description" => $this->apiResponse['message'],
                      "value" => $this->apiResponse['message'],
                      "custom_content" => json_encode($data)
                ]);
            }
        } else {

            $this->apiResponse['error'] = Configure::read('message.request');
            RentMy::logToGenius([
                  "event" => "add_to_cart",
                  "status" => "fail",
                  "description" => $this->apiResponse['message'],
                  "value" => $this->apiResponse['message'],
                  "custom_content" => ''
            ]);
        }
    }


    /**
     * @desc Add items to cart by category
     * @API @POST /carts/add-to-cart/by-category
     * checking availability
     *
     */

    public function addToCartByCategory(){

        RentMy::addModel(['Products', 'Quantities', 'Carts', 'ProductPrices', 'VariantsProducts', 'Holidays', 'CartItems', 'ProductsAvailabilities', 'ProductPackages', 'Images']);
        $data = $this->request->getData();

        if (empty($data['category_ids'])){
            $this->apiResponse['error'] = 'No Product found for your search';
            return;
        }
        $category_ids = $data['category_ids'];
        $products = RentMy::$Model['Products']->find()->innerJoinWith('Categories', function ($q)use($category_ids){
                return $q->where(['category_id IN'=>$category_ids]);
            })
            ->where(['Products.store_id'=>RentMy::$store->id])
            ->toArray();

        if (empty($products)){
            $this->apiResponse['error'] = 'No Product found for your search';
            return;
        }
        $data['rental_type'] = 'buy';

        if ((!empty($data['rent_start']) && !empty($data['rent_end']))) {
            $data['rental_type'] = 'rent';
            if (strtotime($data['rent_end']) < strtotime($data['rent_start'])){
                $this->apiResponse['error'] = 'Invalid Rental Date';
                return;
            }
        }
        $siteLinkContent = RentMy::getSiteLinkContent();
        if ($data['rental_type'] == 'rent'  && !in_array(RentMy::$token->source, ['admin'])){
            $storeTime = RentMy::$Model['Holidays']->checkStoreTime(RentMy::$store->id, $data['rent_start'], []);

            if (!$storeTime && !in_array(RentMy::$token->source, ['admin'])) {
                $this->apiResponse['error'] = $siteLinkContent['message']['error_store_close'];
                return;
            }

            $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end']);
            $data['rent_start'] = $rentalDates['start_date'];
            $data['rent_end'] = $rentalDates['end_date'];

            $data['rent_start'] = RentMy::toUTC($data['rent_start']);
            $data['rent_end'] = RentMy::toUTC($data['rent_end']);

            $holiday = RentMy::$Model['Holidays']->checkHoliday(RentMy::$store->id, $data['rent_start'], $data['rent_end']);
            if ($holiday['success']  && !in_array(RentMy::$token->source, ['admin'])) {
                $this->apiResponse['error'] = "Product not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                return;
            }
        }

        $responseOption = RentMy::$storeConfig;
        $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];

        foreach ($products as $index=>$product){
            $cartData = array();
            $variantProducts = RentMy::$Model['VariantsProducts']->find()->where(['store_id'=>RentMy::$store->id, 'product_id'=>$product->id])->first();
            $product->images = RentMy::$Model['Images']->image_by_store_plan('product_details', ['product_id' => $product->id])->toArray();
            $product->prices = RentMy::$Model['ProductPrices']->getPriceDetails($variantProducts);
            $cartData['token'] =   $this->cartToken;
            $cartData['quantity'] = $data['quantity']??1;
            $cartData['location'] = $data['location']??'';
            $cartData['rental_type'] = $data['rental_type'];
            $cartData['rental_duration'] = !empty($data['rental_duration'])?$data['rental_duration']:1;
            $cartData['rent_start'] = $data['rent_start'];
            $cartData['rent_end'] = $data['rent_end'];
            $cartData['product_id'] = $product['id'];
            $cartData['variants_products_id'] = $product['variants_products_id'];

            $cartData['store_id'] = RentMy::$store->id;
            $cartData['user_id'] = RentMy::$storeUser->id ?? 0;

            $quantity = RentMy::$Model['Quantities']->find()
                ->where(['variants_products_id' => $cartData['variants_products_id']])
                ->where(['location' => $cartData['location']])
                ->first();

            if (empty($quantity)) {
                $notAdded[] = [
                    'success' => 'false',
                    'data' => $product,
                    'message' => 'Out of stock',
                ];
                continue;
            }

            $cartData['quantity_id'] = $quantity->id;
            $priceType = RentMy::$Model['VariantsProducts']->get($cartData['variants_products_id']);
            $priceBuy = RentMy::$Model['ProductPrices']->find()->where(['duration_type'=>'base'])->where(['variants_products_id' => $cartData['variants_products_id']])->toArray();
            $priceRent = RentMy::$Model['ProductPrices']->find()->where(['duration_type !='=>'base'])->where(['variants_products_id' => $cartData['variants_products_id']])->toArray();

            if (empty($priceBuy) && empty($priceRent)){
                $notAdded[] = [
                    'success' => false,
                    'product_info' => $product,
                    'message' => 'No prices found',
                ];
                continue;
            }
            if (($cartData['rental_type'] == 'buy') && empty($priceBuy)){
                if (!empty($priceRent) && !empty($cartData['rent_start']) && !empty($cartData['rent_end'])){
                    $cartData['rental_type'] = RentMy::$Model['ProductPrices']->getRentalType($cartData);
                }else{
                    $notAdded[] = [
                        'success' => false,
                        'product_info' => $product,
                        'message' => 'No prices found',
                    ];
                    continue;
                }
            }else{
                if (empty($priceRent) && !empty($priceBuy)){
                    $cartData['rental_type'] = 'buy';
                }
            }
            if ($product['type'] == 2){
                $productPackages = RentMy::$Model['ProductPackages']->find()
                    ->where(['ProductPackages.store_id' => RentMy::$store->id])
                    ->where(['ProductPackages.package_id' => $product->id])
                    ->toArray();

                if (!empty($productPackages)) {
                    $productIds = Hash::extract($productPackages, '{n}.product_id');

                    $quantities = Hash::extract($productPackages, '{n}.quantity');
                    $packageProducts = RentMy::$Model['ProductPackages']->productDetails($productIds, $productPackages, RentMy::$store->id);
                    $pProducts = [];
                    $outOfStock = false;
                    $packageNotAvailable = false;
                    foreach ($productIds as $productId) {
                        foreach ($packageProducts as $packageProduct) {
                            if ($productId == $packageProduct['id']) {
                                $pProducts[] = [
                                    'product_id'=>$packageProduct['id'],
                                    'variants_products_id'=>$packageProduct['variants'][0]['id'],
                                    'quantity'=>$packageProduct['quantity'],
                                ];

                                $quantity = RentMy::$Model['Quantities']->find()
                                    ->where(['variants_products_id' => $packageProduct['variants'][0]['id']])
                                    ->where(['location' => $cartData['location']])
                                    ->first();
                                if (empty($quantity)) {
                                    $outOfStock = true;

                                }
                                $rent_start = $cartData['rental_type']=='buy'?Time::now()->format('Y-m-d H:i'):$cartData['rent_start'];
                                $rent_end = $cartData['rental_type']=='buy'?Time::now()->addDays(14)->format('Y-m-d H:i'):$cartData['rent_start'];
                                $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($packageProduct['id'], $packageProduct['variants'][0]['id'], $cartData['location'], $rent_start, $rent_end);
                                $addedInCart = !empty($cartData['token']) ? RentMy::$Model['CartItems']->available($cartData['token'], $quantity->id) : 0;

                                if ($available < ($cartData['quantity'] + $addedInCart)) {
                                    $packageNotAvailable = true;
                                }
                            }
                        }
                    }



                    $cartData['products'] = $pProducts;
                    if ($outOfStock){
                        $notAdded[] = [
                            'success' => 'false',
                            'data' => $product,
                            'message' => 'Out of stock',
                        ];
                        continue;
                    }

                    if (!empty($packageNotAvailable)){
                        $notAdded[] = [
                            'success' => 'false',
                            'data' => $product,
                            'message' => 'Not available',
                        ];
                        continue;
                    }

                }
            }else{

                $rent_start = $cartData['rental_type']=='buy'?Time::now()->format('Y-m-d H:i'):$cartData['rent_start'];
                $rent_end = $cartData['rental_type']=='buy'?Time::now()->addDays(14)->format('Y-m-d H:i'):$cartData['rent_start'];

                $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($cartData['product_id'], $cartData['variants_products_id'], $cartData['location'], $rent_start, $rent_end);
                $addedInCart = !empty($data['token']) ? RentMy::$Model['CartItems']->available($cartData['token'], $quantity->id) : 0;

                if ($available < ($cartData['quantity'] + $addedInCart)) {
                    $notAdded[] = [
                        'success' => 'false',
                        'data' => $product,
                        'message' => 'Not available',
                    ];
                    continue;
                }
            }





            if ($cartData['rental_type'] == 'buy'){
                $price = $priceBuy;
                $price = RentMy::formatPricing($price, $cartData);
                $cartData['price'] = $price[0]->price;
                    if (RentMy::$storeConfig['inventory']['promo_price'])
                        $cartData['price'] = !empty($price[0]->promo_price) ? $price[0]->promo_price : $price[0]->price;
                } else {
                    if ($priceType->price_type == 2) {
                        $price = RentMy::$Model['ProductPrices']->find()->where(['duration_type' => 'fixed'])->where(['variants_products_id' => $cartData['variants_products_id']])->toArray();
                        $price = RentMy::formatPricing($price, $cartData);
                        $cartData['price'] = $price[0]->price;
                        $cartData['rental_type'] = 'fixed';
                    } else if ($priceType->price_type == 3) {
                        $cartData['price'] = (new Price())->getFlatPrice($cartData);

                        $cartData['rental_type'] = RentMy::$Model['ProductPrices']->getRentalType($cartData);
                    } else if ($priceType->price_type == 4) {
                        $cartData['price'] = RentMy::$Model['ProductPrices']->getPriceValue($cartData, $priceType->price_type, null);
                    }

                    $cartData['rental_duration'] = empty($cartData['rental_duration']) ? 1 : $cartData['rental_duration'];

                }

                $variant = RentMy::$Model['Products']->_getDefaultAttribute($cartData['variants_products_id']);
                $cartData['variant_chain_id'] = $variant['variant_chain_id'];
                $cartData['variant_chain_name'] = $variant['variant_chain_name'];

                if ($product['type'] == 1){

                    $details = RentMy::$Model['Carts']->addToCart($cartData);
                    $this->cartToken = $data['token'] = $details['token'];
                }else{
                    $cartData['package_id'] = $cartData['product_id'];
                    $details = RentMy::$Model['Carts']->addPackageToCart($cartData);
                    $details = $details['data'];
                    $this->cartToken = $data['token'] = $details['token'];
                }
        }

        $this->apiResponse['data'] = $details;
        $this->apiResponse['not_added'] = $notAdded;
    }


    /**
     * Get package price &
     * Availability checking for products of a package
     *
     * @param package_id, variants_products_id, location
     * @param $data ['rent_end'] = "2019-03-06 09:02"
     * @param $data ['rent_start'] = "2019-03-03 07:02"
     * @return price, available quantity
     */
    public function getPriceValue()
    {
        if ($this->request->is('post')) {
            RentMy::addModel(['Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'Carts', 'CartItems', 'ProductPrices', 'VariantsProducts', 'Holidays']);
            $data = $this->request->getData();
            $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
            if ($this->array_keys_exist($data, $requiredParam)) {
                $quantity = RentMy::$Model['Quantities']->find()
                    ->where(['variants_products_id' => $data['variants_products_id']])
                    ->where(['location' => $data['location']])
                    ->first();
                if (empty($quantity)) {
                    $this->apiResponse['error'] = 'Out of Stock!';
                    return;
                }

                $storeConfig = RentMy::$storeConfig;
//                checking existing recurring terms
//                $rental = ['before_rental', 'after_rental'];
//                if (($storeConfig['arb']['active']) && (in_array($storeConfig['arb']['store_active'], $rental)) && !empty($data['token'])) {
//                    $cart =  RentMy::$Model['Carts']->find()->select(['id', 'uid', 'options'])->where(['store_id'=>RentMy::$store->id, 'uid'=>$data['token']])->first();
//                    $options = !empty($cart['options'])?json_decode($cart['options'], true):[];
//                    $recurring = !empty($options['recurring'])?$options['recurring']:[];
//
//                    $priceDetails = RentMy::$Model['ProductPrices']->find()->where(['id' => $data['price_id'], 'variants_products_id' => $data['variants_products_id']])->first();
//
//                    if (!empty($recurring) && ($recurring['duration_type'] != $priceDetails['duration_type'])){
//                        $this->apiResponse['error'] = 'This duration type can not be added to this order. please choose another type';
//                        return;
//                    }
//                }

                $isForceRentalAdjustment = isset($storeConfig['checkout']['force_rent_time'])?$storeConfig['checkout']['force_rent_time']:true;
                // allow rental return
                $isAllowRentalReturn= isset($storeConfig['checkout']['show_separate_date_picker'])?$storeConfig['checkout']['show_separate_date_picker']:false;
                //if ((isset($data['is_apply']) && $data['is_apply']) && $isForceRentalAdjustment && (!empty(RentMy::$storeConfig['show_start_time']))){
                if ($data['rental_type'] == 'rent') {
                    if ($isForceRentalAdjustment && !$isAllowRentalReturn){
                        $options = [
                            'current_time' => $data['current_time'] ?? null,
                        ];
                        $holiday = RentMy::$Model['Holidays']->checkStoreCloseTime($data['rent_start'], $data['rent_end'], $data['price_id'], $options);
                        $this->apiResponse['start_date'] = $data['rent_start'] = $holiday['start_date'];
                        $this->apiResponse['end_date'] = $data['rent_end'] =  $holiday['end_date'];
                        $this->apiResponse['message'] = $holiday['message'];
                    }


                    if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
                        $this->apiResponse['error'] = 'Invalid Rental Date';
                        return;
                    }
                    $vp = RentMy::$Model['VariantsProducts']->get($data['variants_products_id']);
                    $product = [];
                    if (!empty($vp)){
                        $product = RentMy::$Model['Products']->find()->where(['id'=>$vp['product_id'], 'store_id'=>RentMy::$store->id])->first();
                        $product['options']  = (!empty($product['options']) && !is_array($product['options']))?json_decode($product['options'], true):[];
                    }
                    $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end'], $product);
                    $data['rent_start'] = $rentalDates['start_date'];
                    $data['rent_end'] = $rentalDates['end_date'];


                }

                $priceType = RentMy::$Model['VariantsProducts']->get($data['variants_products_id'])->price_type;

                if ($data['rental_type'] == 'buy') {
                    $price = RentMy::$Model['ProductPrices']->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->where(['location' => $data['location']])->toArray();
                    $price = RentMy::formatPricing($price, $data);
                    $data['price'] = $price[0]->price;
                    if (RentMy::$storeConfig['inventory']['promo_price']) {
                        $data['price'] = !empty($price[0]->promo_price) ? $price[0]->promo_price : $price[0]->price;
                    }
                    $rent_start = Time::now()->format('Y-m-d H:i');
                    $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
                    $data['price'] = RentMy::$Model['ProductPrices']->seasonalPrice($data['price'], $rent_start, $data['product_id']);
                    $product_available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);
                } else {
                    $data['price'] = RentMy::$Model['ProductPrices']->getRentalPriceByDates($data['product_id'], $data['variants_products_id'], $data['rent_start'], $data['rent_end'], [], 'cart', $data['custom_fields'], $data);
                    $rent_start = RentMy::toUTC($data['rent_start'], 'Y-m-d H:i');
                    $rent_end = RentMy::toUTC($data['rent_end'], 'Y-m-d H:i');
                    $product_available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);
                }

                $inCartAvailable = !empty($data['token']) ? TableRegistry::getTableLocator()->get('CartItems')->available($data['token'], $quantity->id) : 0;
                $this->apiResponse['data'] = $data['price'];
                $this->apiResponse['cart_available'] = $inCartAvailable;
                $this->apiResponse['available'] = ($product_available - $inCartAvailable);

            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
            }
        } else {
            $this->apiResponse['error'] = Configure::read('message.request');
        }
    }

    /**
     * @desc Remove an item from cart
     * @param cart item id, cart token
     */
    public function removeItemFromCart()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            RentMy::addModel(['OrderCharge']);
            if (!empty($data) && isset($data['token']) && !empty($data['cart_item_id'])) {
                $this->add_model(array('Carts', 'CartItems'));
                $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                if (empty($cart)) {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['error'] = 'Not found!';
                    return;
                }
                $result = $this->Carts->CartItems->removeItem($cart, $data);
                $countCartItems = $this->CartItems->find()->where(['cart_id' => $cart->id])->count();

                if (empty($countCartItems)) {
                    $cart->rent_start = null;
                    $cart->rent_end = null;
                    $cart->options = null;
                    $this->Carts->save($cart);
                }
                $countRentalItems = $this->CartItems->find()->where(['cart_id' => $cart->id, 'rental_type !=' => 'buy'])->count();

                if (empty($countRentalItems)) {
                    $cart->rent_start = null;
                    $cart->rent_end = null;
                    $this->Carts->save($cart);
                }

                if ($result) {
                    RentMy::$Model['OrderCharge']->changesRecalculate('', $cart->id);
                    $details = $this->Carts->getCartDetails($cart->id,['action'=>'cart_item_delete']);
                    if (!empty($data['view_token'])) { // pos screen customer view
                        (new PosCustomerView($data['view_token']))->save('cart', ['cart' => $details]);
                    }
                    $this->apiResponse['data'] = $details;

                    $account = (isset($data['is_admin']) && $data['is_admin'] == true) ? 'Admin' : 'Customer';
                    RentMy::logToGenius([
                          "account" => $account,
                          "event" => "product_removed_from_cart",
                          "status" => "success",
                          "description" => 'Product removed from cart',
                          "value" => 'Product ID: ' . $data['cart_item_id'],
                          "custom_content" => json_encode([
                              'data' => $data,
                              'details' => $details
                          ]),
                          "ref2" => @$details['user_id'],
                    ]);

                } else {
                    $this->apiResponse['error'] = 'Cart item can not be removed!';
                }
            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
            }
        } else {
            $this->apiResponse['error'] = Configure::read('message.request');
        }
    }

    /**
     * checking existence of cart
     * @param cart id
     * @return boolean
     */
    public function checkCart($token)
    {
        try {
            $this->add_model(array('Carts'));
            $cart = $this->Carts->find()->where(['uid' => $token])->first();
            if (empty($cart)) {
                $this->apiResponse['data'] = array('status' => false);
                return;
            }
            $r = $this->Carts->CartItems->find()->where(['cart_id' => $cart->id])->first();
            if (empty($r)) {
                throw new Exception();
            }
            $status = true;
        } catch (Exception $e) {
            $status = false;
        }
        $this->apiResponse['data'] = array('status' => $status);
    }

    /**
     * Apply coupon to a cart
     * checking the expired date
     * discount apply either in %(percentage) or fixed amount
     *
     * @param cart token
     * @param coupon code
     */
    public function applyCoupon()
    {
        $data = $this->request->getData();
        try {
            RentMy::addModel(['Carts', 'CartItems','OrderProductOptions']);
            if ($this->request->is('post') && !empty($data) && isset($data['token']) && isset($data['coupon'])) {
                $cart = $this->Carts->find()
                    ->contain(['CartItems'])
                    ->where(['Carts.uid' => $data['token']])->first();

                if ($cart) {
                    $coupon = $this->Carts->Coupons->find()
                        ->contain('CouponProducts', function ($q){
                            return $q->select(['coupon_id','content_id']);
                        })
                        ->where([ 'Coupons.code' => $data['coupon'], 'Coupons.status' => 1, 'type' => 1, 'Coupons.store_id' =>RentMy::$store->id,
                            'DATE(Coupons.start_time) <= CURDATE()', 'DATE(Coupons.end_time) >= CURDATE()'])
                      ->first();
                    if ($coupon) {

                        $cart->coupon_id = $coupon->id;
                        // check for uses limit if users_limit is not empty - uses limit empty mean - it is not used
                        // if limit exceeds, return error message 'Coupon use limit exceeds'

                        if (!empty($coupon->limit_to_order) && ($coupon->uses_limit > 0) && ($coupon->uses_limit <= $coupon->used_limit)){
                            $this->httpStatusCode = 400;
                            $this->apiResponse['message'] = 'Coupon use limit exceeds';
                            return;
                        }
                        $couponProducts = $coupon->coupon_products;

                        if ($this->Carts->save($cart)) {
                            foreach ($cart['cart_items'] as $cart_item) {
                                // check for entire order or specific product
                                // for specific product , just apply for that that product
                                if ($coupon->applies_to == 'product'){
                                    $couponProductsIds = array_column($couponProducts, 'content_id');
                                    if (in_array($cart_item->product_id, $couponProductsIds)) {
                                        $item_sub_total = $cart_item['substantive_price'];
                                        //$item_sub_total = $cart_item['substantive_price'] + $cart_item['product_option_price'];
                                        $discountable_items = array_filter($cart['cart_items'], function ($item)use($couponProductsIds){
                                            return in_array($item->product_id, $couponProductsIds);
                                        });
                                        $substantive_price = array_sum(array_column($discountable_items, 'substantive_price'));
                                        $product_options_price = array_sum(array_column($discountable_items, 'product_option_price'));
                                        if ($coupon->unit_type == 1) {
                                            $couponDiscount = ($item_sub_total * $coupon->amount) / 100;
                                        } else {
                                            $couponDiscount = ($coupon->amount * $cart_item['substantive_price']) / ($substantive_price+$product_options_price);
                                        }


                                        $cart_item['coupon_amount'] = $couponDiscount;
                                        $cart_item['sub_total'] = $cart_item['substantive_price'] - ($couponDiscount + $cart_item['off_amount']+ $cart_item['automatic_coupon_amount']);
                                        $cart_item['total'] = RentMy::$Model['CartItems']->cartItemTotal($cart_item);
                                        $additional = json_decode($cart_item['additional'], true);
                                        $additional['coupon_sub_total'] = $cart_item['sub_total'] ;
                                        //$additional['coupon_sub_total'] = $cart_item['sub_total']  +  $cart_item['product_option_price'];
                                        $additional['coupon_discount'] = $couponDiscount;
                                        $additional['coupon_type'] = ($coupon->unit_type == 1) ? 'percent' : 'amount';
                                        if ($couponDiscount > $cart_item['substantive_price']){
                                            $cart_item['sub_total'] = 0;
                                            $additional['coupon_sub_total'] = 0;
                                        }
                                        $cart_item['additional'] = json_encode($additional);
                                    }else{
                                        $cart_item['coupon_amount'] = '';
                                        //$cart_item['sub_total'] = $cart_item['substantive_price'];
                                        $cart_item['sub_total'] = $cart_item['substantive_price'] - ($cart_item['off_amount']+ $cart_item['automatic_coupon_amount']);
                                        $cart_item['total'] = RentMy::$Model['CartItems']->cartItemTotal($cart_item);
                                    }
                                }else{
                                   // $item_sub_total = $cart_item['substantive_price'] +  $cart_item['product_option_price'];
                                    $item_sub_total = $cart_item['substantive_price'];
                                    if ($coupon->unit_type == 1) {
                                        $couponDiscount = ($item_sub_total * $coupon->amount) / 100;
                                    } else {
                                        $couponDiscount = ($coupon->amount * $item_sub_total) / ($cart['substantive_price']);
                                        //$couponDiscount = ($coupon->amount * $item_sub_total) / ($cart['substantive_price']+ $cart['product_option_price']);
                                    }
                                    $cart_item['coupon_amount'] = $couponDiscount;
                                    $cart_item['sub_total'] =  $cart_item['substantive_price'] - ($couponDiscount + $cart_item['off_amount']+ $cart_item['automatic_coupon_amount']);
                                    $cart_item['total'] = RentMy::$Model['CartItems']->cartItemTotal($cart_item);

                                    $additional = json_decode($cart_item['additional'], true);
                                    $additional['coupon_sub_total'] = $cart_item['sub_total'];
                                    //$additional['coupon_sub_total'] = $cart_item['sub_total']  +  $cart_item['product_option_price'];
                                    $additional['coupon_discount'] = $couponDiscount;
                                    $additional['coupon_type'] = ($coupon->unit_type == 1) ? 'percent' : 'amount';

                                    if ($couponDiscount > $item_sub_total){
                                        $cart_item['sub_total'] = 0;
                                        $additional['coupon_sub_total'] = 0;
                                    }
                                    $cart_item['additional'] = json_encode($additional);
                                }
                                RentMy::$Model['CartItems']->save($cart_item);
                            }
                            $this->Carts->_updatePriceNQty($cart);
                            $cartDetails = $this->Carts->getCartDetails($cart->id, ['source' => 'coupon', 'coupon' => $coupon]);
                            if (RentMy::$storeConfig['tax']['address'] == 'store') {
                                $cartDetails = $this->Carts->addTaxLookup($cartDetails);
                            }
                        }
                        if ($cart) {
                            $this->httpStatusCode = 200;
                            $this->apiResponse['data'] = $cartDetails;

                            $account = (isset($data['is_admin']) && $data['is_admin'] == true) ? 'Admin' : 'Customer';
                            RentMy::logToGenius([
                                  "account" => $account,
                                  "event" => "coupon_applied",
                                  "status" => "success",
                                  "description" => 'Coupon applied',
                                  "value" => 'Coupon ID: ' . $coupon->id,
                                  "custom_content" => json_encode([
                                      'data' => $data,
                                      'coupon' => $coupon
                                  ])
                            ]);

                        } else {
                            throw new Exception("Sorry! Coupon could not be applied.");
                        }
                    } else {
                        throw new Exception("Invalid coupon code!");
                    }
                } else {
                    throw new Exception("Bad request!");
                }
            } else {
                throw new Exception("Bad request!");
            }
        } catch (Exception $ex) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = $ex->getMessage();
        }
    }

    /**
     * Delete any coupon
     * @param $data['coupon_id']
     * @param $data['token']
     * @API - POST /carts/coupon/delete
     *
     */
    function deleteCoupon()
    {
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();
        $requiredFields = ['coupon_id', 'token'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }
        RentMy::addModel(['Carts', 'CartItems', 'Coupons']);

        $cart =RentMy::$Model['Carts']->find()
            ->contain(['CartItems'])
            ->where(['Carts.uid' => $data['token'],'store_id'=> RentMy::$store->id])->first();

        if(empty($cart)){
            throw new MissingParamsException('Invalid Cart',401);
            return;
        }
        $coupon = RentMy::$Model['Coupons']->find()
            ->where(['id'=> $data['coupon_id']])->first();

        if(empty($coupon)){
            throw new MissingParamsException('Invalid Coupon code',401);
            return;
        }
        $cart->coupon_id = '';
        if (RentMy::$Model['Carts']->save($cart)) {
            foreach ($cart['cart_items'] as $cart_item) {
                $item_sub_total = $cart_item['substantive_price'];
                $couponDiscount = 0;
                $cart_item['coupon_amount'] = $couponDiscount;
                $cart_item['sub_total'] = $cart_item['substantive_price'] - ($couponDiscount + $cart_item['off_amount']);
                $cart_item['total'] = RentMy::$Model['CartItems']->cartItemTotal($cart_item);

                $additional = json_decode($cart_item['additional'], true);
                unset($additional['coupon_sub_total']);
                unset($additional['coupon_discount']);
                unset($additional['coupon_type']);
                $cart_item['additional'] = json_encode($additional);
                RentMy::$Model['CartItems']->save($cart_item);
            }
            RentMy::$Model['Carts']->_updatePriceNQty($cart);
            $cartDetails = RentMy::$Model['Carts']->getCartDetails($cart->id);
            if (RentMy::$storeConfig['tax']['address'] == 'store') {
                $cartDetails = RentMy::$Model['Carts']->addTaxLookup($cartDetails);
            }
            $this->apiResponse['data'] = $cartDetails;
        }

    }

    /**
     * @desc Update cart
     * quantity increment or decrement
     * @param cart token, item id, price
     * @param increment(boolean)
     */
    public function update($id = null)
    {
        $this->add_model(array('Products', 'Quantities', 'Carts', 'CartItems', 'Coupons'));
        RentMy::addModel(['ProductsAvailabilities', 'OrderCharge','Products', 'Carts', 'Customers', 'CartItems']);
        $data = $this->request->getData();
        try {
            if ($this->request->is(['patch', 'post', 'put'])
                && (!empty($data['token']) && !empty($data['id']))
                && isset($data['increment']) && isset($data['price'])
            ) {
                $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                if (empty($cart)) {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['error'] = 'Not found!';
                    return;
                }
                $cartItem = $this->Carts->CartItems->get($data['id']);
                if ($cart->id != $cartItem->cart_id) {
                    $this->httpStatusCode = 401;
                    $this->apiResponse['error'] = 'Access denied!';
                    return;
                }

                // for closet
                if (isset($data['closet']) && !empty($data['customer_id'])){
                    $customer = RentMy::$Model['Customers']->find()->where(['Customers.id'=>$data['customer_id']])->contain(['CustomerPlans'])->first();
                    $inventory = !empty($customer['customer_plan']['number_of_inventory'])?$customer['customer_plan']['number_of_inventory']:0;

                    $cartItemQty = RentMy::$Model['CartItems']->find()->select(['total_qty'=>'SUM(quantity)'])->where(['cart_id'=>$cart->id,'options LIKE'=>'%"closet":true%'])->first();

                    if (($cartItemQty->total_qty  + $cartItem->quantity) <= $inventory){
                        $options = !empty($cartItem->options)?json_decode($cartItem->options, true):[];
                        $options['closet'] = $data['closet'];
                        $cartItem->options = json_encode($options);
                        $this->Carts->CartItems->save($cartItem);
                        $this->apiResponse['message'] = $data['closet']?'Closet has been saved':'Closet has been removed';
                        return;
                    }else{
                        $this->apiResponse['error'] = 'Request quantity has been exceeded!';
                        return;
                    }

                }
                // end closet


                $data['sales_tax'] = $cartItem->sales_tax;

                RentMy::addModel(['OrderProductOptions']);

                $cartProduct = RentMy::$Model['Products']->find()->where(['id' => $cartItem['product_id']])->map(function ($product){
                    if(!empty($product->options))
                        $product->options = json_decode($product->options, true);

                    return $product;
                })->first();

                if ($data['increment']) {
                    //$product = $this->Products->get($cartItem->product_id);

                    if ($cartItem->product_type == 1) {

                        // $quantity = $this->Quantities->get($cartItem->quantity_id);
                        //$productAvailable = $this->Products->_quantityAvailable($product, $quantity, $cartItem->rent_start);
                        $productAvailable = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($cartItem->product_id, $cartItem->variants_products_id, $cartItem->location, Time::parse($cart->rent_start)->format('Y-m-d H:i'), Time::parse($cart->rent_end)->format('Y-m-d H:i'));
                        if (($productAvailable < $cartItem['quantity'] + 1) && (empty($data['option_level']) || ($data['option_level'] != 'child'))) {
                            if (((in_array(RentMy::$token->source, ['admin', 'pos']) && empty($cartProduct['options']['booking']) && empty($data['modified_price'])) ||
                                !in_array(RentMy::$token->source, ['admin', 'pos']))){
                                $this->apiResponse['error'] = 'Not available!';
                                return;
                            }
                        }
                    }else{
                        RentMy::addModel(['Products', 'VariantsProducts', 'CartItems']);
                        $packageItems = RentMy::$Model['CartItems']->find()->where(['cart_id' => $cartItem->cart_id,'parent_id' => $data['id']])->toArray();
                        $productAvailable = array();
                        foreach ($packageItems as $item) {
                            $product = RentMy::$Model['Products']->get($item['product_id']);
                            // $pAvailable = $this->Products->_quantityAvailable($product, $quantity, $data['rent_start']);
                            $pAvailable = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($item->product_id, $item->variants_products_id, $item->location, Time::parse($cart->rent_start)->format('Y-m-d H:i'), Time::parse($cart->rent_end)->format('Y-m-d H:i'));
                            $productAvailable[] = ['product_id' => $item['product_id'], 'variants_products_id' => $item['variants_products_id'], 'available' => $pAvailable];


                            $variant = RentMy::$Model['VariantsProducts']->get($item['variants_products_id']);
//                    $product = RentMy::$Model['Products']->get($variant->product_id);
                            if (!empty($data['token'])) {
                                $cart_available = RentMy::$Model['CartItems']->available($data['token'], $item['quantity_id']);

                                $pAvailable = $pAvailable - $cart_available;
                            }
                            if ( ($pAvailable < $item['quantity'] + 1)) {
                                if ($variant->chain == 1)
                                    $product->variant_set = json_encode([1]);

                                $variant_chain = RentMy::$Model['VariantsProducts']->getVariantChain(json_decode($product->variant_set, true), $variant->chain);
                                $unavailableProducts[] = [ 'product_name' => $product->name, 'variant_chain' => $variant_chain];
                            }
                        }

                        if (isset($unavailableProducts) && ((in_array(RentMy::$token->source, ['admin', 'pos']) && empty($cartProduct['options']['booking']) && empty($data['modified_price'])) ||
                                !in_array(RentMy::$token->source, ['admin', 'pos']))){
                            $messages = [];
                            foreach ($unavailableProducts as $unavailableProduct)
                                if ($unavailableProduct['variant_chain'] != 'Unassigned: Unassigned')
                                    $messages[] = $unavailableProduct['product_name'].' ('.$unavailableProduct['variant_chain'] . ')';
                                else
                                    $messages[] = $unavailableProduct['product_name'];

                            $this->apiResponse['errors'] = implode(', ', $messages).' not available';
                            $this->apiResponse['error'] = 'Not available!';
                            return;
                        }
                    }

                    if (isset($data['modified_price'])) {
                        $cartItem['price'] = $data['price'] = $data['modified_price'];
                        $quantity = $cartItem['quantity'];
                    }else{
                        $quantity = $cartItem['quantity'] + 1;
                        if ($data['option_level'] == 'child')
                            $quantity = $cartItem['quantity'];
                    }

                    $data['rental_duration'] = (!empty($data['rental_duration'])) ? $data['rental_duration'] : $cartItem->rental_duration;
                    if (empty($data['rental_duration'])) $data['rental_duration'] = 1;
                    $cartItem->rental_duration = ($cartItem['rental_type'] != 'buy') ? $data['rental_duration'] : null;
                    if (isset($data['option_id'])) {
                        RentMy::$Model['OrderProductOptions']->updateQuantity($cartItem->id, true, 'cart', $data['option_id']);
                        $price =   RentMy::$Model['OrderProductOptions']->getAverage('cart',$cart->id,$cartItem->id);
                        $data['price'] = !empty($price['unit_price']) ? $price['unit_price'] : $data['price'];
                        $data['price'] = !empty($price['modified_price']) ? $price['modified_price'] : $data['price'];
                        $priceWithoutTax = !empty($price['sub_total']) ? $price['sub_total'] : ( $data['price'] * $quantity * $data['rental_duration']);
                    }else{
                        $priceWithoutTax = $data['price'] * $quantity * $data['rental_duration'];
                    }

                    $cartItem['substantive_price'] = $priceWithoutTax; // original price change to modified price


                    $cartItemOptions = json_decode($cartItem['additional'], true);

                    $product = RentMy::$Model['Products']->find()->where(['id'=> $cartItem['product_id']])->first();
                    $depositAmount = !empty($product['deposit_amount'])? $product['deposit_amount']: 0;
                    $cartItem['deposit_amount'] = $depositAmount * $quantity;

                    // get discount amount
                    if (!empty($cartItemOptions['discount_type'])) {
                        if ($cartItemOptions['discount_type'] == 'percent') {
                            $cartItem['off_amount'] = round(($cartItem['off_amount'] / $cartItem['quantity']) * $quantity, 2);
                        }
                        $cartItemOptions['discount_sub_total'] = ($cartItem['substantive_price'] - $cartItem['off_amount']);
                    }
                    // get coupon amount
                    if (!empty($cartItemOptions['coupon_type'])) {
                        if ($cartItemOptions['coupon_type'] == 'percent') {
                            $cartItem['coupon_amount'] = round(($cartItem['coupon_amount'] / $cartItem['quantity']) * $quantity, 2);
                        }
                        $cartItemOptions['coupon_sub_total'] = ($cartItem['substantive_price'] - ($cartItem['coupon_amount'] + $cartItem['off_amount']));
                    }


                    $cartItem['sub_total'] = $priceWithoutTax - ($cartItem['off_amount'] + $cartItem['coupon_amount']);
                    // coupon automatic discount
                    $coupon = $this->Coupons->fixedOfferCoupon([
                        'cart_id' => $cart->id, 'token' => $data['token'],
                        'product_id' => $cartItem['product_id'], 'sub_total' => $cartItem['sub_total'],
                        'quantity' => ($quantity),
                        'price' => $data['price']
                    ]);
                    $cartItem['sub_total'] = $coupon['sub_total'];
                    if(!empty($coupon['off_amount'])) {
                        $cartItem['automatic_coupon_amount'] = $coupon['off_amount'];
                        $cartItemOptions['automatic_coupon_sub_total'] =  $coupon['sub_total'];
                        $cartItemOptions['automatic_coupon_discount'] = $coupon['off_amount'];
                        if ($cartItem['sub_total'] < $coupon['off_amount']){
                            $cartItemOptions['automatic_coupon_sub_total'] = 0;
                            $cartItem['sub_total'] = 0;
                        }
                    }else{
                        $cartItem['automatic_coupon_amount'] = 0;
                        unset($cartItemOptions['automatic_coupon_sub_total']);
                        unset($cartItemOptions['automatic_coupon_discount']);
                    }

                    //$tax = $priceWithoutTax * $data['sales_tax'] * 0.01;
                    $cartItem['total'] = $priceWithoutTax;
                    $cartItem['quantity'] = $quantity;
                    $cartItem['additional'] = json_encode($cartItemOptions);

                    if ($this->Carts->CartItems->save($cartItem)) {
                        $this->Carts->CartItems->updateChildQuantity($cartItem);
                        $this->Carts->CartItems->updateCartAddon($cartItem); // updating the add on item here while data is saved
                    }

                } else {
                    $data['rental_duration'] = (!empty($data['rental_duration'])) ? $data['rental_duration'] : $cartItem->rental_duration;
                    if (empty($data['rental_duration'])) $data['rental_duration'] = 1;
                    $cartItem->rental_duration = ($cartItem['rental_type'] != 'buy') ? $data['rental_duration'] : null;
                    if (isset($data['option_id'])) {
                        RentMy::$Model['OrderProductOptions']->updateQuantity($cartItem->id, false, 'cart', $data['option_id']);
                        $price =   RentMy::$Model['OrderProductOptions']->getAverage('cart',$cart->id,$cartItem->id);
                        $data['price'] = !empty($price['unit_price']) ? $price['unit_price'] : $data['price'];
                        $priceWithoutTax = !empty($price['sub_total']) ? $price['sub_total'] : ( $data['price'] * ($cartItem['quantity'] - 1) *  $data['rental_duration']);
                    }else{
                        $priceWithoutTax = $data['price'] * ($cartItem['quantity'] - 1) * $data['rental_duration'];
                    }
                    $cartItem['substantive_price'] = $priceWithoutTax; // original price
                    $quantity = $cartItem['quantity'] - 1;
                    if ($data['option_level'] == 'child')
                        $quantity = $cartItem['quantity'];
                    $cartItemOptions = json_decode($cartItem['additional'], true);

                    // get discount amount
                    if (!empty($cartItemOptions['discount_type'])) {
                        if ($cartItemOptions['discount_type'] == 'percent') {
                            $cartItem['off_amount'] = round(($cartItem['off_amount'] / $cartItem['quantity']) * $quantity, 2);
                        }
                        $cartItemOptions['discount_sub_total'] = ($cartItem['substantive_price'] - $cartItem['off_amount']);
                    }
                    // get coupon amount
                    if (!empty($cartItemOptions['coupon_type'])) {
                        if ($cartItemOptions['coupon_type'] == 'percent') {
                            $cartItem['coupon_amount'] = round(($cartItem['coupon_amount'] / $cartItem['quantity']) * $quantity, 2);
                        }
                        $cartItemOptions['coupon_sub_total'] = ($cartItem['substantive_price'] - ($cartItem['coupon_amount'] + $cartItem['off_amount']));
                    }

                    $cartItem['sub_total'] = $priceWithoutTax - ($cartItem['off_amount'] + $cartItem['coupon_amount']);

                    // coupon automatic discount
                    $coupon = $this->Coupons->fixedOfferCoupon([
                        'cart_id' => $cart->id, 'token' => $data['token'],
                        'product_id' => $cartItem['product_id'], 'sub_total' => $cartItem['sub_total'], 'quantity' => ($cartItem['quantity'] - 1), 'price' => $data['price']
                    ]);
                    $cartItem['sub_total'] = $coupon['sub_total'];
                    if(!empty($coupon['off_amount'])) {
                        $cartItem['automatic_coupon_amount'] = $coupon['off_amount'];
                        $cartItemOptions['automatic_coupon_sub_total'] =  $coupon['sub_total'];
                        $cartItemOptions['automatic_coupon_discount'] = $coupon['off_amount'];
                        if ($cartItem['sub_total'] < $coupon['off_amount']){
                            $cartItemOptions['automatic_coupon_sub_total'] = 0;
                            $cartItem['sub_total'] = 0;
                        }
                    }else{
                        $cartItem['automatic_coupon_amount'] = 0;
                        unset($cartItemOptions['automatic_coupon_sub_total']);
                        unset($cartItemOptions['automatic_coupon_discount']);
                    }

                    //$tax = $priceWithoutTax * $data['sales_tax'] * 0.01;
                    $cartItem['total'] = $priceWithoutTax;
                    $cartItem['quantity'] = $quantity;
                    $product = RentMy::$Model['Products']->find()->where(['id'=>$cartItem['product_id']])->first();
                    $depositAmount = !empty($product['deposit_amount'])? $product['deposit_amount']: 0;
                    $cartItem['deposit_amount'] = $depositAmount * $quantity;

                    $cartItem['additional'] = json_encode($cartItemOptions);
                    if ($this->Carts->CartItems->save($cartItem)) {
                        $this->Carts->CartItems->updateChildQuantity($cartItem);
                       // if (isset($data['option_id']))
                           //  RentMy::$Model['OrderProductOptions']->updateQuantity($cartItem->id, false, 'cart', $data['option_id']);
                        $this->Carts->CartItems->updateCartAddon($cartItem); // updating the add on item here while data is saved
                    }
                }
                $this->Carts->_updatePriceNQty($cart);
                //RentMy::$Model['OrderCharge']->changesRecalculate('', $cart->id);
                $cartDetails = $this->Carts->getCartDetails($cart->id);
                if (RentMy::$storeConfig['tax']['address'] == 'store') {
                    $cartDetails = $this->Carts->addTaxLookup($cartDetails);
                }
                if (!empty($data['view_token'])) { // pos screen customer view
                    (new PosCustomerView($data['view_token']))->save('cart', ['cart' => $cartDetails]);
                }
                $this->apiResponse['data'] = $cartDetails;


            } else {
                throw new Exception('Bad request! Invalid cart id.');
            }
        } catch (Exception $ex) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = $ex->getMessage();
        }
    }

    /**
     * @desc Update cart add on items
     * quantity increment or decrement
     * @param cart items array
     * @param increment(boolean)
     */
//    public function updateCartAddon($cartItem){
//        $this->add_model(['Carts', 'CartItems']);
//        $saved_items = [];
//        if($this->Carts->CartItems->save($cartItem)){
//            $addon_items = $this->Carts->CartItems->find()->where([
//                    'cart_id' => $cartItem->cart_id,
//                    'parent_id' => $cartItem->id,
//                    'product_type' => 3
//                ])->toArray();
//            if(!empty($addon_items)){
//                foreach($addon_items as $item){
//                    $item->quantity = (int)$cartItem['quantity'] * (int)sizeof($addon_items);
//                    $saved_items[] = $this->Carts->CartItems->save($item);
//                }
//
//                return $saved_items;
//            }
//        }
//
//        return;
//    }

    /**
     * Apply discount to a cart-item
     * this is substantial discount
     * @param sub_total
     * @param off_amount
     * @API - POST carts/add-discount
     *
     */
    public function addDiscount()
    {
        $data = $this->request->getData();
        if (isset($data['cart_item_id']) && isset($data['token'])) {
            $this->add_model(array('Carts', 'CartItems'));
            $cart = $this->Carts->find()->where(['uid' => $data['token'], 'store_id' => RentMy::$store->id])->first();
            if (empty($cart)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Not found!';
                return;
            }
            $cartItem = $this->CartItems->get($data['cart_item_id']);
            if ($cart->id != $cartItem->cart_id) {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = 'Access denied!';
                return;
            }
            $cartAdditions = json_decode($cartItem->additional, true);

            $discount = !empty($data['discount_amount']) ? $data['discount_amount'] : 0;
            if (empty($data['off_amount'])) {
                $cartAdditions['discount_type'] = 'amount';
            } else {
                $cartAdditions['discount_type'] = 'percent';
            }

            $coupon_discount = $cartItem->coupon_amount;
            $cartItem['sub_total'] = $cartItem['substantive_price'] - ($discount + $coupon_discount + $cartItem->automatic_coupon_amount);
            $cartItem['off_amount'] = $discount;
            $cartAdditions['subtaintial_note'] = $data['note'];
            $cartAdditions['discount'] = $discount;
            $cartAdditions['discount_sub_total'] = $cartItem['sub_total'] + $cartItem['product_option_price'];
            $cartItem['additional'] = json_encode($cartAdditions);
            $cartItem['total'] = $this->CartItems->cartItemTotal($cartItem);
            if ($this->CartItems->save($cartItem)) {

                $this->Carts->_updatePriceNQty($cart);
                $cartInfo = $this->Carts->getCartDetails($cart->id);
                if (RentMy::$storeConfig['tax']['address'] == 'store') {
                    $cartInfo = $this->Carts->addTaxLookup($cartInfo);
                }
                if (!empty($data['view_token'])) { // pos screen customer view
                    (new PosCustomerView($data['view_token']))->save('cart', ['cart' => $cartInfo]);
                }
                $this->apiResponse['data'] = array('success' => true, 'message' => 'Successfully updated.', 'cart' => $cartInfo);
            }
        } else {
            $this->apiResponse['data'] = array('success' => false, 'message' => 'Method not allowed.');

        }
    }

    /**
     * @desc Gives the details of a cart (cart of a logged in user)
     * @param cart $token
     * @API  - GET - /carts/:token
     */
    public function details($token)
    {
        try {
            $this->loadModel('Carts');
            $cart = $this->Carts->find()->where(['Carts.uid' => $token, 'order_id IS NULL', 'store_id'=>RentMy::$store->id])->first();
            if ($cart) {
                $details = $this->Carts->getCartDetails($cart->id, $this->request->getQueryParams());
                if (!empty($this->request->getQueryParams()['view_token'])) { // pos screen customer view
                    (new PosCustomerView($this->request->getQueryParams()['view_token']))->save('cart', ['cart' => $details]);
                }
                if ($details) {
                    $this->httpStatusCode = 200;
                    $this->apiResponse['data'] = $details;
                } else {
                    throw new Exception('No cart details found for the given user.');
                }
            } else {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Invalid cart token!';
            }

        } catch (Exception $ex) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = $ex->getMessage();
        }
    }

    /**
     * delete(soft) a cart
     * @param cart $token
     */
    public function delete($token = null)
    {
        if ($token != null) {
            $this->add_model(array('Carts'));
            $cart = $this->Carts->find()->where(['uid' => $token])->first();
            if (empty($cart)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Not found!';
                return;
            }
            if ($this->Carts->clearCart($cart->id)) {
                $this->apiResponse['message'] = 'Successfully deleted.';
            } else {
                $this->apiResponse['message'] = 'Can not deleted.';
            }
        } else {
            $this->apiResponse['message'] = 'Empty Cart ID.';
        }
    }

    /**
     * add buy Package To Cart
     * checking availability
     *
     * @return cart details
     */

    public function addPackageToCartForBuyItems()
    {

        $this->add_model(array('Carts', 'Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'CartItems', 'ProductPrices', 'VariantsProducts', 'Holidays'));
        RentMy::addModel(['ProductsAvailabilities']);
        $data = $this->request->getData();

        $data['store_id'] = RentMy::$store->id;
        $data['sales_tax'] = $this->productContainer->getSalesTax($data['package_id'], $data['store_id'], 'buy');

        unset($data['rent_start']);
        unset($data['rent_end']);
        $requiredParam = ['package_id', 'variants_products_id', 'location'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['error'] = 'Required field missing';
            return;
        }

        $quantity = $this->Quantities->find()->where(['product_id' => $data['package_id'], 'location' => $data['location'], 'variants_products_id' => $data['variants_products_id']])->first();
        if (empty($quantity)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Out of stock';
        }
        $data['quantity_id'] = $quantity->id;

        $responseOption = RentMy::$storeConfig;
        $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];

        $data = $this->_getPrice($data, $optionPrice);
        $productAvailable = array();

        foreach ($data['products'] as &$item) {
            $quantity = $this->Quantities->find()
                ->where(['variants_products_id' => $item['variants_products_id']])
                ->where(['location' => $data['location']])
                ->first();
            if (empty($quantity)) {
                $this->apiResponse['error'] = 'Out of Stock!';
                return;
            }
            $item['quantity_id'] = $quantity->id;

            $rent_start = Time::now()->format('Y-m-d H:i');
            $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
            $pAvailable = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($item['product_id'], $item['variants_products_id'], $data['location'], $rent_start, $rent_end);
            $productAvailable[] = ['product_id' => $item['product_id'], 'variants_products_id' => $item['variants_products_id'], 'available' => $pAvailable];

            if ($pAvailable < $item['quantity']) {
                $this->apiResponse['error'] = 'Not available!';
                return;
            }
        }

        $result = $this->Carts->addPackageToCart($data);

        if ($result['success']) {
            $this->apiResponse['data'] = $result['data'];
            return;
        } else {
            $this->apiResponse['error'] = $result['message'];
            return;
        }
    }


    /**
     * @desc Add item to cart
     * checking availability
     *
     * $data['rent_end'] = "2019-03-06 09:02"
     * $data['rent_start'] = "2019-03-03 07:02"
     */
    public function v2add()
    {
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();
        RentMy::addModel(['Carts', 'Products', 'Quantities', 'CartItems']);
        // update quantity of any cart item when date is missing.
        if ($data['action'] == 'update-cart-item') {
            $requiredParam = ['id', 'token'];
            if (!$this->array_keys_exist($data, $requiredParam)) {
                throw new MissingParamsException('Required field missing');
                return;
            }
            $cartItem = RentMy::$Model['CartItems']->find()->where(['id' => $data['id'], 'store_id' => RentMy::$store->id])->first();
            $data['product_id'] = $cartItem['product_id'];
            $data['quantity'] = ($data['increment'] == 1) ? 1 : -1;
            $data['rental_type'] = $cartItem['rental_type'];
            $data['variants_products_id'] = $cartItem['variants_products_id'];
            $data['product_type'] = $cartItem['product_type'];
        }


        $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            throw new MissingParamsException('Required field missing');
            return;
        }
        if (empty($data['rent_start']) && empty($data['rent_end'])) {
            // get quantity id
            $quantity = RentMy::$Model['Quantities']->find()->where(['product_id' => $data['product_id'], 'location' => $data['location'], 'variants_products_id' => $data['variants_products_id']])->first();
            $data['quantity_id'] = $quantity->id;
            if (empty($quantity->id)) {
                throw new UnauthorizedException('Invalid request');
            }

            // get variant chain
            $variant = RentMy::$Model['Products']->_getDefaultAttribute($data['variants_products_id']);
            $data['variant_chain_id'] = $variant['variant_chain_id'];
            $data['variant_chain_name'] = $variant['variant_chain_name'];


            // add to cart
            $details = RentMy::$Model['Carts']->addToCartWithoutRentalDates($data);
            if ($details) {
                $this->apiResponse['data'] = $details;
            } else {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = "This item can't be added in cart";
            }

        }

    }

    public function abandoned()
    {
        try {


        RentMy::addModel(['Carts', 'CartItems', 'Customers', 'CartItems.Products']);
        $pageNo = 1;
        $limit = 10;
        $options = array();
        $data = array();
        $options = array_merge(array('Carts.order_id IS NULL'), $options);
        $options = array_merge(array('Carts.store_id' => RentMy::$store->id), $options);
        $options = array_merge(array('Carts.location' => RentMy::$token->location), $options);
        $options = array_merge(array('Carts.status' => 1), $options);
        $currentTime = FrozenTime::now()->subMinutes(20)->format('Y-m-d H:i:s');
        $options = array_merge(array('Carts.created <='=> $currentTime), $options);

        if (!empty($this->request->getQuery())) {
            $data = $this->request->getQuery();

            if (!empty($data['customer_name'])) {
                $options = array_merge(
                    [
                        'OR' => [
                            ['Carts.options LIKE' => '%"first_name":"%' . $data['customer_name'] . '%"%'],
                            ['Carts.options LIKE' => '%"last_name":"%' . $data['customer_name'] . '%"%'],
                        ]
                    ],
                    $options
                );
            }

            if (!empty($data['customer_email'])) {
                $options = array_merge(
                    $options,
                    ['Carts.options LIKE' => '%"email":"' . $data['customer_email'] . '%']
                );
            }

            if (!empty($data['order_value'])){
                $options = array_merge(
                    $options,
                    ['Carts.sub_total >=' => $data['order_value']]
                );
            }

            if (!empty($data['start_date'])){
                $startDate = Time::parse($data['start_date'])->format('Y-m-d');
                $options = array_merge(
                    $options,
                    ["DATE_FORMAT(Carts.rent_start,'%Y-%m-%d')" => $startDate]
                );
            }

            if (!empty($data['page_no'])) {
                $pageNo = $data['page_no'];
            }

            if (!empty($data['limit'])) {
                $limit = $data['limit'];
            }
        }

        $offset = ($pageNo - 1) * $limit;
        $abandonedCartsObj = RentMy::$Model['Carts']->find()
            ->contain(['Customers', 'CartItems', 'CartItems.Products'])
            ->InnerJoinWith('CartItems', function ($q) use ($data) {
                if (!empty($data['product_id'])) {
                    return $q->where(['product_id' => $data['product_id']]);
                }
                return $q;
            })
            ->where($options)
            ->group('Carts.id');
        if (empty($data['export'])){

        $abandonedCarts = $abandonedCartsObj->offset($offset)
            ->limit($limit)
            ->order(['Carts.id' => 'DESC'])
            ->map(function ($cart){
                $cart['options'] = !empty($cart['options']) ? json_decode($cart['options'], true): [];
                return $cart;
            })
            ->toArray();

        }else{
            if ($data['export'] == 'excel') {
                $exportData = [
                    [
                        "Cart Id",
                        "Customer Name",
                        "Email",
                        "Phone",
                        "Rent Start",
                        "First Product Name",
                        "Created"
                    ]
                ];
                if (!empty($data['cart_ids'])) {
                    $abandonedCartsObj = $abandonedCartsObj->where(['Carts.id' => explode(',', $data['cart_ids'])]);
                }
                $abandonedCartsObj
                    ->order(['Carts.id' => 'DESC'])
                    ->map(function ($cart) use (&$exportData) {
                        $options = !empty($cart['options']) ? json_decode($cart['options'], true): [];

                        $exportData[] = [
                            $cart['id'],
                            $options['billing_info']['first_name'] . ' ' . $options['billing_info']['last_name'],
                            $options['billing_info']['email']  ?? '',
                            $options['billing_info']['mobile'] ?? '',
                            !empty($cart['rent_start']) ? RentMy::toStoreTimeZone($cart['rent_start'], 'Y-m-d H:i')  : '',
                            $cart['cart_items'][0]['product']['name'] ?? '',
                            Time::parse($cart['created'])->format('Y-m-d H:i'),
                        ];

                    })
                    ->toArray();

                $this->apiResponse['data'] = $exportData;
                return;
            }
        }

        $total = $abandonedCartsObj->count();

        $this->apiResponse['page_no'] = $pageNo;
        $this->apiResponse['limit'] = $limit;
        $this->apiResponse['total'] = $total;
        $this->apiResponse['data'] = $abandonedCarts;

        }catch (\Throwable $throwable){
            RentMy::dbgAndEnd($throwable->getMessage());
        }
    }
    /**
     * @POST checkout/process
     * this function will check prerequisite of checkout
     * @return void
     */
    public function checkoutProcess(){
        $data = $this->request->getData();
        $response = [];
        $customerId = !empty($data['customer_id'])?$data['customer_id']:RentMy::$token->customer_id;
        if (!empty($customerId)){
            RentMy::addModel(['Customers']);
            $customer = RentMy::$Model['Customers']->find()->where(['id'=>$customerId])->first();
            $options = !empty($customer->optional)?json_decode($customer->optional, true):[];
            $response['is_subscribed'] = !empty($customer['subscription_id']) && empty($options['optional']['is_subscription_canceled']);
        }

        $this->apiResponse['data'] = $response;
    }

    public function sendAbandonedEmail($id)
    {
        RentMy::addModel(['Carts', 'CartItems', 'ProductsAvailabilities']);
        $storeId = RentMy::$store->id;
        $cart = RentMy::$Model['Carts']->checkAvailability($id);

        if (empty($cart)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Some of the items of this cart are not available";
            return;
        }

        RentMy::addNotificationQueue('abandoned_cart', $storeId,['cart_id' => $cart['id'], 'store_id' => $storeId, 'location' => $cart['location'], 'created' => Time::parse($cart['created'])->format('Y-m-d H:i:s')]);
        $this->apiResponse['message'] = "Notification has been sent";
    }
    public function sendAbandonedBulkEmail()
    {
        $data = $this->request->getData();
        if (empty($data['cart_ids'])){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Cart IDs is required";
            return;
        }
        $unavailableCarts = [];
        RentMy::addModel(['Carts', 'CartItems', 'ProductsAvailabilities']);
        $carts = RentMy::$Model['Carts']->find()->where(['id IN' => $data['cart_ids']])
            ->contain(['CartItems' => function ($q) {
                return $q->where([
                    'OR' => [
                        'CartItems.parent_id IS NULL',
                        'CartItems.parent_id' => 0,
                    ]
                ]);
            }])->toArray();
        foreach ($carts as $cart){
            $check = RentMy::$Model['Carts']->checkAvailability($cart['id'], $cart);

            if (empty($check)){
                $unavailableCarts[] = $cart['id'];
                continue;
            }

            RentMy::addNotificationQueue('abandoned_cart', RentMy::$store->id,['cart_id' => $cart['id'], 'store_id' => RentMy::$store->id, 'location' => $cart['location'], 'created' => Time::parse($cart['created'])->format('Y-m-d H:i:s')]);
        }
        $this->apiResponse['unavailable_carts'] = $unavailableCarts;
        $this->apiResponse['message'] = "Notifications will send shortly";
    }
    /**
     * @POST /carts/:token/update-checkout-info
     * @return mixed
     */
    public function updateCheckoutInfo($token)
    {
        RentMy::addModel(['Carts']);
        $data = $this->request->getData();
        $cart = RentMy::$Model['Carts']->find()->where(['uid' => $token])->first();
        if (empty($cart)) {
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'Invalid cart!';
            return;
        }

        $options = !empty($cart['options']) ? json_decode($cart['options'], true) : [];

        if (isset($data['billing_info']))
            $options['billing_info'] = $data['billing_info'];

        if (isset($data['fulfilment_info']))
            $options['fulfilment_info'] = $data['fulfilment_info'];

        if (isset($data['additional']))
            $options['additional'] = $data['additional'];

        $cart['options'] = json_encode($options);
        if (RentMy::$Model['Carts']->save($cart)){
            $this->apiResponse['message'] = 'Cart information saved';
            return;
        }
    }

    public function deleteCart()
    {
        $data = $this->request->getQueryParams();
        if (empty($data['cart_ids'])){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Cart IDs is required";
            return;
        }

        RentMy::addModel(['Carts']);
        RentMy::$Model['Carts']->deleteAll([
            'id IN' => explode(',', $data['cart_ids'])
        ]);

        $this->apiResponse['message'] = "Cart information has been deleted";

    }
}
