<?php

namespace App\Controller;

use App\Exception\MissingParamsException;
use App\Lib\Payment\AuthorizeNet\AuthorizeNet;
use App\Lib\Payment\BoltCardConnect;
use App\Lib\Payment\ConvenuPay;
use App\Lib\Payment\DeltaPay;
use App\Lib\Payment\Empyrean;
use App\Lib\Payment\FreedomPay;
use App\Lib\Payment\goMerchant\goMerchant;
use App\Lib\Payment\Midtrans;
use App\Lib\Payment\Pax;
use App\Lib\Payment\Payment;
use App\Lib\Payment\Square;
use App\Lib\Payment\Stripe\Charge;
use App\Lib\Payment\Stripe\EnduringRental;
use App\Lib\Payment\Stripe\Stripe;
use App\Lib\Payment\Stripe\StripeIntent;
use App\Lib\Payment\Stripe\Subscription;
use App\Lib\Payment\Transafe;
use App\Lib\Payment\ValorPay\ValorPay;
use App\Lib\ProductContainer;
use App\Lib\RentMy\Availability;
use App\Lib\RentMy\Delivery;
use App\Lib\RentMy\Email;
use App\Lib\RentMy\Order;
use App\Lib\RentMy\RentMy;
use App\Lib\S3;
use App\PaymentGateway\AuthorizeDOTNET;
use App\PaymentGateway\StripePayment;
use App\PaymentGateway\PayPalPayment;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\Datasource\ConnectionManager;
use Cake\Event\Event;
use Cake\Http\Exception\NotFoundException;
use Cake\Http\Exception\UnauthorizedException;
use Cake\I18n\FrozenTime;
use Cake\I18n\Number;
use Cake\I18n\Time;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use DateTime;
use Davaxi\VCalendar;
use Exception;

class OrdersController extends AppController
{
    private $storeId, $userId, $productContainer;

    public function initialize()
    {
        parent::initialize();
        $this->productContainer = new ProductContainer();
    }

    public function beforeFilter(Event $event)
    {

        parent::beforeFilter($event);
        $this->storeId = $this->parseToken->store_id;
        $this->userId = $this->parseToken->id ?? 0;
    }

    /**
     * all order of an user
     */
    public function userOrder()
    {
        $userId = $this->jwtPayload->id;

        $order = $this->Orders->userOrder($userId);

        if (!empty($order)) {
            $this->apiResponse['data'] = $order;
        } else {
            $this->apiResponse['data'] = [];
        }
    }


    /**
     * all orders with filtering
     */
    public function index()
    {
        $pageNo = 1;
        $limit = 10;
        $options = array();
        $orderBy = 'created';
        $order = 'DESC';
        $tableName = 'Orders';
        $this->add_model(array('Orders', 'Locations', 'OrderItem', 'Products', 'OrderAsset', 'Assets', 'DeliveryDetails'));
        $locations = $this->Locations->find('list')->toArray();

        if (!empty($this->request->getQuery())) {
            $data = $this->request->getQuery();

            $data['location'] = RentMy::$token->location;

            if (!empty($data['email'])) {
                $options = array_merge(array('Orders.email' => $data['email']), $options);
            }
            if (!empty($data['id'])) {
                if (!empty(RentMy::$storeConfig['order']['order_sequence']['active'])){
                    $prefix = RentMy::getOrderPrefix();
                    $id = str_replace($prefix, '', $data['id']);
                    $options = array_merge(array('Orders.store_order_id' => $id), $options);
                }else{
                    $options = array_merge(array('Orders.id' => $data['id']), $options);
                }

            }
            if (!empty($data['payment_type'])) {
                $options = array_merge(array('Orders.payment_type' => $data['payment_type']), $options);
            }

            if (!empty($data['payment_status'])) {
                $options = array_merge(array('Orders.payment_status' => $data['payment_status']), $options);
            }

            if (!empty($data['shipping_method'])) {
                $options = array_merge(array('Orders.shipping_method' => $data['shipping_method']), $options);
            }

            if (isset($data['rental_orders']) && $data['rental_orders'] == 1) {
                $statusArr = [3, 5];
                $options = array_merge(array('Orders.status IN' => $statusArr), $options);
                $options = array_merge(array("Orders.purchase_type LIKE '%" . 'rent' . "%'"), $options);

            }
            if (isset($data['order_type']) && $data['order_type'] != null) {
                if ($data['order_type'] == 1) {
                    $options = array_merge(array("Orders.purchase_type LIKE '%" . 'buy' . "%'"), $options);

                } else if ($data['order_type'] == 2) {
                    $options = array_merge(array("Orders.purchase_type LIKE '%" . 'rent' . "%'"), $options);
                }
            }
            if (!empty($data['created_start']) && !empty($data['created_end'])) {
                $createdStart = Time::parse($data['created_start'])->format('Y-m-d 00:00:00');
                $createdStart = RentMy::toUTC($createdStart);
                $createdEnd = Time::parse($data['created_end'])->format('Y-m-d 23:59:59');
                $createdEnd =  RentMy::toUTC($createdEnd);
                $options = array_merge(array('Orders.created >=' => $createdStart,'Orders.created <='=> $createdEnd), $options);
            }

            if (!empty($data['start_date'])) {
                $dateStart = RentMy::toUTC($data['start_date'], 'Y-m-d H:i:00');

                //$endDate = empty($data['end_date']) ? $data['start_date'] : $data['end_date'];

                $endDate= Time::parse($data['end_date'])->format('Y-m-d 23:59:00');
                $dateEnd = RentMy::toUTC($endDate);

                //$dateEnd = RentMy::toUTC($endDate, 'Y-m-d 23:59:00');

                $options = array_merge(array("Orders.purchase_type LIKE '%" . 'rent' . "%'"), $options);
                $options = array_merge(array("Orders.rent_start >=" => $dateStart), $options);
                $options = array_merge(array("Orders.rent_start <=" => $dateEnd), $options);
                //$options = array_merge(array("Orders.rent_start >=" => $dateStart), $options);
                //$options = array_merge(array("Orders.rent_end <=" => $dateEnd), $options);
                unset($options['Orders.created >=']);
                unset($options['Orders.created <=']);
            }

            $options = array_merge(array('Orders.type' => 1), $options);

            if (!empty($data['phone'])) {
                $options = array_merge(array('Orders.mobile' => $data['phone']), $options);
            }
            if (!empty($data['salesman'])) {
                $options = array_merge(array('Orders.salesman' => $data['salesman']), $options);
            }
            if (!empty($data['location'])) {
                $options = array_merge(array('Orders.location' => $data['location']), $options);
            }
            if (!empty($data['address'])) {
                $options = array_merge(array("( Orders.address_line1 LIKE '%" . $data['address'] . "%' OR Orders.city LIKE '%" . $data['address'] . "%' OR Orders.zipcode LIKE '%" . $data['address'] . "%' OR Orders.state_id LIKE '%" . $data['address'] . "%' OR Orders.country_id LIKE '%" . $data['address'] . "%' OR Orders.shipping_address1 LIKE '%" . $data['address'] . "%' OR Orders.shipping_address2 LIKE '%" . $data['address'] . "%' OR Orders.shipping_city LIKE '%" . $data['address'] . "%' OR Orders.shipping_country LIKE '%" . $data['address'] . "%' OR Orders.shipping_state LIKE '%" . $data['address'] . "%' OR Orders.shipping_zipcode LIKE '%" . $data['address'] . "%')"), $options);
            }
            if (!empty($data['city'])) {
                $options = array_merge(array('Orders.city LIKE "%' . $data['city'] . '%"'), $options);
            }
            if (!empty($data['state_id'])) {
                $options = array_merge(array('Orders.state_id LIKE "%' . $data['state_id'] . '%"'), $options);
            }
            if (!empty($data['zipcode'])) {
                $options = array_merge(array('Orders.zipcode LIKE "%' . $data['zipcode'] . '%"'), $options);
            }
            if (!empty($data['event_location'])) {
                $options = array_merge(array('Orders.event_location' => $data['event_location']), $options);
            }
            if (!empty($data['type'])) {
                $options = array_merge(array('Orders.type' => $data['type']), $options);
            }
            if (!empty($data['search'])) {
                $search = $data['search'];
                $search = strtolower($search);
                $str = "%" . trim($search) . "%";
                $options = array(
                    'OR' => array(
                        "CONCAT(trim(Orders.first_name),' ',trim(Orders.last_name)) LIKE '" .  $str . "'",
                        "Orders.email LIKE" => $str,
                        "Orders.phone LIKE" => $str,
                        "Orders.mobile LIKE" => $str,
                        "Orders.zipcode LIKE" => $str,
                        "Orders.state_id LIKE" => $str,
                        "Orders.address_line1 LIKE" => $str,
                        "Orders.city LIKE" => $str,
                        "Orders.country_id LIKE" => $str,
                        "Orders.shipping_address1 LIKE" => $str,
                        "Orders.shipping_address2 LIKE" => $str,
                        "Orders.shipping_city LIKE" => $str,
                        "Orders.shipping_country LIKE" => $str,
                        "Orders.shipping_state LIKE" => $str,
                        "Orders.shipping_zipcode LIKE" => $str,
                    )
                );
                if (!empty($data['location'])) {
                    $options['location'] = $data['location'];
                }
                // get order_ids from order_items
                // get order_ids from order assets .
                $ids = [];
                RentMy::addModel(['OrderItems', 'OrderAssets']);
                RentMy::$Model['OrderItems']->find()
                    ->select(['OrderItems.order_id'])
                    ->contain(['Products'])
                    ->where(['Products.name LIKE' => $str, 'Products.store_id' => RentMy::$store->id])
                    ->map(function ($q) use (&$ids) {
                        $ids[] = $q['order_id'];
                    })
                    ->toArray();
                RentMy::$Model['OrderAssets']->find()
                    ->select(['OrderAssets.order_id'])
                    ->contain(['Assets'])
                    ->where(['Assets.serial_no LIKE' => $str, 'Assets.store_id' => RentMy::$store->id])
                    ->map(function ($q) use (&$ids) {
                        $ids[] = $q['order_id'];
                    })
                    ->toArray();
//                $prefix = RentMy::getOrderPrefix();
//                $storeOrderId = str_replace($prefix, '', $str);
                $ids = array_unique($ids);
                if (!empty($ids))
                    $options['OR']['Orders.id IN'] = $ids;
                else{
                    $prefix = RentMy::getOrderPrefix();
                    $storeOrderId = str_replace($prefix, '', $data['search']);
                    if (!empty((int)$storeOrderId)){
                        if (!empty(RentMy::$storeConfig['order']['order_sequence']['active'])){
                            $options['OR']['Orders.store_order_id'] = (int)$storeOrderId;
                        }else{
                            $options['OR']['Orders.id'] = (int)$storeOrderId;
                        }
                    }

                }

            }

            if (!empty($data['status'])) {
                RentMy::addModel(['SystemStatus']);
                $expStatuses = explode(',', $data['status']);
                if (!empty($expStatuses)) {
                    $status = [];
                    foreach ($expStatuses as $expStatus) {

                        if ($expStatus == 4) {
                            $status[] = '4';
                            $status[] = '4-1';
                            $status[] = '4-2';
                            $status[] = '4-3';
                        } elseif ($expStatus == 7) {
                            $status[] = '7';
                            $status[] = '6-1';
                            $status[] = '6-2';
                            $status[] = '6-3';
                        } else {
                            $status[] = $expStatus;
                        }

                        if (!empty($data['with_child_status'])){
                            $childIds = RentMy::$Model['SystemStatus']->getChildStatusIds($expStatus);

                            foreach ($childIds as $childId){
                                $status[] = $childId;
                            }
                        }

                    }
                    $options = array_merge(array('Orders.status IN' => $status), $options);
                }
            }

            if (!empty($this->request->getQuery('page_no'))) {
                $pageNo = $this->request->getQuery('page_no');
            }
            if (!empty($this->request->getQuery('limit'))) {
                $limit = $this->request->getQuery('limit');
            }

            $orderBy = !empty($data['sort_by']) ? $data['sort_by'] : 'created';
            $order = !empty($data['sort_order']) ? $data['sort_order'] : 'DESC';

            if (!empty($data['product_id'])) {
                $where['product_id'] = $data['product_id'];
                if ($data['location'])
                    $where['location'] = $data['location'];

                RentMy::addModel(['OrderItems']);
                $items = RentMy::$Model['OrderItems']->find()->select(['id', 'order_id'])->where($where)->toArray();
                $ordersIds = [];
                foreach ($items as $item)
                    $ordersIds[] = $item->order_id;


                if (empty($ordersIds))
                    $ordersIds[] = 0;

                $options = array_merge(array('Orders.id IN' => array_unique($ordersIds)), $options);
            }

            if(!empty($data['delivery_date'])) {
                // $deliveryDate = $this->Timezones->_dateGlobal();
                $data['delivery_date'] =RentMy::toUTC($data['delivery_date'], 'Y-m-d'); //RentMy::$Model['Timezones']->_dateGlobal($data['start_date']);

                $method = 2; //todo can be change it to dynamic
                if (!empty($data['method']) && is_numeric($data['method']))
                    $method = $data['method'];

                $deliveryDetails = $this->DeliveryDetails->find();
                $ordersIds = $deliveryDetails->select(['order_id', 'id'])->where([
                    "DATE(delivery_date) >=" => $data['delivery_date'],
                    "DATE(delivery_date) <=" => $data['delivery_date'],
                    'store_id' => RentMy::$store->id,
                    'method' => $method
                ])->map(function ($deliveryD) {
                    return $deliveryD['order_id'];
                })->toArray();

                if (!empty($ordersIds))
                    $options = array_merge(array('Orders.id IN' => array_unique($ordersIds)), $options);
            }
        }

        $orderBy = $tableName . '.' . $orderBy;
        $offset = ($pageNo - 1) * $limit;
        $allData = array();

        if (!empty($data['is_search'])) {
            $options['Orders.store_id'] = RentMy::$store->id;
            if (!empty($data['location'])) {
                $options['Orders.location'] = $data['location'];
            }

            if (!empty($data['site_id'])) {
                $siteIds = explode(',', $data['site_id']); // Split the comma-separated list into an array
                $siteConditions = [];

                foreach ($siteIds as $siteId) {
                    $siteConditions[] = ["Orders.options LIKE" => '%"site_id":' . trim($siteId) . '%'];
                }

                // Add an OR condition for the site_id checks
                $options[] = ['OR' => $siteConditions];
            }
            $orderData = $this->Orders->find()
                ->where($options);

            if (!empty($data['name'])) {
                $names = explode(' ', trim($data['name']));
//                $nameConditions = [];
//                foreach ($names as $name)
//                    $nameConditions = array_merge(["Orders.first_name LIKE '%" . $name . "%'", "Orders.last_name LIKE '%" . $name . "%'"], $nameConditions );
//
//
//                $orderData->where(function (QueryExpression $exp) use ($nameConditions) {
//                    $orConditions = $exp->or($nameConditions);
//                    return $exp->add($orConditions);
//                });

                $orderData->where(function (QueryExpression $exp) use ($data) {
                    $orConditions = $exp->or(["CONCAT(trim(Orders.first_name),' ',trim(Orders.last_name)) LIKE '%" .  trim($data['name']) . "%'"]);
                    return $exp->add($orConditions);
                });
            }

            if ($data['recurring']) {
                $orderData = $orderData->join([
                    'table' => 'orders_recurring', 'alias' => 'OrdersRecurring', 'type' => 'INNER', 'conditions' => 'Orders.id = OrdersRecurring.order_id',
                ])->group('OrdersRecurring.order_id');
            }


            if (!empty($data['ship_customer_name']) || !empty($data['ship_customer_email']) || !empty($data['ship_customer_address']) || !empty($data['company'])) {
                $orderData->join([
                    'table' => 'order_addresses', 'alias' => 'OrderAddresses', 'type' => 'INNER', 'conditions' => 'Orders.id = OrderAddresses.order_id',
                ]);

                if (!empty($data['ship_customer_name']))
                    $orderData->where(function (QueryExpression $exp) use ($data) {
                        $orConditions = $exp->or(["OrderAddresses.shipping_first_name LIKE '%" . $data['ship_customer_name'] . "%'", "OrderAddresses.shipping_last_name LIKE '%" . $data['ship_customer_name'] . "%'"]);
                        return $exp->add($orConditions);
                    });

                if (!empty($data['ship_customer_email']))
                    $orderData->where(function (QueryExpression $exp) use ($data) {
                        $orConditions = $exp->or(["OrderAddresses.shipping_email LIKE '%" . $data['ship_customer_email'] . "%'"]);
                        return $exp->add($orConditions);
                    });

                if (!empty($data['company']))
                    $orderData->where(function (QueryExpression $exp) use ($data) {
                        if (!empty($data['source']) && $data['source'] == 'ionic-rental') {
                            $orConditions = $exp->or(["OrderAddresses.company LIKE '" . $data['company'] . "'"]);
                        }else{
                            $orConditions = $exp->or(["OrderAddresses.company LIKE '%" . $data['company'] . "%'"]);
                        }
                        return $exp->add($orConditions);
                    });

                if (!empty($data['ship_customer_address']))
                    $orderData->where(function (QueryExpression $exp) use ($data) {
                        $orConditions = $exp->or(["OrderAddresses.shipping_address1 LIKE '%" . $data['ship_customer_address'] . "%'", "OrderAddresses.shipping_address2 LIKE '%" . $data['ship_customer_address'] . "%'"]);
                        return $exp->add($orConditions);
                    });
            }

            if (empty($data['status'])){
                $orderData->where(['Orders.status NOT IN' => [1, 18]]);
            }
            $orders = $orderData->contain(['OrderItems'])
                ->offset($offset)
                ->limit($limit)
                ->order([$orderBy => $order])
                ->toArray();
            $total = $orderData->count();

        } else {
            if ($this->request->getQuery('overdue')) {

                RentMy::addModel(['SystemStatus']);

                //                for child status
                $statuses = [1, 6, 11, 12, 18];
                $systemStatuses = RentMy::$Model['SystemStatus']->find()
                    ->select(['id'])
                    ->where(['store_id IN' => [RentMy::$store->id, 'store_id' => 0]])
                    ->where(['status' => 1, 'content_type' => 'order', 'content_type_id IN'=>$statuses])
                    ->where(['reference_id IS NULL'])
                    ->toArray();
                $statusIds = [];
                foreach ($systemStatuses as $status)
                    $statusIds[] = $status['id'];


                if (!empty($statusIds)){
                    $childStatus = RentMy::$Model['SystemStatus']->find()
                        ->select(['id'])
                        ->where(['store_id IN' => [RentMy::$store->id]])
                        ->where(['status' => 1, 'content_type' => 'order', 'parent_id IN'=>$statusIds])
                        ->toArray();

                    foreach ($childStatus as $status)
                        $statuses[] = $status['id'];
                }
                //                for child status end

                $options[] = "Orders.purchase_type LIKE '%" . 'rent' . "%'";
                $options["DATE_FORMAT(Orders.rent_end,'%Y-%m-%d') < "] = RentMy::toUTC('today', 'Y-m-d');
                $options['Orders.status NOT IN'] = $statuses; // not archived, returned, completed & paid other
                //$options['Orders.payment_status'] = 2; // unpaid orders //disabled on RNTM-2414
                $orderData = $this->Orders->find();
                $orderData = $orderData->where(['Orders.store_id' => RentMy::$store->id])
                    ->join([
                        'table' => 'orders_recurring', 'alias' => 'OrdersRecurring', 'type' => 'LEFT', 'conditions' => ['Orders.id = OrdersRecurring.order_id', 'OrdersRecurring.payment_type'=>'manual'],
                    ])
                    ->join([
                        'table' => 'order_items', 'alias' => 'OrderRecurringItems', 'type' => 'LEFT', 'conditions' => [
                            'Orders.id = OrderRecurringItems.order_id',
                            'OrderRecurringItems.options LIKE '=>'%subscription_schedule_id%'
                        ],
                    ])
                    ->where(['OrdersRecurring.id is NULL'])->where(['OrderRecurringItems.id is NULL'])->where($options);

                $orders = $orderData->contain(['OrderItems'])->toArray();
                $total = $limit = $orderData->count();
            } else {
                //$orderData = $this->Orders->find()->where(['Orders.store_id' => RentMy::$store->id])->where(['Orders.status NOT IN' => [1, 18]])->where($options)->contain(['OrderItems', 'OrderAddresses']);
                $orderData = $this->Orders->find()->where(['Orders.store_id' => RentMy::$store->id])->where(['Orders.status NOT IN' => [1, 18]])->where($options)->contain(['OrderItems']);
                $orders = $orderData->offset($offset)->limit($limit)->order([$orderBy => $order])->toArray();
                $total = $orderData->count();
            }
        }


        if (!empty($orders)) {

            $orderIds = array_column($orders, 'id');
            RentMy::addModel(['OrderAddresses']);
            $orderAddresses = RentMy::$Model['OrderAddresses']->find()->select(['id', 'order_id', 'company'])->where(['order_id IN' => $orderIds])->order(['id' => 'desc'])->toArray();

            RentMy::addModel(['OrderProductOptions']);
            foreach ($orders as $row) {
                $a_data = array();
                $a_data['total_price'] = $this->Orders->getOrderTotal($row);
                $a_data['total'] = $this->Orders->getOrderTotal($row);
                $a_data['sub_total'] = $row->sub_total;
                $a_data['id'] = $row->id;
                $a_data['store_order_id'] = RentMy::getOrderPrefix() . $row->store_order_id;
                $a_data['type'] = $row->type;
                $a_data['tax'] = $row->tax;
                $a_data['customer_id'] = $row->customer_id;
                $a_data['delivery_charge'] = $row->delivery_charge;
                $a_data['delivery_tax'] = $row->delivery_tax;
                $a_data['delivery_date'] = $row->delivery_date;
                $a_data['created'] = RentMy::toStoreTimeZone($row->created);
                $a_data['total_quantity'] = $row->total_quantity;
                $a_data['name'] = $row->first_name . ' ' . $row->last_name;
                $a_data['address'] = $row->address_line1;
                $a_data['country'] = strtoupper($row->country_id);
                $a_data['state'] = $row->state_id;
                $a_data['city'] = $row->city;
                $a_data['zipcode'] = $row->zipcode;
                $a_data['status_str'] = RentMy::getOrderStatusStr($row->status);
                $a_data['status'] = $row->status;
                $a_data['shipping_method'] = $row->shipping_method;
                $a_data['payment_status'] = $row->payment_status;
                $a_data['total_discount'] = $row->total_discount;
                $a_data['total_deposit'] = $row->total_deposit;
                $a_data['coupon'] = $row->coupon;
                $a_data['event_location'] = $row->event_location;
                $a_data['rent'] = strpos($row->purchase_type, 'rent') !== false ? true : false;

                $pickup = '';
                if (!empty($row->pickup)) {
                    $pickup = $locations[$row->pickup];
                }
                $a_data['pickup'] = $pickup;
                $a_data['payment'] = $row->o_payment;
                $a_data['rent_start'] = !empty($row->rent_start) ? RentMy::toStoreTimeZone($row->rent_start) : null;
                $a_data['rent_end'] = !empty($row->rent_end) ? RentMy::toStoreTimeZone($row->rent_end) : null;
                $a_data['options'] = !empty($row['options']) ? json_decode($row['options'], true) : [];
                if (empty($data['overdue']))
                    $a_data['recurring_due'] = $this->recurringDue($row->id);

                if ($this->request->getQuery('exchange'))
                    $a_data['exchange'] = RentMy::$Model['OrderProductOptions']->hasExchangeable($row->id, 'order');

                $buyItems = array_values(array_filter($row['order_items'], function ($item){
                    return $item['rental_type'] == 'buy';
                }));

                $rentItems = array_values(array_filter($row['order_items'], function ($item){
                    return $item['rental_type'] != 'buy';
                }));

                $a_data['has_buy_item'] = !empty($buyItems);
                $a_data['has_rent_item'] = !empty($rentItems);

                $orderAddress = array_values(array_filter($orderAddresses, function ($orderAddress) use ($row){
                    return $orderAddress['order_id'] == $row->id;
                }));
                $a_data['company'] = !empty($orderAddress) ? $orderAddress[0]['company'] : '';

                $allData[] = $a_data;
            }

            $this->apiResponse['data'] = $allData;
            $this->apiResponse['page_no'] = $pageNo;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = $total;
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    private function recurringDue($orderId): bool
    {
        RentMy::addModel(['Orders', 'OrderRecurring']);
        $orderRecurrings = RentMy::$Model['OrderRecurring']->find()->where(['order_id'=>$orderId, 'store_id'=>RentMy::$store->id])->toArray();

        if (empty($orderRecurrings))
            return false;

        $totalPaid = 0;
        $totalDue = 0;
        foreach ($orderRecurrings as $orderRecurring){
            $totalPaid += $orderRecurring->amount_paid;
            $totalDue += $orderRecurring->amount_due;
        }
        return ($totalPaid < $totalDue);
    }

    public function reservationOrder()
    {
        $this->add_model(array('ProductsAvailabilities', 'OrderItems', 'Products'));
        $data = $this->request->getData();
        $order = $this->Orders->newEntity();
        if (!empty($data)) {
            $product = $this->Products->get($data['item']['product_id']);
            if ($this->OrderItems->checkAvailability($data['item'])) {
                $udata = $data['customer'];
                $order = $this->Orders->patchEntity($order, $udata);
                $order = $this->Orders->save($order);
                if ($order) {
                    $rdata = $data['availability'];
                    $reservation = $this->ProductsAvailabilities->find()->where(['id' => $rdata['id']])->first();
                    $rdata['order_id'] = $order['id'];
                    $reservation = $this->ProductsAvailabilities->patchEntity($reservation, $rdata);
                    $this->ProductsAvailabilities->save($reservation);
                    $aData = $data['item'];
                    $aData['order_id'] = $order['id'];
                    $this->Orders->addProduct($aData);
                }
                $reservation = $this->ProductsAvailabilities->find('all')->where(['id' => $rdata['id']])->first();
                $this->apiResponse['data'] = array('success' => true, 'reserve' => $reservation, 'quantity' => $product['quantity']);
            } else {
                $this->apiResponse['data'] = array('success' => false, 'reserve' => [], 'quantity' => $product['quantity']);
            }
        } else {
            $this->apiResponse['message'] = 'Method not allowed';
        }
    }

    public function makeOrder()
    {
        // Load models
        $errorMessage = 'Invalid Request';
        RentMy::addModel(['PaymentGateways', 'OrderItems', 'CartItems', 'Carts', 'Products', 'Coupons', 'Payments', 'Stores', 'StoresUsers', 'ProductsAvailabilities']);
        // check is post method && ip or device blocking issue
        if (!$this->request->is('post')) {
            $this->apiResponse['message'] = 'Invalid Request';
            return;
        }
        $data = $this->request->getData();
        if (empty($data['token'])) {
            $this->apiResponse['error'] = 'Cart Token is missing';
            return;
        }
        // check cart token &
        $cart = RentMy::$Model['Carts']->find()->where(['uid' => $data['token']])->first();
        if (empty($cart) || $cart->total_quantity < 1) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Invalid or Empty Cart.';
            return;
        }

        $data['user_id'] = RentMy::$token->id ?? 0;
        $data['store_id'] = RentMy::$token->store_id;
        $data['currency'] = RentMy::getCurrency();
        $data['cart_id'] = $cart->id;
        RentMy::saveLogFile('alert', 'store ID: ' . RentMy::$token->store_id . '-- Request Data: ' . json_encode($data), ['online']);

        // check required fields

        // check availability
        $checkAvailability = (new Availability())->cartItemsAvailability($data['cart_id']); // $this->_checkAvailability($data['cart_id']);
        if (!$checkAvailability['success']) {
            $this->apiResponse['data'] = ['availability' => $checkAvailability, 'payment' => []];
            return;
        }

        /** @TODO - new to organize coupon codes */
        if (!empty($cart->coupon_id)) {
            $coupon = $this->Coupons->getCouponData($cart->coupon_id);               // Coupon Validity Checking
            if (empty($coupon)) {                                                    // Coupon Apply
                $tax = $cart->sub_total * (Configure::read('taxValue') * 0.01);
                $cart->coupon_id = '';
                $cart->tax = $tax;
                $cart->total_discount = 0;
            }
        }


        $data['amount'] = $cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax - $cart->total_discount;


        (new Order())->makeOrder($data, $cart);
        exit();
        // Create Order
        $order = $this->_makeOrder($data, $cart, $payment_type);

        /* Check Payment*/

        if ($data['type'] == 1 && (!isset($data['swipe']) || ($data['swipe'] == false))) {
            $getPayment = $this->_getPayment($data);
            if ($getPayment['success']) {
                $payment = $getPayment['data'];
                $data['customerRef'] = !empty($payment['customerRef']) ? $payment['customerRef'] : [];
                $this->paymentLog(null, 'Payments', null, 1, json_encode($getPayment['data']));
            } else {
                $paymentData = array(
                    'availability' => array('success' => true),
                    'payment' => array('success' => false, 'message' => $getPayment['message']),
                );
                $this->paymentLog(null, 'Payments', null, 2, $getPayment['message']);

                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($paymentData), ['scope' => ['online']]);

                $this->apiResponse['data'] = $paymentData;
                return;
            }
        }

        // Get payment type
        $payment_type = $this->_getPaymentType($data);
        $data['status'] = $this->_getOrderStatus($data);

        // $order = $this->_makeOrder($data, $cart, $payment_type);
        $data['order_id'] = $order['id'];

        if ($data['type'] == 1 || $data['type'] == "Online") {
            /* Start Authorized data insert to payment table */
            $data['payment_method'] = 'Captured';
            $data['transaction_id'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['retref'] : $payment['transaction_id'];
            $data['response_text'] = json_encode($payment['data']);
            $data['content_id'] = isset($data['is_admin']) ? 2 : 1;
            $data['action_name'] = 'card-entry';
            $option = $data['payment_gateway_name'] == 'CardConnect' ? $payment['resptext'] : 'Approval';

            $this->_payment($data, $option);
        } else {
            $action_name = 'Additional';
            if (!empty($data['gateway_id'])) {
                $gateway = $this->PaymentGateways->get($data['gateway_id']);
                if ($gateway) {
                    $action_name = $action_name . '(' . $gateway->name . ')';
                }
            }

            $data['payment_method'] = 'Unpaid';
            $data['content_id'] = 6;
            $data['action_name'] = $action_name;
            $this->_payment($data, 'Online store – Additional');
        }


        // format billing & shipping address
        // check coupon
        // calculated order details & make order
        // create customer & address
        // make payment
        // add order log & payment log

    }


    /**
     * DEPRECATED FUNCTION
     * ALL SUN FUNCTION ALSO DEPRECATED
     *
     */

    public function add() // Unused
    {
        $this->add_model(array('OrderItems', 'CartItems', 'Carts', 'Products', 'Coupons', 'Payments', 'Stores', 'StoresUsers', 'ProductsAvailabilities'));
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (isset($data['token']) && !empty($data['token'])) {
                $data['user_id'] = isset($this->parseToken->id) ? $this->parseToken->id : 0;
                $data['store_id'] = $this->parseToken->store_id;
                $data['currency'] = isset($data['currency']) ? $data['currency'] : 'USD';

                $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
                if (empty($cart)) {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['error'] = 'Not found.';
                    return;
                }
                $data['cart_id'] = $cartId = $cart->id;

                if (isset($data['is_admin'])) {
                    $checkAvailability['success'] = true;
                } else {
                    $checkAvailability = $this->_checkAvailability($cartId);
                }
                $checkAvailability['success'] = 1;

                if ($checkAvailability['success']) {
                    if (!empty($cart->coupon_id)) {
                        $coupon = $this->Coupons->getCouponData($cart->coupon_id);               // Coupon Validity Checking
                        if (empty($coupon)) {                                                    // Coupon Apply
                            $tax = $cart->sub_total * (Configure::read('taxValue') * 0.01);
                            $cart->coupon_id = '';
                            $cart->tax = $tax;
                            $cart->total_discount = 0;
                        }
                    }
                    $data['amount'] = $cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax - $cart->total_discount;

                    $paymentStatus = true;

                    /* Check Payment*/
                    if ($data['type'] == 1 && (!isset($data['swipe']) || ($data['swipe'] == false))) {

                        if ($data['payment_gateway_name'] != '' && $data['payment_gateway_name'] != 'CardConnect') {

                            $param = [
                                'amount' => $data['amount'],
                                'store_id' => $data['store_id'],
                                'description' => 'rent my',
                                'currency' => $data['currency'],
                            ];

                            $card = !empty($data['card']) ? $data['card'] : [];

                            $payment = [];
                            switch ($data['payment_gateway_name']) {

                                case "Stripe":
                                    $param['account'] = $data['account'];
                                    $stripePayment = new StripePayment($param);
                                    $data['payment_method'] = 'Authorized';
                                    $data['action_name'] = 'card-entry';
                                    $payment = $stripePayment->authorize();

                                    break;

                                case "PayPal":
                                    $payPalPayment = new PayPalPayment($param, $card);
                                    $data['payment_method'] = 'Authorized';
                                    $data['action_name'] = 'card-entry';
                                    $payment = $payPalPayment->authorize();

                                    break;

                                case "AuthorizeDOTNET":
                                    $authorizeNETPayment = new AuthorizeDOTNET($param, $card);
                                    $data['payment_method'] = 'Authorized';
                                    $data['action_name'] = 'card-entry';
                                    $payment = $authorizeNETPayment->authorize();
                                    break;

                            }
                            if ($payment['success'] != 1 || isset($payment['redirect'])) {

                                $paymentStatus = false;
                                $paymentData = array(
                                    'availability' => array('success' => true),
                                    'payment' => array('success' => false, 'message' => $payment['msg']),
                                );
                                // payment log
                                $logData = array(
                                    'order_id' => '',
                                    'content_type' => 'Payments',
                                    'status' => 2,
                                    'options' => $payment['msg']
                                );
                                $this->paymentLog($logData);

                                $this->apiResponse['error'] = $payment['msg'];
                                return;

                                //return

                            } else {
                                $payment_type_here = 1;
                                $data['content_id'] = 1;
                                if (isset($data['is_admin'])) {
                                    $data['content_id'] = 2;
                                    $payment_type_here = 2;

                                }
                                /* create order */

                                $order = $this->_makeOrder($data, $cart, $payment_type_here);

                                /*saving authorized payment */

                                $data['order_id'] = $order['id'];
                                $data['transaction_id'] = $payment['transaction_id'];
                                $data['response_text'] = json_encode($payment);
                                $data['payment_gateway'] = $data['payment_gateway_name'];

                                $this->_payment($data, $data['response_text']);

                                /* saved authorized payment */


                                $rentalStatus = $this->Carts->getRentalStatus($data['cart_id']);
                                $greatestReturnDateStatus = $rentalStatus ? $this->greatestReturnDateStatus($data['cart_id']) : false;

//                                if (!$rentalStatus || $greatestReturnDateStatus) {
//
//                                    // for rent
//                                    if (!isset($payment['transaction_id'])) {
//                                        $paymentData = array(
//                                            'availability' => array('success' => true),
//                                            'payment' => array('success' => false, 'message' => 'Payment not found for capturing, make sure  transaction_id provided'),
//                                        );
//                                        $this->apiResponse['data'] = $paymentData;
//                                        return;
//                                    }
//                                    $param = [
//                                        'transaction_id' => $payment['transaction_id'],
//                                        'amount' => $data['amount'],
//                                        'store_id' => $data['store_id'],
//                                        'description' => 'rent my capture',
//                                        'currency' => $data['currency']
//                                    ];
//
//                                    $paymentCapture = [];
//
//                                    switch ($data['payment_gateway_name']) {
//                                        case "Stripe":
//                                            $stripePayment = new StripePayment($param);
//                                            $data['payment_method'] = 'Capture';
//                                            $data['action_name'] = 'capture';
//                                            $paymentCapture = $stripePayment->capture();
//
//                                            break;
//
//                                        case "PayPal":
//                                            $payPalPayment = new PayPalPayment($param);
//                                            $data['payment_method'] = 'Capture';
//                                            $data['action_name'] = 'capture';
//                                            $paymentCapture = $payPalPayment->capture();
//                                            break;
//
//                                        case "AuthorizeDOTNET":
//                                            $authorizeNETPayment = new AuthorizeDOTNET($param);
//                                            $data['payment_method'] = 'Authorized';
//                                            $data['action_name'] = 'card-entry';
//                                            $paymentCapture = $authorizeNETPayment->capture();
//                                            break;
//                                    }
//
//                                    if ($paymentCapture['success'] != 1) {
//                                        $logData = ['order_id' => '', 'status' => 2,
//                                            'options' => $paymentCapture['msg'],
//                                            'content_type' => 'Payments', 'api_request' => json_encode($data),
//                                            'response_text' => json_encode($paymentCapture)];
//                                        $this->paymentLog($logData);
//                                        $paymentData = array(
//                                            'availability' => array('success' => true),
//                                            'payment' => array('success' => false, 'message' => $payment['msg']),
//                                        );
//                                        $this->apiResponse['data'] = $paymentData;
//                                        $this->apiResponse['error'] = $payment['msg'];
//                                        return;
//
//                                    }
//
//                                    $data['order_id'] = $order['id'];
//                                    $data['transaction_id'] = $paymentCapture['transaction_id'];
//                                    $data['response_text'] = json_encode($paymentCapture);
//                                    $data['payment_gateway'] = $data['payment_gateway_name'];
//                                    $data['payment_method'] = 'Capture';
//                                    $data['action_name'] = 'capture';
//                                    $this->_payment($data, $data['response_text']);
//
//                                }

                            }

                            return;

                        } else {
                            $payment = $this->onlinePayment($data);
                            if (empty($payment['respstat']) || $payment['respstat'] != "A") {
                                $paymentStatus = false;
                                $msg = empty($payment['resptext']) ? 'Server not found!' : $payment['resptext'];

                                $paymentData = array(
                                    'availability' => array('success' => true),
                                    'payment' => array('success' => false, 'message' => $msg),
                                );
                                // payment log
                                $logData = array(
                                    'order_id' => '',
                                    'status' => 2,
                                    'options' => $msg,
                                );
                                $this->paymentLog($logData);
                            }
                        }
                    }

                    //$paymentStatus = true; // for testing purposes
                    if ($paymentStatus) {
                        if ($cart->total_quantity > 0) {
                            // Get payment type
                            $payment_type = 4;
                            if ($data['type'] == 1 || $data['type'] == "Online") {
                                if (isset($data['swipe']) && ($data['swipe'] == true)) {
                                    $payment_type = 3;
                                } else {
                                    $payment_type = 1;
                                    if (isset($data['is_admin'])) {
                                        $payment_type = 2;
                                    }
                                }
                            }

                            $order = $this->_makeOrder($data, $cart, $payment_type);
                            $data['order_id'] = $order['id'];
                            if (!empty($data['email'])) {
                                Email::sendOrderEmail($order['id']);
                            }
                            if ($data['type'] == 1 || $data['type'] == "Online") {
                                if (isset($data['swipe']) && ($data['swipe'] == true)) {
                                    //bolt
                                    $response = json_decode($data['response_text'], true);
                                    $data['transaction_id'] = $response['retref'];
                                    if (isset($data['log_text']) && !empty($data['log_text'])) {
                                        $log_text = $data['log_text'];
                                        for ($i = 0; $i < count($log_text) - 1; $i++) {
                                            $logData = array(
                                                'order_id' => $order['id'],
                                                'status' => $log_text[$i]['status'],
                                                'options' => $log_text[$i]['options'],
                                                'action_name' => $log_text[$i]['action_name'],
                                                'response_text' => $log_text[$i]['response_text'],
                                            );
                                            $this->paymentLog($logData);
                                        }
                                    }
                                    $data['payment_method'] = $log_text[count($log_text) - 1]['options'];
                                    $data['content_id'] = 3;
                                    $options = $log_text[count($log_text) - 1]['options'];
                                    $responseText = $log_text[count($log_text) - 1]['response_text'];
                                    $data['action_name'] = $log_text[count($log_text) - 1]['action_name'];
                                    $this->_payment($data, $options, $responseText);

                                } else {
                                    //card connect
                                    if ($data['payment_gateway_name'] == 'CardConnect') {
                                        $rentalStatus = $this->Carts->getRentalStatus($data['cart_id']);
                                        $greatestReturnDateStatus = $rentalStatus ? $this->greatestReturnDateStatus($data['cart_id']) : false;
                                        $message = 'Authorized';
                                        $responseText = json_encode($payment);

                                        /* Start Authorized data insert to payment table */
                                        $data['payment_method'] = $message;
                                        $data['transaction_id'] = $payment['retref'];
                                        $data['response_text'] = $responseText;
                                        $data['content_id'] = 1;
                                        $data['action_name'] = 'card-entry';
                                        if (isset($data['is_admin'])) {
                                            $data['content_id'] = 2;
                                        }
                                        $this->_payment($data, $payment['resptext']);

                                        /* Start Authorized data insert to payment table */

//                                        if (!$rentalStatus || $greatestReturnDateStatus) {
//                                            $capture = $this->CardConnect->capture([
//                                                'retref' => Hash::get($payment, 'retref')
//                                            ]);
//
//                                            if (Hash::get($capture, 'respstat') == "A") {
//                                                $message = 'Capture';
//                                                $responseText = json_encode($capture);
//
//                                                $data['payment_method'] = $message;
//                                                $data['transaction_id'] = $payment['retref'];
//                                                $data['response_text'] = $responseText;
//                                                $data['content_id'] = 1;
//                                                $data['action_name'] = 'card-entry';
//                                                if (isset($data['is_admin'])) {
//                                                    $data['content_id'] = 2;
//                                                }
//                                                $this->_payment($data, $payment['resptext']);
//                                            } else {
//                                                $logData = array(
//                                                    'order_id' => $order['id'],
//                                                    'status' => 2,
//                                                    'content_type' => 'Capture',
//                                                    'options' => $capture['resptext'],
//                                                    'action_name' => 'capture',
//                                                    'response_text' => json_encode($capture),
//                                                );
//                                                $this->paymentLog($logData);
//                                            }
//                                        }

                                    }


                                }
                            } else {
                                $data['payment_method'] = !empty($data['is_admin']) ? 'Paid' : 'Unpaid';
                                $data['content_id'] = 4;
                                $data['action_name'] = 'cash';
                                $this->_payment($data, 'In-store – Cash');
                            }

                        } else {
                            $this->apiResponse['message'] = 'Your Cart is empty!';
                        }
                    } else {
                        $this->apiResponse['data'] = $paymentData;
                    }
                } else {
                    $data = array(
                        'availability' => $checkAvailability,
                        'payment' => [],
                    );
                    $this->apiResponse['data'] = $data;
                }
            } else {
                $this->apiResponse['message'] = 'Method Not Allowed';
            }
        } else {
            $this->apiResponse['message'] = 'Method Not Allowed';
        }
    }

    /*
     * get greatest  return date
     */

    private function onlinePayment($data)
    {
        $this->loadComponent('CardConnect');
        if (
            (isset($data['account']) && !empty($data['account'])) &&
            (isset($data['expiry']) && !empty($data['expiry'])) &&
            (isset($data['cvv2']) && !empty($data['cvv2'])) &&
            (isset($data['amount']) && !empty($data['amount'])) &&
            (isset($data['currency']) && !empty($data['currency']))
        ) {
            $cardData = array(
                'account' => $data['account'],
                'expiry' => $data['expiry'],
                'cvv2' => $data['cvv2'],
                'amount' => $data['amount'] * 100,
                'currency' => $data['currency']
            );
            $fName = empty($data['first_name']) ? '' : $data['first_name'];
            $lName = empty($data['last_name']) ? '' : $data['last_name'];
            $customerData = array(
                'name' => $fName . ' ' . $lName,
                'address' => empty($data['address_line1']) ? '' : $data['address_line1'],
                'city' => empty($data['city']) ? '' : $data['city'],
                'region' => empty($data['state_id']) ? '' : $data['state_id'],
                'country' => !empty($data['country_id']) ? $data['country_id'] : NULL
            );
            $response = $this->CardConnect->paymentRequest($cardData, $customerData);
            /*only testing purposes*/
            // return json_decode('{"gsacard":"N","amount":"0.01","resptext":"Approval","acctid":"1","cvvresp":"M","respcode":"000","avsresp":"N","defaultacct":"Y","merchid":"************","token":"****************","authcode":"260305","respproc":"RPCT","profileid":"15720676655846214181","retref":"************","respstat":"A","account":"55XXXXXXXXXX0387"}',true);
            return $response;
        }

        return false;
    }

    public function paymentLog($data)
    {
        $data['user_id'] = isset($this->parseToken->id) ? $this->parseToken->id : 0;
        $data['store_id'] = $this->parseToken->store_id;
        $this->add_model(array('PaymentLogs'));
        $paymentLogs = $this->PaymentLogs->newEntity();
        $paymentLogs = $this->PaymentLogs->patchEntity($paymentLogs, $data);
        $this->PaymentLogs->save($paymentLogs);
    }


    private function _makeOrder($data, $cart, $payment_type)
    {
        if (isset($data['email']) && !empty($data['email'])) {
            $data['customer_id'] = $this->customerInfo($data);
        }
        if (!empty($data['delivery']) && empty($data['pickup'])) {
            if ($cart->shipping_method == 1) {
                $data['pickup'] = $data['delivery']['id'];
            }
        }

        if (!empty($data['custom_values'])) {
            $customValue = json_decode($data['custom_values'], true);

            if (!empty($data['special_instructions'])) {
                $customValue[] = ['field_name' => 'special_instructions', 'field_label' => 'Special Instructions', 'field_values' => $data['special_instructions']];
            }
            if (!empty($data['special_requests'])) {
                $customValue[] = ['field_name' => 'special_requests', 'field_label' => 'Special Requests', 'field_values' => $data['special_requests']];
            }
            if (!empty($data['driving_license'])) {
                $customValue[] = ['field_name' => 'driving_license', 'field_label' => 'Driving License', 'field_values' => $data['driving_license']];
            }
            $data['custom_values'] = json_encode($customValue);
        }

        $order = $this->Orders->newEntity();
        $data['coupon_id'] = $cart->coupon_id;
        $data['total_deposit'] = $cart->deposit_amount;
        //   $data['total_discount_tax'] = $cart->total_discount_tax;
        $data['tax'] = $cart->tax;
        $data['total_discount'] = $cart->total_discount;
        $data['sub_total'] = $cart->sub_total;
        $data['delivery_tax'] = $cart->delivery_tax;
        $data['total_quantity'] = $cart->total_quantity;
        $data['shipping_method'] = $cart->shipping_method;
        $data['delivery_charge'] = $cart->delivery_charge;
        $data['total_price'] = $cart->sub_total + $data['tax'];
        //$data['payment_date'] = date('Y-m-d');
        $data['payment_type'] = $payment_type;
        $data['state_id'] = $data['state'] ?? null;
        $data['country_id'] = strtoupper($data['country']) ?? null;

        $cart_options = $cart['options'];
        if (!is_array($cart['options']))
            $cart_options = json_decode($cart->options, true);
        $cart_options['referrer'] = $data['referrer'];

        $data['options'] = json_encode($cart_options);

        $order = $this->Orders->patchEntity($order, $data);
        if ($this->Orders->save($order)) {
            $orderId = $order->id;
            $data['order_id'] = $orderId;
            /* Delivey details start */
            $this->_addDeliveryDetails($data);
            /* Delivey details end */

            if (!empty($data['mobile'])) {
                $this->loadModel('Stores');
                $storeName = $this->Stores->get($order->store_id)->name;
                $orderUid = $this->randomnum(3) . $order->id;
//                $domain = 'https://' . $storeName . '.rentmy.co';
                //$domain = Configure::read('HOST');
                RentMy::getStore($order->store_id, RentMy::$token->location);
                $domain = RentMy::storeDomain();
                $link = $domain . '/order/' . $orderUid;
                $msg = "Please click the link below to download your receipt: " . $link;
                $this->loadComponent('Sms');
                $this->Sms->sendingSms($data['mobile'], $msg);
            }
            $cartData = $this->CartItems->find('all')->where(['cart_id' => $cart->id])->where(['parent_id' => 0])->toArray();
            $orderProductOptions = [];
            foreach ($cartData as $row) {
                $aData = array();
                $aData['product_id'] = $row->product_id;
                $aData['store_id'] = $row->store_id;
                $aData['quantity'] = $row->quantity;
                $aData['quantity_id'] = $row->quantity_id;
                $aData['price'] = $row->price;
                $aData['sales_tax'] = $row->sales_tax;
                $aData['sales_tax_price'] = $row->sales_tax_price;
                $aData['off_amount'] = $row->off_amount;
                $aData['sub_total'] = $row->sub_total;
                $aData['substantive_price'] = $row->substantive_price;
                $aData['total'] = $row->total;
                $aData['product_type'] = $row->product_type;
                $aData['term'] = $row->term;
                $aData['deposit_amount'] = $row->deposit_amount;
                $aData['driving_license_required'] = $row->driving_license_required;
                $aData['rental_type'] = $row->rental_type;
                $aData['rental_duration'] = $row->rental_duration;
                $aData['rent_start'] = $row->rent_start;
                $aData['rent_end'] = $row->rent_end;
                $aData['variant_chain_id'] = $row->variant_chain_id;
                $aData['location'] = $row->location;
                $aData['variant_chain_name'] = $row->variant_chain_name;
                $aData['variants_products_id'] = $row->variants_products_id;
                $item = $this->OrderItems->newEntity();
                $aData['order_id'] = $orderId;
                $item = $this->OrderItems->patchEntity($item, $aData);
                $orderItem = $this->OrderItems->save($item);

                $orderProductOptions[] = [
                    'where' => [
                        'content_type' => 'cart',
                        'content_id' => $cart->id,
                        'content_item_id' => $row['id'],
                    ],
                    'data' => [
                        'content_type' => 'order',
                        'content_id' => $orderId,
                        'content_item_id' => $item->id,
                    ]
                ];

                $parent_id = $item->id;

                /** Package product */
                if ($row->product_type == 2) {
                    $cartItems = $this->CartItems->find('children', ['for' => $row->id])
                        ->find('threaded')
                        ->toArray();
                    foreach ($cartItems as $cartItem) {
                        $item = $this->OrderItems->newEntity();
                        $item->product_id = $cartItem->product_id;
                        $item->store_id = $cartItem->store_id;
                        $item->location = $cartItem->location;
                        $item->variants_products_id = $cartItem->variants_products_id;
                        $item->quantity_id = $cartItem->quantity_id;
                        $item->quantity = $cartItem->quantity;
                        $item->parent_id = $parent_id;
                        $item->order_id = $data['order_id'];
                        $item->product_type = $cartItem->product_type;
                        $item->rental_type = $cartItem->rental_type;
                        $item->rent_start = $cartItem->rent_start;
                        $item->rent_end = $cartItem->rent_end;
                        $item->price = $cartItem->price;
                        $item->variant_chain_id = $cartItem->variant_chain_id;
                        $item->variant_chain_name = $cartItem->variant_chain_name;

                        $this->OrderItems->save($item);

                        /*Insert to Products Availabilities*/
                        if ($cartItem->rental_type != 'buy') {
                            $aItem = $this->ProductsAvailabilities->newEntity();
                            $aItem->product_id = $cartItem->product_id;
                            $aItem->order_id = $data['order_id'];
                            $aItem->rental_type = $cartItem->rental_type;
                            $aItem->variants_products_id = $cartItem->variants_products_id;
                            $aItem->quantity_id = $cartItem->quantity_id;
                            $aItem->quantity = $cartItem->quantity * $row->quantity;
                            $aItem->location = $cartItem->location;
                            $aItem->order_item_id = $orderItem->id;

                            $aItem->start_date = $row->rent_start;
                            $aItem->end_date = $row->rent_end;
                            $aItem->actual_start_date = $this->Orders->_getLeadLagTime('lead', $row->rent_start);
                            $aItem->actual_end_date = $this->Orders->_getLeadLagTime('lag', $row->rent_end);
                            $aItem->before_time = $this->Orders->_getLeadTime();
                            $aItem->after_time = $this->Orders->_getLagTime();

                            $this->ProductsAvailabilities->save($aItem);
                        }
                    }
                } else {
                    /*Insert to Products Availabilities*/
                    if ($row->rental_type != 'buy') {
                        $aItem = $this->ProductsAvailabilities->newEntity();
                        $aItem->product_id = $row->product_id;
                        $aItem->order_id = $data['order_id'];
                        $aItem->rental_type = $row->rental_type;
                        $aItem->variants_products_id = $row->variants_products_id;
                        $aItem->quantity_id = $row->quantity_id;
                        $aItem->quantity = $row->quantity;
                        $aItem->location = $row->location;
                        $aItem->order_item_id = $orderItem->id;

                        $aItem->start_date = $row->rent_start;
                        $aItem->end_date = $row->rent_end;
                        $aItem->actual_start_date = $this->Orders->_getLeadLagTime('lead', $row->rent_start);
                        $aItem->actual_end_date = $this->Orders->_getLeadLagTime('lag', $row->rent_end);
                        $aItem->before_time = $this->Orders->_getLeadTime();
                        $aItem->after_time = $this->Orders->_getLagTime();

                        $this->ProductsAvailabilities->save($aItem);
                    }
                }
            }

            RentMy::addModel(['OrderProductOptions']);
            RentMy::$Model['OrderProductOptions']->migrateCartToOrder($orderProductOptions);

            /** Item log */

            if (isset($data['item_log']) && !empty($data['item_log'])) {
                $item_log = $data['item_log'];
                for ($i = 0; $i < count($item_log); $i++) {
                    $this->paymentLog($orderId, 'Products', $item_log[$i]['product_id'], null, $item_log[$i]['note'], 'discount', null, json_encode($item_log[$i]));
                }
            }

            /** ********* */

            $this->Orders->updatePurchaseType($orderId);

            /** *** Order address ****** */

            $this->loadModel('OrderAddresses');
            if (!empty($data['delivery_multi_store']))
                $data['multi_store_delivery'] = json_encode($data['delivery_multi_store']);

            $orderAddress = $this->OrderAddresses->newEntity();
            $orderAddress = $this->OrderAddresses->patchEntity($orderAddress, $data);
            $this->OrderAddresses->save($orderAddress);

            /** *********************** */

            /** Customer Address */

            $this->loadModel('CustomerAddresses');
            $this->CustomerAddresses->_customerAddress($data);

            /** ***** ******** ***** */
        }

        return $order;
    }

    private function _addDeliveryDetails($data)
    {
        if (empty($data['delivery']) && !empty($data['pickup'])) {
            $this->loadModel('Locations');
            $location = $this->Locations->get($data['pickup']);
            $data['delivery'] = array(
                'charge' => 0.00,
                'tax' => 0.00,
                'charge_by' => 'in-store',
                'location' => [
                    'id' => $location->id,
                    'pickup_from' => $location->name,
                    'delivery_to' => $location->name,
                ]
            );
        } else {
            if ($data['shipping_method'] == 2) {
                $data['delivery']['location']['delivery_to'] = $this->_getShippingAddress($data);
            }
        }
        $this->loadModel('DeliveryDetails');
        $deliveryDetails = $this->DeliveryDetails->newEntity();
        $deliveryData = array();
        $deliveryData['order_id'] = $data['order_id'];
        $deliveryData['config'] = json_encode($data['delivery']);
        $deliveryDetails = $this->DeliveryDetails->patchEntity($deliveryDetails, $deliveryData);
        $this->DeliveryDetails->save($deliveryDetails);
        return;
    }

    private function _getShippingAddress($dAddress)
    {
        $delivery_to = array();
        if (!empty($dAddress['shipping_address1'])) {
            $delivery_to[] = $dAddress['shipping_address1'];
        }
        if (!empty($dAddress['shipping_zipcode'])) {
            $delivery_to[] = $dAddress['shipping_zipcode'];
        }
        if (!empty($dAddress['shipping_city'])) {
            $delivery_to[] = $dAddress['shipping_city'];
        }
        $delivery_to = implode(', ', $delivery_to);
        return $delivery_to;
    }

    /*
     * Customer info
     */
    public function customerInfo($data)
    {

        $this->add_model(array('Customers'));
        $check = $this->Customers->find()->where(['email' => $data['email']])->first();
        if ($check) {
            return $check->id;
        } else {
            $customerObj = $this->Customers->newEntity();
            $customerObj = $this->Customers->patchEntity($customerObj, $data);
            if ($this->Customers->save($customerObj)) {
                return $customerObj->id;
            }

        }
        return false;
    }


    private function _payment($data = array(), $options = null, $responseText = null)
    {
        $this->add_model(array('Payments', 'Carts'));
        if (!empty($data)) {
            $pData = array();
            $pData['status'] = $data['payment_method'] == 'Unpaid' ? 1 : $data['payment_method'] == 'Authorized' ? 2 : 1;
            $pData['order_id'] = empty($data['order_id']) == true ? 0 : $data['order_id'];
            $pData['store_id'] = empty($data['store_id']) == true ? 0 : $data['store_id'];
            $pData['type'] = empty($data['type']) == true ? 0 : $data['type'];
            $pData['payment_amount'] = empty($data['amount']) == true ? 0 : $data['amount'];
            $pData['payment_method'] = empty($data['payment_method']) == true ? null : $data['payment_method'];
            $pData['payment_gateway'] = empty($data['payment_gateway_name']) == true ? null : $data['payment_gateway_name'];
            $pData['transaction_id'] = empty($data['transaction_id']) == true ? 0 : $data['transaction_id'];
            $pData['note'] = empty($data['note']) == true ? 0 : $data['note'];
            $pData['response_text'] = empty($data['response_text']) == true ? 0 : $data['response_text'];
            $pData['terminal'] = empty($data['terminal']) == true ? '' : $data['terminal'];
            $pData['terminal_id'] = empty($data['terminal_id']) == true ? '' : $data['terminal_id'];
            $pData['content_id'] = empty($data['content_id']) == true ? '' : $data['content_id'];
            $payment = $this->Payments->newEntity();
            $payment = $this->Payments->patchEntity($payment, $pData);
            if ($this->Payments->save($payment)) {
                // payment log
                $logData = array(
                    'order_id' => $data['order_id'],
                    'content_id' => $payment->id,
                    'content_type' => 'Payments',
                    'action_name' => !empty($data['action_name']) ? $data['action_name'] : NULL,
                    'status' => 1,
                    'options' => $options,
                    'api_request' => json_encode(array('amount' => $data['amount'], 'order_id' => $data['order_id'])),
                    'response_text' => empty($responseText) == true ? json_encode($payment) : $responseText,
                );
                $this->paymentLog($logData);

                $this->_updateQuantity($data['cart_id']);
                // //now clearing the cart
                $this->Carts->clearCart($data['cart_id']);
                $payment = array('success' => true, 'order_id' => $data['order_id']);
                $order = array('success' => true, 'product' => []);
                $data = array(
                    'availability' => array('success' => true),
                    'order' => $order,
                    'payment' => $payment,
                );
                $this->apiResponse['data'] = $data;
            } else {
                // payment log
                $logData = array(
                    'order_id' => $data['order_id'],
                    'content_id' => '',
                    'content_type' => 'Payments',
                    'status' => 2,
                    'options' => $options,
                    'response_text' => empty($responseText) == true ? json_encode($payment) : $responseText,
                );
                $this->paymentLog($logData);

                $payment = array('success' => false, 'message' => 'Payment submission problem.');
                $order = array('success' => true, 'product' => []);
                $data = array(
                    'availability' => array('success' => true),
                    'order' => $order,
                    'payment' => $payment,
                );
                $this->apiResponse['data'] = $data;
            }
        } else {
            $this->apiResponse['message'] = 'Method Not Allowed';
        }

    }

    private function _updateQuantity($cartId)
    {
        $this->add_model(array('VariantsProducts', 'Quantities'));
        $cartItem = $this->CartItems->find('all')->where(['cart_id' => $cartId])->toArray();
        $pCount = array();
        foreach ($cartItem as $item) {
            if ($item->rental_type == 'buy') {
                $pCount[$item->quantity_id] = 0;
            }
        }
        foreach ($cartItem as $item) {
            if ($item->rental_type == 'buy') {
                $pCount[$item->quantity_id] = empty($pCount[$item->quantity_id]) == true ? $item->quantity : ($pCount[$item->quantity_id] + $item->quantity);
            }
        }
        if (!empty($pCount)) {
            foreach ($pCount as $k => $v) {
                $quantity = $this->Quantities->get($k);
                if (!empty($quantity)) {
                    $newQuantity = $quantity->quantity - $v;
                    $quantity['quantity'] = ($newQuantity < 0) == true ? 0 : $newQuantity;
                    $this->Quantities->save($quantity);
                }
            }

        }
        return;
    }

    public function greatestReturnDateStatus($cartId)
    {
        $this->add_model(array('CartItems'));
        $cartItems = $this->CartItems->find('all')->where(['cart_id' => $cartId])->toArray();
        $rent_end = array();
        foreach ($cartItems as $cartItem) {
            $rent_end[] = ($cartItem->rent_end);
        }
        $datetime1 = new DateTime(max($rent_end));
        $datetime2 = new DateTime(date('Y-m-d H:i:s'));
        $interval = $datetime1->diff($datetime2);
        $greatestReturnDateStatus = ($interval->days > 30) ? true : false;
        //$this->apiResponse['data'] = $greatestReturnDateStatus;

        return $greatestReturnDateStatus;
    }

    /**
     * Bolt API cancel
     */
    public function cancel()
    {
        $this->add_model(array('StoresTerminals'));
        $terminal_id = $this->request->getData('terminal_id');
        $storesTerminal = $this->StoresTerminals->get($terminal_id);
        $bolt = new BoltCardConnect($storesTerminal['hsn'], 'BoltCardConnect');
        $bolt->cancelOperation();
        //$this->Orders->cancelOperation($storesTerminal['hsn']);
    }

    /**
     * Bolt API inquire
     */
    public function inquire()
    {
        $this->loadComponent('CardConnect');
        $retref = $this->request->getData('transaction_id');
        $inquire = $this->CardConnect->inquire($retref);
        if (Hash::get($inquire, 'respstat') == "A") {

            $this->apiResponse['data'] = [
                'success' => true,
                'data' => $inquire,
                'response_text' => Hash::get($inquire, 'resptext')
            ];
        } else {
            $this->apiResponse['data'] = [
                'success' => false,
                'data' => json_encode($inquire),
                'response_text' => Hash::get($inquire, 'resptext')
            ];
        }
    }

    /**
     * Card Swipe
     *
     * Bolt API authorize
     *
     * @param amount
     * @param terminal_id
     */
    public function boltCardConnect()
    {
        Configure::write('debug', true);
        $this->add_model(array('StoresUsers', 'Stores', 'StoresTerminals', 'Carts', 'Payments'));
        $this->loadComponent('CardConnect');
        $data = $this->request->getData();

//        $rentalStatus = $this->Carts->getRentalStatus($data['cart_id']);
//        $greatestReturnDateStatus = $rentalStatus ? $this->greatestReturnDateStatus($data['cart_id']) : false;

        $amount = $this->request->getData('amount');
        $terminal_id = $this->request->getData('terminal_id');
        $storesTerminal = $this->StoresTerminals->get($terminal_id);
        Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Request Data: ' . json_encode($data), ['scope' => ['bolt']]);
        $amount = number_format($amount, 2, '', '');

        $cardDetails = $this->readCard($amount, $storesTerminal['hsn']);
//        $cardDetails =  array (
//            'token' => '****************',
//            'expiry' => '0827',
//            'signature' => 'H4sICAAAAAAC/1NJRy5CTVAA7Jo/rtpAEIe/lSXcPD23FAi3USpKpCCQ0ucOnCCipHhK9lA5wHavySH2CO5CgZ5TGOM1sfHOOOX+Ott87IznN/sH8fXb988AsAc+Ad7AL8BgANjcnoeq65qkpKSkpKSkpKSkpKSkpP+mYsY581B7NVtx1aLGUTolm1sy7cAL4KAc+AXILnqW/Qw2t3rWeD3LW/feaydkS9vZ7CKqEWRVe30WvLiG5Ud77ci9kC1uQRuL+RCy7Zs2Nkx+Qtk9Tyg9xoKphOwaqK8Y26vYRA/aez9RldZYoBCyxoPLKmOBrLFHPllp1yXseGu+qkm4jmbX4CisA9jFDcuxm0AcmXe0CZdEs8bj4OxoEz5Ps5vOyUco/W3yJPMCds2R1sy7OHctuxlzc0+hgFV0AwPGbzq3+S6ekY/3L87L7u6FEVNvBxoJWAXsmpGp5zzUDJAH8eTGji1fA4aG7Hdw933k9fhhlj/B7fp56zwYGr4ED/bP2/3BlFC46Jb9h83F7CZcx4lt2QdTwmmaPY25bDvN7hg2Voz/izE2YoLM3EjJYpai6mGXdP+aSppwN5i5SBM+hW0rTDioTMxK1MtrJSpSv0tehZvmMOhcuHutxntSFLSxM4J2M4J2M4I+og96iz7olZAN++FVyhZqc/TW9Uy8bb+K5ta+giOVeMsfnGykBYbDwNIQfZxzA0tDrC6DGxBh0AvFGbRSdn+4MTBWzhZe2cGht44KtlQXuGsIRYHvQb9o2FtDqNjbbwwLDcvBas3RdqLGHMBPq+r+wNQnFdscuZc6tvDaItGc0XVFas5iWtZ8aAvcDKxmTa01B1Be9Sxz/tCSpT/DJCUlJSUlPdHfAQDya/ogbioAAA==',
//            'name' => '/',
//        );
        Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Read Card Data: ' . json_encode($cardDetails), ['scope' => ['bolt']]);
        $payment = $this->CardConnect->offlinePaymentRequest($cardDetails, $amount);
        //RentMy::dbg($payment);
        Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- offlinePaymentRequest Data: ' . json_encode($payment), ['scope' => ['bolt']]);
        if (Hash::get($payment, 'respstat') == "A") {
            $message = 'Authorized';
            $response_text = json_encode($payment);

            $data['amount'] = $payment['amount'];
            $data['cvvresp'] = $payment['cvvresp'];
            $data['terminal'] = $storesTerminal['hsn'];
            $data['response_text'] = $response_text;

            if (!empty($data['order_id'])) {
                $data['payment_method'] = 'Captured';
                $data['transaction_id'] = $payment['retref'];
                $data['response_text'] = json_encode($payment);
                $data['content_id'] = 3;
                $data['action_name'] = 'card-swipe';
                $data['log_option'] = $payment['resptext'];
                $data['store_id'] = RentMy::$store->id;
                $data['type'] = 3;
                $status = $this->Payments->create($data['order_id'], $data);
            }
//            if (!$rentalStatus || $greatestReturnDateStatus) {
//                $capture = $this->CardConnect->capture([
//                    'retref' => Hash::get($payment, 'retref')
//                ]);
//
//                if (Hash::get($capture, 'respstat') == "A") {
//                    $message = 'Capture';
//                    //$message = Hash::get($capture, 'resptext');
//                    $response_text = json_encode($capture);
//                }
////                else {
////                    $message = 'Authorized';
////                }
//            }


            // payment log
            $logData = array(
                'status' => 1,
                'options' => $message,
                'action_name' => 'swipe',
                'response_text' => $response_text,
            );
            //$this->paymentLog($logData);

            $this->apiResponse['data'] = [
                'success' => true,
                'payment' => $data,
                'log_text' => $logData,
                'response_text' => $message
            ];
        } else {
            // payment log
            $logData = array(
                'status' => 2,
                'options' => Hash::get($payment, 'resptext'),
                'action_name' => 'swipe',
                'response_text' => json_encode($payment),
            );
            //$this->paymentLog($logData);

            $this->apiResponse['data'] = [
                'success' => false,
                'payment' => $data,
                'log_text' => $logData,
                'response_text' => Hash::get($payment, 'resptext')
            ];
        }
    }

    /**
     * Bolt API
     * readCard method it is an optional endpoint that displays a request to the user for a signature.
     */
    public function readCard($amount, $hsn)
    {
        try {
            if (!empty($amount)) {
                $bolt = new BoltCardConnect($hsn, 'BoltCardConnect');
                $result = $bolt->readCard($amount);
                //$result = $this->Orders->readCard($amount, $hsn);
                if (!empty($result['status']) && !$result['status']) {
                    $this->apiResponse['message'] = $result['message'];
                    return [];
                }
                return $result;
            }

            return [];
        } catch (Exception $e) {
            $bolt = new BoltCardConnect($hsn, 'BoltCardConnect');
            $bolt->cancel($hsn);
            $bolt->display('Please try again', $hsn);
            $this->apiResponse['message'] = 'Please try again';
            return [];
        }
    }


    /**
     * capture for any payment gateway
     *
     * CardConnect
     * Stripe
     * AuthorizeDotNet
     * PayPal
     *
     */
    public function capture()
    {

        if ($this->request->is('post')) {
            $this->add_model(array('Payments', 'Refunds', 'CardConnect', 'Orders'));
            $data = $this->request->getData();
            $order_id = $data['order_id'];
            $store_id = $this->parseToken->store_id;
            $transaction_id = $data['transaction_id'];


            if ($data['amount'] > 0) {
                $getPayment = $this->Payments
                    ->find()
                    ->where(['order_id' => $order_id])
                    ->where(['store_id' => $store_id])
                    ->where(['transaction_id' => $transaction_id])
                    ->first();


                if ($data['amount'] > $getPayment['payment_amount']) {
                    $this->apiResponse = ['success' => false,
                        'message' => 'You cannot capture a charge for an amount greater than it already has.'];
                    return;
                }

                if (empty($getPayment)) {
                    $this->apiResponse = ['success' => false,
                        'message' => 'Payment not found for capturing, make sure order_id and transaction_id provided'];
                } else {
                    $paymentCapture = [];
                    $param = [
                        'amount' => $data['amount'],
                        'store_id' => $getPayment['store_id'],
                        'transaction_id' => $getPayment['transaction_id'],
                        'description' => '',
                        'currency' => isset($data['currency']) ? $data['currency'] : 'USD'
                    ];

                    switch ($getPayment['payment_gateway']) {
                        case "Stripe":
                            $config = (new Payment())->getStorePaymentConfig('Stripe');
                            $stripeLog = json_decode($getPayment['response_text'], true);
                            $param['object'] = $stripeLog['object'] ?? 'charge';
                            if ($param['object'] == 'charge'){
                                $paymentObj = new Charge('customer', $config['config']);
                                $paymentCapture = $paymentObj->capture($param);
                            }else{
                                $paymentObj = new StripeIntent('PaymentIntent', $config['config']);
                                $paymentCapture = $paymentObj->captureIntent($param);
                            }

                            RentMy::addModel(['OrderRecurring']);
                            $recurring = RentMy::$Model['OrderRecurring']->find()->where(['order_id' => $order_id])->first();
                            if (!empty($recurring)) {
                                $rental = ['before_rental', 'after_rental'];
                                if ((RentMy::$storeConfig['arb']['store_active']) && (in_array(RentMy::$storeConfig['arb']['store_active'], $rental))) {
                                    //RentMy::addQueue('Order', ['type' => 'Recurring', 'content_id' => $getPayment->id, 'user_id' => RentMy::$token->id, 'user_type' => 'admin', 'order_id' => $getPayment->order_id, 'source' => RentMy::$token->source]); // add recurring payment
                                }
                            }

                            break;
                        case "goMerchant":
                            $config = (new Payment())->getStorePaymentConfig('goMerchant');
                            $goMerchantObj = new goMerchant($config['config']);
                            $paymentCapture = $goMerchantObj->settle($param);
                            break;
                        case "PayPal":
                            $payPalPayment = new PayPalPayment($param);
                            $paymentCapture = $payPalPayment->capture();
                            break;

//                        case "AuthorizeDOTNET":
//                            $authorizeDOTNETPayment = new AuthorizeDOTNET($param);
//                            $paymentCapture = $authorizeDOTNETPayment->capture();

                        case "Authorize.Net":
                            $config = (new Payment())->getStorePaymentConfig('Authorize.Net');
                            $authorizeNetObj = new AuthorizeNet($config['config']);
                            $paymentCapture = $authorizeNetObj->capture($data);
                            break;
                        case "PAX":
                            RentMy::addModel(['StoreTerminals']);
                            $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $getPayment['terminal_id']])->first();
                            $config = (new Payment())->getStorePaymentConfig('PAX');
                            $config['config']['port'] = $terminal['hsn'];
                            $paxObj = new Pax($config['config']);
                            $response_text = json_decode($getPayment['response_text'], true);
                            $paymentCapture = $paxObj->trnsPostAuth($param['transaction_id'], $response_text['auth_code'], $response_text['credit']['traceDetails']['transactionNumber'], $data['amount']);
                            break;
                        case "Empyrean":
                            $config = (new Payment())->getStorePaymentConfig('Empyrean');
                            $paymentObj = new Empyrean($config['config']);
                            $paymentCapture = $paymentObj->capture($data);
                            break;
                        case "Square":
                            $config = (new Payment())->getStorePaymentConfig('Square');
                            $paymentObj = new Square($config['config']);
                            $paymentCapture = $paymentObj->capture($data);
                            break;
                        case "Midtrans":
                            $config = (new Payment())->getStorePaymentConfig('Midtrans');
                            $paymentObj = new Midtrans($config['config']);
                            $paymentCapture = $paymentObj->capture($data);
                            break;
                        case "FreedomPay":
                            $config = (new Payment())->getStorePaymentConfig('FreedomPay');
                            $paymentObj = new FreedomPay($config['config']);
                            $paymentCapture = $paymentObj->capture($param);
                            break;
                        case "Transafe":
                        case "TransafeCardPresent":
                            $config = (new Payment())->getStorePaymentConfig('Transafe');
                            $paymentObj = new Transafe($config['config']);
                            $paymentCapture = $paymentObj->capture($data);
                            break;
                        case "CardConnect":
                            $paymentCapture = $this->capture_card_connect($data);
                            //$this->apiResponse['data'] = $card_connect_capture;
                            //return;
                            break;

                        case "ConvenuPay":
                            $config = (new Payment())->getStorePaymentConfig('ConvenuPay');
                            $paymentObj = new ConvenuPay($config['config']);
                            $paymentCapture = $paymentObj->capture($data);
                            break;
                        case "DeltaPay":
                            $config = (new Payment())->getStorePaymentConfig("DeltaPay");
                            $paymentObj = new DeltaPay($config['config'], $data);
                            $paymentCapture = $paymentObj->capture();
                            break;
                        case "ValorPay":
                            $config = (new Payment())->getStorePaymentConfig("ValorPay");
                            $paymentObj = new ValorPay($config['config']);
                            $paymentCapture = $paymentObj->capture($data['transaction_id'], $data['amount']);
                            break;

                        case "ValorPayCardPresent":
                            RentMy::addModel(['StoreTerminals']);
                            $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $getPayment['terminal_id']])->first();
                            $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
                            $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
                            $terminalGatewayConfig = [
                                "appId" => $terminalOptions['app_id'],
                                "appKey" => $terminalOptions['app_key'],
                                "epi" => $terminalOptions['epi'],
                                "apiUrl" => $config['config']['apiUrl'],
                            ];

                            $paymentObj = new ValorPay($terminalGatewayConfig);

                            $paymentCapture = $paymentObj->capture($data['transaction_id'], $data['amount']);


                    }
                    if ($paymentCapture['success'] == true) {
                        $data['store_id'] = $store_id;
                        $logData = array(
                            'order_id' => $getPayment['order_id'],
                            'status' => 1,
                            'content_type' => 'Capture',
                            'content_id' => $getPayment['id'],
                            'action_name' => 'capture',
                            'options' => '',
                            'response_text' => json_encode($paymentCapture['data']),
                            'api_request' => json_encode($data),
                        );

                        $paymentData = array(
                            'id' => $getPayment['id'],
                            'amount' => $data['amount'],
                            'status' => 1,
                            'order_id' => $data['order_id'],
                            'transaction_id' => $paymentCapture['transaction_id'],
                            'payment_method' => 'Captured',
                            'is_captured' => 1,
                            'response_text' => json_encode($paymentCapture['data'])
                        );

                        $this->updatePaymentStatus($paymentData, $logData);
                        $this->Payments->updateOrderPaymentStatus($order_id);
                        $this->apiResponse = [
                            'success' => true,
                            'message' => 'Captured successful',
                            'data' => ['transaction_id' => $paymentCapture['transaction_id']]
                        ];

                        return;

                    } else {
                        $this->apiResponse = [
                            'success' => false,
                            'message' => $paymentCapture['message']
                        ];

                    }

                }
            } else {
                $this->apiResponse = ['success' => false, 'message' => 'Amount invalid!'];

            }

        } else {
            $this->httpStatusCode = 405;
            $this->apiResponse['error'] = 'Method Not Allowed';
        }

    }

    /**
     * Bolt API capture
     * @param $data
     * @return array
     */
    public function capture_card_connect($data)
    {
        $this->loadComponent('CardConnect');
        $amount = $data['amount'];
        $amount = number_format($amount, 2, '', '');
        $capture = $this->CardConnect->capture([
            'retref' => $data['transaction_id'],
            'amount' => $amount
        ]);
        $status = Hash::get($capture, 'resptext');
        $result = [];
        if (Hash::get($capture, 'respstat') == "A") {
            //$data['amount'] = $capture['amount'];
            //$data['cvvresp'] = $capture['cvvresp'];
            //$data['response_text'] = json_encode($capture);
//            $paymentData = array(
//                'id' => $data['id'],//payment id
//                'order_id' => $data['order_id'],
//                'response_text' => json_encode($capture),
//                'transaction_id' => $capture['retref'],
//                'payment_method' => 'Captured',
//                'is_captured' => 1
//            );
//            $log_data = array(
//                'options' => $status,
//                'action_name' => 'capture',
//                'response_text' => json_encode($capture),
//                'api_request' => json_encode($data),
//            );
//            $this->updatePaymentStatus($paymentData, $log_data);
            $result = [
                'success' => true,
                'transaction_id' => $capture['retref'],
                'data' => $capture,
                'response_text' => Hash::get($capture, 'resptext')
            ];
        } else {
//            $logData = array(
//                'order_id' => $data['order_id'],
//                'status' => 2,
//                'options' => $status,
//                'action_name' => 'capture',
//                'response_text' => json_encode($capture),
//                'api_request' => json_encode($data),
//            );
//            $this->paymentLog($logData);

            $result = [
                'success' => false,
                'data' => $data,
                'response_text' => Hash::get($capture, 'resptext')
            ];
        }
        return $result;
    }

    /**
     * Changing status after payment(capture, refund) done
     * @param array $data
     * @param array $log_data
     */
    private function updatePaymentStatus($data = array(), $log_data = array())
    {
        $this->add_model(array('Payments'));
        if (!empty($data['id'])) {
            // Update existing authorize data
            $paymentData = $this->Payments->get($data['id']);
            if (!empty($data['is_void'])) {
                // save existing status 0
                $paymentData->status = 0;
                $paymentData->is_void = 1;
                if ($this->Payments->save($paymentData)) {
                    RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location'=>RentMy::$token->location], ['type' => 'Payments', 'content_id' => $paymentData->id, 'user_id' => RentMy::$token->id, 'order_id' => $paymentData->order_id, 'source' => RentMy::$token->source]);

                    //  RentMy::addQueue('Order', ['type' => 'Payments', 'content_id' => $paymentData->id, 'user_id' => RentMy::$token->id, 'order_id' => $paymentData->order_id, 'source' => RentMy::$token->source]); // add order log queue
                }
            }

            if (!empty($data['is_captured'])) {
                // save existing status 0
                $paymentData->status = 0;
                $paymentData->is_captured = 1;
                $this->Payments->save($paymentData);
                // add new entity for capture data
                $capturedData = [
                    'store_id' => $paymentData->store_id,
                    'order_id' => $paymentData->order_id,
                    'type' => $paymentData->type, 'content_id' => $paymentData->content_id,
                    'payment_gateway' => $paymentData->payment_gateway, 'gateway_id' => $paymentData->gateway_id,
                    'terminal' => $paymentData->terminal, 'terminal_id' => $paymentData->terminal_id,
                    'payment_method' => empty($data['payment_method']) ? null : $data['payment_method'],
                    'transaction_id' => empty($data['transaction_id']) ? null : $data['transaction_id'],
                    'payment_amount' => $data['amount'],
                    'status' => empty($data['status']) ? 2 : $data['status'],
                    'is_captured' => 1,
                    'response_text' => $data['response_text']
                ];
                $payment = $this->Payments->newEntity();
                $payment = $this->Payments->patchEntity($payment, $capturedData);
                if ($this->Payments->save($payment)) {
                    RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location'=>RentMy::$token->location], ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$token->id, 'order_id' => $paymentData->order_id, 'source' => RentMy::$token->source]);

                    //  RentMy::addQueue('Order', ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$token->id, 'order_id' => $paymentData->order_id, 'source' => RentMy::$token->source]); // add order log queue
                }

            }

            // payment log
//                $logData = array(
//                    'order_id' => $data['order_id'],
//                    'content_id' => $payment->id,
//                    'content_type' => 'Payments',
//                    'status' => 1,
//                    'options' => $log_data['options'],
//                    'action_name' => $log_data['action_name'],
//                    'response_text' => $log_data['response_text'],
//                    'api_request' => $log_data['api_request'],
//                );
//
//                $this->paymentLog($logData);
            $payment = array('success' => true, 'order_id' => $data['order_id']);
            $order = array('success' => true, 'product' => []);
            $data = array(
                'order' => $order,
                'payment' => $payment,
            );
            $this->apiResponse['data'] = $data;
        } else {
            $this->apiResponse['message'] = 'Method Not Allowed';
        }
    }

    /**
     * Bolt API void
     */
    public function void()
    {
        //Configure::write('debug',2);
        $this->request->allowMethod('post');

        $this->add_model(array('Payments'));
        $data = $this->request->getData();
        $order_id = $data['order_id'];
        $store_id = RentMy::$store->id;
        $transaction_id = $data['transaction_id'];

        $getPayment = $this->Payments
            ->find()
            ->where(['order_id' => $order_id])
            ->where(['store_id' => $store_id])
            ->where(['transaction_id' => $transaction_id])
            ->first();

        if (empty($getPayment)) {
            $this->httpStatusCode = 403;
            $this->apiResponse['message'] = "Void can't be possible. Make sure you provided all required data.";
        }

        $paymentVoid = [];
        $param = [
            'amount' => $data['amount'],
            'store_id' => $getPayment['store_id'],
            'transaction_id' => $getPayment['transaction_id'],
            'description' => '',
            'currency' => isset($data['currency']) ? $data['currency'] : 'USD'
        ];

        switch ($getPayment['payment_gateway']) {
            case "Stripe":
                $config = (new Payment())->getStorePaymentConfig('Stripe');
                $stripeLog = json_decode($getPayment['response_text'], true);
                $param['object'] = $stripeLog['object'] ?? 'charge';
                if ($param['object'] == 'charge'){
                    $paymentObj = new Charge('customer', $config['config']);
                    $paymentVoid = $paymentObj->void($param);
                }else{
                    $paymentObj = new StripeIntent('PaymentIntent', $config['config']);
                    $paymentVoid = $paymentObj->voidIntent($param);
                }

                break;
            case "goMerchant":
                $config = (new Payment())->getStorePaymentConfig('goMerchant');
                $goMerchantObj = new goMerchant($config['config']);
                $paymentVoid = $goMerchantObj->void($param);
                break;
            case "PayPal":
                $payPalPayment = new PayPalPayment($param);
                $paymentVoid = $payPalPayment->void();
                break;

            case "Authorize.Net":
                $config = (new Payment())->getStorePaymentConfig('Authorize.Net');
                $authorizeNetObj = new AuthorizeNet($config['config']);
                $paymentVoid = $authorizeNetObj->void($param);

//                $authorizeDOTNETPayment = new AuthorizeDOTNET($param);
//                $paymentVoid = $authorizeDOTNETPayment->void();
                break;
            case "PAX":
                RentMy::addModel(['StoreTerminals']);
                $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $getPayment['terminal_id']])->first();
                $config = (new Payment())->getStorePaymentConfig('PAX');
                $config['config']['port'] = $terminal['hsn'];
                $paxObj = new Pax($config['config']);
                $responseText = json_decode($getPayment['response_text'], true);
                $paymentVoid = $paxObj->trnsVoid($param['transaction_id'], $responseText['credit']['traceDetails']['transactionNumber']);
                break;
            case "Empyrean":
                $config = (new Payment())->getStorePaymentConfig('Empyrean');
                $paymentObj = new Empyrean($config['config']);
                $paymentVoid = $paymentObj->void($param);
                break;
            case "Square":
                $config = (new Payment())->getStorePaymentConfig('Square');
                $paymentObj = new Square($config['config']);
                $paymentVoid = $paymentObj->void($param);
                break;
            case "Midtrans":
                $config = (new Payment())->getStorePaymentConfig('Midtrans');
                $paymentObj = new Midtrans($config['config']);
                $paymentVoid = $paymentObj->void($param);
                break;
            case "FreedomPay":
                $config = (new Payment())->getStorePaymentConfig('FreedomPay');
                $paymentObj = new FreedomPay($config['config']);
                $paymentVoid = $paymentObj->void($param);
                break;
            case "Transafe":
            case "TransafeCardPresent":
                $config = (new Payment())->getStorePaymentConfig('Transafe');
                $paymentObj = new Transafe($config['config']);
                $paymentVoid = $paymentObj->void($param);
                break;
            case "CardConnect":
                $card_connect_capture = $this->cardConnectVoid($data);
                $this->apiResponse['data'] = $card_connect_capture;
                return;

            case "ConvenuPay":
                $config = (new Payment())->getStorePaymentConfig('ConvenuPay');
                $paymentObj = new ConvenuPay($config['config']);
                $paymentVoid = $paymentObj->void($param);
                break;
            case "DeltaPay":
                $config = (new Payment())->getStorePaymentConfig("DeltaPay");
                $paymentObj = new DeltaPay($config['config'], $param);
                $paymentVoid = $paymentObj->void($param['transaction_id']);
                break;

            case "ValorPay":
                $config = (new Payment())->getStorePaymentConfig("ValorPay");
                $paymentObj = new ValorPay($config['config']);
                $paymentVoid = $paymentObj->voidTransaction($param['transaction_id']);
                break;

            case "ValorPayCardPresent":
                RentMy::addModel(['StoreTerminals']);
                $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $getPayment['terminal_id']])->first();
                $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
                $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
                $terminalGatewayConfig = [
                    "appId" => $terminalOptions['app_id'],
                    "appKey" => $terminalOptions['app_key'],
                    "epi" => $terminalOptions['epi'],
                    "apiUrl" => $config['config']['apiUrl'],
                ];

                $paymentObj = new ValorPay($terminalGatewayConfig);
                $paymentVoid = $paymentObj->voidTransaction($param['transaction_id']);
                break;

        }

        if (!$paymentVoid['success']) {
            $logData = array(
                'order_id' => $getPayment['order_id'], 'status' => 2, 'content_type' => 'Voided', 'content_id' => $getPayment['id'],
                'options' => $paymentVoid['message'], 'action_name' => 'void', 'response_text' => json_encode($paymentVoid),
                'api_request' => json_encode($data),
            );
            $this->paymentLog($logData);
            $this->httpStatusCode = 403;
            $this->apiResponse['message'] = $paymentVoid['message'];
            return;
        } else {
            $data['store_id'] = $store_id;
            $logData = array(
                'order_id' => $getPayment['order_id'], 'status' => 1, 'content_type' => 'Payments', 'content_id' => $getPayment['id'],
                'action_name' => 'void', 'options' => '', 'response_text' => json_encode($paymentVoid['data']),
                'api_request' => json_encode($data),
            );

            $paymentData = array(
                'id' => $getPayment['id'],
                'is_void' => 1,
            );

            $this->updatePaymentStatus($paymentData, $logData);

            $this->apiResponse = [
                'message' => 'Voided successful',
                'transaction_id' => $paymentVoid['transaction_id']
            ];
            return;
        }


    }

    function cardConnectVoid($data)
    {
        $this->loadComponent('CardConnect');
        $amount = false;

        if (!empty($this->request->getData('amount'))) {
            $amount = $this->request->getData('amount');
            $amount = number_format($amount, 2, '', '');
        }
        $void = $this->CardConnect->void([
            'retref' => $this->request->getData('transaction_id'),
            'amount' => $amount
        ]);
        $status = Hash::get($void, 'resptext');
        if (Hash::get($void, 'respstat') == "A") {
            $data['amount'] = $void['amount'];
            $data['authcode'] = $void['authcode'];
            $data['response_text'] = json_encode($void);

            $paymentData = array(
                'id' => $this->request->getData('id'),
                'order_id' => $this->request->getData('order_id'),
                'response_text' => json_encode($void),
                'transaction_id' => $void['retref'],
                'payment_method' => 'Voided',
                'is_void' => 1
                //'payment_method' => Hash::get($void, 'resptext')
            );
            $log_data = array(
                'options' => $status,
                'action_name' => 'void',
                'response_text' => json_encode($void),
                'api_request' => json_encode($this->request->getData()),
            );
            $this->updatePaymentStatus($paymentData, $log_data);

            $this->apiResponse['data'] = [
                'success' => true,
                'void' => $data,
                'response_text' => Hash::get($void, 'resptext')
            ];
        } else {
            $logData = array(
                'order_id' => $this->request->getData('order_id'),
                'status' => 2,
                'options' => $status,
                'action_name' => 'void',
                'response_text' => json_encode($void),
                'api_request' => json_encode($this->request->getData()),
            );
            $this->paymentLog($logData);

            $this->apiResponse['data'] = [
                'success' => false,
                'void' => [],
                'response_text' => Hash::get($void, 'resptext')
            ];
        }
    }

    /**
     * payment refund for Stripe, PayPal, AuthorizeDOTNET, CardConnect
     * @deprecatd
     */

    public function refund()
    {
        if ($this->request->is('post')) {
            $this->add_model(array('Payments', 'Refunds', 'CardConnect'));
            $data = $this->request->getData();
            $order_id = $data['order_id'];
            $transaction_id = $data['transaction_id'];
            $card = !empty($data['card']) ? $data['card'] : []; //card information required for authorizeNET for refunding

            if ($data['amount'] > 0) {
                $getPayment = $this->Payments
                    ->find()->where(['order_id' => $order_id])
                    ->where(['transaction_id' => $transaction_id])
                    ->where(['store_id' => $this->parseToken->store_id])
                    ->first();

                if (empty($getPayment)) {
                    $this->apiResponse['data'] =
                        ['success' => false,
                            'message' =>
                                'Refundable Payment not found, make sure order_id & transaction_id provided'
                        ];
                } else {

                    $paymentRefund = [];
                    $param = [
                        'amount' => $data['amount'],
                        'store_id' => $getPayment['store_id'],
                        'transaction_id' => $transaction_id,
                        'description' => 'rent my refund'
                    ];
                    switch ($getPayment['payment_gateway']) {
                        case "Stripe":
                            $stripePayment = new StripePayment($param);
                            $paymentRefund = $stripePayment->refund();
                            break;

                        case "PayPal":
                            $payPalPayment = new PayPalPayment($param);
                            $paymentRefund = $payPalPayment->refund();
                            break;

                        case "Authorize.Net":
                            $config = (new Payment())->getStorePaymentConfig('Authorize.Net');
                            $authorizeNetObj = new AuthorizeNet($config['config']);
                            $paymentRefund = $authorizeNetObj->refund($param);

//                           $authorizeNETPayment = new AuthorizeDOTNET($param, $card);
//                           $paymentRefund = $authorizeNETPayment->refund();
                            break;
                        case "CardConnect":
                            $card_connect_refund = $this->refund_card_connect($data);
                            $this->apiResponse['data'] = $card_connect_refund;
                            return;

                    }

                    if ($paymentRefund['success'] == true) {
                        $this->apiResponse['data'] = [
                            'success' => true,
                            'response_text' => 'Refund made successful'
                        ];

                        $data['change_status'] = 1;
                        $data['store_id'] = $this->parseToken->store_id;
                        $refundSave = $this->Refunds->add($data);
                        $logData = array(
                            'order_id' => $getPayment['order_id'],
                            'status' => 1,
                            'content_type' => 'Refund',
                            'content_id' => $refundSave[0]['id'],
                            'options' => '',
                            'action_name' => 'refund',
                            'response_text' => json_encode($paymentRefund),
                            'api_request' => json_encode($data),
                        );

                        $paymentData = array(
                            'id' => $getPayment['id'],
                            'status' => 1,
                            'amount' => $data['amount'],
                            'order_id' => $data['order_id'],
                            'response_text' => json_encode($paymentRefund),
                            'transaction_id' => $getPayment['transaction_id'],
                            'payment_method' => 'Refund'
                        );

                        $this->updatePaymentStatus($paymentData, $logData);

                        return;

                    } else {
                        $this->apiResponse['data'] = [
                            'success' => false,
                            'response_text' => $paymentRefund['msg']
                        ];
                        $logData = array(
                            'order_id' => $getPayment['order_id'],
                            'status' => 2,
                            'content_type' => 'Refund',
                            'content_id' => '',
                            'options' => $paymentRefund['msg'],
                            'action_name' => 'refund',
                            'response_text' => json_encode($paymentRefund),
                            'api_request' => json_encode($data),
                        );
                        $this->paymentLog($logData);
                    }

                }
            } else {
                $this->apiResponse['data'] = ['success' => false, 'response_text' => 'Amount invalid!'];

            }

        } else {
            $this->httpStatusCode = 405;
            $this->apiResponse['error'] = 'Method Not Allowed';
        }


    }

    /**
     * refund  for card connect gateway
     * */
    public function refund_card_connect($data)
    {
        $this->loadComponent('CardConnect');
        $result = [];
        $this->add_model(array('CardConnect', 'Refunds'));

        if ($data['amount'] > 0) {
            $amount = number_format($data['amount'], 2, '', '');
            $data['amount'] = number_format($data['amount'], 2, '.', '');

            $refund = $this->CardConnect->refund($data['transaction_id'], $amount);

            $status = Hash::get($refund, 'resptext');
            $data['store_id'] = $this->parseToken->store_id;

            if (Hash::get($refund, 'respstat') == "A") {
                $paymentData = array(
                    'id' => $data['id'],//payment id
                    'order_id' => $data['order_id'],
                    'response_text' => json_encode($refund),
                    'transaction_id' => $refund['retref'],
                    'payment_method' => 'Refund'
                );
                $log_data = array(
                    'options' => $status,
                    'action_name' => 'refund',
                    'response_text' => json_encode($refund),
                    'api_request' => json_encode($data),
                );
                $this->updatePaymentStatus($paymentData, $log_data);

                $refunds = $this->Refunds->add($data);

                $result = [
                    'success' => true,
                    'refund_data' => $refunds,
                    'data' => $refund,
                    'response_text' => 'Refund made successful'
                ];
            } else {
                $logData = array(
                    'order_id' => $data['order_id'],
                    'status' => 2,
                    'options' => $status,
                    'action_name' => 'refund',
                    'response_text' => json_encode($refund),
                    'api_request' => json_encode($this->request->getData()),
                );
                $this->paymentLog($logData);

                $result = [
                    'success' => false,
                    'data' => $refund,
                    'response_text' => $status
                ];
            }
        } else {
            $result = [
                'success' => false,
                'response_text' => 'Amount invalid!'
            ];
        }

        return $result;

    }

    // testing sms sending

    public function sendingSms()
    {
        $mobile = $this->request->getData('mobile');
        $this->loadComponent('Sms');
        if ($mobile != null) {
            $result = $this->Sms->sendingSms($mobile);
        }
    }

    /*
     * Deprecated function, Not used
     */
    public function sort($id)
    {
        $this->add_model(array('Categories'));
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (isset($data['categories']) && !empty($data['categories'])) {
                for ($i = 0; $i < count($data['categories']); $i++) {
                    $category = $this->Categories->get($data['categories'][$i]);
                    $category['sequence_no'] = $i + 1;
                    if ($this->Categories->save($category)) {
                        $categories = $this->Categories->find('all')
                            ->select(['id', 'name'])
                            ->where(['parent_id' => $id])->order(['sequence_no' => 'ASC'])->toArray();
                        $this->apiResponse['data'] = $categories;
                    } else {
                        $this->httpStatusCode = 405;
                        $this->apiResponse['error'] = 'Method Not Allowed';
                    }
                }
            } else {
                $this->httpStatusCode = 400;
                $this->apiResponse['error'] = 'Bad Request';
            }
        } else {
            $categories = $this->Categories->find('all')
                ->select(['id', 'name'])
                ->where(['parent_id' => $id])->order(['sequence_no' => 'ASC'])->toArray();
            if (!empty($categories)) {
                $this->apiResponse['data'] = $categories;
            } else {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Not Found';
            }
        }
    }

    /**
     * Delete method
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        try {
            $this->loadModel('Orders');
            $order = $this->Orders->get($id);
            if ($this->Orders->delete($order, ['atomic' => false])) {
                $this->apiResponse['message'] = 'Order has been deleted successfully.';
            }
        } catch (Exception $e) {
            $this->apiResponse['error'] = $e->getMessage();
        }
    }

    public function bulkUpdate()
    {
        $requiredFields = ['order_list', 'status'];
        $data = $this->request->getData();
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        RentMy::addModel(['Orders', 'ProductsAvailabilities']);
        foreach ($data['order_list'] as $id) {
            $order = RentMy::$Model['Orders']->get($id);
            $olderStatus = $order->status;
            $order->status = $data['status'];
            if (RentMy::$Model['Orders']->save($order)) {
                RentMy::addNotificationQueue('ChangeStatus', RentMy::$store->id, ['location'=>RentMy::$token->location], ['type' => 'ChangeStatus', 'order_id' => $id, 'content_id' => $id, 'user_id' => RentMy::$token->id, 'old_status' => $olderStatus, 'new_status' => $order->status, 'source' => RentMy::$token->source]);
                if ($order->type == 1) {
                    // for achieved orders , move all availability disabled
                    if ($order->status == "1") {
                        RentMy::$Model['ProductsAvailabilities']->updateAll(['status' => 0], ['order_id' => $id]);
                    } // activate all availability when moved from achieved to any other status
                    elseif ($olderStatus == "1" && $data['status'] != '1') {
                        RentMy::$Model['ProductsAvailabilities']->updateAll(['status' => 1], ['order_id' => $id]);
                    }
                }
            }
        }

        $this->apiResponse['message'] = 'Order has been updated successfully.';
    }

    public function bulkReceiptDownload()
    {
        $requiredFields = ['order_list'];
        $data = $this->request->getData();
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        $this->add_model(array('Orders', 'OrderItems', 'Payments', 'Locations', 'Timezones', 'Templates',
            'Contents', 'DeliveryDetails', 'Stores', 'Refunds'));
        RentMy::addModel(['TaxLookup', 'OrderAddresses', 'OrderProductOptions']);

        $receipts = [];
        $orders = $this->Orders
            ->find()
            ->where(['Orders.id IN' => $data['order_list']])
            ->contain(['OrderItems', 'OrderItems.Products', 'Payments', 'Coupons'])
            ->toArray();
        $store = RentMy::$store;
        $store_config = json_decode($store['config'], true);
        $currency_format = ['pre' => true, 'post' => true, 'symbol' => '$', 'code' => 'USD', 's' => ''];
        if (!empty($store_config['currency_format']))
            $currency_format = $store_config['currency_format'];

        foreach ($orders as $order) {
            $template = $this->Templates->find()
                ->where(['store_id' => RentMy::$store->id])
                ->where(['type' => 1])
                ->first();
            $orderid = $order['id'];
            $order->currency_format = $currency_format;
            $this->_assigningTimezone($order->store_id);
            $order->store_name = RentMy::$store->slug;

            if (!empty($order->rent_start)) {
                $order->rent_start = RentMy::format_date(RentMy::toStoreTimeZone($order->rent_start), true);
                $order->rent_end = RentMy::format_date(RentMy::toStoreTimeZone($order->rent_end), true);
            }
            $order->created = RentMy::format_date(RentMy::toStoreTimeZone($order->created));
            $order->pickup = !empty($order->pickup) ? $this->Locations->get($order->pickup)->name : '';
            $orderAddress = RentMy::$Model['OrderAddresses']->find()->where(['order_id'=>$order->id])->first();
            $order->address = $this->productContainer->getCustomerAddress($order, $orderAddress);
            $order->multi_store_delivery = [];
            if (!empty($orderAddress->multi_store_delivery)){
                $multiStoreDeliveryAddresses = json_decode($orderAddress->multi_store_delivery, true);
                if (!empty($multiStoreDeliveryAddresses['request']['drop_address']))
                    $order->multi_store_delivery['drop_address'] =  $this->productContainer->getDeliveryAddress($multiStoreDeliveryAddresses['request']['drop_address']);

                if (!empty($multiStoreDeliveryAddresses['request']['pickup_address']))
                    $order->multi_store_delivery['pickup_address'] = $this->productContainer->getDeliveryAddress($multiStoreDeliveryAddresses['request']['pickup_address']);
            }

            // quote_link
            if ($order->type == 2) {
                // get quote link
                $domain = RentMy::storeDomain(false, true);
                $order->quote_link = $domain . '/order/' . $order->uuid . '/quote/accept';

            }
            $order->quote_accept_btn_lbl = Configure::read('site_specific_contents.checkout_info.btn_quote_accept');

            // additional chage
            $order->charges = TableRegistry::getTableLocator()->get('OrderCharge')
                ->find()
                ->where(['order_id' => $orderid, 'store_id' => RentMy::$store->id])->toArray();

            $customArr = array();
            if (!empty($order->custom_values)) {
                $customFileds = json_decode($order->custom_values, true);

                foreach ($customFileds as $filed)
                    $customArr['custom.' . $filed['field_name']] = $filed['field_values'];
            }


            $payment_method = '';
            $transaction_reference = '';
            $deliveryAddress = $this->productContainer->getDeliveryAddress($order);
            $payment_amount = '';
            $change_amount = '';
            $amount_tendered = '';
            $payment_note = '';

            if (!empty($order->payments)) {
                $payment_method = $order->payments[0]['payment_gateway'];// Hash::get(Configure::read('paymentContent'), $order->payments[0]->content_id);
                $transaction_reference = $order->payments[0]->transaction_id ?? '';
                $payment_amount = $order->payments[0]->payment_amount ?? '';
                $payment_note = $order->payments[0]->note ?? '';
                $change_amount = $order->payments[0]->change_amount ?? '';
                $amount_tendered = $order->payments[0]->amount_tendered ?? '';

                foreach ($order->payments as $payment) {
                    if (in_array($payment->status, [1, 2])) {
                        if (in_array($payment->content_id, [1, 2])) {
                            $pcontent = 'Card';
                        } elseif ($payment->content_id == 3) {
                            $pcontent = 'Card swipe';
                        } elseif ($payment->content_id == 4) {
                            $pcontent = 'Cash';
                        } elseif ($payment->content_id == 7) {
                            $pcontent = 'Atrium';
                        } elseif (in_array($payment->content_id, [5, 6])) {
                            $pcontent = $payment->payment_gateway;
                        } else {
                            $pcontent = 'Other';
                        }
                        if ($payment->payment_method == 'Authorized') {
                            $pcontent .= ' (Authorized)';
                        }
                        $orderPayments['list'][] = ['content' => $pcontent, 'amount' => Number::precision($payment->payment_amount, 2)];
                    }
                }

                $_subTotal = $order->sub_total;
                $paymentSummary = $this->Payments->paymentSummary($order);
                $order->sub_total = $_subTotal;
                $orderPayments['summary'] = [
                    'total' => $paymentSummary['grand_total'],
                    'order_total' => $paymentSummary['order_total'],
                    'paid' => $paymentSummary['paid'],
                    'due' => $paymentSummary['due'],
                    'deposit' => $paymentSummary['deposit'],
                    'amount_tendered' => $paymentSummary['amount_tendered'],
                    'amount_change' =>   $paymentSummary['amount_change'],
                ];

                $totalRefundObj = $this->Refunds->find();
                $totalRefund = $totalRefundObj
                    ->select(
                        ['sum' => $totalRefundObj->func()->sum('amount')])
                    ->where(['order_id' => $orderid])
                    ->first();
                $refund = !empty($totalRefund['sum']) ? $totalRefund['sum'] : 0.00;
                $orderPayments['summary']['due'] += $refund;
                $orderPayments['summary']['due'] = Number::precision($orderPayments['summary']['due'],2) ;
                $orderPayments['summary']['paid'] = ($orderPayments['summary']['paid'] - $refund);
                $orderPayments['summary']['paid'] =  Number::precision($orderPayments['summary']['paid'],2);
            }
            $order->payments = $orderPayments;
            /** Payment Info End */

            $order->store_order_id = RentMy::getOrderPrefix() . $order->store_order_id;

            $order->delivery_method = $this->DeliveryDetails->_getDeliveryMethod($order);
            // get delivery zone name
            if ($order->shipping_method == 2) {
                $delivery = $this->DeliveryDetails->find()->where(['order_id' => $order->id])->order(['id' => 'ASC'])->first();
                $deliveryZoneConfig = json_decode($delivery['config'], true);
                $order->delivery_zone = $deliveryZoneConfig['name'];
            }
            $order->coupon = !empty($order->coupon) ? '(' . $order->coupon->code . ')' : '';
            foreach (RentMy::getOrderStatus() as $childs) {
                $orderStatus[$childs['id']] = $childs['label'];
                if (!empty($childs['child'])) {
                    foreach ($childs['child'] as $child) {
                        $orderStatus[$child['id']] = $child['label'];
                    }
                }
            }
            $order->status = Hash::get($orderStatus, $order->status);
            $order->total = $this->Orders->getOrderTotal($order);


            $itemData = [];
            foreach ($order->order_items as $item){
                $item->additional = json_decode($item->additional, true);
                $item->discount =['off_amount' => $item->off_amount,'coupon_amount'=> $item->coupon_amount,
                    'discount_sub_total'=>   $item->additional['discount_sub_total'],'coupon_sub_total'=>   $item->additional['coupon_sub_total']];
                if (!empty($item->additional['automatic_coupon_sub_total']) && ($item->discount['coupon_sub_total'] > $item->additional['automatic_coupon_sub_total'])){
                    $item->discount['coupon_sub_total'] = $item->additional['automatic_coupon_sub_total'];
                }
                //product option features
                $getItemizedAndTotal = RentMy::$Model['OrderProductOptions']->getItemizedAndTotal($item->id, 'order');
                $item->order_product_options = $getItemizedAndTotal['items'];

                $itemData[] = $item;
            }
            $order->order_items = $itemData;

            $contents = $this->Contents->find()->where(['store_id' => $order->store_id])->where(['tag' => 'site_specific'])->first();
            if ($contents) {
                $contents = json_decode($contents->contents, true);
                $contents_product_details = $contents['product_details'];
                $contents = $contents['general'];
            }

            $storeInfo = array();
            $storeInfo['address'] = empty($contents) ? '' : $contents['address'];
            $storeInfo['email'] = empty($contents) ? '' : $contents['email'];
            $storeInfo['phone'] = empty($contents) ? '' : $contents['phone'];

            $storeInfo['logo'] = !empty($store['logo']) ? RentMy::makeS3Url('/store-logo/' . $store['id'] . '/' . $store['logo']) : null;
            $storeInfo['start_date'] = empty($contents_product_details['start_date']) ? 'Rent Start: ' : $contents_product_details['start_date'] . ': ';
            $storeInfo['end_date'] = empty($contents_product_details['end_date']) ? 'Rent End: ' : $contents_product_details['end_date'] . ': ';
            $storeInfo['plan'] = strtoupper($store['store_type']);
            $order->store = $storeInfo;

            /* ########## start replacing template text  ########### */
            if ($template) {
                /** Rental date start */
                $rental_start_date = '';
                $rental_start_time = '';
                $rental_end_date = '';
                $rental_end_time = '';

                $orderItem = $this->OrderItems->find()->where(['order_id' => $orderid])->where(['rental_type !=' => 'buy'])->first();
                if ($orderItem) {
                    $rental_start_date = RentMy::format_date(RentMy::toStoreTimeZone($orderItem->rent_start), false, true);
                    $rental_start_time = RentMy::toStoreTimeZone($orderItem->rent_start, 'h:i A');
                    $rental_end_date = RentMy::format_date(RentMy::toStoreTimeZone($orderItem->rent_end), false, true);
                    $rental_end_time = RentMy::toStoreTimeZone($orderItem->rent_end, 'h:i A');

                }

                $orderShowId = !empty(RentMy::$storeConfig['order']['order_sequence']['active'])?$order->store_order_id:$order->id;

                /** Rental date end */
                $replacingObj = array(
                    'rental.start.date' => $rental_start_date,
                    'rental.start.time' => $rental_start_time,
                    'rental.end.date' => $rental_end_date,
                    'rental.end.time' => $rental_end_time,

                    'order.amount_tendered' => $amount_tendered,
                    'order.change_amount' => $change_amount,
                    'order.paid_amount' => $payment_amount,
                    'order.payment_method' => $payment_method,
                    'order.delivery_address' => $deliveryAddress,
                    'order.transaction_reference' => $transaction_reference,

                    'order.order_id' => $orderShowId,
                    'order.type' => $order->type,
                    'order.delivery' => $order->delivery_method,
                    'order.delivery_zone' => $order->delivery_zone,
                    'customer.name' => $order->first_name . ' ' . $order->last_name,
                    'customer.email' => $order->email,
                    'customer.mobile' => $order->mobile,
                    'customer.address' => $order->address,
                    'order.date' => $order['created'],
                    'order.status' => $order->status,

                    'store.name' => $order->store_name,
                    'store.address' => empty($contents) ? '' : $contents['address'],
                    'store.email' => empty($contents) ? '' : $contents['email'],
                    'store.phone' => empty($contents) ? '' : $contents['phone'],
                );

                if (!empty($order->multi_store_delivery)){
                    if (!empty($order->multi_store_delivery['drop_address']))
                        $replacingObj['order.delivery_address'] = $order->multi_store_delivery['drop_address'];

                    if (!empty($order->multi_store_delivery['pickup_address']))
                        $replacingObj['order.pickup_address'] = $order->multi_store_delivery['pickup_address'];
                }

                $replacingObj = array_merge($customArr, $replacingObj);
                $template->general = $this->replaceTemplateText($replacingObj, $template->general);
                if ($order->type == 2)
                    $template->general = str_replace('Order Status', 'Quote Status', $template->general);

                $template->address_body = $this->replaceTemplateText($replacingObj, $template->address_body);
                $template->store_address = $this->replaceTemplateText($replacingObj, $template->store_address);
                $template->message_1 = $this->replaceTemplateText($replacingObj, $template->message_1);
                $template->message_2 = $this->replaceTemplateText($replacingObj, $template->message_2);
                $template->options = json_decode($template->options, true);

                if (!empty($template->options['store_name_text']))
                    $template->options['store_name_text'] = $this->replaceTemplateText($replacingObj, $template->options['store_name_text']);

                if (!empty($template->options['customer_info_text']))
                    $template->options['customer_info_text'] = $this->replaceTemplateText($replacingObj, $template->options['customer_info_text']);
            }


            /* ######### end replacing template text ########## */
            $order['payment_method'] = $payment_method;
            $order['payment_note'] = $payment_note;
//Tax classes
            $taxes=RentMy::$Model['TaxLookup']->getTaxData('order', $orderid);
            $order['tax']=$taxes;
            $order['price_with_tax'] = $store_config['tax']['price_with_tax'];
//Tax classes end
            $s3Obj = new S3();
            if ($s3Obj->exist('orders/' . 'signature_' . $orderid . '.png')) {
                $order['signature'] = RentMy::makeS3Url(DS . 'orders' . DS . 'signature_' . $orderid . '.png');
            }

            $receipts[] = ['order' => $order, 'template' => $template,];
        }

        $this->viewBuilder()->className('Dompdf.Pdf')
            ->layout('Dompdf.pdf/default')
            ->template('Pages/pdf/bulk_order')
            ->options(['config' => [
                'filename' => 'bulk_receipts',
                'render' => 'download'
            ]]);
        $view = $this->viewBuilder()->build();
        $view->set(compact( 'receipts', $receipts));
        $view->render();

        $this->apiResponse['message'] = 'Order has been updated successfully.';
    }

    public function bulkPickListDownload()
    {

        $requiredFields = ['order_list'];
        $data = $this->request->getData();
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Required fields missing', 'error' => $required];
            return;
        }

        RentMy::addModel(array('Orders', 'Images', 'OrderItems', 'Payments', 'Locations', 'Timezones', 'Templates', 'Contents', 'DeliveryDetails', 'OrderNotes', 'Stores', 'Assets', 'OrderAssets', 'ItemAssets', 'OrderProductOptions', 'OrderAddresses'));
        $orders = RentMy::$Model['Orders']->find() ->where(['Orders.id IN' => $data['order_list']]) ->toArray();

        $pickList = [];
        foreach ($orders as $order) {
            $orderid = $order['id'];
            //Load the order's store data
            RentMy::getStore($order->store_id, RentMy::$token->location);
            $order->store_name = RentMy::$store->slug;
            $order->logo = !empty(RentMy::$store->logo) ? RentMy::makeS3Url('/store-logo/' . RentMy::$store->id . '/' . RentMy::$store->logo) : null;
            $order->current_date = RentMy::format_date(RentMy::toStoreTimeZone(Time::now()));
            $order->created = RentMy::format_date(RentMy::toStoreTimeZone($order->created));
            $order->rent_start = RentMy::format_date(RentMy::toStoreTimeZone($order->rent_start), true);
            $order->pickup = !empty($order->pickup) ? RentMy::$Model['Locations']->get($order->pickup)->name : '';
            $order->status = Hash::get(RentMy::getOrderStatus(), $order->status);

            $orderAddress = RentMy::$Model['OrderAddresses']->find()->where(['order_id' => $order->id])->first();
            $order->address = $this->productContainer->getCustomerAddress($order, $orderAddress);
            $order->multi_store_delivery = [];
            if (!empty($orderAddress->multi_store_delivery)) {
                $multiStoreDeliveryAddresses = json_decode($orderAddress->multi_store_delivery, true);
                if (!empty($multiStoreDeliveryAddresses['request']['drop_address']))
                    $order->multi_store_delivery['drop_address'] = $this->productContainer->getDeliveryAddress($multiStoreDeliveryAddresses['request']['drop_address']);

                if (!empty($multiStoreDeliveryAddresses['request']['pickup_address']))
                    $order->multi_store_delivery['pickup_address'] = $this->productContainer->getDeliveryAddress($multiStoreDeliveryAddresses['request']['pickup_address']);
            }

            //get all items of the order
            $orderItems = RentMy::$Model['OrderItems']
                ->find('threaded')
                ->contain('Products', function ($q) {
                    return $q->select(['id', 'uuid', 'name', 'variant_set', 'is_tracked', 'client_specific_id']);
                })
                ->where(['order_id' => $order->id])
                ->toArray();
            //filter variant's product id's


            foreach ($orderItems as $key => $items) {
                $variantsProductsIds[] = $items['variants_products_id'];
                if (!empty($items['children'])) {
                    foreach ($items['children'] as $m => $child) {
                        $variantsProductsIds[] = $child['variants_products_id'];
                    }
                }
                $orderAssets = RentMy::$Model['OrderAssets']->find()
                    ->select(['id', 'return_status', 'return_date', 'return_charge', 'return_note', 'order_item_id', 'pickup_date'])
                    ->contain('Assets', function ($q) {
                        return $q->select(['id', 'serial_no', 'description', 'current_status', 'current_condition']);
                    })
                    ->where(['order_item_id' => $items->id])->toArray();
                $orderItems[$key]['order_assets'] = $orderAssets;

//            product Options
                $orderItems[$key]['order_product_options'] = RentMy::$Model['OrderProductOptions']->find()
                    ->select(['id', 'options', 'quantity', 'price'])->where(['content_item_id' => $items->id, 'content_type' => 'order'])
                    ->map(function ($fields) {
                        $fields['options'] = json_decode($fields['options'], true);
                        $customFields = [];
                        $value = '';
                        if (!empty($fields['options'])) {
                            foreach ($fields['options'] as $key => $option) {
                                $com = (count($fields['options']) - 1 == $key) ? '' : ', ';
                                $value .= $option['label'] . ': ' . $option['value'] . $com;
                            }
                        }

                        $fields['values'] = $value;
                        $fields['custom_fields'] = $customFields;
                        return $fields;
                    })->toArray();
            }

            //load images
            if (!empty($variantsProductsIds)) {
                $pImages = RentMy::$Model['Images']->find()->where(['variants_products_id IN' => $variantsProductsIds])->toArray();
                $imageCollection = new Collection($pImages);
                foreach ($orderItems as $items) {
                    $image = $imageCollection->match(['variants_products_id' => $items['variants_products_id']])->first();
                    $items->image = $image['image_small'];
                    if (!empty($items['children'])) {
                        foreach ($items['children'] as $m => $child) {
                            $image = $imageCollection->match(['variants_products_id' => $child['variants_products_id']])->first();
                            $child->image = $image['image_small'];
                        }
                    }
                }
            }

            // order notes .
            $orderNotes = RentMy::$Model['OrderNotes']->find()->where(['store_id' => RentMy::$store->id, 'content_type' => 'OrderNotes', 'order_id' => $order->id])
                ->map(function ($note) {
                    $note->created = RentMy::format_date(RentMy::toStoreTimeZone($note->created), false, false, true);
                    return $note;
                })
                ->toArray();
            $order->id = !empty(RentMy::$storeConfig['order']['order_sequence']['active'])?RentMy::getOrderPrefix() . $order->store_order_id:$order->id;

            $pickList[] = ['order' => $order, 'orderItems' => $orderItems, 'orderNotes' => $orderNotes];

        }

        if (empty($pickList)){
            $this->apiResponse['message'] = 'Not Found!';
            return;
        }

        //Set template name
        $pdfTemplate = 'order_picklist';

        $this->viewBuilder()->className('Dompdf.Pdf')
            ->layout('Dompdf.pdf/default')
            ->template('Pages/pdf/bulk_' . $pdfTemplate)
            ->options(['config' => [
                'filename' => $orderid,
                'render' => 'download'
            ]]);

        $view = $this->viewBuilder()->build();
        $view->set(compact('pickList')); //compact('order', 'orderItems', 'orderNotes')
        $view->render();
    }


    /**
     * @desc Gives the details of a order
     */
    public function view($id)
    {
        try {
            if ($id != null) {
                RentMy::setDbReadable();
                $details = $this->Orders->view($id);
                if ($details) {
                    $this->httpStatusCode = 200;
                    $this->apiResponse['data'] = $details;
                } else {
                    throw new Exception('No order found.');
                }
            } else {
                throw new Exception('Sorry! Invalid order.');
            }
        } catch (Exception $ex) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = $ex->getMessage();
        }
    }

    /**
     * item delete of an order
     */
    public function itemDelete($id)
    {
        try {
            if ($id != null) {
                $order = $this->Orders->OrderItems->itemDelete($id);
                if ($order) {
                    $this->apiResponse['data'] = $order;
                }
            } else {
                throw new Exception('Sorry! Invalid order.');
            }
        } catch (Exception $ex) {
            $this->apiResponse['message'] = 'No order found';
        }
    }

    /**
     * change status of an order
     *
     * @param status
     * @param order_id
     */
    public function changeStatus($id, $status)
    {
        RentMy::addModel(['Orders', 'ProductsAvailabilities', 'OrderItems', 'Quantities']);
        $data = $this->request->getQueryParams();

        $order = RentMy::$Model['Orders']->get($id);
        $olderStatus = $order->status;
        $order->status = $status;
        if ($status == 1){
            $order->quote_status = 2;
            $assetChecker = RentMy::$Model['Orders']->orderAssetChecking($id);
            $order_asset_ids = [];
            if ($assetChecker['returnable_asset'] && !$data['force_return']){
                $this->httpStatusCode = 400;
                $this->apiResponse['returnable_asset'] = true;
                $this->apiResponse['message'] = "Some assets didn't return yet.";
                $this->apiResponse['assets'] = $assetChecker['assets'];
                return;
            }
        }

        if (RentMy::$Model['Orders']->save($order)) {

            if (!empty($data['force_return']) && !empty($assetChecker['assets'])){
                $order_asset_ids = array_column($assetChecker['assets'], 'order_asset_id');
                RentMy::$Model['Orders']->returnAsset($order->id, $order_asset_ids);
            }
            if ($olderStatus != $status)
                RentMy::addNotificationQueue('ChangeStatus', RentMy::$store->id, ['location'=>RentMy::$token->location], ['type' => 'ChangeStatus', 'order_id' => $id, 'content_id' => $id, 'user_id' => RentMy::$token->id, 'old_status' => $olderStatus, 'new_status' => $order->status, 'source' => RentMy::$token->source]);

            if ($order->type == 1) {
                $orderItems = RentMy::$Model['OrderItems']->find()->where(['order_id'=> $order->id, 'store_id' => RentMy::$store->id])->toArray();
                if ($order->status == "1") { // for archived orders , move all availability disabled
                    RentMy::$Model['ProductsAvailabilities']->updateAll(['status' => 0], ['order_id' => $id]);
                    foreach ($orderItems as $orderItem) {
                        if ($orderItem->rental_type == 'buy') {
                            $quantity = RentMy::$Model['Quantities']->get($orderItem->quantity_id);
                            $quantity->quantity += $orderItem->quantity;
                            RentMy::$Model['Quantities']->save($quantity);
                        }
                    }
                }elseif ($olderStatus == "1" && $status != '1') { // activate all availability when moved from archived to any other status
                    RentMy::$Model['ProductsAvailabilities']->updateAll(['status' => 1], ['order_id' => $id]);
                    foreach ($orderItems as $orderItem) {
                        if ($orderItem->rental_type == 'buy') {
                            $quantity = RentMy::$Model['Quantities']->get($orderItem->quantity_id);
                            $quantity->quantity -= $orderItem->quantity;
                            RentMy::$Model['Quantities']->save($quantity);
                        }
                    }
                }
            }
            $this->apiResponse['message'] = "Order status changed";
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
        }
    }


    /**
     * change status of multiple orders
     *
     * @param status
     * @params id - array order_ids
     * @API POST '/orders/status/:status
     */
    public function changeMultipleStatus($status)
    {
        try {
            RentMy::addModel(['Orders', 'ProductsAvailabilities']);
            $this->request->allowMethod(['post']);
            $data = $this->request->getData();
            $ids = $data['order_ids'];

            if (empty($ids)) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Invalid order id';
                return;
            }
            $orders = RentMy::$Model['Orders']->updateAll(['status' => $status], ['store_id' => RentMy::$store->id, 'id in' => $ids]);
            RentMy::$Model['ProductsAvailabilities']->updateAll(['status' => 0], ['order_id in' => $ids]);
            $this->apiResponse['message'] = "Order status changed";
            return;
        } catch (Exception $ex) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Orders can't be archived. Please try again.";
        }

    }

    /**
     * Change status by process ( as complete)
     * @param $order_id
     * @API POST - /orders/status/:id
     */
    public function changeStatusByProcess()
    {
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();
        RentMy::addModel(['Orders']);
        $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id'], 'store_id' => RentMy::$store->id])->first();
        if (empty($order)) {
            throw new UnauthorizedException('Invalid order.');
            return;
        }
        $order_status = $data['status'];// status
        if (!empty($order_status)) {
            $order->status = $order_status;
            $old_order_status = $order->status;
            if (RentMy::$Model['Orders']->save($order)) {
                // RentMy::addQueue('Order', ['type' => 'ChangeStatus', 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'old_status' => $old_order_status, 'new_status' => $order->status, 'auto' => true, 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source]); // add order log queue
                RentMy::addNotificationQueue('ChangeStatus', RentMy::$store->id, ['location'=>RentMy::$token->location], ['type' => 'ChangeStatus', 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'old_status' => $old_order_status, 'new_status' => $order->status, 'auto' => true, 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source]);

                $this->apiResponse['message'] = "Order processed successfully.";
                return;
            }
        }
    }

    /**
     * update order
     */
    public function update($id)
    {
        $this->loadModel('Orders');
        $this->loadModel('Locations');
        try {
            if ($id != null) {
                $order = $this->Orders->get($id);
                $pickUpLocation = $order->pickup;
                $returnLocation = $order->return_to ?? $pickUpLocation;
                $data = $this->request->getData();
                if (!empty($data['created'])) {
                    $data['created'] = RentMy::toUTC($data['created'], 'Y-m-d H:i:00');
                }

                if (!empty($data['delivery_date'])){
                    $data['delivery_date'] = RentMy::toUTC($data['delivery_date'], 'Y-m-d H:i:00');

                    RentMy::addModel(['DeliveryDetails']);
                    RentMy::$Model['DeliveryDetails']->updateALL(['delivery_date' => $data['delivery_date']], ['order_id' => $id]);
                }

                $options = !empty($order->options) ? json_decode($order->options, true) : [];

                if (!empty($data['quote_emails'])){
                    $options['quote']['emails'] = $data['quote_emails'];
                }


                if (!empty($data['quote_note'])){
                    $options['quote']['note'] = $data['quote_note'];
                }

                $data['options'] = json_encode($options);

                $order = $this->Orders->patchEntity($order, $data);
                if (!$this->Orders->save($order)) {
                    throw new Exception('No order found.');
                }
                if (!empty($data['pickup'])) {
                    $returnTo = '';
                    if (!empty($order['return_to']))
                        $returnTo = $this->Locations->get($order['return_to'])->name;

                    if ($pickUpLocation != $data['pickup'])
                        RentMy::addQueue('Order', ['type'=>'PickupLocationChange', 'order_id'=>$order->id, 'content_id'=>$order->id, 'user_id'=>RentMy::$token->id, 'store_id'=>RentMy::$store->id, 'prev_location'=>$pickUpLocation, 'new_location'=>$data['pickup'], 'source'=>RentMy::$token->source]);
                    $this->apiResponse['data'] = $returnTo;
                }

                if (!empty($data['return_to'])) {
                    if ($returnLocation != $data['return_to'])
                        RentMy::addQueue('Order', ['type'=>'ReturnLocationChange', 'order_id'=>$order->id, 'content_id'=>$order->id, 'user_id'=>RentMy::$token->id, 'store_id'=>RentMy::$store->id, 'prev_location'=>$returnLocation, 'new_location'=>$data['return_to'], 'source'=>RentMy::$token->source]);;
                }


            } else {
                throw new Exception('Sorry! Invalid order.');
            }
        } catch (Exception $ex) {
            $this->apiResponse['message'] = 'No order found';
        }
    }

    /**
     * Item add to an order
     * checking availability
     * checking store time, holiday
     *
     * @param deposit_amount: 0
     * @param deposite_tax: "false"
     * @param driving_license_required: false
     * @param location: 24
     * @param price: 25
     * @param price_id: 6956
     * @param product_id: 6109
     * @param quantity: 1
     * @param rent_end: "2019-06-02 15:00"
     * @param rent_start: "2019-05-31 15:00"
     * @param rental_duration: 1
     * @param rental_type: "rent"
     * @param sales_tax: 0
     * @param term: 2
     * @param token: ""
     * @param variants_products_id: 6737
     */
    public function addItem()
    {
        if (!$this->request->is('post')) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid Request';
            return;
        }
        $this->add_model(array('Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'Orders', 'ProductPrices', 'VariantsProducts', 'CartItems'));
        $data = $this->request->getData();
        $data['rental_type'] = ($data['rental_type'] == 'buy') ? 'buy' : 'rent';
        $data['sales_tax'] = $this->productContainer->getSalesTax($data['product_id'], $this->storeId, $data['rental_type']);
        $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->apiResponse['error'] = Configure::read('message.missing_param');
            return;
        }
        $quantity = $this->Quantities->find()
            ->where(['variants_products_id' => $data['variants_products_id']])
            ->where(['location' => $data['location']])
            ->first();


        if ($data['source'] != 'admin') {

            if (empty($quantity)) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Out of Stock!';
                return;
            }
        }

        if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
            $this->apiResponse['error'] = 'Invalid Rental Date';
            return;
        }

        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end']);
            $data['rent_start'] = $rentalDates['start_date'];
            $data['rent_end'] = $rentalDates['end_date'];

            $order = $this->Orders->find()->where(['id' => $data['order_id']])->first();

            RentMy::addModel(['OrderItems']);
            $orderItem = RentMy::$Model['OrderItems']->find()->where(['rental_type !=' => 'buy', 'order_id' => $data['order_id']])->first();

            if (!empty($order) && ((empty($order->rent_start) || empty($order->rent_end)) || empty($orderItem))) {
                $order_data = [
                    'rent_start' => RentMy::toUTC($data['rent_start']),
                    'rent_end' => RentMy::toUTC($data['rent_end']),
                    'purchase_type' => 'rent'
                ];
                $order = $this->Orders->patchEntity($order, $order_data);
                $this->Orders->save($order);
            }
        }
        if (!empty($data['order_id'])) { // get order date
            $order = $this->Orders->find()->where(['id' => $data['order_id']])->first();

            if (!empty($order['rent_start']) && !empty($orderItem)) {
                $data['rent_start'] = RentMy::toStoreTimeZone($order['rent_start'], 'Y-m-d H:i:00');
                $data['rent_end'] = RentMy::toStoreTimeZone($order['rent_end'], 'Y-m-d H:i:00');
            }
        }

        if ($data['rental_type'] == 'buy') {
            $prices = $this->ProductPrices->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->toArray();
            if (isset($data['custom_fields']) && !empty($data['custom_fields'])) {
                RentMy::addModel(['OrderProductOptions']);
                RentMy::$Model['OrderProductOptions']->calculatePricing($prices, $data['custom_fields']);
            }
            $data['price'] = 0;
            if (!empty($prices)) {
                $price = $prices[0];
                $data['price'] = $price->price;
                if (RentMy::$storeConfig['inventory']['promo_price'] && ($price->promo_price > 0)) {
                    $data['price'] = $price->promo_price;
                }
            }
        } else {
            $data['price'] = $this->ProductPrices->getRentalPriceByDates($data['product_id'], $data['variants_products_id'], $data['rent_start'], $data['rent_end'], [], 'order', $data['custom_fields'] ?? []);
            $data['rental_type'] = 'rent';
        }

        $data['quantity_id'] = $quantity->id;
        $data['rental_duration'] = empty($data['rental_duration']) ? 1 : $data['rental_duration'];
        if ($data['source'] != 'admin') { // admin don't have availability checking
            if (in_array($data['rental_type'], ['hourly', 'daily', 'weekly', 'monthly', 'fixed', 'rent'])) {
                $this->loadModel('Holidays');
                /**
                 * @TODO - Need to fix holiday checking.
                 */
////                    $storeTime = $this->Holidays->checkStoreTime($this->parseToken->store_id, $data['rent_start']);
////                    if (!$storeTime) {
////                        $this->apiResponse['error'] = 'Sorry! off season.';
////                        return;
////                    }
                $holiday = $this->Holidays->checkHoliday(RentMy::$store->id, RentMy::toUTC($data['rent_start']));
                if ($holiday['success']) {
                    $this->apiResponse['error'] = "Product not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                    return;
                }
                $rent_start = RentMy::toUTC($data['rent_start'], 'Y-m-d H:i');
                $rent_end = RentMy::toUTC($data['rent_end'], 'Y-m-d H:i');
                $available = $this->ProductsAvailabilities->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);
            } else {
                $rent_start = Time::now()->format('Y-m-d H:i');
                $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
                $available = $this->ProductsAvailabilities->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);
            }
            if ($available < $data['quantity']) {
                $this->apiResponse['error'] = 'Not available.';
                return;
            }
        }
        $data['store_id'] = RentMy::$store->id;
        $data['user_id'] = isset($this->parseToken->id) ? $this->parseToken->id : 0;
        $variant = $this->Products->_getDefaultAttribute($data['variants_products_id']);
        $data['variant_chain_id'] = $variant['variant_chain_id'];
        $data['variant_chain_name'] = $variant['variant_chain_name'];

        $order = $this->Orders->addProduct($data);

        if ($order) {
            $this->apiResponse['data'] = $order;
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = Configure::read('message.save');
        }
    }


    /**
     * package Item add to an order
     * checking availability
     * checking store time, holiday
     *
     * @param deposit_amount: 0
     * @param deposite_tax: "false"
     * @param driving_license_required: false
     * @param location: 24
     * @param price: 25
     * @param price_id: 6956
     * @param product_id: 6109
     * @param quantity: 1
     * @param rent_end: "2019-06-02 15:00"
     * @param rent_start: "2019-05-31 15:00"
     * @param rental_duration: 1
     * @param rental_type: "rent"
     * @param sales_tax: 0
     * @param term: 2
     * @param token: ""
     * @param variants_products_id: 6737
     */
    public function addPackageItem(){
        if ($this->request->is('post')) {
            RentMy::addModel(['Orders', 'OrderItems', 'Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'CartItems', 'ProductPrices', 'VariantsProducts', 'Holidays', 'DeliveryDetails', 'ProductPackages']);
            $data = $this->request->getData();

            $quantity = RentMy::$Model['Quantities']->find()->where(['product_id' => $data['package_id'], 'variants_products_id' => $data['variants_products_id']])->first();

            if (empty($quantity)) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Out of stock';
                return;
            }

            $data['quantity_id'] = $quantity->id;
            $data['store_id'] = RentMy::$store->id;
            $data['sales_tax'] = $this->productContainer->getSalesTax($data['package_id'], $this->storeId, $data['rental_type']);
            $requiredParam = ['package_id', 'variants_products_id', 'location', 'order_id'];
            if ($this->array_keys_exist($data, $requiredParam)) {
                $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
                if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
                    $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end']);
                    $data['rent_start'] = $rentalDates['start_date'];
                    $data['rent_end'] = $rentalDates['end_date'];
                    RentMy::addModel(['OrderItems']);
                    $orderItemInfo = RentMy::$Model['OrderItems']->find()->where(['rental_type !=' => 'buy', 'order_id' => $data['order_id']])->first();

                    if (!empty($order) && ((empty($order->rent_start) || empty($order->rent_end)) || empty($orderItemInfo))) {

                        $order_data = [
                            'rent_start' => RentMy::toUTC($data['rent_start']),
                            'rent_end' => RentMy::toUTC($data['rent_end']),
                            'purchase_type' => 'rent'
                        ];
                        $order = RentMy::$Model['Orders']->patchEntity($order, $order_data);
                        RentMy::$Model['Orders']->save($order);
                    }
                }

                if (!empty($data['order_id'])) { // get order date
                    $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
                    if (!empty($order['rent_start']) && !empty($orderItemInfo)) {
                        $data['rent_start'] = RentMy::toStoreTimeZone($order['rent_start'], 'Y-m-d H:i:00');
                        $data['rent_end'] = RentMy::toStoreTimeZone($order['rent_end'], 'Y-m-d H:i:00');
                    }
                }
                $siteLinkContent = RentMy::getSiteLinkContent();
                if ($data['rental_type'] != 'buy') {
                    if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))){
                        $this->apiResponse['error'] = 'Invalid Rental Date';
                        return;
                    }
                    $responseOption = RentMy::$storeConfig;
                    $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];

                    $storeTime = RentMy::$Model['Holidays']->checkStoreTime(RentMy::$store->id, $data['rent_start'], $responseOption);
                    if (!$storeTime) {
                        $this->apiResponse['error'] = $siteLinkContent['message']['error_store_close'];
                        return;
                    }
                    $holiday = RentMy::$Model['Holidays']->checkHoliday(RentMy::$store->id, $data['rent_start']);
                    if ($holiday['success']) {
                        $this->apiResponse['error'] = "Package not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                        return;
                    }
                    $data = $this->_getPrice($data, $optionPrice);

                    $rent_start = $data['rent_start'];
                    $rent_end = $data['rent_end'];
                    $data['rental_type'] = 'rent';
                }else{
                    $rent_start = Time::now()->format('Y-m-d H:i');
                    $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
                }

//                Availability checking
                $productAvailable = array();
                $unavailableProducts = [];
                foreach ($data['products'] as $item) {
                    $quantity = RentMy::$Model['Quantities']->find()
                        ->where(['variants_products_id' => $item['variants_products_id']])
                        ->where(['location' => $data['location']])
                        ->first();
                    if (empty($quantity)) {
                        $this->apiResponse['error'] = 'Out of Stock!';
                        return;
                    }
                    $rentStartToUtc = RentMy::toUTC($rent_start);
                    $rentEndToUtc = RentMy::toUTC($rent_end);
                    $pAvailable = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($item['product_id'], $item['variants_products_id'], $data['location'], $rentStartToUtc, $rentEndToUtc);
                    $productAvailable[] = ['product_id' => $item['product_id'], 'variants_products_id' => $item['variants_products_id'], 'available' => $pAvailable];

                    RentMy::addModel(['VariantsProducts', 'Products']);
                    $variant = RentMy::$Model['VariantsProducts']->get($item['variants_products_id']);
                    $product = RentMy::$Model['Products']->get($variant->product_id);

                    if (($data['quantity'] * $item['quantity']) > $pAvailable) {
                        $unavailableProducts[] = ["product_id" => $product['id'], 'name' => $product['name']];

                    }
                }
                if (!empty($unavailableProducts)){
                    $this->httpStatusCode = 400;
                    $productsName = implode(', ', array_column($unavailableProducts, 'name'));
                    $this->apiResponse['error'] = "Following item(s) is not available: ". $productsName;
                    return;
                }

                $packageItem = RentMy::$Model['OrderItems']->addPackageItem($data, $order);
                if (!empty($packageItem)){
                    RentMy::$Model['Orders']->updateOrderTaxLookup($order['id']);
                    RentMy::$Model['Orders']->_updatePriceNQty($order);
                }
                $this->apiResponse['data'] = RentMy::$Model['Orders']->view($order->id);

            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
            }
        } else {
            $this->apiResponse['error'] = Configure::read('message.request');
        }

    }

    /**
     * Used on package add to cart , need to fix  with global pricing
     * @param $data
     * @param $optionPrice
     */
    private function _getPrice($data, $optionPrice)
    {
        RentMy::addModel(['VariantsProducts', 'ProductPrices']);
        $priceType = RentMy::$Model['VariantsProducts']->get($data['variants_products_id']);
        if ($data['rental_type'] == 'buy') {
            $price = RentMy::$Model['ProductPrices']->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->toArray();
            $price = RentMy::formatPricing($price);

            if (isset($data['custom_fields']) && !empty($data['custom_fields'])) {
                RentMy::addModel(['OrderProductOptions']);
                RentMy::$Model['OrderProductOptions']->calculatePricing($price, $data['custom_fields']);
            }

            $data['price'] = $price[0]->price;
            if (RentMy::$storeConfig['inventory']['promo_price']) {
                $data['price'] = !empty($price[0]->promo_price) ? $price[0]->promo_price : $price[0]->price;
            }
        } else {
            $data['price'] =  RentMy::$Model['ProductPrices']->getRentalPriceByDates($data['package_id'], $data['variants_products_id'], $data['rent_start'], $data['rent_end'], [], 'order', $data['custom_fields'] ?? []);
        }

        return $data;
    }
//    /**
//     * Update an item of an order
//     */
//    public function updateItem()
//    {
//        if ($this->request->is('post')) {
//            $this->add_model(array('Products', 'Quantities', 'ProductsAvailabilities', 'Timezones', 'Orders', 'ProductPrices', 'VariantsProducts', 'CartItems'));
//            $data = $this->request->getData();
//            $data['sales_tax'] = $this->productContainer->getSalesTax($data['product_id'], $this->storeId, $data['rental_type']);
//            $requiredParam = ['product_id', 'quantity', 'variants_products_id', 'location'];
//            if ($this->array_keys_exist($data, $requiredParam)) {
//                $quantity = $this->Quantities->find()
//                    ->where(['variants_products_id' => $data['variants_products_id']])
//                    ->where(['location' => $data['location']])
//                    ->first();
//                if (empty($quantity)) {
//                    $this->apiResponse['error'] = 'Out of Stock!';
//                    return;
//                }
//                if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
//                    $this->apiResponse['error'] = 'Invalid Rental Date';
//                    return;
//                }
//                $responseOption = $this->ProductPrices->getOption($this->parseToken->store_id);
//                $optionPrice = empty($responseOption['rental_price_option']) ? false : $responseOption['rental_price_option'];
//                if (empty($responseOption['show_start_date']) && empty($responseOption['show_start_time'])) {
//                    $data['rent_start'] = date('Y-m-d H:i');
//                    $data['rent_start'] = $this->Timezones->_dateLocal($data['rent_start'], 'yyyy-MM-dd HH:mm');
//                } elseif (!empty($responseOption['show_start_date']) && empty($responseOption['show_start_time'])) {
//                    $dt = new \DateTime($data['rent_start']);
//                    $data['rent_start'] = $dt->format('Y-m-d') . ' ' . date('H:i');
//                    $data['rent_start'] = $this->Timezones->_dateLocal($data['rent_start'], 'yyyy-MM-dd HH:mm');
//                }
//
//                if (!empty($responseOption['show_end_date']) && empty($responseOption['show_end_time'])) {
//                    $dt = new \DateTime($data['rent_end']);
//                    $data['rent_end'] = $dt->format('Y-m-d') . ' ' . '23:59';
//                }
//
//                $priceType = $this->VariantsProducts->get($data['variants_products_id']);
//                $data['rental_type'] = empty($data['rental_type']) ? 'buy' : $data['rental_type'];
//
//                if ($data['rental_type'] == 'buy') {
//                    $price = $this->ProductPrices->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->first();
//                    $data['price'] = $price->price;
//                } else {
//                    if ($priceType->price_type == 2) {
//                        $price = $this->ProductPrices->find()->where(['duration_type' => 'fixed'])->where(['variants_products_id' => $data['variants_products_id']])->first();
//                        $data['price'] = $price->price;
//                        $data['rental_type'] = 'fixed';
//                    } else if ($priceType->price_type == 3) {
//                        if ($optionPrice) {
//                            $priceData = $this->ProductPrices->get($data['price_id']);
//                            $data['rental_type'] = $priceData->duration_type;
//                            if (empty($data['rent_end'])) {
//                                $price = $this->ProductPrices->get($data['price_id']);
//                                $data['price'] = $price->price;
//                            } else {
//                                $data['price'] = $this->ProductPrices->getItemPrice($data, $priceType->price_type, $priceData);
//                            }
//                        } else {
//                            $requiredParam = ['rent_start', 'rent_end'];
//                            if (!$this->array_keys_exist($data, $requiredParam)) {
//                                $this->apiResponse['error'] = Configure::read('message.missing_param');
//                                return;
//                            }
//                            $priceData = $this->ProductPrices->getPriceData($data, $priceType->price_type);
//                            $data['price_id'] = $priceData->id;
//                            $data['price'] = $this->ProductPrices->getPriceValue($data, $priceType->price_type, $priceData);
//                            $data['rental_type'] = $priceData->duration_type;
//                        }
//                    } else if ($priceType->price_type == 4) {
//                        $optionPrice = false;
//                        if ($optionPrice) {
//                            $priceData = $this->ProductPrices->get($data['price_id']);
//                            $data['rental_type'] = $priceData->duration_type;
//                            if (empty($data['rent_end'])) {
//                                $price = $this->ProductPrices->get($data['price_id']);
//                                $data['price'] = $price->price;
//                            } else {
//                                $data['price'] = $this->ProductPrices->getPriceValue($data, $priceType->price_type, $priceData);
//                                /* get flex price according to immediate lowest
//                                $data['price'] = $this->ProductPrices->getItemPrice($data, $priceType->price_type, $priceData);
//                                */
//                            }
//                        } else {
//                            $requiredParam = ['rent_start', 'rent_end'];
//                            if (!$this->array_keys_exist($data, $requiredParam)) {
//                                $this->apiResponse['error'] = Configure::read('message.missing_param');
//                                return;
//                            }
//                            $data['price'] = $this->ProductPrices->getPriceValue($data, $priceType->price_type, null);
//                            $data['rental_type'] = $this->ProductPrices->getRentalType($data);
//                        }
//                    }
//                }
//                $data['quantity_id'] = $quantity->id;
//                $data['rental_duration'] = empty($data['rental_duration']) ? 1 : $data['rental_duration'];
//
//                if (in_array($data['rental_type'], ['hourly', 'daily', 'weekly', 'monthly', 'fixed'])) {
//                    $this->loadModel('Holidays');
//
//                    // holiday checking
////                    $storeTime = $this->Holidays->checkStoreTime($this->parseToken->store_id, $data['rent_start'],$responseOption);
////                    if (!$storeTime) {
////                        $this->apiResponse['error'] = 'Rentals can not start when store is closed.  Enter valid rental start date and time.';
////                        return;
////                    }
//
//                    $data['rent_start'] = $this->Timezones->_dateGlobal($data['rent_start'], 'yyyy-MM-dd HH:mm:ss');
//                    if (empty($data['rent_end'])) {
//                        $data['rent_end'] = $this->CartItems->_getRentEndTime($data['rent_start'], $data['rental_duration'], $data['rental_type'], $priceData->duration);
//                    }
//                    $data['rent_end'] = $this->Timezones->_dateGlobal($data['rent_end'], 'yyyy-MM-dd HH:mm:ss');
//                    $holiday = $this->Holidays->checkHoliday($this->parseToken->store_id, $data['rent_start']);
//                    if ($holiday['success']) {
//                        $this->apiResponse['error'] = "Product not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
//                        return;
//                    }
//                    $data['start_date'] = $data['rent_start'];
//                    $data['end_date'] = $data['rent_end'];
//                    $notAvailableQuantity = $this->ProductsAvailabilities->available($data);
//                    $available = !empty($quantity->available) ? $quantity->available : $quantity->quantity - $notAvailableQuantity;
//                } else {
//                    $data['start_date'] = $time = Time::now();
//                    $data['end_date'] = $time->modify('+14 days');
//                    $data['start_date'] = $this->Timezones->_dateGlobal($data['start_date'], 'yyyy-MM-dd');
//                    $data['end_date'] = $this->Timezones->_dateGlobal($data['end_date'], 'yyyy-MM-dd');
//                    $notAvailableQuantity = $this->ProductsAvailabilities->available($data);
//                    $available = $quantity->quantity - $notAvailableQuantity;
//                }
//                if ($available < $data['quantity']) {
//                    $this->apiResponse['error'] = 'Not available.';
//                    return;
//                }
//                $data['store_id'] = $this->parseToken->store_id;
//                $data['user_id'] = isset($this->parseToken->id) ? $this->parseToken->id : 0;
//                $variant = $this->Products->_getDefaultAttribute($data['variants_products_id']);
//                $data['variant_chain_id'] = $variant['variant_chain_id'];
//                $data['variant_chain_name'] = $variant['variant_chain_name'];
//
//                if (empty($data['additional'])) {
//                    $data['add_to_order_total'] = 0;
//                }
//
//                $order = $this->Orders->addProduct($data);
//
//                if ($order) {
//                    $this->apiResponse['data'] = $order;
//                } else {
//                    $this->apiResponse['error'] = Configure::read('message.save');
//                }
//            } else {
//                $this->apiResponse['error'] = Configure::read('message.missing_param');
//            }
//        } else {
//            $this->apiResponse['error'] = Configure::read('message.request');
//        }
//    }

    /**
     * Update order item
     * @data[item_id]
     * @data[quantity]
     * @data[order_id]
     * @data[item_id]
     */
    public function updateItem()
    {
        // step 1 : get order details and order item details
        // step 2 : find the item availability
        // step 3 : calculate price
        // step 3 : calculated coupon & fixed discount
        // step 4 : update order item and quantity
        // step 5 : update order total
        // step 6 : update  product availabilities

        $this->request->allowMethod('post');
        RentMy::addModel(['Products', 'Quantities', 'ProductsAvailabilities', 'Orders', 'OrderItems', 'ProductPrices', 'VariantsProducts']);
        $data = $this->request->getData();
        $requiredParam = ['item_id', 'quantity'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Required field missing';
            return;
        }
        $orderItem = RentMy::$Model['OrderItems']->find()->where(['id' => $data['item_id'], 'order_id' => $data['order_id']])->first();
        $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
        $beforeTotal = RentMy::$Model['Orders']->getOrderTotal($order);
        $orderOptions = !empty($order->options) ? json_decode($order->options, true) : [];
        $vendorId = !empty($orderOptions['vendor_id']) ? $orderOptions['vendor_id'] : '';
        if (isset($data['tax_apply'])){
            $options = !empty($orderItem->options)?json_decode($orderItem->options, true):[];
            $options['tax_apply'] = !empty($data['tax_apply']);
            $orderItem->options = json_encode($options);

            RentMy::$Model['OrderItems']->save($orderItem);
            RentMy::$Model['Orders']->updateOrderTaxLookup($order->id);
            RentMy::$Model['Orders']->_updatePriceNQty($order);
            $orderDetails = RentMy::$Model['Orders']->view($order->id, $beforeTotal);

            $this->apiResponse['data'] = $orderDetails;
            return;
        }

        $orderItem->updated_quantity = ($data['quantity'] - $orderItem->quantity);
        $orderItem->quantity = $data['quantity'];
        $data['rent_start'] = Time::parse($order->rent_start)->format('Y-m-d H:i:00');
        $data['rent_end'] = Time::parse($order->rent_end)->format('Y-m-d H:i:00');
        $data['rental_type'] = $orderItem->rental_type;
        // availability for product
        if ($orderItem->product_type == 1) {
            // for buy items rent start will be current date
            if ($orderItem->rental_type == 'buy') {
                $data['rent_start'] = Time::now()->format('Y-m-d H:i:00');
                $data['rent_end'] = Time::now()->addDays(14)->format('Y-m-d H:i:00');
            }
//            $available = RentMy::$Model['ProductsAvailabilities']
//                ->getAvailableForProduct(
//                    $orderItem->product_id,
//                    $orderItem->variants_products_id,
//                    $orderItem->location, $data['rent_start']
//                );
            $available = RentMy::$Model['ProductsAvailabilities']
                ->getProductAvailability(
                    $orderItem->product_id,
                    $orderItem->variants_products_id, $orderItem->location,
                    $data['rent_start'], $data['rent_end']
                );

        } else { // availability for package
            if ($orderItem->rental_type == 'buy') {
                $data['rent_start'] = Time::now()->format('Y-m-d H:i:00');
                $data['rent_end'] = Time::now()->addDays(14)->format('Y-m-d H:i:00');
            }
            $available = RentMy::$Model['ProductsAvailabilities']
                ->getAvailableForPackage('order',
                    $orderItem->product_id, $orderItem->location, $data['rent_start'], $data['rent_end'],
                    ['order_item_id' => $orderItem->id]
                );
        }

        if ($available < $orderItem->updated_quantity) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Not available.';
            return;
        }

        // get buy price
        if ($orderItem->rental_type == 'buy') {
            $price = RentMy::$Model['ProductPrices']->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $orderItem->variants_products_id])->first();
            $orderItem->rental_price = $price->price;
            if (RentMy::$storeConfig['inventory']['promo_price'] && ($price->promo_price > 0)) {
                $orderItem->rental_price = $price->promo_price;
            }
        } else { // rent price
            $_startDate = RentMy::toStoreTimeZone($data['rent_start']);
            $_endDate = RentMy::toStoreTimeZone($data['rent_end']);
            $orderItem->rental_price = RentMy::$Model['ProductPrices']->getRentalPriceByDates($orderItem->product_id, $orderItem->variants_products_id, $_startDate, $_endDate, [], 'order', [], ['order_type' => 'order']);
            $options = !empty($order->options) ? json_decode($order->options, true) : [];
            if (!empty($options['membership_checkout'])){
                $orderItem->rental_price = 0;
            }
        }

        if (isset($data['modified_price'])){
            $orderItem->rental_price = $data['modified_price'];
        }
        $order['vendor_id'] = $vendorId;
        RentMy::$Model['OrderItems']->updateProductPrice($order, $orderItem);
        RentMy::$Model['Orders']->updateOrderTaxLookup($order->id);
        RentMy::$Model['Orders']->_updatePriceNQty($order);

        // update quantity for buy items

        $quoteOrder = isset($data['order_type']) && ($data['order_type'] == 2);

        if ($orderItem->rental_type == 'buy' && !$quoteOrder) {
            $quantity = RentMy::$Model['Quantities']->get($orderItem->quantity_id);
            $quantity->quantity = $quantity->quantity - $orderItem->updated_quantity;
            RentMy::$Model['Quantities']->save($quantity);
        } else { // update product availabilities .
            $productAvailability = RentMy::$Model['ProductsAvailabilities']->find()->where(['order_item_id' => $orderItem->id])->first();
            if (!empty($productAvailability)) {
                $productAvailability->quantity = $orderItem->quantity;
                RentMy::$Model['ProductsAvailabilities']->save($productAvailability);
            }
        }

        if ($orderItem->updated_quantity != 0) {
            $isIncrement = $orderItem->updated_quantity > 0;
            $requestedQuantity = abs($orderItem->updated_quantity);
            RentMy::addModel(['OrderProductOptions']);
            for ($count = 0; $count < $requestedQuantity; $count++)
                RentMy::$Model['OrderProductOptions']->updateQuantity($orderItem->id, $isIncrement, 'order');
        }


        RentMy::$Model['OrderItems']->updateChildQuantity($orderItem);
        /*
         * If update any order item recalculate the additional charge accordingly
         */
        RentMy::addModel(['OrderCharge']);
        RentMy::$Model['OrderCharge']->changesRecalculate($order->id);
        RentMy::$Model['Orders']->updateOrderTaxLookup($order->id);
        RentMy::$Model['Orders']->deliveryRecalculate($order->id);
        $orderDetails = RentMy::$Model['Orders']->view($order->id, $beforeTotal);
        $this->apiResponse['data'] = $orderDetails;
    }

    /**
     * Get order summary
     */
    public function summary($uid)
    {
        $id = substr($uid, 4);
        $this->apiResponse['data'] = $this->Orders->view($id);
    }

    /**
     * Get order details of a complete order
     */
    public function complete($uid)
    {
        $this->loadModel('Orders');
        $order = $this->Orders->find()->where(['uuid' => $uid])->first();
        $summary = [];
        if ($order) {
            $summary = $this->Orders->view($order->id);
            RentMy::logToGenius([
                "account" => @$order->event_location,
                "event" => "order_complete",
                "status" => "success",
                "description" => @$order->event_location . ' Order complete',
                "value" => 'Order ID: ' . $order->id,
                "custom_content" => json_encode([
                    'data' => $summary,
                ]),
                "ref2" => $order->customer_id
            ]);
        }
        $this->apiResponse['data'] = $summary;

    }

    public function iCalDownload($uid, $isWp = false)
    {
        $this->loadModel('Orders');
        $order = $this->Orders->find()->contain(['OrderItems.Products'])->where(['uuid' => $uid])->first();

        $timeZone = RentMy::getTimeZone();
        if (!in_array($timeZone, timezone_identifiers_list()))
            $timeZone = Configure::read('TIMEZONE');

        $desc = '';
        $desc .= "\\n";
        $desc .= 'Order ID: '.$order->id."\\n";
        $desc .= 'Timezone: '.$timeZone."\\n";
        $desc .= 'Rent Start: '.RentMy::toStoreTimeZone($order->rent_start,'Y-m-d H:i:s', $timeZone)."\\n";
        $desc .= 'Rent End: '.RentMy::toStoreTimeZone($order->rent_end,'Y-m-d H:i:s', $timeZone)."\\n";
        $desc .= "\\nOrder Items:\\n";
        foreach ($order['order_items'] as $item)
            $desc .= $item['product']['name'].'x'.$item['quantity']."\\n";

        $desc .= "\\n \\n";

        $VCalendar = new VCalendar();
        $VCalendar->setProcess('RentMy', strtoupper(RentMy::$store->name), 'v1.0', 'EN');
        $VCalendar->setMethod('PUBLISH');
        $VCalendar->setCalendarName(RentMy::$store->slug.' - rental');
        $VCalendar->setTimeZone($timeZone);
        $VCalendar->setStartDateTime(RentMy::toStoreTimeZone($order->rent_start,'Y-m-d H:i:s', $timeZone));
        $VCalendar->setEndDateTime(RentMy::toStoreTimeZone($order->rent_end,'Y-m-d H:i:s', $timeZone));
        $VCalendar->setStatus('CONFIRMED');
        $VCalendar->setTitle(ucwords(RentMy::$store->slug). ' | Order '.$order->id);
        $VCalendar->setDescription($desc);
        //$VCalendar->setOrganizer(ucwords(RentMy::$store->slug), '');
        $VCalendar->setClass('PUBLIC');
        $VCalendar->setCreatedDateTime(RentMy::toStoreTimeZone($order->created,'Y-m-d H:i:s', $timeZone));
        //$VCalendar->setLocation('Paris', 48.874086, 2.345640);
        $VCalendar->setUrl(RentMy::storeDomain($isWp).'/checkout/complete/'.$uid);
        $VCalendar->setSequence(4);
        $VCalendar->setLastUpdatedDateTime(RentMy::toStoreTimeZone($order->modified,'Y-m-d H:i:s', $timeZone));
        $VCalendar->setCategories(['SHOPPING', 'RENTALS', 'BUY']);
        $VCalendar->setUID('RNTM-'.RentMy::$store->id.'-'.time());

        if (filter_var($order->email, FILTER_VALIDATE_EMAIL) !== false )
            $VCalendar->addAttendee($order->first_name.' '.$order->last_name, 'REQ-PARTICIPANT', $order->email,false);
        $VCalendar->stream();
        exit();

    }

    /**
     * Get order details of a complete order
     */
    public function review($id)
    {
        RentMy::addModel(['Orders']);
        $order = RentMy::$Model['Orders']->find()->select(['store_id'])->where(['id' => $id, 'store_id' => RentMy::$store->id])->first();
        if (empty($order)) {
            throw new UnauthorizedException('Unauthorized');
            return;
        }
        $this->apiResponse['data'] = $this->Orders->details($id);
    }

    /**
     * Print receipt it will print the receipt based in the order information
     */
    public function printReceipt()
    {
        $this->add_model(array('Locations', 'Timezones'));
        $data = $this->request->getData();
        $order = $this->Orders
            ->find()
            ->contain(['OrderItems', 'OrderItems.Products', 'Payments', 'Coupons'])
            ->where(['Orders.id' => $data['order_id']])
            ->first();
        $this->_assigningTimezone($order->store_id);
        foreach ($order->order_items as $row) {
            $row->rent_start = empty($row->rent_start) ? $row->rent_start : $this->Timezones->_dateFormate($this->Timezones->_dateLocal($row->rent_start, 'yyyy-MM-dd HH:mm:ss'));
            $row->rent_end = empty($row->rent_end) ? $row->rent_end : $this->Timezones->_dateFormate($this->Timezones->_dateLocal($row->rent_end, 'yyyy-MM-dd HH:mm:ss'));
        }
        $order->created = $this->Timezones->_dateFormate($this->Timezones->_dateLocal($order->created, 'yyyy-MM-dd HH:mm:ss'));
        $order->pickup = !empty($order->pickup) ? $this->Locations->get($order->pickup)->location : '';
        $order->coupon = !empty($order->coupon) ? '(' . $order->coupon->code . ')' : '';
        $payment = array();
        if (!empty($order->payments)) {
            //$response = json_decode($order->payments[0]->response_text);
            $payment = array(
                'payment_gateway' => isset($order->payments[0]->payment_gateway) ? $order->payments[0]->payment_gateway : '',
                'transaction_id' => isset($order->payments[0]->transaction_id) ? $order->payments[0]->transaction_id : '',
            );
        }
        $order->status = RentMy::getOrderStatusStr($order->status);
//        $statuses = RentMy::getOrderStatus();
//        if (!empty($statuses[$order->status])) {
//            $order->status = $statuses[$order->status];
//        }

        $paymentMethod = Configure::read('paymentType');
        if (!empty($order->payments)) {
            if (!empty($paymentMethod[$order->payments[0]['type']])) {
                $order->payment_type = $paymentMethod[$order->payments[0]['type']];
            }
        }
        $this->viewBuilder()
            ->className('Dompdf.Pdf')
            ->layout('Dompdf.pdf/default')
            ->template('Orders/pdf/print_receipt')
            ->options(['config' => [
                'filename' => 'receipt - ' . $data['order_id'],
                'render' => 'download',
                'dpi' => 300
            ]]);
        $amount_tendered = isset($data['amount_tendered']) == true ? $data['amount_tendered'] : "";
        $change_amount = isset($data['change_amount']) == true ? $data['change_amount'] : "";
        $view = $this->viewBuilder()->build();
        $view->set(compact('order', 'amount_tendered', 'change_amount', 'payment'));
        $view->render();
    }

    /**
     * Availability checking of a product for order
     * @param $cartId
     * @return array
     */
    private function _checkAvailability($cartId)
    {
        $this->add_model(array('VariantsProducts', 'Quantities'));
        $cartItem = $this->CartItems->find('all')->where(['cart_id' => $cartId])->toArray();
        $pCount = array();
        foreach ($cartItem as $item) {
            $pCount[$item->quantity_id] = 0;
        }
        foreach ($cartItem as $item) {
            $pCount[$item->quantity_id] = empty($pCount[$item->quantity_id]) ? $item->quantity : ($pCount[$item->quantity_id] + $item->quantity);
        }
        if (!empty($pCount)) {
            foreach ($pCount as $k => $v) {
                $quantity = $this->Quantities->get($k);
                $quantity->quantity = $quantity->available > 0 ? $quantity->available : $quantity->quantity;
                if ($quantity->quantity < $v) {
                    $product = $this->Products->get($quantity->product_id);
                    $data = array();
                    $data['id'] = $product->id;
                    $data['name'] = $product->name;
                    $data['quantity'] = $quantity->quantity;
                    return array('success' => false, 'product' => $data);
                }
            }
        }
        return array('success' => true, 'product' => []);
    }


    /**
     * Adding item discount after order .
     * @param order_id | int | required
     * @param oder_item_id | int  | required
     * @param off_amount | int | requred value calculated as percent
     * @param subtotal | float | New subtotal value
     * @note note | string | new substaintial note ;
     * @API - POST /orders/add-discount
     */
    public function itemDiscount()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (!empty($data['order_item_id']) && !empty($data['order_id']) && (isset($data['sub_total']) && $data['sub_total'] >= 0)) {
                $this->add_model(array('Orders', 'OrderItems'));
                $orderItem = $this->OrderItems->find()->where(['id' => $data['order_item_id'], 'order_id' => $data['order_id']])->first();

                $orderItemAdditions = json_decode($orderItem->additional, true);
                $discount = !empty($data['discount_amount']) ? $data['discount_amount'] : 0;

                $coupon_discount =  $orderItem->coupon_amount;
                $orderItem->sub_total = $orderItem->substantive_price - ($discount + $coupon_discount + $orderItem->automatic_coupon_amount);
                $orderItem->off_amount = $coupon_discount + $discount;
                $orderItemAdditions['subtaintial_note'] = !empty($data['note']) ? $data['note'] : '';
                $orderItemAdditions['discount'] = $discount;
                $orderItemAdditions['discount_sub_total'] = $orderItem['sub_total'] + $orderItem['product_option_price'];
                $orderItem->additional = json_encode($orderItemAdditions);

//                $orderItem->subtotal = $data['sub_total'];
//                $orderItem->off_amount = $data['off_amount'];
//                $orderItem->additional = json_encode(array('subtaintial_note' => !empty($data['note']) ? $data['note'] : ''));
//                $orderItem->sales_tax = $this->productContainer->getSalesTax($orderItem['product_id'], $this->storeId, $orderItem['rental_type']);
//                $orderItem->sales_tax_price = $data['sub_total'] * ($orderItem->sales_tax * 0.01);
                $orderItem->total =  $orderItem->sub_total + $orderItem->deposit_amount;

                // $orderItem = $this->OrderItems->patchEntity($orderItem, $data);
                if ($this->OrderItems->save($orderItem)) {
                    $order = $this->Orders->get($orderItem['order_id']);
                    $this->Orders->_updatePriceNQty($order);
                    $this->Orders->updateOrderTaxLookup($order['id']);
                    $order = $this->Orders->view($orderItem['order_id']);
                    $this->apiResponse['data'] = $order;
                }
            }
        }
    }

    /**
     * getting order discount.
     * @param id | int | required
     * @API - POST /orders/:id/discount
     */

    public function getOrderDiscount($order_id = '')
    {
        $this->request->allowMethod('get');
        RentMy::addModel(['Orders', 'OrderItems']);
        $orderItems = RentMy::$Model['OrderItems']->find()->where(['store_id' => RentMy::$store->id, 'order_id' => $order_id])->all();
        $result = [];
        foreach ($orderItems as $key => $orderItem) {
            $formated = [];
            if ($orderItem['off_amount'] > 0) {
                $formated['order_item_id'] = $orderItem['id'];
                $formated['discount_amount'] = $orderItem['off_amount'];
                $additional = json_decode($orderItem['additional'], true);
                $formated['note'] = $additional['subtaintial_note'] ?? '';
                $result[] = $formated;
            }
        }
        $this->apiResponse['data'] = $result;
    }


    /**
     * Accept Quote as order
     * POST - orders/accept-quote
     * @param  @data['order_id']
     */
    public function acceptQuote()
    {
        $this->request->allowMethod('post');
        $data = $this->request->getData();
        if (empty($data['order_id'])) {  // check order id in post request
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid quote';
            return;
        }

        if (!empty($data['accepted_by']) && $data['accepted_by'] == 'customer'){
            RentMy::addModel(['OrderNotes', 'Orders']);
            $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
            $order->quote_status = 5;
            RentMy::$Model['Orders']->save($order);
            RentMy::$Model['OrderNotes']->addOrderLog('QuoteConfirmed',
                ['content_id' => $order->id, 'user_id' => RentMy::$token->id, 'order_id' => $order->id, 'source' => RentMy::$token->source, 'store_id'=> $order->store_id]
            );
            $this->apiResponse['message'] = 'Quote accepted';
            return;
        }
        RentMy::addModel(['Orders', 'OrderItems']);
        $itemCount = RentMy::$Model['OrderItems']->find()->where(['order_id' => $data['order_id']])->count();
        if ($itemCount <= 0){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Please add at least one product from inventory to prepare your quote";
            return;
        }

        $manualItemsCounts = RentMy::$Model['OrderItems']->find()->where(['order_id' => $data['order_id'], 'product_id' => 0])->count();
        if ($manualItemsCounts > 0){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "You have $manualItemsCounts manual items here, please add the item(s) to the inventory.";
            return;
        }
        // Check availability for items in the quote
        $availabilityCheck = RentMy::$Model['Orders']->availabilityFromOrder($data['order_id']);
        if (!$availabilityCheck['is_available']){
            $unavailableProductIds = array_column($availabilityCheck['unavailable_items'], 'product_id');
            RentMy::addModel(['Products']);
            $products = RentMy::$Model['Products']->find('list', [
                'keyField'=> 'id',
                'valueField' => 'name'
            ])->where(['id IN' => $unavailableProductIds])->toList();

            $productsName = implode(', ', array_values($products));

            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Following item(s) is not available: ". $productsName;
            return;
        }

        $return = RentMy::$Model['Orders']->acceptQuote($data['order_id']);
        if (!$return['success']) { // unsuccessful transaction
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $return['message'];
            return;
        }

        $this->apiResponse['message'] = $return['message'];
    }

    /**
     * Copy any order
     * copy order
     * copy order items if availability checking okay
     * copy order notes
     * copy orde_address
     * order notes
     * order assets
     * @param $id
     * @API - POST /orders/:id/copy
     */
    public function copy($id)
    {
        $resData = array();
        RentMy::addModel(['Orders', 'OrderItems', 'ProductsAvailabilities', 'OrderAssets', 'OrderAddresses', 'OrderNotes', 'OrderCharge']);
        // get order details
        // $orders =RentMy::$Model['Orders']->details($id);
        $postData = $this->request->getData();
        $orders = RentMy::$Model['Orders']->find()
            ->where(['id' => $id, 'store_id' => RentMy::$store->id])
            ->contain(['OrderItems'])
            ->first();

        if (empty($orders)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid order';
            return;
        }
        // get order items
        $order_item_data = $orders['order_items'];

        // check availability of each item
        $availableStatus = 1;
        $availabilityChecking = true;
        if (isset($postData['available']) && ($postData['available'])) {
            $availabilityChecking = false;
        }

        if ($availabilityChecking) {
            foreach ($orders['order_items'] as $item) {
                // buy or rent
                if ($item['product_type'] == 1) {
                    $available = RentMy::$Model['ProductsAvailabilities']->getAvailableForProduct($item['product_id'], $item['variants_products_id'], $item['location'], $item['rent_start']);
                } elseif ($item['product_type'] == 2) {
                    $available = RentMy::$Model['ProductsAvailabilities']->getAvailableForPackage('cart', $item['product_id'], $item['location'], $item['rent_start'], ['cart_item_id' => $item['id']]);
                }
                //$available = 0;
                if ($available < 1) {
                    $availableStatus = 0;
                    $unAvailableProduct_id = $item['product_id'];
                    $unAvailableVariantProductId = $item['variants_products_id'];
                    break;
                }
            }
        }
        if ($availableStatus == 0) { // if any product availability is empty then stop works & return;
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Duplicating this order may result in negative quantities.';
            //$this->apiResponse['message'] = $unAvailableProduct_id . ' is unavailable';
            $this->apiResponse['data']['available'] = false;
            return;
        }

        // Copy Order

        unset($orders['order_items']);
        unset($orders['id']);
        unset($orders['uuid']);
        unset($orders['created']);
        unset($orders['modified']);
        $newOrder = RentMy::$Model['Orders']->newEntity($orders->toArray());

        RentMy::$Model['Orders']->save($newOrder);
        $copyOrderId = $newOrder->id;
        if (empty($copyOrderId)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Copy failed.';
            return;
        }

        // copy order address
        $orderAddressData = RentMy::$Model['OrderAddresses']->find()->where(['order_id' => $id])->first();
        if (!empty($orderAddressData)) {
            unset($orderAddressData['id']);
            unset($orderAddressData['created']);
            unset($orderAddressData['modified']);
            $orderAddressData['order_id'] = $copyOrderId;
            $newData = RentMy::$Model['OrderAddresses']->newEntity($orderAddressData->toArray());
            RentMy::$Model['OrderAddresses']->save($newData);
        }

        // copy order notes
        $orderNotesData = RentMy::$Model['OrderNotes']->find()->where(['order_id' => $id])->toArray();
        if (!empty($orderNotesData)) {
            foreach ($orderNotesData as $orderNote) {
                unset($orderNote['id']);
                unset($orderNote['created']);
                unset($orderNote['modified']);
                $orderNote['order_id'] = $copyOrderId;
                $newData = RentMy::$Model['OrderNotes']->newEntity($orderNote->toArray());
                RentMy::$Model['OrderNotes']->save($newData);
            }
        }

        // copy order additional charges
        $orderCharges = RentMy::$Model['OrderCharge']->find()->where(['order_id' => $id])->toArray();
        if (!empty($orderCharges)) {
            foreach ($orderCharges as $orderCharge) {
                unset($orderCharge['id']);
                unset($orderCharge['created']);
                unset($orderCharge['modified']);
                $orderCharge['order_id'] = $copyOrderId;
                $newData = RentMy::$Model['OrderCharge']->newEntity($orderCharge->toArray());
                RentMy::$Model['OrderCharge']->save($newData);
            }
        }

        // Copy Order Items
        foreach ($order_item_data as $item) {
            $item = $item->toArray();
            $old_item_id = $item['id'];
            unset($item['id']);
            unset($item['created']);
            unset($item['modified']);
            $item['order_id'] = $copyOrderId;
            if (!empty($item['parent_id'])){
                $filtered = array_values(array_filter($order_item_data, function ($orderItem) use ($item){
                    return $orderItem['id'] == $item['parent_id'];
                }));

                if (!empty($filtered[0])){
                    $parentItem = RentMy::$Model['OrderItems']->find()->where([
                        'order_id' => $copyOrderId,
                        'product_id' => $filtered[0]['product_id'],
                        'variants_products_id' => $filtered[0]['variants_products_id'],
                    ])->first();

                    if (!empty($parentItem))
                        $item['parent_id'] = $parentItem['id'];
                }
            }
            $newOrderItem = RentMy::$Model['OrderItems']->newEntity($item);
            RentMy::$Model['OrderItems']->save($newOrderItem);
            $order_item_id = $newOrderItem->id;

            // save product availabilities
            $productAvailability = RentMy::$Model['ProductsAvailabilities']->find()->where(['order_id' => $id, 'order_item_id' => $old_item_id])->first();
            if (!empty($productAvailability)) {
                unset($productAvailability['id']);
                unset($productAvailability['created']);
                unset($productAvailability['modified']);
                $productAvailability['order_id'] = $copyOrderId;
                $productAvailability['order_item_id'] = $order_item_id;
                $newAvailability = RentMy::$Model['ProductsAvailabilities']->newEntity($productAvailability->toArray());
                RentMy::$Model['ProductsAvailabilities']->save($newAvailability);
            }

            $orderAssetsData = RentMy::$Model['OrderAssets']->find()->where(['order_id' => $id, 'order_item_id' => $order_item_id])->toArray();
            if (!empty($orderAssetsData)) {
                foreach ($orderAssetsData as $i => $orderAssets) {
                    unset($orderAssets['id']);
                    unset($orderAssets['created']);
                    unset($orderAssets['modified']);
                    $orderAssets['order_id'] = $copyOrderId;
                    $orderAssets['order_item_id'] = $order_item_id;
                    RentMy::$Model['OrderAssets']->newEntity($orderAssets->toArray());
                    RentMy::$Model['OrderAssets']->save($orderAssets);
                }
            }
        }

        $this->apiResponse['data']['order_id'] = $copyOrderId;
        $this->apiResponse['data']['available'] = true;
        $this->apiResponse['message'] = 'Order copied successfully';
        return;
    }

    function getRecurringDetails($order_id)
    {
        RentMy::addModel(['OrderRecurring']);
        $recurring = RentMy::$Model['OrderRecurring']->find()->where(['order_id' => $order_id])->first();
        if (empty($recurring)) {
            throw new NotFoundException('Invalid recurring details');
        }

        $response = [
            'order_id' => $recurring['order_id'],
            'duration' => $recurring['duration'],
            'duration_type' => $recurring['duration_type'],
            'start_date' => RentMy::toStoreTimeZone($recurring['start_date'], 'Y-m-d'),
            'end_date' => RentMy::toStoreTimeZone($recurring['end_date'], 'Y-m-d'),
            'status' => $recurring['status'],
            'amount' => $recurring['amount'],
            'payment_gateway' => $recurring['payment_gateway']
        ];
        $this->apiResponse['data'] = $response;
    }

    /**
     * @param $order_id
     * @API - POST - {{host}}orders/recurring/:order_id
     */
    public function recurringModify($order_id)
    {
        $data = $this->request->getData();
        RentMy::addModel(['OrderRecurring', 'Payments']);
        $recurring = RentMy::$Model['OrderRecurring']->find()->where(['order_id' => $order_id])->first();
        $payments = RentMy::$Model['Payments']->find()->where(['id' => $recurring['payment_id']])->first();
        $data['referenceNumber'] = $payments->transaction_id;
        $config = (new Payment())->getStorePaymentConfig('goMerchant');
        $goMerchantObj = new goMerchant($config['config']);
        $response = $goMerchantObj->recurringModify($data);
        if (!$response['success']) {
            throw new NotFoundException('Recurring payments can"t be updated. Please try again');
        }

        $recurring->duration_type = $data['recurringType'];
        $recurring->amount = $data['recurringAmount'];
        $recurring->status = $data['recurring'];
        $recurring->end_date = RentMy::toUTC($data['recurringEndDate'], 'Y-m-d H:i:00');
        RentMy::$Model['OrderRecurring']->save($recurring);
        $this->apiResponse = $response;
        $this->apiResponse['message'] = 'Recurring payments updated successfully.';

    }

    /**
     * Tax exempt for cart & order
     * @params data[type] = order / cart
     * @params data[id] = cart_id / order _id
     * @params data[status] = 0 / 1
     * POST /admin/tax/exempt
     */
    public function exempt()
    {
        $data = $this->request->getData();
        $requiredFields = ['id', 'type'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            throw new MissingParamsException('Required field missing');
            return;
        }

        // Get status
        $status = false;
        if (isset($data['status']) && $data['status'] === 1)
            $status = true;

        // Step 1: check if cart or order
        if ($data['type'] === 'cart') {
            $response = [];
            RentMy::addModel(['Carts']);
            $cart = RentMy::$Model['Carts']->find()
                ->where(['id' => $data['id']])
                ->first();

            if (empty($cart)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Cart not found.';
                return;
            }

            $options = json_decode($cart->options, true);
            // Step 2 : for cart, set options[tax_exempt] = 0 when false, default is true

            if ($status) {
                $options['tax_exempt'] = 1;
                $cart->tax = 0;
                RentMy::addModel(['TaxLookup']);
                RentMy::$Model['TaxLookup']->deleteAll(['type' => 'cart', 'content_id' => $data['id']]);
            } else {
                $options['tax_exempt'] = 0;
            }
            $cart->options = json_encode($options);
            // Step 4 : if false , remove all tax data for that  cart / order, update tax = 0
            if (RentMy::$Model['Carts']->save($cart)) {
                // Step 5 : update cart details calculations.
                RentMy::$Model['Carts']->_updatePriceNQty($cart);
                $cart = $this->Carts->getCartDetails($cart->id);
            }
            $this->apiResponse['data'] = $cart;
            return;
        }

        if ($data['type'] === 'order') {
            $response = [];
            RentMy::addModel(['Orders']);
            $order = RentMy::$Model['Orders']->find()
                ->where(['id' => $data['id']])
                ->first();

            if (empty($order)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Order not found.';
                return;
            }

            $options = json_decode($order->options, true);
            // Step 3 : for order set additional[tax_exempt] = 0 when false, default is true

            if ($status) {
                $options['tax_exempt'] = 1;
                $order->tax = 0;
                RentMy::addModel(['TaxLookup']);
                RentMy::$Model['TaxLookup']->deleteAll(['type' => 'order', 'content_id' => $data['id']]);

            } else {
                // Step 4 : if false , remove all tax data for that order, update tax = 0
                $options['tax_exempt'] = 0;
            }

            $order->options = json_encode($options);
            if (RentMy::$Model['Orders']->save($order)) {
                // Step 5 : update order details calculations.
                RentMy::$Model['Orders']->_updatePriceNQty($order);
                $order = RentMy::$Model['Orders']->view($order->id);
                $this->apiResponse['data'] = $order;
            }
        }
    }

    /**
     * Tax calculate for cart & order
     * @params data[type] = order / cart
     * @params data[id] = cart_id / order _id
     * POST /admin/tax/calculate
     */
    public function calculate()
    {
        $data = $this->request->getData();
        $requiredFields = ['id', 'type'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            throw new MissingParamsException('Required field missing');
            return;
        }

        if ($data['type'] === 'order') {
            RentMy::addModel(['Orders']);
            $order = RentMy::$Model['Orders']->find()
                ->where(['id' => $data['id']])
                ->first();

            if (empty($order)) {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Order not found.';
                return;
            }

            $options = json_decode($order->options, true);
            if (!$options['tax_exempt']) {
                RentMy::addModel(['TaxLookup']);
                RentMy::$Model['Orders']->updateOrderTaxLookup($order->id);
            }

            RentMy::$Model['Orders']->_updatePriceNQty($order);
            $this->apiResponse['data'] = RentMy::$Model['Orders']->view($order->id);
        }
    }

    /**
     * Get all custom order fields for any order
     * @params $id = order_id
     * @return  order's all custom fields
     * @API GET /orders/:order_id/custom-fields
     */
    public function customFields($id)
    {
        RentMy::addModel(['Orders', 'CustomFields','OrderAdditionalInfo']);
        $this->add_model(array('OrderAdditionalInfo'));
        $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $id])->first();
        $params = $this->request->getQueryParams();
        $section = !empty($params['section']) ? $params['section'] : '';
        if (empty($order)) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Invalid order.';
            return;
        }
        $orderAdditionalInfo = $this->OrderAdditionalInfo->find()
            ->where(['content_id' => $id, 'checkout_type' => 'order'])
            ->order(['field_type' => 'ASC'])
            ->toArray();

        $customFields = [];
        foreach($orderAdditionalInfo as $additionalInfo){
            $customFields[] = json_decode($additionalInfo->content, true);
        }
        $this->apiResponse['data'] = $customFields;
    }


    /**
     * @params $id = order_id
     * update an order's custom fields
     * @return an order's all custom fields
     * @API POST orders/:order_id/customer
     */
    public function updateOrderCustomFields($id)
    {
        RentMy::addModel(['Orders', 'CustomFields']);
        $this->add_model(array('OrderAdditionalInfo'));
        $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
        $data = $this->request->getData();
        $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $id])->first();
        if (empty($order)) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Order not found.';
            return;
        }
        $orderCustomValues = json_decode($order->custom_values, true);
        if (!empty($data["custom_values"])) {
            $custom_values = $data["custom_values"];

            foreach ($custom_values as $key => $custom_value) {
                $key = array_search($custom_value['field_name'], array_column($orderCustomValues, 'field_name'));
                array_splice($orderCustomValues, $key, 1);

                if ($custom_value['type'] == 2) {
                    $s3 = new S3();
                    if (!file_exists($directoryPath . $custom_value['field_values'])) {
                        unset($custom_value[$key]);
                        continue;
                    }
                    $key = array_search($custom_value['field_name'], array_column($orderCustomValues, 'field_name'));
                    if ($key) {
                        $s3->deleteFiles(['orders/' . $id . '/' . $orderCustomValues[$key]['field_values']]);
                    }
                    try {
                        $s3->upload([
                            ['path' => $directoryPath . $custom_value['field_values'], 'dir' => 'orders/' . $id . '/' . $custom_value['field_values']],
                        ]);
                        RentMy::deleteFile($directoryPath, $custom_value['field_values']);
                    } catch (Exception $e) {
                        unset($custom_values[$key]);
                    }
                }
            }
            if (!empty($orderCustomValues))
                $custom_values = array_merge($orderCustomValues, $custom_values);

            $data["custom_values"] = json_encode($custom_values);
        }
        $order = RentMy::$Model['Orders']->patchEntity($order, $data);
        if (RentMy::$Model['Orders']->save($order)) {
            //$customFields = json_decode($order->custom_values, true);
            $section = !empty($data['section']) ? $data['section'] : '';

            $orderAdditionalInfo = $this->OrderAdditionalInfo->find()
                ->where(['content_id' => $id, 'checkout_type' => 'order'])
                ->order(['field_type' => 'ASC'])
                ->toArray();

            $customFields = [];
            foreach($orderAdditionalInfo as $additionalInfo){
                $customFields = json_decode($additionalInfo->content, true);
            }
            $customFields = RentMy::$Model['CustomFields']->serializeCustomFields($customFields, $section);
            $this->apiResponse['data'] = $customFields;
            $this->apiResponse['message'] = 'Custom fields has been updated.';
        }
    }

    /**
     * get delivery estimation
     * @params $id = order_id
     * @API GET /order/delivery/estimate
     */
    public function deliveryEstimate()
    {
        $data = $this->request->getData();
        RentMy::addModel(['Carts', 'Orders', 'OrderItems', 'CartItems']);

        if (!empty($data['order_id'])) {
            $cart = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
            RentMy::addModel(['OrderAddresses']);
            $address = RentMy::$Model['OrderAddresses']->find()
                ->where(['store_id' => RentMy::$store->id, 'order_id' => $data['order_id']])
                ->first();

            $multiStoreDelivery = json_decode($address->multi_store_delivery, true);
            $data['delivery_flow'] = !empty($data['delivery_flow']) ? $data['delivery_flow'] :$multiStoreDelivery['request']['delivery_flow'];

            if (empty($data['drop_address']))
                $data['drop_address'] = $multiStoreDelivery['request']['drop_address'];

            if (empty($data['pickup_address']))
                $data['pickup_address'] = $multiStoreDelivery['request']['pickup_address'];

            if (empty($data['id']))
                $data['id'] = $multiStoreDelivery['request']['id'];

            if (empty($data['location_id']))
                $data['location_id'] = $multiStoreDelivery['request']['location_id'];

            if (empty($data['move']) && !empty($multiStoreDelivery['request']['move']))
                $data['move'] = $multiStoreDelivery['request']['move'];

            if (empty($data['drop_off']) && !empty($multiStoreDelivery['request']['drop_off']))
                $data['drop_off'] = $multiStoreDelivery['request']['drop_off'];

            if (empty($data['pickup']) && !empty($multiStoreDelivery['request']['pickup']))
                $data['pickup'] = $multiStoreDelivery['request']['pickup'];

            if (empty($data['storage']) && !empty($multiStoreDelivery['request']['storage']))
                $data['storage'] = $multiStoreDelivery['request']['storage'];

            if (isset($data['token']))
                unset($data['token']);

            $data['quantity'] = RentMy::$Model['OrderItems']->find()->where(['order_id' => $cart->id, 'parent_id' => 0])->select(['quantity' => RentMy::$Model['OrderItems']->find()->func()->sum('quantity')])->first()->quantity;
        }

        if (!empty($data['token'])) {
            $requiredFields = ['id', 'delivery_flow', 'drop_address', 'location_id'];

            $cart = RentMy::$Model['Carts']->find()->where(['uid' => $data['token']])->first();

            $options = json_decode($cart->options, true);

            $multiStoreDelivery = !empty($options['multi_store_delivery']) ? $options['multi_store_delivery'] : [];

            $data['delivery_flow'] = !empty($data['delivery_flow']) ? $data['delivery_flow'] :$multiStoreDelivery['request']['delivery_flow'];

            if (empty($data['drop_address']) && !empty($multiStoreDelivery['request']['drop_address']))
                $data['drop_address'] = $multiStoreDelivery['request']['drop_address'];

            if (empty($data['pickup_address']) && !empty($multiStoreDelivery['request']['pickup_address']))
                $data['pickup_address'] = $multiStoreDelivery['request']['pickup_address'];

            if (empty($data['id']) && !empty($multiStoreDelivery['request']['id']))
                $data['id'] = $multiStoreDelivery['request']['id'];

            if (empty($data['location_id']) && !empty($multiStoreDelivery['request']['location_id']))
                $data['location_id'] = $multiStoreDelivery['request']['location_id'];


            $required = RentMy::requiredKeyExist($data, $requiredFields);
            if (!empty($required)) {
                throw new MissingParamsException('Required field missing');
                return;
            }

            if (empty($data['move']) && !empty($multiStoreDelivery['request']['move']) && empty($data['reset']))
                $data['move'] = $multiStoreDelivery['request']['move'];

            if (empty($data['drop_off']) && !empty($multiStoreDelivery['request']['drop_off']) && empty($data['reset']))
                $data['drop_off'] = $multiStoreDelivery['request']['drop_off'];

            if (empty($data['pickup']) && !empty($multiStoreDelivery['request']['pickup']) && empty($data['reset']))
                $data['pickup'] = $multiStoreDelivery['request']['pickup'];

            if (empty($data['storage']) && !empty($multiStoreDelivery['request']['storage']) && empty($data['reset']))
                $data['storage'] = $multiStoreDelivery['request']['storage'];

            $data['quantity'] = RentMy::$Model['CartItems']->find()->where(['cart_id' => $cart->id, 'parent_id' => 0])->select(['quantity' => RentMy::$Model['CartItems']->find()->func()->sum('quantity')])->first()->quantity;
        }

        $response = (new Delivery($data))->getDetails();

        if (isset($response['success']) && !$response['success']){
            $this->httpStatusCode = 400;
            $this->apiResponse = $response;
            return;
        }

        $options = json_decode($cart->options, true);
        if (empty($options))
            $options = [];

        $options['multi_store_delivery'] = [
            'request' => $response['request'],
            'charges' => $response['charges'],
            'total' => $response['total'],
            'is_tax_applicable' => $response['is_tax_applicable'],
            'flows' => $response['flows'],
            'distance_unit' => $response['distance_unit'],
            'delivery_flow' => $data['delivery_flow']
        ];

        $cart->options = json_encode($options);
        if (!empty($data['token']))
            RentMy::$Model['Carts']->save($cart);

        if (!empty($data['order_id']) && ($data['source'] != 'fulfilment_update')) {
            $cart->delivery_charge = $response['total'];
            RentMy::$Model['Orders']->save($cart);
            if (isset($address) && !empty($address)) {
                $address->multi_store_delivery = json_encode($response);
                RentMy::$Model['OrderAddresses']->save($address);
            }
        }

        $this->apiResponse['data'] = $response;
    }


    /**
     * @API -GET /orders/:id/multi-store-delivery
     * @param $id
     */
    public function multiStoreDeliveryViewByOrder($id)
    {
        RentMy::addModel(['OrderAddresses']);
        $address = RentMy::$Model['OrderAddresses']->find()
            ->where(['store_id' => RentMy::$store->id, 'order_id' => $id])
            ->map(function ($address) {
                $address->multi_store_delivery = json_decode($address->multi_store_delivery);
                return $address;
            })->first();

        if (empty($address)) {
            $this->apiResponse['error'] = Configure::read('message.found');
            return;
        }

        $this->apiResponse['data'] = $address;
    }

    /**
     * @API -POST /orders/:id/multi-store-delivery
     * @param $id
     */
    public function multiStoreDeliveryUpdateByOrder($id)
    {
        $data = $this->request->getData();

        //@todo implement here
    }

    /**
     * add delivery/shipping method to a order
     * @param cart token
     * @param shipping method
     * @param shipping cost
     * @param shipping tax
     */
    public function delivery()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (!empty($data['shipping_method']) && !empty($data['order_id'])) {
                RentMy::addModel(['Orders', 'OrderItems', 'OrderAddresses']);
                $order = RentMy::$Model['Orders']->get($data['order_id']);

                if (empty($order)) {
                    $this->httpStatusCode = 404;
                    $this->apiResponse['error'] = 'Not found!';
                    return;
                }
                $order->shipping_method = $data['shipping_method'];
                $order->delivery_charge = $data['shipping_cost'];
                $order->delivery_tax = !empty($data['tax']) ? $data['tax'] : 0;

                if ($data['shipping_cost'] == 0){
                    $order_options = json_decode($order->options, true);
                    if (isset($order_options['multi_store_delivery'])){
                        unset($order_options['multi_store_delivery']);
                        $order->options = json_encode($order_options);
                    }
                }

                if ($data['shipping_method'] == 1) { // remove tax when address is delivery / shipping but local pickup is selected
                    if (RentMy::$storeConfig['tax']['address'] != 'store') {
                        $order['tax'] = 0;
                        RentMy::addModel(['TaxLookup']);
                        RentMy::$Model['TaxLookup']->removeExistingTax('order', $order->id);
                    }
                }
                if (!empty($data['address'])){
                    $data = array_merge($data, $data['address']);
                    RentMy::$Model['OrderAddresses']->updateOrderAddress($order->id, $data);
                }
                if (RentMy::$Model['Orders']->save($order)) {
                    if ($data['shipping_method'] != 1) {
                        $data['address']['shipping_method'] = $data['shipping_method'];
                        $this->apiResponse['data'] = RentMy::$Model['Orders']->details($order->id, $data['address']);
                    } else {
                        $this->apiResponse['data'] = RentMy::$Model['Orders']->details($order->id);
                    }
                } else {
                    $this->apiResponse['error'] = Configure::read('message.save');
                }
            } else {
                $this->apiResponse['error'] = Configure::read('message.missing_param');
            }
        } else {
            $this->apiResponse['error'] = 'Method not allowed.';
        }
    }

    public function orderChecklist($id){
        RentMy::addModel(['Orders']);
        $data = $this->request->getData();
        if (empty($data['checklist'])){
            $this->apiResponse['error'] = Configure::read('message.missing_param');
            $this->httpStatusCode = 422;
            return;
        }

        $order = RentMy::$Model['Orders']->get($id);
        $options = !empty($order->options)?json_decode($order->options, true):[];
        $options['checklist'] = $data['checklist'];
        $order->options = json_encode($options);
        if(RentMy::$Model['Orders']->save($order)){
            $this->apiResponse['data'] = RentMy::$Model['Orders']->details($order->id);
            return;
        }
        $this->apiResponse['error'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
        $this->httpStatusCode = 500;
    }

    /**
     * Purpose: Retrieves detailed information of a particular additional field
     *
     * @param int $order_id The ID of the order
     * @param int $additional_field_id The ID of the additional field
     *
     * @API view-additional-field/{order_id}/{additional_field_id}
     */
    public function viewAdditionalField($order_id, $additional_field_id){
        RentMy::addModel(['OrderAdditionalInfo','CustomFields']);
        $additionalInfo = RentMy::$Model["OrderAdditionalInfo"]->find()
            ->where(['content_id' => $order_id, 'checkout_type' => 'order', 'id' => $additional_field_id])->first();

        $custom_value = json_decode($additionalInfo->content, true);
        $custom_value['id'] = $additionalInfo->id;
        $custom_value['field_values'] =  RentMy::$Model["CustomFields"]->formatByVariables('order', $order_id, $custom_value['field_values']);

        if (!empty($custom_value['type']) && ($custom_value['type'] == 2)) {
            $file = RentMy::makeS3Url('orders' . DS . $order_id . DS . $custom_value['field_values']);
            $file_info = new \finfo(FILEINFO_MIME_TYPE);
            $mimeType = $file_info->buffer(file_get_contents($file));

            $custom_value['filepath'] = !empty($custom_value['field_values']) ? $file : '';
            $custom_value['filetype'] = !empty($custom_value['field_values']) ? explode('/', $mimeType)[0] : '';
        }
        if (!empty($custom_value['signature'])) {
            $file = RentMy::makeS3Url('orders/signature/'.$custom_value['signature']);
            $file_info = new \finfo(FILEINFO_MIME_TYPE);
            $mimeType = $file_info->buffer(file_get_contents($file));

            $custom_value['signaturefilepath'] = !empty($custom_value['signature']) ? $file : '';
            $custom_value['signaturefiletype'] = !empty($custom_value['signature']) ? explode('/', $mimeType)[0] : '';
        }

        $this->apiResponse['data'] = $custom_value;
    }


    public function viewOrderChecklist($id){
        RentMy::addModel(['Checklists','OrderAdditionalInfo','CustomFields']);
        $where = ['store_id'=>RentMy::$store->id, 'location' => RentMy::$token->location];
        $checklist = RentMy::$Model['Checklists']->find()->select(['id', 'store_id', 'location', 'name', 'custom_field', 'status', 'is_required'])->where($where)->toArray();

        foreach($checklist as $list){
            $additionalInfo = RentMy::$Model["OrderAdditionalInfo"]->find()
                ->where(['content_id' => $id, 'checkout_type' => 'order', 'field_id' => $list['custom_field']])->first();
            $custom_value =  json_decode($additionalInfo->content, true);
            $custom_value['id'] = $additionalInfo->id;

            if (!empty($custom_value['type']) && ($custom_value['type'] == 2)) {
                $file = RentMy::makeS3Url(DS . 'orders' . DS . $id . DS . $custom_value['field_values']);
                $file_info = new \finfo(FILEINFO_MIME_TYPE);
                $mimeType = $file_info->buffer(file_get_contents($file));

                $custom_value['filepath'] = !empty($custom_value['field_values']) ? $file : '';
                $custom_value['filetype'] = !empty($custom_value['field_values']) ? explode('/', $mimeType)[0] : '';
            }
            if (!empty($custom_value['signature'])) {
                $file = RentMy::makeS3Url(DS . 'orders/signature/'.$custom_value['signature']);
                $file_info = new \finfo(FILEINFO_MIME_TYPE);
                $mimeType = $file_info->buffer(file_get_contents($file));

                $custom_value['signaturefilepath'] = !empty($custom_value['signature']) ? $file : '';
                $custom_value['signaturefiletype'] = !empty($custom_value['signature']) ? explode('/', $mimeType)[0] : '';
            }
            $list['field'] = $custom_value;
        }
        $this->apiResponse['data'] = $checklist;
    }

    public function additionalField($id){

        RentMy::addModel(array('OrderAdditionalInfo', 'CustomFields'));
        $where = ['content_id'=> $id, 'checkout_type' => 'order'];
        $additionalFields = RentMy::$Model["OrderAdditionalInfo"]->find()->where($where)->order(['field_type' => 'ASC'])->toArray();
        $params = $this->request->getQueryParams();
        $section = $params['section'] ?? '';

        $custom_values = [];
        foreach($additionalFields as $additionalInfo){
            $custom_field = RentMy::$Model["CustomFields"]->find()->where(['id'=> $additionalInfo->field_id])->first();
            $custom_value =  json_decode($additionalInfo->content, true);
            $custom_value['id'] = $additionalInfo['id'];
            if (!empty($custom_value['type']) && ($custom_value['type'] == 2)) {
                $file = RentMy::makeS3Url(DS . 'orders' . DS . $id . DS . $custom_value['field_values']);
                $file_info = new \finfo(FILEINFO_MIME_TYPE);
                $mimeType = $file_info->buffer(file_get_contents($file));

                $custom_value['filepath'] = !empty($custom_value['field_values']) ? $file : '';
                $custom_value['filetype'] = !empty($custom_value['field_values']) ? explode('/', $mimeType)[0] : '';
            }
            if (!empty($custom_value['signature'])) {
                $file = RentMy::makeS3Url(DS . 'orders/signature/'.$custom_value['signature']);
                $file_info = new \finfo(FILEINFO_MIME_TYPE);
                $mimeType = $file_info->buffer(file_get_contents($file));

                $custom_value['signaturefilepath'] = !empty($custom_value['signature']) ? $file : '';
                $custom_value['signaturefiletype'] = !empty($custom_value['signature']) ? explode('/', $mimeType)[0] : '';
            }

            $custom_value['field_values'] = RentMy::$Model["CustomFields"]->formatByVariables('order',$id, $custom_value['field_values']);
            $custom_value['data'] = $custom_field;
            $custom_values[] = $custom_value;
        }
//        $custom_values = RentMy::$Model['CustomFields']->serializeCustomFields($custom_values, $section);
        $this->apiResponse['data'] = $custom_values;
    }

    /**
     * @param $id
     * @return void
     */
    public function editableAdditionalField($id){
        RentMy::addModel(array('OrderAdditionalInfo', 'CustomFields'));
        $customFields = RentMy::$Model["CustomFields"]->find()->where(['CustomFields.store_id'=>RentMy::$store->id, 'CustomFields.location'=>RentMy::$token->location])->order(['field_type' => 'ASC'])->toArray();
        $custom_values = [];
        $params = $this->request->getQueryParams();
        foreach ($customFields as $customField){
            $sectionOption = json_decode($customField['section'], true);
            if (!empty($params['section'])){
                if (!$sectionOption[$params['section']]){
                    continue;
                }
            }
            $orderAdditionalInfo = RentMy::$Model["OrderAdditionalInfo"]->find()->where(['content_id'=>$id, 'field_id'=>$customField->id])->first();
            $custom_value = !empty($orderAdditionalInfo)?json_decode($orderAdditionalInfo['content'], true):[];

            $custom_value['field_values'] = RentMy::$Model["CustomFields"]->formatByVariables('order', $id, $custom_value['field_values']);

            if (!empty($orderAdditionalInfo))
                $custom_value['id'] = $orderAdditionalInfo->id;

            $customField['section'] = !empty($customField['section'])?json_decode($customField['section'], true):[];
            unset($customField['order_additional_info']);
            if (!empty($custom_value['type']) && ($custom_value['type'] == 2)) {
                $file = RentMy::makeS3Url(DS . 'orders' . DS . $id . DS . $custom_value['field_values']);
                $file_info = new \finfo(FILEINFO_MIME_TYPE);
                $mimeType = $file_info->buffer(file_get_contents($file));

                $custom_value['filepath'] = !empty($custom_value['field_values']) ? $file : '';
                $custom_value['filetype'] = !empty($custom_value['field_values']) ? explode('/', $mimeType)[0] : '';
            }
            if (!empty($custom_value['signature'])) {
                $file = RentMy::makeS3Url(DS . 'orders/signature/'.$custom_value['signature']);
                $file_info = new \finfo(FILEINFO_MIME_TYPE);
                $mimeType = $file_info->buffer(file_get_contents($file));

                $custom_value['signaturefilepath'] = !empty($custom_value['signature']) ? $file : '';
                $custom_value['signaturefiletype'] = !empty($custom_value['signature']) ? explode('/', $mimeType)[0] : '';
            }
            $custom_value['data'] = $customField;
            $custom_values[] = $custom_value;
        }

        $this->apiResponse['data'] = $custom_values;
    }

    public function updateAdditionalField($id)
    {
        RentMy::addModel(['OrderAdditionalInfo']);
        $data = $this->request->getData();
        RentMy::$Model['OrderAdditionalInfo']->add($data['custom_values'], $id, 'order');
        RentMy::addQueue('Order', ['type' => 'customFields', 'order_id'=>$id, 'store_id' => RentMy::$store->id, 'location' => RentMy::$token->location]);
        $this->apiResponse['data'] = $data;
    }

    /**
     * @param $id = order id
     * @data swap_order_id
     * @return void
     */

    public function addClosetItems(){
        $connection = ConnectionManager::get('default');
        $connection->begin();
        try {
            RentMy::addModel(['Orders', 'OrderItems', 'Quantities', 'ProductPrices', 'Holidays', 'ProductsAvailabilities', 'Products', 'Customers', 'OrderExchange']);
            $data = $this->request->getData();
            $location = $this->request->getHeader('Location');
            if (!empty($location))
                $data['location'] = $location[0];

            if (!empty(RentMy::$token->location)){
                $data['location'] = RentMy::$token->location;
            }

            $data['rent_start'] = Time::now()->format('Y-m-d H:i');
            $data['rent_end'] = Time::parse($data['rent_start'])->addHour()->format('Y-m-d H:i');
            $customer = RentMy::$Model['Customers']->find()->where(['Customers.id'=>$data['customer_id']])->contain(['CustomerPlans'])->first();
            if (!empty($customer)){

                if (empty($customer['customer_plan'])){
                    $this->httpStatusCode = 400;
                    $this->apiResponse['error'] = "Please choose a plan first";
                    return;
                }

                $allowedInventories = $customer->customer_plan->number_of_inventory;
                $duration_type = $customer->customer_plan->duration_type;
                $duration = $customer->customer_plan->duration;
                $endDate = Time::parse($data['rent_start']);
                if ($duration_type == 'day')
                    $endDate = $endDate->addDays($duration);
                if ($duration_type == 'week')
                    $endDate = $endDate->addWeeks($duration);
                if ($duration_type == 'month')
                    $endDate = $endDate->addMonths($duration);

                $data['rent_end'] = $endDate->format('Y-m-d H:i');

                $requestedInventories = array_sum(array_column($data['items'], 'quantity'));
                if ($allowedInventories < $requestedInventories){
                    $this->httpStatusCode = 400;
                    $this->apiResponse['error'] = "You are only allowed ".$allowedInventories . ' inventories to add but you did '.$requestedInventories;
                    return;
                }

            }
            $holiday = RentMy::$Model['Holidays']->checkHoliday(RentMy::$store->id, RentMy::toUTC($data['rent_start']));
            if ($holiday['success']) {
                $this->apiResponse['error'] = "Product not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                return;
            }

            $unavailable = [];
            foreach ($data['items'] as $item){

                $rent_start = RentMy::toUTC($data['rent_start'], 'Y-m-d H:i');
                $rent_end = RentMy::toUTC($data['rent_end'], 'Y-m-d H:i');

                if ($item['type'] == 2){
                    RentMy::addModel(['ProductPackages', 'Products']);
                    $productPackages = RentMy::$Model['ProductPackages']->find()
                        ->where(['ProductPackages.store_id' => $this->parseToken->store_id])
                        ->where(['ProductPackages.package_id' => $item['product_id']])
                        ->toArray();
                    $productIds = Hash::extract($productPackages, '{n}.product_id');
                    $products = RentMy::$Model['Products']->find()->where(['id IN' => $productIds])->toArray();
                    $variants = Hash::extract($products, '{n}.variants_products_id');
                    $packageTerms = RentMy::$Model['ProductsAvailabilities']->getPackageAvailability($productPackages, $variants, $data['location'], $data['rent_start'], $data['rent_end']);
                    $available = !empty($packageTerms[0]['term']) ? $packageTerms[0]['term'] : 0;
                }else{
                    $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($item['product_id'], $item['variants_products_id'], $data['location'], $rent_start, $rent_end);

                }

                if ($available < $item['quantity']) {
                    $unavailable[] = $item;
                }
            }
            if (!empty($unavailable)){
                $this->httpStatusCode = 400;
                $this->apiResponse['data'] = $unavailable;
                $this->apiResponse['message'] = 'Some items are not available';
                return;
            }

            if (!empty($data['swap_order_id'])){
                $order = RentMy::$Model['Orders']->get($data['swap_order_id']);
                $existingItems = RentMy::$Model['OrderItems']->find()->select(['product_id', 'order_item_id'=>'id', 'quantity', 'variants_products_id'])->where(['order_id'=>$data['swap_order_id'], 'rental_type !=' => 'buy', 'parent_id' => 0])->toArray();

                $remainItem = [];
                foreach ($existingItems as $item){
                    if (!in_array($item['variants_products_id'], array_column($data['return_items'], 'variants_products_id'))){
                        $remainItem[] = $item;
                    }
                }
                $exchangeItems = array_merge($data['new_items'], $remainItem);

                $swapItems = $this->swapItems($data['return_items'], $data['new_items']);

                $data['items'] = array_merge($remainItem, $swapItems);
                RentMy::$Model['OrderExchange']->deleteAll(['store_id'=>RentMy::$store->id, 'order_id'=>$data['swap_order_id'], 'status'=>0]);

                foreach($data['items'] as $item){
                    $item['location'] = $data['location'];
                    $item['rent_start'] = $data['rent_start'];
                    $item['rent_end'] = $data['rent_end'];
                    $options = [
                        'type' => !empty($item['type']) ? $item['type'] : 1, // here type = 1 is product and 2 is package
                    ];
                    if ($options['type'] == 2 && !empty($item['products'])){
                        $options['products'] = $item['products'];
                    }
                    $item['options'] = $options;
                    RentMy::$Model['OrderExchange']->addItem($data['swap_order_id'], $item);
                }
                RentMy::addNotificationQueue('closet_exchange', RentMy::$store->id, ['store_id'=>RentMy::$store->id, 'location'=>RentMy::$token->location, 'order_id'=>$order['id']], ['store_id'=>RentMy::$store->id, 'location'=>RentMy::$token->location, 'order_id'=>$order['id'], 'type'=>'ClosetExchange', 'exchange_type'=>'request']);

                $options = !empty($order['options']) ? json_decode($order['options'], true) : [];
                $options['checkout_by'] = !empty($data['checkout_by']) ? $data['checkout_by'] : 'cart';
                $order->options = json_encode($options);
                RentMy::$Model['Orders']->save($order);

                $connection->commit();
                $this->apiResponse['message'] = 'Your exchange request has been saved. please wait for admin approval';
                return;
            }
            $order = RentMy::$Model['Orders']->makeClosetOrder($data['customer_id'], $data);

            if (empty($order)){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
                return;
            }

            $data['order_id'] = $order['id'];
            foreach ($data['items'] as $item){
                $item['order_id'] = $order['id'];
                $item['location'] = $data['location'];
                $item['rent_start'] = $data['rent_start'];
                $item['rent_end'] = $data['rent_end'];
                $this->makeSwapItem($item);
            }

            $orderDetails = RentMy::$Model['Orders']->details($order['id']);

            $connection->commit();
            $this->apiResponse['data'] = $orderDetails;
            $this->apiResponse['message'] = 'Closet has been added';
        }catch (\Exception $exception){
            $connection->rollback();
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Closet has been added';
        }
    }

    private function swapItems($returnedItems, $newItems): array
    {
        $exchangedItems = [];
        $remainingItems = [];

        foreach ($returnedItems as $returnedItem) {
            foreach ($newItems as $key => $newItem) {
                $exchangedQuantity = min($returnedItem["quantity"], $newItem["quantity"]);

                $exchangedItem = [
                    "variants_products_id" => $newItem["variants_products_id"],
                    "product_id" => $newItem["product_id"],
                    "quantity" => $exchangedQuantity,
                    "name" => $newItem["name"],
                    "images" => $newItem["images"],
                    "order_item_id" => isset($returnedItem["order_item_id"]) ? $returnedItem["order_item_id"] : "",
                    "type" => $newItem['type'] ?? 1,
                    "products" => $newItem['products'] ?? []
                ];

                $exchangedItems[] = $exchangedItem;

                // Update quantity for the new item
                $newItems[$key]["quantity"] -= $exchangedQuantity;

                // If quantity of new item becomes zero, remove it from new items array
                if ($newItems[$key]["quantity"] <= 0) {
                    unset($newItems[$key]);
                }

                // Update quantity for the returned item
                $returnedItem["quantity"] -= $exchangedQuantity;

                // If quantity of returned item becomes zero, break the loop
                if ($returnedItem["quantity"] <= 0) {
                    break 2;
                }
            }
        }

        // Any remaining items from new items array
        $remainingItems = $newItems;

        return array_merge(array_values($exchangedItems), array_values($remainingItems));

    }
//    private function swapItems($swapItems, $existingItems){
//
//        foreach ($swapItems as $index=>$item){
//            $availableQuantity = !empty($existingItems)?$existingItems[0]['quantity']:$item['quantity'];
//
//            if (!empty($existingItems))
//                $swapItems[$index]['order_item_id'] = $existingItems[0]['order_item_id'];
//
//            $requireQuantity = $item['quantity'];
//            if ($requireQuantity < $availableQuantity){
//                $currentAvailable = $availableQuantity - $requireQuantity;
//                $existingItems[0]['quantity'] = $currentAvailable;
//            }elseif ($requireQuantity == $availableQuantity){
//                array_shift($existingItems);
//            }else{
//                array_shift($existingItems);
//                $requireAvailable = $requireQuantity - $availableQuantity;
//                $swapItems[$index]['quantity'] = $requireAvailable;
//                return $this->swapItems($swapItems, $existingItems);
//            }
//
//        }
//       return [$swapItems, $existingItems];
//    }
    private function makeSwapItem($item){
        RentMy::addModel(['Quantities', 'ProductPrices', 'Products', 'Orders', 'ProductsAvailabilities']);
        $quantity = RentMy::$Model['Quantities']->find()
            ->where(['variants_products_id' => $item['variants_products_id']])
            ->where(['location' => $item['location']])
            ->first();

        $item['quantity_id'] = $quantity->id;
//            $item['rent_end'] = Time::parse($data['rent_start'])->addYears(99)->format('Y-m-d H:i');
        $item['price'] = RentMy::$Model['ProductPrices']->getRentalPriceByDates($item['product_id'], $item['variants_products_id'], $item['rent_start'], $item['rent_end'], [], 'order');
//        $item['rental_type'] = 'rent';
        $item['store_id'] = RentMy::$store->id;
        $item['user_id'] = RentMy::$token->id ?? 0;
        $variant = RentMy::$Model['Products']->_getDefaultAttribute($item['variants_products_id']);
        $item['variant_chain_id'] = $variant['variant_chain_id'];
        $item['variant_chain_name'] = $variant['variant_chain_name'];
        $item['rental_duration'] = 1;
        $item['exchange'] = true;
        if ($item['rental_type'] == 'buy'){
            $item['rent_start'] = $item['rent_end'] = "";
        }else{
            $item['price'] = 0;
        }
        RentMy::$Model['Orders']->addProduct($item);

        $availability = RentMy::$Model['ProductsAvailabilities']->find()->where(['product_id'=>$item['product_id'], 'variants_products_id'=>$item['variants_products_id'], 'order_id'=>$item['order_id']])->first();

        if (!empty($availability)){
            $availability->actual_end_date = Time::now()->addYears(99)->format('Y-m-d H:i');
            RentMy::$Model['ProductsAvailabilities']->save($availability);
        }
    }

    private function makeSwapPackageItem($item){
        RentMy::addModel(['Quantities', 'OrderItems', 'Orders']);
        $quantity = RentMy::$Model['Quantities']->find()->where(['product_id' => $item['package_id'], 'variants_products_id' => $item['variants_products_id']])->first();
        $itemOptions = !empty($item['options']) ? json_decode($item['options'], true): [];
        $order = RentMy::$Model['Orders']->find()->where(['id' => $item['order_id']])->first();
        $item['quantity_id'] = $quantity->id;
        $item['store_id'] = RentMy::$store->id;
        $item['type'] = 2;
        $item['package_id'] = $item['product_id'];
        $item['products'] = $itemOptions['products'];
        $item['price'] = 0;
        $item['rental_duration'] = 1;

        $packageItem = RentMy::$Model['OrderItems']->addPackageItem($item, $order);
        if (!empty($packageItem)) {
            RentMy::$Model['Orders']->updateOrderTaxLookup($order['id']);
            RentMy::$Model['Orders']->_updatePriceNQty($order);
        }

    }


//    private function swapItems($swapItems, $existingItems){
//
//        foreach ($swapItems as $index=>$item){
//            $availableQuantity = !empty($existingItems)?$existingItems[0]['quantity']:$item['quantity'];
//
//            if (!empty($existingItems))
//                $swapItems[$index]['order_item_id'] = $existingItems[0]['order_item_id'];
//
//            $requireQuantity = $item['quantity'];
//            if ($requireQuantity < $availableQuantity){
//                $currentAvailable = $availableQuantity - $requireQuantity;
//                $existingItems[0]['quantity'] = $currentAvailable;
//            }elseif ($requireQuantity == $availableQuantity){
//                array_shift($existingItems);
//            }else{
//                array_shift($existingItems);
//                $requireAvailable = $requireQuantity - $availableQuantity;
//                $swapItems[$index]['quantity'] = $requireAvailable;
//                return $this->swapItems($swapItems, $existingItems);
//            }
//
//        }
//       return [$swapItems, $existingItems];
//    }


    /**
     * @param $order_id
     * @return void
     */
    public function approveClosetSwap($order_id){
        $connection = ConnectionManager::get('default');
        $connection->begin();
        try {
            RentMy::addModel(['Orders', 'OrderItems', 'OrderExchange', 'OrderAddresses', 'ProductsAvailabilities']);
            $order = RentMy::$Model['Orders']->find()->where(['store_id'=>RentMy::$store->id, 'id'=>$order_id])->first();


            $assetChecker = RentMy::$Model['Orders']->orderAssetChecking($order_id);

            $order_asset_ids = [];
            if ($assetChecker['returnable_asset']){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Some assets are not being returned';
                return;
            }

            $data = [
                'location'=>$order->location,
                'rent_start'=>$order->rent_start,
                'rent_end'=>$order->rent_end,
            ];
            $swapableOrderItems = RentMy::$Model['OrderExchange']->find()->select(['OrderExchange.id','OrderExchange.quantity', 'OrderExchange.product_id', 'OrderExchange.variants_products_id', 'OrderExchange.order_id', 'OrderExchange.order_item_id', 'OrderExchange.options'])->where(['store_id'=>RentMy::$store->id,'order_id'=>$order_id, 'status'=>0])->toArray();
            $swapableOrderItems = json_decode(json_encode($swapableOrderItems), true);


            $orderAddress = RentMy::$Model['OrderAddresses']->find()->where(['order_id'=>$order_id])->first();
            $data['addresses'] = !empty($orderAddress)?$orderAddress->toArray():[];

            $newOrder = $order;
            $options = !empty($order->options) ? json_decode($order->options, true) : [];

            if (empty($options['checkout_by']) || $options['checkout_by'] != 'order'){
                $newOrder = RentMy::$Model['Orders']->makeClosetOrder($order->customer_id, $data);
                $order->status = 1;
            }


            $orderItems = RentMy::$Model['OrderItems']->find()->select([
                'OrderItems.id', 'OrderItems.order_id', 'OrderItems.product_id', 'OrderItems.variants_products_id', 'OrderItems.quantity', 'OrderItems.rental_type', 'OrderItems.price'
            ])->where(['OrderItems.order_id' => $order_id])->contain(['Products'=>function ($q) {
                return $q->select(['Products.id', 'name']);
            }])->toArray();

            // deleting exiting items
            if (!empty($options['checkout_by']) && $options['checkout_by'] == 'order'){
                RentMy::$Model['OrderItems']->deleteAll([
                    'order_id' => $newOrder->id,
                    'rental_type !=' => 'buy'
                ]);
                RentMy::$Model['ProductsAvailabilities']->deleteAll(
                    [
                        'order_id' => $newOrder->id
                    ]);
            }else{
                RentMy::$Model['ProductsAvailabilities']->updateAll(
                    [
                        'actual_end_date' => Time::now()
                    ],
                    [
                        'order_id' => $newOrder->id
                    ]
                );
            }

            $logs = [];

            foreach ($swapableOrderItems as $item){

                $orderItemInfo = array_values(array_filter($orderItems, function ($orderItem) use ($item) {
                    return $orderItem->id == $item['order_item_id'];
                }));

                $old_order_id = $item['order_id'];
                $id = $item['id']??'';
                unset($item['id']);
                $item['order_id'] = $newOrder['id'];
                $item['location'] = $data['location'];
                $item['rent_start'] = $data['rent_start'];
                $item['rent_end'] = $data['rent_end'];
                $item['checkout_by'] = $options['checkout_by'] ?? '';
                if (!empty( !empty($orderItemInfo)) && ($orderItemInfo[0]['rental_type'] == 'buy')){
                    continue;
                }
                $item['rental_type'] = !empty($orderItemInfo) ? $orderItemInfo[0]['rental_type'] : 'rent';

                $itemOptions = !empty($item['options']) ? json_decode($item['options'], true): [];
                if (!empty($itemOptions['products']) && $itemOptions['type'] == 2){
                    $this->makeSwapPackageItem($item);
                }else{
                    $this->makeSwapItem($item);
                }

                //  Removing reserved quantity for exchange
                //                RentMy::$Model['ProductsAvailabilities']->remove($item, $item['rent_start']);
                RentMy::$Model['ProductsAvailabilities']->deleteAll([
                    'product_id' => $item['product_id'],
                    'variants_products_id' => $item['variants_products_id'],
                    'order_id' => 0,
                    'order_item_id' => $item['order_item_id'],
                ]);

                $newOrderItem = RentMy::$Model['OrderItems']->find()->where(['order_id'=>$newOrder['id'], 'parent_id' => 0])->contain(['Products'])->order(['OrderItems.created'=>'DESC'])->first();
                $exchange = RentMy::$Model['OrderExchange']->find()->where(['id'=>$id])->first();
                if (empty($exchange))
                    $exchange = RentMy::$Model['OrderExchange']->newEntity();

                $exchange = RentMy::$Model['OrderExchange']->patchEntity($exchange, [
                    'order_id'=>$old_order_id,
                    'store_id'=>RentMy::$store->id,
                    'location'=>$item['location'],
                    'new_order_id'=>$newOrderItem->order_id,
                    'order_item_id'=>$item['order_item_id'],
                    'new_order_item_id'=>$newOrderItem->id,
                    'product_id'=>$item['product_id'],
                    'quantity'=>$item['quantity'],
                    'variants_products_id'=>$item['variants_products_id'],
                    'status'=>1
                ]);
                RentMy::$Model['OrderExchange']->save($exchange);


                // Exchange Logs

                if (empty($item['order_item_id'])){
                    $logs[] = $newOrderItem['product']['name'] .'('.$newOrderItem['product']['id'] .") has been added";
                }else{
                    $filteredItems = array_values(array_filter($orderItems, function ($orderItem) use ($newOrderItem, $item) {
                        return $orderItem->product_id != $newOrderItem->product_id && $orderItem->id == $item['order_item_id'];
                    }));

                    if (!empty($filteredItems)){
                        $logs[] = $filteredItems[0]['product']['name'] .'('.$filteredItems[0]['product']['id'] .") has been exchanged with ". $newOrderItem['product']['name'] . '(' .$newOrderItem['id'].')';
                    }

                }

            }

            RentMy::addQueue('Order', ['type' => 'OrderItemExchange', 'order_id' => $newOrder->id, 'user_id' => $newOrder->user_id, 'location' => $newOrder->location, 'store_id' => $newOrder->store_id, 'logs' => $logs, 'source' => RentMy::$token->source]);

            RentMy::$Model['Orders']->save($order);
            RentMy::$Model['Orders']->updateOrderTaxLookup($order['id']);
            RentMy::$Model['Orders']->deliveryRecalculate($order['id']);
            RentMy::$Model['Orders']->_updatePriceNQty($order);

            $connection->commit();
            $orderDetails = $newOrder;
            $this->apiResponse['data'] = $orderDetails;
            $this->apiResponse['message'] = 'Closet has been swapped';
        }catch (\Exception $exception){
            $connection->rollback();
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
        }
    }

    /**
     * @param $order_id
     * @return void
     */
    public function exchangeRequests($order_id){
        RentMy::addModel(['OrderExchange', 'OrderItems']);
        $requests = RentMy::$Model['OrderExchange']->find()->where(['OrderExchange.store_id'=>RentMy::$store->id,'OrderExchange.order_id'=>$order_id, 'OrderExchange.status'=>0])->contain(['Products'])->map(function ($exchange){
            $imageObj = TableRegistry::get('Images');
            $pImages = $imageObj->find()->where(['variants_products_id' => $exchange['variants_products_id']])->toArray();

            $exchange['order_item'] = RentMy::$Model['OrderItems']->find()->where(['OrderItems.id'=>$exchange['order_item_id']])->contain(['Products'])->first();
            $images = array();
            foreach ($pImages as $image)
                if (!empty($image['image_small']))
                    $images[] = $image;

            $exchange['product']['images'] = $images;
            return $exchange;

        })->toArray();
        $this->apiResponse['data'] = $requests;
    }

    /**
     * this method is only for rental items.
     * @GET /orders/with-details
     * @return void
     */
    public function orderWithDetails(){

        $data = $this->request->getQueryParams();
        RentMy::addModel(['Orders']);
        $limit = !empty($data['limit'])?$data['limit']:12;
        $pageNo = !empty($data['page_no'])?$data['page_no']:1;
        $offset = ($pageNo - 1) * $limit;
        $ship = !empty($data['ship']);
        $where = [
            'Orders.store_id' => RentMy::$store->id,
            'Orders.location' => RentMy::$token->location,
            'Orders.type' => 1,
            'Orders.status NOT IN' => [6, 11]
        ];

        if (!empty($data['name'])){
            $name  = explode(' ', $data['name']);
            $firstName = $name[0];
            $lastName = '';
            $name = array_shift($name);
            if (!empty($name)){
                $lastName = implode(' ', $name);
            }

            $condition = ['Orders.first_name LIKE'=>"%".$firstName."%"];
            if (!empty($lastName)){
                $condition[] = ['Orders.last_name LIKE'=>"%".$lastName."%"];
            }
            $where = array_merge($where, [
                "OR" => $condition
            ]);
        }

        if (!empty($data['email'])){
            $where = array_merge($where, ['Orders.email LIKE'=>"%".$data['email']."%",]);
        }

        if (!empty($data['mobile'])){
            $where = array_merge($where, ['Orders.mobile LIKE'=>"%".$data['mobile']."%",]);
        }
        if (!empty($data['order_id'])){
            $where = array_merge($where, ['Orders.id'=>$data['order_id']]);
        }

        if (!empty($data['rent_start'])){
            $rentStart = RentMy::toStoreTimeZone($data['rent_start']);
            $where = array_merge($where, ['Orders.rent_start >='=>$rentStart]);
        }

        if (!empty($data['rent_end'])){
            $rentEnd = RentMy::toStoreTimeZone($data['rent_end']);
            $where = array_merge($where, ['Orders.rent_end <='=>$rentEnd]);
        }

        if (!empty(RentMy::$token->customer_id)){
            $where = array_merge($where, ['Orders.customer_id'=>RentMy::$token->customer_id]);
        }

        RentMy::addModel(['OrderItems', 'OrderAssets']);


//        RentMy::$Model['OrderAssets']->find()
//            ->select(['OrderAssets.order_id'])
//            ->contain(['Assets'])
//            ->where(['OrderAssets.return_process_status !='=>1, 'Assets.store_id' => RentMy::$store->id])
//            ->map(function ($q) use (&$ids) {
//                $ids[] = $q['order_id'];
//            })
//            ->toArray();

        if (!empty($data['product_id'])){
            $ids = [0];
            $orderItems = RentMy::$Model['OrderItems']->find()
                ->select(['OrderItems.order_id'])
                ->contain(['Products'])
                ->where(['OrderItems.product_id' => $data['product_id'], 'Products.store_id' => RentMy::$store->id])
                ->map(function ($q) use (&$ids) {
                    $ids[] = $q['order_id'];
                })
                ->toArray();
            $ids = array_unique($ids);

            $where = array_merge($where, ['Orders.id IN '=>$ids]);
        }

        if (!empty($data['batch_id'])){
            $where = array_merge($where, ['OrderAssets.batch_id'=>$data['batch_id']]);
        }

//RentMy::dbgAndEnd($ids);
        $sort = ['Orders.id'=>'DESC'];
        $ordersObj = RentMy::$Model['Orders']->find()
            ->where($where);

        $orders = $ordersObj->select(['Orders.id', 'Orders.uuid', 'Orders.customer_id', 'Orders.store_id', 'Orders.first_name', 'Orders.last_name', 'Orders.email', 'Orders.mobile','Orders.total_price', 'Orders.rent_start', 'Orders.rent_end'])
            ->contain(['OrderItems'=> function($q)use($ship){
                return $q->select(['OrderItems.id', 'OrderItems.order_id', 'OrderItems.product_id', 'OrderItems.variants_products_id', 'OrderItems.quantity', 'OrderItems.product_type','OrderItems.parent_id'])->where(['OrderItems.rental_type !=' => 'buy'])
                    ->contain(['ItemAssets'=>function($q2)use($ship){
                        $orderAssetWhere = ['ItemAssets.return_process_status !='=>1];
                        if ($ship){
                            $orderAssetWhere = array_merge($orderAssetWhere, ['ItemAssets.return_process_status'=>2]);
                        }
                        if (!$ship){
                            $orderAssetWhere = array_merge($orderAssetWhere, ['ItemAssets.return_process_status'=>0]);
                        }

                        return $q2->select(['ItemAssets.id', 'ItemAssets.asset_id', 'ItemAssets.order_id', 'ItemAssets.order_item_id', 'ItemAssets.pickup_date', 'ItemAssets.return_status', 'ItemAssets.return_date', 'ItemAssets.quantity', 'ItemAssets.return_process_status', 'ItemAssets.batch_id'])->where($orderAssetWhere)
                            ->contain(['Assets'=>function($assetQuery){
                                return $assetQuery->select(['Assets.id', 'Assets.serial_no', 'Assets.current_condition', 'Assets.current_status']);
                            }]);
                    }, 'Products' => function($q){
                        return $q->select(['Products.id', 'Products.name', 'Products.is_tracked']);
                    }]);
            }])
            ->innerJoinWith('OrderAssets',function ($q)use($ship){
                $orderAssetWhere = ['OrderAssets.return_process_status !='=>1];
                if ($ship){
                    $orderAssetWhere = array_merge($orderAssetWhere, ['OrderAssets.return_process_status'=>2]);
                }
                if (!$ship){
                    $orderAssetWhere = array_merge($orderAssetWhere, ['OrderAssets.return_process_status'=>0]);
                }

                return $q->where($orderAssetWhere);
            })
            ->limit($limit)
            ->offset($offset)
            ->order($sort)
            ->group(['Orders.id'])
            ->map(function($order){
                $order->rent_start = RentMy::toStoreTimeZone($order['rent_start']);
                $order->rent_end = RentMy::toStoreTimeZone($order['rent_end']);
                $orderItems = $order->order_items;
                $items = [];
                foreach ($orderItems as $item){

                    $returnedQuantity = RentMy::$Model['OrderAssets']->find()->select(['total_quantity'=>'SUM(quantity)'])->where(['order_id'=>$item['order_id'], 'order_item_id'=>$item['id'], 'return_date IS NOT NULL', 'return_process_status'=>1])->first();
                    $requestedQuantity = RentMy::$Model['OrderAssets']->find()->select(['total_quantity'=>'SUM(quantity)'])->where(['order_id'=>$item['order_id'], 'order_item_id'=>$item['id'], 'return_process_status'=>2])->first();
                    $item['remain_quantity'] = $item['return_quantity'] = $item['quantity'] - $returnedQuantity->total_quantity;
                    $item['returned_quantity'] = (int)$returnedQuantity->total_quantity;
                    if (!empty($requestedQuantity->total_quantity)){
                        $item['return_quantity'] = $requestedQuantity->total_quantity;
                    }
                    if ($item['product_type'] == 1 && empty($item['parent_id'])){
                        if (!empty($item['item_assets']))
                            $items[] = $item;
                    }
                    if ($item['product_type'] == 2 && empty($item['parent_id'])){
                        $productPackages = array_values(array_filter($orderItems, function ($orderItem)use($item){
                            return $orderItem->parent_id == $item['id'] && !empty($orderItem->item_assets);
                        }));

                        if (!empty($productPackages)){
                            $item['package_items'] = $productPackages;
                        }
                        $items[] = $item;
                    }
                }

                $order->order_items = $items;
                return $order;
            })
            ->toArray();


        if (!empty($ship)){
            RentMy::addModel(['MembershipAdditionalCharges']);
            $collection = new Collection($orders);
            $collection = $collection->groupBy(function ($order) {
                return $order->order_items[0]->item_assets[0]->batch_id; // Adjust this based on your data structure
            });
            $orders = $collection->map(function ($order, $key){
                $order[0]['return_delivery_charge'] = '';
                $additionalCharge = RentMy::$Model['MembershipAdditionalCharges']->find()->where(['content_id'=>$key, 'customer_id'=>$order[0]['customer_id'], 'store_id'=>RentMy::$store->id])->first();
                if (!empty($additionalCharge)){
                    $order[0]['return_delivery_charge'] = $additionalCharge->amount;
                }
                return $order;
            })->toArray();

            // object to array conversion
//            $ordersArray = [];
//            foreach ($orders as $groupedOrder) {
//                $ordersArray[] = $groupedOrder;
//            }
//            $orders = $ordersArray;
        }

        $total = $ordersObj->count();
        $this->apiResponse['data'] = $orders;
        $this->apiResponse['page_no'] = $pageNo;
        $this->apiResponse['limit'] = $limit;
        $this->apiResponse['total'] = $total;
    }

    /**
     * @POST orders/items/return
     * @return void
     */
    public function itemReturn(){
        $data = $this->request->getData();
        $batchId = rand(100, 999) . time() . rand(100, 999);
        RentMy::addModel(['Orders']);
        if (!empty($data['orders'])){
            foreach ($data['orders'] as $item){
                $item['store_id'] = RentMy::$store->id;
                $item['return_request'] = $data['return_request'];
                $item['batch_id'] = $batchId;
                $item['return_process_status'] = !empty($data['return_request']) && $data['return_request'] == 1?2:1;
                $response = RentMy::$Model['Orders']->returnItem($item);
            }

        }
        $content = RentMy::getSiteLinkContent();
        $message = !empty($content['custom']['msg_return_request_sent']) ? $content['custom']['msg_return_request_sent'] : 'Return request has been sent. please wait for the admin approval';
        $msgReturn = !empty($content['custom']['msg_return_order']) ? $content['custom']['msg_return_order'] : 'Orders has been returned';


        if($data['return_request'])
            $this->apiResponse['data'] = ['batch_id'=>$batchId];

        $this->apiResponse['message'] = empty($data['return_request'])?$msgReturn:$message;
    }

    /**
     * @return void
     */
    public function cancelReturnRequest(){
        RentMy::addModel(['OrderAssets']);
        $data = $this->request->getData();

        $removeOrderAsset = RentMy::$Model['OrderAssets']->find()->where([
            'order_id' => $data['order_id'],
            'order_item_id' => $data['order_item_id'],
            'return_process_status' => 0
        ])->first();

        $orderAssetObj = RentMy::$Model['OrderAssets']->find()->where([
            'order_id' => $data['order_id'],
            'id' => $data['order_asset_id'],
            'OrderAssets.return_process_status'=>2
        ]);


        $orderAsset =  $orderAssetObj->first();

        if (empty($orderAsset)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'The item is not been requested yet';
            return;
        }

        if (empty($orderAsset->asset_id))
            $orderAsset->quantity = $orderAsset->quantity + ($removeOrderAsset->quantity??0);

        $orderAsset->batch_id = '';
        $orderAsset->return_date = null;
        $orderAsset->return_process_status = 0;
        RentMy::$Model['OrderAssets']->save($orderAsset);

        if (empty($orderAsset->asset_id) && !empty($removeOrderAsset)){
            RentMy::$Model['OrderAssets']->delete($removeOrderAsset);
        }
        RentMy::addQueue('Order', ['type' => 'ReturnProcess', 'order_id' => $orderAsset['order_id'], 'order_asset_id' => $orderAsset['id'], 'order_item_id'=>$orderAsset->order_item_id,'return_quantity'=>$orderAsset['quantity'], 'return_status'=>$orderAsset['return_status'], 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source, 'return_request'=>$data['return_request'], 'asset_id'=>$orderAsset->asset_id, 'batch_id'=>$orderAsset->batch_id, 'user_type'=> (RentMy::$token->source == 'online'?'customer':'user'), 'cancel'=>true]); // add order log queue

        $this->apiResponse['message'] = 'Return request has been canceled';
    }

    public function changeShipId(){
        RentMy::addModel(['OrderAssets']);
        $data = $this->request->getData();

        $orderAssetObj = RentMy::$Model['OrderAssets']->find()->where([
            'order_id' => $data['order_id'],
            'id' => $data['order_asset_id'],
            'OrderAssets.return_process_status'=>2
        ]);

        $orderAsset =  $orderAssetObj->first();

        if (empty($orderAsset)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'The item is not been requested yet';
            return;
        }

        $orderAsset->batch_id  = $data['batch_id'];

        if (RentMy::$Model['OrderAssets']->save($orderAsset)){
            $this->apiResponse['message'] = 'Shipment id has been changed';
            return;
        }

        $this->httpStatusCode = 400;
        $this->apiResponse['message'] = 'Something went wrong!';
    }

    /**
     * add delivery return charge to the customer membership charges
     * payload batch_id, customer_id, amount
     * @return void
     *
     */
    public function addReturnDeliveryCharge()
    {
        try {
            RentMy::addModel(['MembershipAdditionalCharges']);
            $data = $this->request->getData();

            if (!empty($data['batch_id'])) {
                $additionalCharge = RentMy::$Model['MembershipAdditionalCharges']->find()->where(['content_id' => $data['batch_id'], 'customer_id' => $data['customer_id'], 'store_id' => RentMy::$store->id])->first();
                if (empty($additionalCharge)) {
                    $additionalCharge = RentMy::$Model['MembershipAdditionalCharges']->newEntity();
                }

                $chargeData = [
                    'customer_id' => $data['customer_id'],
                    'content_type' => 'ReturnDeliveryCharge',
                    'content_id' => $data['batch_id'],
                    'store_id' => RentMy::$store->id,
                    'amount' => $data['amount'],
                    'description' => 'Return delivery charge',
                    'status' => 0
                ];
//                RentMy::dbgAndEnd($chargeData);
                $additionalCharge = RentMy::$Model['MembershipAdditionalCharges']->patchEntity($additionalCharge, $chargeData);

                if (RentMy::$Model['MembershipAdditionalCharges']->save($additionalCharge)) {
                    $this->apiResponse['message'] = 'Return shipping charge has been saved';
                    return;
                }
            }
        }catch (\Exception $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
        }
    }

    /**
     * @param $id
     * @return void
     *
     * @step 1 : first check order is existing(validation stuff)
     * @step 2: after that, make all order items available here, make actual start date =actual end date
     * @step 3: make all assigned asset available
     * @step 4: mark order as canceled status = 18
     *
     */
    public function cancelOrder($id)
    {
        try {
            RentMy::addModel(['Orders', 'ProductsAvailabilities', 'OrderItems', 'Assets', 'OrderAssets', 'Quantities']);
            $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $id])->first();
            $total = RentMy::$Model['Orders']->getOrderTotal($order);
            if (empty($order)) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Order not found!';
                return;
            }

            $orderItems = RentMy::$Model['OrderItems']->find()->contain(['ItemAssets'])->where([
                'order_id' => $id,
                'product_type' => 1,
            ])->toArray();

            foreach ($orderItems as $orderItem) {
                // make available
                $aItem = RentMy::$Model['ProductsAvailabilities']->find()->where(['order_item_id' => $orderItem->id])->first();
                if (!empty($aItem)) {
                    $aItem = RentMy::$Model['ProductsAvailabilities']->find()
                        ->where(['variants_products_id' => $orderItem['variants_products_id']])
                        ->where(['order_id' =>$id])
                        ->first();
                    $aItem->actual_end_date = $aItem->actual_start_date;
                    RentMy::$Model['ProductsAvailabilities']->save($aItem);
                }

                // make assets available
                if (!empty($orderItem->item_assets)){
                    foreach ($orderItem->item_assets as $itemAsset){
                        if (!empty($itemAsset['asset_id'])){
                            $itemAsset->return_date = Time::now();
                            RentMy::$Model['OrderAssets']->save($itemAsset);
                            RentMy::$Model['Assets']->makeAssetAvailable($itemAsset['asset_id']);
                            $asset = RentMy::$Model['Assets']->get($itemAsset['asset_id']);
                            $eventAvailable = new Event('Model.Assets.available', $this, ['data' => $asset]);
                            $this->getEventManager()->dispatch($eventAvailable);
                        }

                    }
                }else{
                    if ($orderItem['rental_type'] == 'buy'){
                        $qty = RentMy::$Model['Quantities']->find()->where(['id'=>$orderItem['quantity_id']])->first();

                        if (!empty($qty)){
                            $qty->quantity = $qty->quantity + $orderItem['quantity'];
                            RentMy::$Model['Quantities']->save($qty);
                        }
                    }
                }

            }

            $order->status = 18; // 18 = canceled
            if (RentMy::$Model['Orders']->save($order)){
                if (!empty(RentMy::$storeConfig['currency_format']['display_point'])){
                    $amountRefund = -$total;
                    RentMy::addModel(['Customers']);
                    RentMy::$Model['Customers']->recalculateCustomerBalance($order->id, $amountRefund);
                }
                $this->apiResponse['message'] = 'Order has been canceled';
                return;
            }
        }catch (\Exception $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
        }

        $this->httpStatusCode = 400;
        $this->apiResponse['message'] = RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong');
        return;
    }

    /**
     * change order item status
     * @GET /orders/item/:id/status/:status
     * @return void
     */
    public function itemStatusUpdate($orderItemId, $status){
        try {
            RentMy::addModel(['OrderItems']);
            $orderItem = RentMy::$Model['OrderItems']->find()->where(['id' => $orderItemId])->first();
            if (empty($orderItem)){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Invalid order item';
                return;
            }

            $orderItem->status = $status;
            if (RentMy::$Model['OrderItems']->save($orderItem)){
                $this->apiResponse['message'] = 'Order item status has been changed';
                return;
            }

        }catch (\Exception $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
            return;
        }

    }

    /**
     * @POST /orders/:id/delivery-charge
     * this function will change delivery charge manually
     * @param $orderId
     * @return mixed
     */
    public function updateDeliveryCharge($orderId){
        try {
            RentMy::addModel(['Orders']);
            $data = $this->request->getData();
            if (!$this->array_keys_exist($data, ['delivery_charge'])){
                $this->apiResponse['message'] = "Required field missing";
                return;
            }

            $data['delivery_charge'] = !empty($data['delivery_charge']) ? $data['delivery_charge'] : 0;
            $order = RentMy::$Model['Orders']->find()->where(['id' => $orderId])->first();
            if (!empty($order)){
                $order->delivery_charge = $data['delivery_charge'];
                if (RentMy::$Model['Orders']->save($order)){
                    RentMy::$Model['Orders']->updateOrderTaxLookup($order->id);
                    RentMy::$Model['Orders']->_updatePriceNQty($order);
                    $this->apiResponse['data'] = RentMy::$Model['Orders']->view($order->id);
                    $this->apiResponse['message'] = "Delivery charge has been changed";
                    return;
                }
            }
        }catch (\Exception $exception){
            $this->apiResponse['message'] = $exception->getMessage();
            return;
        }
    }

    /**
     * @POST /orders/using-product
     * @return mixed
     */
    public function orderUsingProduct()
    {
        $connection = ConnectionManager::get('default');
        $connection->begin();
        try {

        RentMy::addModel(['Quantities', 'Orders', 'Products', 'OrderItems', 'OrderProductOptions', 'ProductPrices', 'ProductsAvailabilities', 'OrderAddresses']);
        $data = $this->request->getData();

        $options = [];
        if(!empty($data['billing']['site_id']))
            $options['site_id'] = $data['billing']['site_id'];

        $orderData = [
            'type'=>1,
            'customer_id'=> $data['customer_id'] ?? '',
            'shipping_method'=>1,
            'store_id'=>RentMy::$store->id,
            'is_delivered'=>0,
            'location' => RentMy::$token->location,
            'status'=>2,
            'rent_start'=>$data['rent_start'] ?? '',
            'rent_end'=>$data['rent_end'] ?? '',
            'options'=>json_encode($options)
        ];
        $order = RentMy::$Model['Orders']->newEntity();
        $order = RentMy::$Model['Orders']->patchEntity($order, $orderData);
        if (RentMy::$Model['Orders']->save($order)){

            if (!empty($data['variants_products_id'])){
                $data['order_id'] = $order['id'];
                $quantity = RentMy::$Model['Quantities']->find()
                    ->where(['variants_products_id' => $data['variants_products_id']])
                    ->where(['location' => $data['location']])
                    ->first();


                if ((!empty($data['rent_start']) && !empty($data['rent_end'])) && (strtotime($data['rent_end']) < strtotime($data['rent_start']))) {
                    $this->apiResponse['error'] = 'Invalid Rental Date';
                    return;
                }

                if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
                    $rentalDates = RentMy::formatRentalDates($data['rent_start'], $data['rent_end']);
                    $data['rent_start'] = $rentalDates['start_date'];
                    $data['rent_end'] = $rentalDates['end_date'];

                    RentMy::addModel(['OrderItems']);
                    $orderItem = RentMy::$Model['OrderItems']->find()->where(['rental_type !=' => 'buy', 'order_id' => $data['order_id']])->first();

                    if (!empty($order) && ((empty($order->rent_start) || empty($order->rent_end)) || empty($orderItem))) {
                        $order_data = [
                            'rent_start' => RentMy::toUTC($data['rent_start']),
                            'rent_end' => RentMy::toUTC($data['rent_end']),
                            'purchase_type' => 'rent'
                        ];
                        $order = RentMy::$Model['Orders']->patchEntity($order, $order_data);
                        RentMy::$Model['Orders']->save($order);
                    }
                }
                $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();

                if (!empty($order['rent_start']) && !empty($orderItem)) {
                    $data['rent_start'] = RentMy::toStoreTimeZone($order['rent_start'], 'Y-m-d H:i:00');
                    $data['rent_end'] = RentMy::toStoreTimeZone($order['rent_end'], 'Y-m-d H:i:00');
                }

                if ($data['rental_type'] == 'buy') {
                    $prices = RentMy::$Model['ProductPrices']->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $data['variants_products_id']])->toArray();
                    if (!empty($options['custom_fields'])) {
                        RentMy::addModel(['OrderProductOptions']);
                        RentMy::$Model['OrderProductOptions']->calculatePricing($prices, $options['custom_fields']);
                    }
                    $data['price'] = 0;
                    if (!empty($prices)) {
                        $price = $prices[0];
                        $data['price'] = $price->price;
                        if (RentMy::$storeConfig['inventory']['promo_price'] && ($price->promo_price > 0)) {
                            $data['price'] = $price->promo_price;
                        }
                    }
                } else {
                    $data['price'] = RentMy::$Model['ProductPrices']->getRentalPriceByDates($data['product_id'], $data['variants_products_id'], $data['rent_start'], $data['rent_end'], [], 'order', $data['custom_fields'] ?? []);
                    $data['rental_type'] = 'rent';
                }

                $data['quantity_id'] = $quantity->id;
                $data['rental_duration'] = empty($data['rental_duration']) ? 1 : $data['rental_duration'];
                if (in_array($data['rental_type'], ['hourly', 'daily', 'weekly', 'monthly', 'fixed', 'rent'])) {
                    RentMy::addModel(['Holidays']);
                    $holiday = RentMy::$Model['Holidays']->checkHoliday(RentMy::$store->id, RentMy::toUTC($data['rent_start']));
                    if ($holiday['success']) {
                        $this->apiResponse['error'] = "Product not added because we're closed for " . $holiday['data']->description . " for the date you selected.";
                        return;
                    }
                    $rent_start = RentMy::toUTC($data['rent_start'], 'Y-m-d H:i');
                    $rent_end = RentMy::toUTC($data['rent_end'], 'Y-m-d H:i');
                } else {
                    $rent_start = Time::now()->format('Y-m-d H:i');
                    $rent_end = Time::now()->addDays(14)->format('Y-m-d H:i');
                }
                $available = RentMy::$Model['ProductsAvailabilities']->getProductAvailability($data['product_id'], $data['variants_products_id'], $data['location'], $rent_start, $rent_end);

                if ($available < $data['quantity']) {
                    $this->apiResponse['error'] = 'Not available.';
                    return;
                }
                $data['store_id'] = RentMy::$store->id;
                $data['user_id'] = isset($this->parseToken->id) ? $this->parseToken->id : 0;
                $variant = RentMy::$Model['Products']->_getDefaultAttribute($data['variants_products_id']);
                $data['variant_chain_id'] = $variant['variant_chain_id'];
                $data['variant_chain_name'] = $variant['variant_chain_name'];

                $order = RentMy::$Model['Orders']->addProduct($data);
            }

            if (!empty($data['billing'])){
                $orderAddresses = RentMy::$Model['OrderAddresses']->newEntity();
                $orderAddressesData = array_merge($data['billing'], [
                    'store_id' => RentMy::$store->id,
                    'order_id' => $order->id,
                ]);

                $orderAddresses = RentMy::$Model['OrderAddresses']->patchEntity($orderAddresses, $orderAddressesData);
                RentMy::$Model['OrderAddresses']->save($orderAddresses);
            }
            $connection->commit();

            $this->apiResponse['data'] = $order;
            $this->apiResponse['message'] = "Order has been created";
            return;

        }
        }catch (\Exception $exception){

            $connection->rollback();
            $this->httpStatusCode =400;
            $this->apiResponse['message'] = $exception->getMessage();
        }
    }
    /*
     * @GET /orders/details
     * @return mixed
     */

    public function orderListDetails()
    {
        $orderIds = $this->request->getQuery('order_ids');
        if (empty($orderIds)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Required field missing";
            return;
        }
        $orderIds = explode(',', $orderIds);
        RentMy::addModel(['OrderItems']);
        $orderItems = RentMy::$Model['OrderItems']->find()
            ->select(['OrderItems.product_id', 'OrderItems.order_id', 'order_item_id' => 'OrderItems.id', 'Products.id', 'Products.name'])
            ->where(['OrderItems.store_id' => RentMy::$store->id, 'order_id IN' => $orderIds])
            ->contain(['Products'])
            ->toArray();

        $result = [];

        foreach ($orderItems as $item) {
            $orderId = $item['order_id'];

            if (!isset($result[$orderId])) {
                $result[$orderId] = [
                    'order_id' => $orderId,
                    'order_items' => []
                ];
            }

            $result[$orderId]['order_items'][] = [
                'product_id' => $item['product_id'],
                'order_item_id' => $item['order_item_id'],
                'product' => $item['product']
            ];
        }

        $result = array_values($result);
        $this->apiResponse['data'] = $result;

    }

    /**
     * @GET /orders/assigned-assets
     * @return void
     */
    public function assignedAssetsOfOrders()
    {
       $data = $this->request->getQueryParams();

       if (empty($data['order_ids'])){
           $this->httpStatusCode = 400;
           $this->apiResponse['message'] = "Required field missing";
           return;
       }

       $orderIds = explode(',', $data['order_ids']);
       RentMy::addModel(['OrderAssets']);
       $orderAssets = RentMy::$Model['OrderAssets']->find()->select(['OrderAssets.order_id', 'OrderAssets.order_item_id', 'asset_id', 'Assets.serial_no', 'Assets.product_id'])->where(['order_id IN' => $orderIds, 'OrderAssets.asset_id IS NOT NULL'])->contain(['Assets'])->toArray();
        $this->apiResponse['data'] = $orderAssets;
    }

}
