<?php

namespace App\Controller\V2;

use App\Controller\AppController;
use App\Lib\RentMy\RentMy;
use App\Services\Product\PackageService;
use App\Services\Product\ProductService;

class PackagesController extends AppController
{
    public function getPackageDetails($slug){
        RentMy::addModel(['Products']);
        $data = $this->request->getQueryParams();
        try {
            $package = (new PackageService($data))->getDetails($slug);
        }catch (\Exception $e) {
            $this->httpStatusCode = 400;
            $this->apiResponse['error'] = $e->getMessage();
            return;
        }
        $this->apiResponse['data'] = $package;
    }
}
