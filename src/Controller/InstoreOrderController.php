<?php

namespace App\Controller;

use App\Lib\Atrium;
use App\Lib\Payment\AuthorizeNet\AuthorizeNet;
use App\Lib\Payment\ConvenuPay;
use App\Lib\Payment\DeltaPay;
use App\Lib\Payment\Empyrean;
use App\Lib\Payment\FreedomPay;
use App\Lib\Payment\goMerchant\goMerchant;
use App\Lib\Payment\goMerchant\Vault;
use App\Lib\Payment\Midtrans;
use App\Lib\Payment\Payment;
use App\Lib\Payment\Square;
use App\Lib\Payment\Stripe\Customer;
use App\Lib\Payment\Stripe\Stripe;
use App\Lib\Payment\Stripe\StripeIntent;
use App\Lib\Payment\Transafe;
use App\Lib\Payment\ValorPay\ValorPay;
use App\Lib\ProductContainer;
use App\Lib\RentMy\Email;
use App\Lib\RentMy\PosCustomerView;
use App\Lib\RentMy\RentMy;
use App\Lib\RentMy\Shipping;
use App\Lib\S3;
use App\Services\Order\OrderAssetService;
use App\Utility\UrlHelper;
use Cake\Collection\Collection;
use Cake\I18n\Time;
use App\PaymentGateway\PayPalPayment;
use Cake\Core\Configure;
use Cake\Event\Event;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\Utility\Text;

class InstoreOrderController extends AppController
{
    private $productContainer, $currency, $storeId, $userId;

    public function initialize()
    {
        parent::initialize();
        $this->productContainer = new ProductContainer();
    }

    /**
     * Initializing the basic variable e.g. store_id, user_id, currency
     */
    public function beforeFilter(Event $event)
    {
        parent::beforeFilter($event);
    }

    private function paymentLog($order_id, $content_type, $content_id = null, $status, $options, $action_name = null, $response_text = null, $api_request = null)
    {
        $logData = array(
            'user_id' => RentMy::$token->id,
            'store_id' => RentMy::$store->id,
            'order_id' => $order_id,
            'content_type' => $content_type,
            'status' => $status,
            'options' => $options,
            'action_name' => $action_name,
            'response_text' => $response_text,
            'api_request' => $api_request,
        );

        $event = new Event('Model.PaymentLogs.PaymentLogs', $this, ['logData' => $logData]);
        $this->getEventManager()->dispatch($event);
    }

    /**
     * Get payment type of an order
     *
     * 1 => 'Online store – Card entry',
     * 2 => 'In-store – Card entry',
     * 3 => 'In-store – Card Swipe',
     * 4 => 'In-store – Cash',
     * 5 => 'In-store – Additional',
     * 6 => 'Online store – Additional',
     */
    private function _getPaymentType($data)
    {
        $payment_type = 4;
        if ($data['type'] == 1 || $data['type'] == "Online") {
            $payment_type = (isset($data['swipe']) && ($data['swipe'] == true)) ? 3 : 2;
        } else {
            if (!empty($data['gateway_id']) && $data['gateway_id'] != 'undefined') {
                $payment_type = 5;
            }
        }
        return $payment_type;
    }

    /**
     * Create an order
     *
     * @param accttyppe: "Paid in Cash"
     * @param address_line1: "gfhfhfh"
     * @param city: "Dhaka"
     * @param combine_address: ""
     * @param country: "bd"
     * @param currency: "USD"
     * @param custom_values: "[]"
     * @param delivery:
     * @param first_name: "Dev"
     * @param gateway_id: "undefined"
     * @param id: 133
     * @param is_admin: true
     * @param item_log: []
     * @param last_name: "Test"
     * @param location: 24
     * @param mobile: "666666"
     * @param pickup: 24
     * @param return_to: "24"
     * @param salesman: 21
     * @param state: "???? ?????"
     * @param terminal_id: null
     * @param token: "1559292686540"
     * @param type: 2
     * @param zipcode: "1000"
     *
     * @return order_id
     */

    public function add()
    {
        try {


        $this->add_model(array('PaymentGateways', 'OrderItems', 'OrderAssets', 'CartItems', 'Carts', 'Products', 'Coupons',
            'Payments', 'Stores', 'StoresUsers', 'ProductsAvailabilities', 'Customers', 'OrderAdditionalInfo', 'Orders'));

        /** Requirement checking Start */

        if (!$this->request->is('post')) {
            $this->apiResponse['message'] = 'Method Not Allowed';
            return;
        }

        $data = $this->request->getData();
        if (empty($data['token'])) {
            $this->apiResponse['error'] = 'Cart Token is missing!';
            return;
        }

        $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();
        if (empty($cart) || $cart->total_quantity < 1) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Invalid or Empty Cart!';
            return;
        }

        /** Requirement checking End */

        Log::alert('-------store ID: ' . $this->parseToken->store_id . '--------- Request Data: ' . json_encode($data), ['scope' => ['instore']]);

        $data['user_id'] = RentMy::$token->id;
        $data['store_id'] = RentMy::$store->id;
        $data['currency'] = RentMy::getCurrency();

        $data['cart_id'] = $cart->id;

//        if (!empty($cart->coupon_id)) {
//            $coupon = $this->Coupons->getCouponData($cart->coupon_id);               // Coupon Validity Checking
//            if (empty($coupon)) {                                                    // Coupon Apply
//                $tax = $cart->sub_total * (Configure::read('taxValue') * 0.01);
//                $cart->coupon_id = '';
//                $cart->tax = $tax;
//                $cart->total_discount = 0;
//                $cart->total_discount_tax = 0;
//            }
//        }
        // @TODO - need to fix this condition properly

        $total_amount = empty($data['amount']) ? (($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge + $cart->product_option_price)) : $data['amount'];

        if (isset($data['payment_amount']) && !empty($data['payment_amount'])) {
            $data['amount'] = $data['payment_amount'];
        } else if (!empty($data['is_cash_register'])) {
            $data['amount'] = !empty($data['payment_amount']) ? $data['payment_amount'] : 0;
        } else {
            //$data['amount'] = empty($data['amount']) ? ( ($cart->sub_total + $cart->tax + $cart->total_discount_tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax) - $cart->total_discount) : $data['amount'];
            //$data['amount'] = empty($data['amount']) ? (($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax) - $cart->total_discount) : $data['amount'];
            $data['amount'] = empty($data['amount']) ? (($cart->sub_total + $cart->tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax + $cart->additional_charge + $cart->product_option_price)) : $data['amount'];
        }


        if (!empty(RentMy::$storeConfig['checkout']['separate_deposit_amount']) && !empty($data['capture']) && !empty($data['email'])) {
            if ((in_array($data['payment_gateway_name'], ['Stripe', 'Transafe'])) && ($total_amount <= $data['amount'])) {
                $data['amount'] = $data['amount'] - $cart->deposit_amount;
                $data['deposit_amount'] = $cart->deposit_amount;
            }
        }

        $data['cart']['options'] = $cart->options;
        $cart_options = json_decode($data['cart']['options'], true);
        if (!empty($cart_options['recurring']['duration_type'])) {
            if ($data['payment_gateway_name'] == 'Stripe') {
                $data['amount'] = empty($cart->deposit_amount) ? 1 : $cart->deposit_amount;
            }
        }
        /* Check Payment*/

        if (isset($data['email']) && !empty($data['email'])) {
            $customer = $this->Customers->create($data);
            $data['customer_id'] = $customer->id;
            $data['customer'] = $customer;
        }

        if ($data['type'] == 1 && (!isset($data['swipe']) || ($data['swipe'] == false))) {
            $getPayment = $this->_getPayment($data);
            if ($getPayment['success']) {
                $payment = $getPayment['data'];
                $data['customerRef'] = !empty($getPayment['customerRef']) ? $getPayment['customerRef'] : [];
                $this->Customers->create($data);
                //$this->paymentLog(null, 'Payments', null, 1, json_encode($getPayment['data']));
            } else {
                $paymentData = array(
                    'availability' => array('success' => true),
                    'payment' => array('success' => false, 'message' => $getPayment['message']),
                );
                //$this->paymentLog(null, 'Payments', null, 2, $getPayment['message']);

                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($paymentData), ['scope' => ['instore']]);
                $this->apiResponse['data'] = $paymentData;
                return;
            }
        }

        // Get payment type
        $payment_type = $this->_getPaymentType($data);
        // $data['status'] = $this->_getOrderStatus($data);

        $order = $this->_makeOrder($data, $cart, $payment_type);

        if (isset($getPayment) && !empty($getPayment['stripe_payment_payment_intent_id'])) {
            $config = (new Payment())->getStorePaymentConfig('Stripe');
            $stripe = new StripeIntent('PaymentIntent', $config['config']);
            $stripe->updateIntent($getPayment['stripe_payment_payment_intent_id'], ['order_id' => $order['id']]);
            if (!empty($getPayment['deposit_payment'])) {
                $stripe->updateIntent($getPayment['deposit_payment']['stripe_payment_payment_intent_id'], ['order_id' => $order['id']]);
            }
        }


        $data['order_id'] = $order['id'];
        $queueData = ['type' => 'Create', 'order_id' => $order['id'], 'content_id' => $order['id'], 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source];
        if ($order['rent_end'])
            $queueData['rent_end'] = $order['rent_end']->format('Y-m-d H:i:s');

        if ($order['rent_start'])
            $queueData['rent_start'] = $order['rent_start']->format('Y-m-d H:i:s');

        $queueData['mobile'] = $data['mobile'] ?? null;

        RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], $queueData);
        RentMy::addQueue('Order', ['type' => 'AfterCreate', 'order_id' => $order->id, 'source' => RentMy::$token->source, 'location' => RentMy::$token->location, 'store_id' => RentMy::$store->id]);
        //RentMy::addQueue('Order', ['type' => 'Create', 'order_id' => $order['id'], 'rent_start'=> $order['rent_start']->format('Y-m-d H:i:s'), 'rent_end'=> $order['rent_end']->format('Y-m-d H:i:s'), 'content_id' => $order['id'], 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source]);

        RentMy::logToGenius([
                "account" => @$order['event_location'],
                "event" => "order_created",
                "status" => "success",
                "description" => @$order['event_location'] . ' Order created',
                "value" => 'Order ID: ' . $order['id'],
                "custom_content" => json_encode([
                    'data' => $queueData,
                    'order' => $order,
                ]),
                "ref2" => $order['customer_id']
        ]);

        if (($data['type'] == 1 || $data['type'] == "Online")) {

            if (isset($data['swipe']) && ($data['swipe'] == true)) {
                if ($data['payment_gateway_name'] == 'PAX') {
                    $response = $data['response_text'];
                    $data['transaction_id'] = $response['transaction_id'];
                    $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured'; //'Captured';//($data['capture']) ? 'Captured' : 'Authorized';
                    $data['content_id'] = 3;
                    $data['action_name'] = 'card-swipe';
                    $data['response_text'] = json_encode($response);
                    $this->_payment($data);
                } else if ($data['payment_gateway_name'] == 'TransafeCardPresent') {
                    $response = $data['response_text'];
                    $data['transaction_id'] = $response['transaction_id'];
                    $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured'; //'Captured';//($data['capture']) ? 'Captured' : 'Authorized';
                    $data['content_id'] = 3;
                    $data['action_name'] = 'card-swipe';
                    $data['response_text'] = json_encode($response);
                    $this->_payment($data);
                } else if ($data['payment_gateway_name'] == 'ConvenuPayCardPresent' && !empty($data['response_text'])) {
                    $response = $data['response_text'];
                    $data['payment_gateway_name'] = 'ConvenuPay';
                    $data['transaction_id'] = $response['id'];
                    $data['payment_method'] = (empty($response['type']) && ($response['type'] == 'authorize')) ? 'Authorized' : 'Captured'; //'Captured';//($data['capture']) ? 'Captured' : 'Authorized';
                    $data['content_id'] = 3;
                    $data['action_name'] = 'card-swipe';
                    $data['response_text'] = is_array($response)?json_encode($response):$response;
                    $this->_payment($data);
                } else if ($data['payment_gateway_name'] == 'StripeCardPresent' && !empty($data['response_text'])) {
                    $response = $data['response_text'];
                    // vaulting
                    if (!empty($response['charges']['data'][0]['payment_method_details']['card_present']['generated_card'])){
                        try {
                            $config = (new Payment())->getStorePaymentConfig('Stripe');

                            $stripe = new StripeIntent('PaymentIntent', $config['config']);
                            $payment = [];
                            $payment['customerRef'] = $stripe->createCustomer(RentMy::$store->id, $data);

                            (new Customer('PaymentIntent', $config['config']))->addSourceForCustomerIntent($payment['customerRef']['ref'], $response['charges']['data'][0]['payment_method_details']['card_present']['generated_card']);

                        }catch (\Throwable $throwable){

                        }

                    }

                    $data['payment_gateway_name'] = 'Stripe';
                    $data['transaction_id'] = $response['id'];
                    $data['payment_method'] = (empty($response['type']) && ($response['type'] == 'authorize')) ? 'Authorized' : 'Captured'; //'Captured';//($data['capture']) ? 'Captured' : 'Authorized';
                    $data['content_id'] = 3;
                    $data['action_name'] = 'card-swipe';
                    $data['response_text'] = is_array($response)?json_encode($response):$response;
                    $this->_payment($data);
                }else if ($data['payment_gateway_name'] == 'ValorPayCardPresent' && !empty($data['response_text'])) {
                    $response = $data['response_text'];
                    $data['payment_gateway_name'] = 'ValorPayCardPresent';
                    $data['transaction_id'] = $response['response']['TXN_ID'];
                    $data['payment_method'] = (!empty($response['response']['TRAN_METHOD']) && $response['response']['TRAN_METHOD'] == 'SALE') ? 'Captured' : 'Authorized'; //'Captured';//($data['capture']) ? 'Captured' : 'Authorized';
                    $data['content_id'] = 3;
                    $data['action_name'] = 'card-swipe';
                    $data['response_text'] = is_array($response)?json_encode($response):$response;
                    // adding vault
                    if (!empty($response['response']['TOKEN'])){
                        try {
                            RentMy::addModel(['StoreTerminals']);
                            $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();
                            $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
                            $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
                            $terminalGatewayConfig = [
                                "appId" => $terminalOptions['app_id'],
                                "appKey" => $terminalOptions['app_key'],
                                "epi" => $terminalOptions['epi'],
                                "apiUrl" => $config['config']['apiUrl'],
                            ];
                            $data['payment_token'] =  $response['response']['TOKEN'];
                            $paymentObj = new ValorPay($terminalGatewayConfig);
                            $paymentObj->createCustomer(RentMy::$store->id, $data);
                        }catch (\Throwable $throwable){

                        }
                    }
                    $this->_payment($data);
                }else {
                    //boltcod
                    $response = json_decode($data['response_text'], true);
                    $data['transaction_id'] = $response['retref'];
                    if (isset($data['log_text']) && !empty($data['log_text'])) {
                        $log_text = $data['log_text'];
                        for ($i = 0; $i < count($log_text) - 1; $i++) {
                            $this->paymentLog($order['id'], null, null, $log_text[$i]['status'], $log_text[$i]['options'], $log_text[$i]['action_name'], $log_text[$i]['response_text']);
                        }
                    }
                    $data['payment_method'] = $log_text[count($log_text) - 1]['options'];
                    $data['content_id'] = 3;
                    $options = $log_text[count($log_text) - 1]['options'];
                    $responseText = $log_text[count($log_text) - 1]['response_text'];
                    $data['action_name'] = $log_text[count($log_text) - 1]['action_name'];
                    $this->_payment($data, $options, $responseText);
                }

            } else {
                /* Start Authorized data insert to payment table */

                $data['payment_method'] = ($data['capture']) ? 'Captured' : 'Authorized';
                if ($data['payment_gateway_name'] == 'Stripe') {
                    $cart_options = json_decode($data['cart']['options'], true); // recurring options.
                    if (!empty($cart_options['recurring']['duration_type'])) {
                        $data['payment_method'] = 'Authorized';
                    }
                }
//                elseif ($data['payment_gateway_name'] == 'Authorize.Net'){
//                    $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
//                }
                $data['transaction_id'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['retref'] : $getPayment['transaction_id'];
                $data['response_text'] = json_encode($getPayment['data']);
                $data['content_id'] = isset($data['is_admin']) ? 2 : 1;
                $data['action_name'] = 'card-entry';
                $option = $data['payment_gateway_name'] == 'CardConnect' ? $payment['resptext'] : 'Approval';

                $this->_payment($data, $option);
                if (!empty($getPayment['deposit_payment'])) {
                    $data['amount'] = $data['deposit_amount'];
                    $data['transaction_id'] = $getPayment['deposit_payment']['transaction_id'];
                    $data['response_text'] = json_encode($getPayment['deposit_payment']['data']);
                    $data['payment_method'] = 'Authorized';
                    $this->_payment($data, $option);
                }
            }
        } elseif ($data['type'] == 3){
            $uuid = $this->Orders->get($order['id'])->uuid;
            $data['uid'] = $uuid;
            $response = array(
                'availability' => ['success' => true],
                'order' => ['success' => true, 'data' => ['uid' => $uuid]],
                'payment' => ['success' => true, 'order_id' => $data['order_id'], 'quote' => true],
            );
            Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['online']]);
            $this->apiResponse['data'] = $response;
        } else {

            // when payment is false
            if (isset($data['payment']) && ($data['payment'] == false)) {
                $uuid = $this->Orders->get($order['id'])->uuid;
                if (!empty($data['email'])) {
                    RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);
                }
                $quoteResponse = array(
                    'availability' => ['success' => true],
                    'order' => ['success' => true, 'data' => ['uid' => $uuid]],
                    'payment' => ['success' => true, 'order_id' => $data['order_id']],
                );
                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['instore']]);
                $this->apiResponse['data'] = $quoteResponse;
                return;
            }
            if ($data['quote']) { // for quote order ignor payments and payment logs
                $uuid = $this->Orders->get($order['id'])->uuid;
                if (!empty($data['email'])) {
                    RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);
                }
                $quoteResponse = array(
                    'availability' => ['success' => true],
                    'order' => ['success' => true, 'data' => ['uid' => $uuid]],
                    'payment' => ['success' => true, 'order_id' => $data['order_id'], 'quote' => true],
                );
                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['instore']]);
                $this->apiResponse['data'] = $quoteResponse;
                return;
            } else {
                $payment_method = 'Paid';
                $content_id = 4;
                $action_name = 'cash';
                if (!empty($data['gateway_id']) && $data['gateway_id'] != 'undefined') {

                    $gateway = $this->PaymentGateways->get($data['gateway_id']);
                    if ($gateway) {
                        if ($gateway->name == 'Atrium') {
                            $atriumObj = new Atrium();
                            $captureData = $data;
                            $captureData['customer_id'] = $order['customer_id'];
                            $response = $atriumObj->capture($captureData);
                            if ($response['approved']) {
                                $payment_method = 'Paid';
                            } else {
                                $payment_method = 'Unpaid';
                            }
                            $action_name = $gateway->name;
                            $content_id = 7;
                        } else {
                            $data['type'] = 3;
                            $gatewayConfig = json_decode($gateway['config'], true);
                            if ($gatewayConfig['is_paid']) {
                                //$data['amount'] = $cart->sub_total + $cart->tax + $cart->total_discount_tax + $cart->deposit_amount + $cart->delivery_charge + $cart->delivery_tax - $cart->total_discount;
                                $payment_method = 'Paid';
                                $content_id = 5;
                                $action_name = 'Additional' . '(' . $gateway->name . ')';
                            } else {
                                $action_name = 'Additional' . '(' . $gateway->name . ')';
                                $payment_method = 'Unpaid';
                                $content_id = 5;
                                $data['amount'] = 0;
                            }
                        }
                    } else {
                        $payment_method = 'Unpaid';
                        $content_id = 5;
                    }
                } else { // cash payment
                    //$payment_method = 'Unpaid';
                    //$content_id = 5;
                }

                $data['payment_method'] = $payment_method;
                $data['content_id'] = $content_id;
                $data['action_name'] = $action_name;
//                $response_text = array(
//                    'amount_tendered' => $data['amount_tendered'] ?? '',
//                    'change_amount' => !empty($data['amount_tendered']) ? $data['amount_tendered'] - $data['amount'] : ""
//                );
//
//                $data['response_text'] = json_encode($response_text);
                $data['change_amount'] = !empty($data['amount_tendered']) ? $data['amount_tendered'] - $data['amount'] : "";
                $this->_payment($data, 'In-store – Cash');
            }

        }
            (new OrderAssetService($order['id']))->assignAssetsAutomaticallyToOrder();
        }catch (\Exception $throwable){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $throwable->getMessage();
        }
    }

    /**
     * @deprecated
     * 1 => 'Archived',
     * 2 => 'Pending',
     * 3 => 'Paid',
     * 4 => 'Rejected',
     * 5 => 'With Customer',
     * 6 => 'Returned',
     * 7 => 'Reserved',
     * 8 => 'Shipped',
     * 9 => 'Unpaid',
     * 10 => 'Quote',
     */
    private function _getOrderStatus($data)
    {
        $status = 2;  // credit cart payment and cash on delivery
        if (in_array($data['type'], [1, 2])) {
            if (!empty($data['gateway_id'])) {
                $gateway = $this->PaymentGateways->find()->where(['id' => $data['gateway_id']])->first();
                $data['payment_gateway_details'] = $gateway;
            }
            $this->Orders->setStatusOnCreate($data['order_id'], $data);
        } else { // for type 3  (additional payments)

            if (!empty($data['gateway_id'])) {
                $gateway = $this->PaymentGateways->find()->where(['id' => $data['gateway_id']])->first();
//                if ($gateway->name == 'Atrium') {
//                    $status = 3; // Atrium - processed
//                }
//                $gatewayConfig = json_decode($gateway['config'], true);
//                if ($gatewayConfig['is_paid']) {
//                    $status = 3;
//                }
                $this->Orders->setStatusOnCreate($data['order_id'], $data);
            } else {
                $order = $this->Orders->get($data['order_id']);
                $order->status = $status;
                $this->Orders->save($order);
            }
        }
    }


    /**
     * Payment using credit card
     *
     * @param payment_gateway_name
     * @option  CardConnect
     * @option  Stripe
     * @option  PayPal
     * @option  AuthorizeDOTNET
     * @return array|response|mixed
     */
    public function _getPayment($data)
    {
        try {
            if ($data['payment_gateway_name'] != '' && $data['payment_gateway_name'] != 'CardConnect') {

                $param = [
                    'amount' => $data['amount'],
                    'store_id' => $data['store_id'],
                    'description' => '',
                    'currency' => $data['currency'],
                ];

                $card = !empty($data['card']) ? $data['card'] : [];

                $payment = [];
                switch ($data['payment_gateway_name']) {

                    case "Stripe":
                        $config = (new Payment())->getStorePaymentConfig('Stripe');

                        $stripe = new StripeIntent('PaymentIntent', $config['config']);

                        // recurring options.
                        $data['capture_method'] = empty($data['capture']) ? 'manual' : 'automatic';

                        $paymentIntent = $stripe->getIntent($data['account']['id']);

                        if ($paymentIntent['status'] == 'requires_confirmation')
                            $paymentIntent = $stripe->confirmIntend($data['account']['id']);

                        $payment = [
                            'success' => true,
                            'transaction_id' => $paymentIntent->id,
                            'data' => $paymentIntent,
                            'stripe_payment_payment_intent_id' => $paymentIntent->id,
                        ];

                        if (!empty($paymentIntent['charges']['data'][0]['captured']) && $paymentIntent['status'] != 'succeeded')
                            $payment['success'] = false;

                        $data['stripe_payment_method'] = $paymentIntent->payment_method;
                        $data['action_name'] = 'card-entry';
                        $data['store_id'] = RentMy::$store->id;
                        if ((!empty($data['paytype']) && ($data['paytype'] == 'stripe_token'))) {
                            $payment['customerRef'] = ['gateway' => 'Stripe', 'ref' => $data['account']['customer'], 'payment_method' => $data['stripe_payment_method']];
                        } else {
                            try {
                                $payment['customerRef'] = $stripe->createCustomer(RentMy::$store->id, $data);

                                (new Customer('PaymentIntent', $config['config']))->addSourceForCustomerIntent($payment['customerRef']['ref'], $paymentIntent->payment_method);
                            }catch (\Throwable $throwable) {
                                RentMy::saveLogFile('alert', "storeId ==> " . RentMy::$store->id . " ==> ".$throwable->getMessage(), ['payment']);
                            }
                        }

                        break;

                    case "StripeCardPresent":
                        break;
                    case "goMerchant":
                        $config = (new Payment())->getStorePaymentConfig('goMerchant');
                        $goMerchantObj = new goMerchant($config['config']);
                        $cart_options = json_decode($data['cart']['options'], true);
                        $data['customer'] = ['first_name' => $data['first_name'],
                            'last_name' => $data['last_name'],
                            'primary_address' =>
                                [
                                    'address_line1' => $data['address_line1'],
                                    'city' => $data['city'],
                                    'state' => $data['state'],
                                    'zipcode' => $data['zipcode']
                                ]
                        ];
                        $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';

                        if (!empty($cart_options['recurring']['duration_type'])) {
                            $vault = (new Vault($config['config']))->addCard($data);
                            $data['recurring'] = [
                                'duration_type' => $cart_options['recurring']['duration_type'],
                                'vaultKey' => $vault['vaultKey'],
                                'vaultId' => $vault['vaultId']
                            ];
                            if ($cart_options['recurring']['payment_type'] == 'before_rental') {
                                $payment = $goMerchantObj->saleUsingVault($data);
                            } elseif ($cart_options['recurring']['payment_type'] == 'after_rental') {
                                $payment = $goMerchantObj->authorizeUsingVault($data);
                            }
                        } else {

                            if (!empty($data['paytype']) && ($data['paytype'] == 'goMerchant_token')) {
                                RentMy::addModel(['Customers']);
                                $vaultKey = RentMy::$Model['Customers']->getGatewayRef($data['email'], 'goMerchant');
                                $data['recurring'] = [
                                    'vaultKey' => $vaultKey,
                                    'vaultId' => $data['account']
                                ];
                                if ($data['capture'])
                                    $payment = $goMerchantObj->saleUsingVault($data);
                                else
                                    $payment = $goMerchantObj->authorizeUsingVault($data);

                            } else {
                                $vault = (new Vault($config['config']))->addCard($data);
                                $data['vaultKey'] = $vault['vaultKey'] ?? '';
                                if ($data['capture']) {
                                    $payment = $goMerchantObj->sale($data);
                                } else {
                                    $payment = $goMerchantObj->authorize($data);
                                }
                            }
                        }

                        break;
                    case "PayPal":
                        $payPalPayment = new PayPalPayment($param, $card);
                        $data['payment_method'] = 'Authorized';
                        $data['action_name'] = 'card-entry';
                        $payment = $payPalPayment->authorize();

                        break;

                    case "Authorize.Net":
                        $config = (new Payment())->getStorePaymentConfig('Authorize.Net');
                        $authorizeNetObj = new AuthorizeNet($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['payment_method'] = ($data['capture']) ? 'Captured' : 'Authorized';
                        $data['capture'] = ($data['capture']) ? true : false;
                        //$data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                        //$data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        //$data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                        //$data['capture'] = true;
                        //$data['payment_method'] = 'Captured';
                        $payment = $authorizeNetObj->charge($data);
                        break;
                    case "Empyrean":
                        $config = (new Payment())->getStorePaymentConfig('Empyrean');
                        $paymentObj = new Empyrean($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['payment_method'] = ($data['capture']) ? 'Captured' : 'Authorized';
                        $data['capture'] = ($data['capture']) ? true : false;
                        $payment = $paymentObj->sale($data);
                        break;
                    case "Square":
                        $config = (new Payment())->getStorePaymentConfig('Square');
                        $paymentObj = new Square($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['payment_method'] = ($data['capture']) ? 'Captured' : 'Authorized';
                        $data['capture'] = ($data['capture']) ? true : false;
                        $data['cardToken'] = $data['account'];
                        if (isset($data['order_id']))
                            $data['note'] = 'Order: ' . $data['order_id'];
                        $payment = $paymentObj->sale($data);
                        break;
                    case "Midtrans":
                        $config = (new Payment())->getStorePaymentConfig('Midtrans');
                        $paymentObj = new Midtrans($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['payment_method'] = ($data['capture']) ? 'Captured' : 'Authorized';
                        $data['capture'] = ($data['capture']) ? true : false;
                        $data['cardToken'] = $data['account'];
                        if (isset($data['order_id']))
                            $data['note'] = 'Order: ' . $data['order_id'];
                        $payment = $paymentObj->sale($data);
                        break;
                    case "FreedomPay":
                        $config = (new Payment())->getStorePaymentConfig('FreedomPay');
                        $data['payment_method'] = ($data['capture']) ? 'Captured' : 'Authorized';
                        $paymentObj = new FreedomPay($config['config']);
                        $_data = $data;
                        $_data['cvv'] = $_data['cvv2'];
                        $_data['card_no'] = $data['account'];
                        $_data['paymentKey'] = ['paymentKeys' => $data['paymentKeys'], 'freedom_pay_session_key' => $data['freedom_pay_session_key']];
                        $payment = $paymentObj->sale($_data);
                        break;

                    case "Transafe":
                        $config = (new Payment())->getStorePaymentConfig('Transafe');
                        $paymentObj = new Transafe($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['payment_method'] = empty($data['capture']) ? 'Authorized' : 'Captured';

                        $payment = $paymentObj->sale($data);
                        break;

                    case "ConvenuPay":
                        $type = empty($data['capture']) ? 'authorize' : 'sale';
                        $data['payment_method'] = empty($data['capture']) ? 'Authorized' : 'Captured';
                        $data['transaction_type'] = $type;
                        $config = (new Payment())->getStorePaymentConfig('ConvenuPay');
                        $paymentObj = new ConvenuPay($config['config']);
                        $payment = $paymentObj->sale($data);
                        break;
                    case "DeltaPay":
                        $data['payment_token'] = $data['account'];
                        $config = (new Payment())->getStorePaymentConfig("DeltaPay");
                        $paymentObj = new DeltaPay($config['config'], $data);
                        $payment = $paymentObj->createPayment();
                        break;

                    case 'ValorPay':
                        $data['payment_token'] = $data['account'];
                        $authOrSale = !empty($data['capture']) ? 'sale' : 'auth';
                        $config = (new Payment())->getStorePaymentConfig("ValorPay");
                        $paymentObj = new ValorPay($config['config']);
                        $payment = $paymentObj->processSale($data['payment_token'], $data['amount'], $authOrSale, $data);
                        if (!empty($payment['success'])) {
                            try {
                                if ((empty($data['paytype']) || ($data['paytype'] != 'ValorPay_token'))) {
                                    $paymentObj->createCustomer(RentMy::$store->id, $data);
                                }
                            }catch (\Exception $e) {

                            }
                        }
                        break;
                }
                if ($payment['success'] != 1 || isset($payment['redirect'])) {
                    return ['success' => false, 'message' => $payment['message']];
                } else {
                    return $payment;
                }
            } else {
                $payment = $this->onlinePayment($data);
                if (empty($payment['respstat']) || $payment['respstat'] != "A") {
                    $msg = empty($payment['resptext']) ? 'Server not found!' : $payment['resptext'];

                    return ['success' => false, 'message' => $msg];
                }
                return ['success' => true, 'data' => $payment];
            }
        }catch (\Exception $exception){
            return ['success' => false, 'message' => $exception->getMessage()];
        }
    }


    /**
     * Creating order
     * Saving customer data
     * Saving data for availability checking
     * Saving purchase type i.e. rental/buy
     */
    private function _makeOrder($data, $cart, $payment_type)
    {
        if (!empty($data['delivery']) && empty($data['pickup'])) {
            if ($cart->shipping_method == 1) {
                $data['pickup'] = $data['delivery']['id'];
            }
        }


        $customValue = $data['custom_values'] ?? [];

        if (!empty($data['special_instructions'])) {
            $customValue[] = ['field_name' => 'special_instructions', 'field_label' => 'Special Instructions', 'field_values' => $data['special_instructions']];
        }
        if (!empty($data['special_requests'])) {
            $customValue[] = ['field_name' => 'special_requests', 'field_label' => 'Special Requests', 'field_values' => $data['special_requests']];
        }
        if (!empty($data['driving_license'])) {
            $customValue[] = ['field_name' => 'driving_license', 'field_label' => 'Driving License', 'field_values' => $data['driving_license']];
        }
        $data['custom_values'] = json_encode($customValue);

        $options = !empty($cart->options) ? json_decode($cart->options, true): [];
        $options['terms_and_condition_accepted'] = !empty($data['terms_and_condition_accepted']);
        $options['intro_video_seen'] = !empty($data['intro_video_seen']);

        $order = $this->Orders->newEntity();
        $data['coupon_id'] = $cart->coupon_id;
        $data['type'] = ($data['quote']) ? 2 : 1;
        $data['total_deposit'] = $cart->deposit_amount;
        // $data['total_discount_tax'] = $cart->total_discount_tax;
        $data['tax'] = $cart->tax;
        $data['total_discount'] = $cart->total_discount;
        $data['off_amount'] = $cart->off_amount;
        $data['coupon_amount'] = $cart->coupon_amount;
        $data['sub_total'] = $cart->sub_total;
        $data['product_option_price'] = $cart->product_option_price;
        $data['delivery_tax'] = $cart->delivery_tax;
        $data['total_quantity'] = $cart->total_quantity;
        $data['shipping_method'] = !empty($data['shipping_method']) ? $data['shipping_method'] :$cart->shipping_method;
        $data['delivery_charge'] = $cart->delivery_charge;
        $data['total_price'] = $cart->sub_total + $data['tax'];
        //$data['payment_date'] = date('Y-m-d');
        $data['payment_type'] = $payment_type;
        $data['state_id'] = $data['state'] ?? null;
        $data['country_id'] = $data['country'] ?? null;
        $data['event_location'] = $data['order_source'] ?? ucfirst(RentMy::$token->source);
        $data['rent_start'] = $cart->rent_start;
        $data['rent_end'] = $cart->rent_end;
        $data['location'] = $cart->location;
        $data['options'] = json_encode($options);

        $order = $this->Orders->patchEntity($order, $data);
        if ($this->Orders->save($order)) {
            if (!empty($cart->coupon_id)) {
                RentMy::addModel(['Coupons']);
                $coupon = RentMy::$Model['Coupons']->find()->where(['id' => $cart->coupon_id])->first();
                if (!empty($coupon)) {
                    $coupon->used_limit = $coupon->used_limit + 1;
                    RentMy::$Model['Coupons']->save($coupon);
                }
            }
            $orderId = $order->id;


            // update cart tax to order
            RentMy::addModel(['TaxLookup']);
            RentMy::$Model['TaxLookup']->updateAll(['type' => 'order', 'content_id' => $order->id], ['type' => 'cart', 'content_id' => $cart->id]);


            $cart->order_id = $orderId;
            $cart->customer_id = $order->customer_id;
            $this->Carts->save($cart);
            $data['order_id'] = $orderId;


//            if (!empty($data['mobile'])) {
//                $this->loadModel('Stores');
//                $storeName = $this->Stores->get($order->store_id)->name;
//                $orderUid = $this->randomnum(3) . $order->id;
//                // $domain = 'https://' . $storeName . '.rentmy.co';
//                RentMy::getStore($order->store_id);
//                $domain = RentMy::storeDomain();
//                $link = $domain . '/order/' . $orderUid;
//                $msg = ($order->type == 2) ? 'Here is your quote. ' : 'Thank you for your order.';
//                $msg .= "Please click the link below to download your new document from " . RentMy::$store->slug . ": " . $link;
//                $this->loadComponent('Sms');
//                if ($data['order_source'] != 'POS') {
//                    $this->Sms->sendingSms($data['mobile'], $msg);
//                }
//            }
            $cartData = $this->CartItems->find('all')->where(['cart_id' => $cart->id])->where(['parent_id' => 0, 'store_id' => RentMy::$store->id])->toArray();
            $assetCounter = 0;
            $orderProductOptions = [];
            $config = (new Payment())->getStorePaymentConfig('Stripe');
            foreach ($cartData as $row) {
                $aData = array();
                $aData['product_id'] = $row->product_id;
                $aData['store_id'] = $row->store_id;
                $aData['quantity'] = $row->quantity;
                $aData['quantity_id'] = $row->quantity_id;
                $aData['price'] = $row->price;
                $aData['sales_tax'] = $row->sales_tax;
                $aData['sales_tax_price'] = $row->sales_tax_price;
                $aData['off_amount'] = $row->off_amount;
                $aData['coupon_amount'] = $row->coupon_amount + $row->automatic_coupon_amount;
                $aData['sub_total'] = $row->sub_total;
                $aData['product_option_price'] = $row->product_option_price;
                $aData['substantive_price'] = $row->substantive_price;
                $aData['total'] = $row->total;
                $aData['product_type'] = $row->product_type;
                $aData['term'] = $row->term;
                $aData['deposit_amount'] = $row->deposit_amount;
                $aData['driving_license_required'] = $row->driving_license_required;
                $aData['rental_type'] = $row->rental_type;
                $aData['rental_duration'] = $row->rental_duration;
                $aData['rent_start'] = $row->rent_start;
                $aData['rent_end'] = $row->rent_end;
                $aData['variant_chain_id'] = $row->variant_chain_id;
                $aData['location'] = $row->location;
                $aData['variant_chain_name'] = $row->variant_chain_name;
                $aData['variants_products_id'] = $row->variants_products_id;
                $aData['additional'] = $row->additional;
                $aData['options'] = $row->options;
                $item = $this->OrderItems->newEntity();
                $aData['order_id'] = $orderId;
                $item = $this->OrderItems->patchEntity($item, $aData);
                $orderItem = $this->OrderItems->save($item);
                //                Recuring payment activate
                $options = json_decode($orderItem->options, true);
                if (!empty($options['recurring']) && !empty($data['customerRef'])) {
                    $recurringData = $orderItem->toArray();

                    $recurringData = array_merge($recurringData, $options['recurring']);
                    $recurringData['customer'] = $data['customerRef']['ref'];
                    $recurringData['start_date'] = $recurringData['rent_start'];
                    $recurringData['end_date'] = $recurringData['rent_end'];
                    $recurringData['amount'] = $recurringData['price'];

                    $stripe = new Stripe('Recurring', $config['config']);
                    $response = $stripe->recurring_process($recurringData);

                    if ($response['subscription_schedule_id']) {
                        $options['recurring']['subscription'] = $response;
                        $orderItem->options = json_encode($options);
                        $itemData = [];
                        $itemData['options'] = json_encode($options);
                        $oItem = $this->OrderItems->patchEntity($orderItem, $itemData);
                        $this->OrderItems->save($oItem);
                    }
                }
                $parent_id = $item->id;
                $orderProductOptions[] = [
                    'where' => [
                        'content_type' => 'cart',
                        'content_id' => $cart->id,
                        'content_item_id' => $row['id'],
                    ],
                    'data' => [
                        'content_type' => 'order',
                        'content_id' => $orderId,
                        'content_item_id' => $parent_id,
                    ]
                ];

                // adding asset to order asset table.
                if (!empty($row->assets)) {
                    if ($this->OrderAssets->saveOrderAssets($row->assets, $order, $orderItem->id)) {
                        $assetCounter++;
                    };
                    if ($assetCounter > 0) {
                        $order->status = 5;
                        $this->Orders->save($order);
                    }
                }

                /** Package product */
                if (in_array($row->product_type, [1, 2])) {
                    if ($row->product_type == 1) {
                        /*Insert to Products Availabilities for normal products*/
                        if ($row->rental_type != 'buy') {
                            $aItem = $this->ProductsAvailabilities->newEntity();
                            $aItem->product_id = $row->product_id;
                            $aItem->order_id = $data['order_id'];
                            $aItem->rental_type = $row->rental_type;
                            $aItem->variants_products_id = $row->variants_products_id;
                            $aItem->quantity_id = $row->quantity_id;
                            $aItem->quantity = $row->quantity;
                            $aItem->location = $row->location;
                            $aItem->order_item_id = $orderItem->id;
                            $aItem->status = ($data['quote']) ? 0 : 1;
                            $aItem->start_date = $row->rent_start;
                            $aItem->end_date = $row->rent_end;
                            $aItem->actual_start_date = $row->rent_start;
                            $aItem->actual_end_date = $this->Orders->_getLeadLagTime('lag', $row->rent_end);
                            if ((RentMy::$storeConfig['rental_day'] == 'calendar') && empty(RentMy::$storeConfig['end_date_calculations'])) { // for hotel booking config
                                $aItem->actual_end_date = $this->ProductsAvailabilities->actualEndDateWithoutEndDateCalculation($row->rent_start, $row->rent_end, true);
                            }
                            $aItem->before_time = $this->Orders->_getLeadTime();
                            $aItem->after_time = $this->Orders->_getLagTime();

                            $this->ProductsAvailabilities->save($aItem);

                            RentMy::addModel(['OrderItems']);
                            RentMy::$Model['OrderItems']->updateEnduringQuantity($orderItem->id, $orderId);
                        }
                    }
                    $cartItems = $this->CartItems->find('children', ['for' => $row->id])
                        ->find('threaded')
                        ->where(['store_id' => RentMy::$store->id])
                        ->toArray();
                    foreach ($cartItems as $cartItem) {
                        $item = $this->OrderItems->newEntity();
                        $item->product_id = $cartItem->product_id;
                        $item->store_id = $cartItem->store_id;
                        $item->location = $cartItem->location;
                        $item->variants_products_id = $cartItem->variants_products_id;
                        $item->quantity_id = $cartItem->quantity_id;
                        $item->quantity = $cartItem->quantity;
                        $item->parent_id = $parent_id;
                        $item->order_id = $data['order_id'];
                        $item->product_type = $cartItem->product_type;
                        $item->rental_type = $cartItem->rental_type;
                        $item->rent_start = $cartItem->rent_start;
                        $item->rent_end = $cartItem->rent_end;
                        $item->price = $cartItem->price;
                        $item->variant_chain_id = $cartItem->variant_chain_id;
                        $item->variant_chain_name = $cartItem->variant_chain_name;

                        $order_item = $this->OrderItems->save($item);

                        /*Insert to Products Availabilities*/
                        if ($cartItem->rental_type != 'buy') {
                            $aItem = $this->ProductsAvailabilities->newEntity();
                            $aItem->product_id = $cartItem->product_id;
                            $aItem->order_id = $data['order_id'];
                            $aItem->rental_type = $cartItem->rental_type;
                            $aItem->variants_products_id = $cartItem->variants_products_id;
                            $aItem->quantity_id = $cartItem->quantity_id;
                            $aItem->quantity = $cartItem->quantity;//* $row->quantity;
                            $aItem->location = $cartItem->location;
                            $aItem->order_item_id = $item->id;//$orderItem->id;
                            $aItem->status = ($data['quote']) ? 0 : 1;
                            $aItem->start_date = $row->rent_start;
                            $aItem->end_date = $row->rent_end;
                            $aItem->actual_start_date = $row->rent_start;
                            $aItem->actual_end_date = $this->Orders->_getLeadLagTime('lag', $row->rent_end);
                            if ((RentMy::$storeConfig['rental_day'] == 'calendar') && empty(RentMy::$storeConfig['end_date_calculations'])) { // for hotel booking
                                $aItem->actual_end_date = $this->ProductsAvailabilities->actualEndDateWithoutEndDateCalculation($row->rent_start, $row->rent_end, true);
                            }
                            $aItem->before_time = $this->Orders->_getLeadTime();
                            $aItem->after_time = $this->Orders->_getLagTime();

                            $this->ProductsAvailabilities->save($aItem);

                            RentMy::addModel(['OrderItems']);
                            RentMy::$Model['OrderItems']->updateEnduringQuantity($orderItem->id, $orderId);
                        }


                    }
                } else {

                }
            }

            /* Delivey details start */
            $this->_addDeliveryDetails($data);
            /* Delivey details end */

            RentMy::addModel(['OrderProductOptions']);
            RentMy::$Model['OrderProductOptions']->migrateCartToOrder($orderProductOptions);

            RentMy::addModel(['CommissionOrders']);
            RentMy::$Model['CommissionOrders']->migrateCartToOrder(RentMy::$store->id, $cart->id, $orderId);

            /** Item log */

            if (isset($data['item_log']) && !empty($data['item_log'])) {
                $item_log = $data['item_log'];
                for ($i = 0; $i < count($item_log); $i++) {
                    $this->paymentLog($orderId, 'Products', $item_log[$i]['product_id'], null, $item_log[$i]['note'], 'discount', null, json_encode($item_log[$i]));
                }
            }

            /** ********* */

            $this->Orders->updatePurchaseType($orderId);

            /** *** Order address ****** */

            $this->loadModel('OrderAddresses');

            if (!empty($data['delivery_multi_store']))
                $data['multi_store_delivery'] = json_encode($data['delivery_multi_store']);

            $orderAddress = $this->OrderAddresses->newEntity();
            $_data = $data;
            if (isset($_data['id']))
                unset($_data['id']);

            $orderAddress = $this->OrderAddresses->patchEntity($orderAddress, $_data);
            $this->OrderAddresses->save($orderAddress);

            /*** Update additional charge */
            TableRegistry::getTableLocator()->get('OrderCharge')->updateOrderForCheckout($cart->id, $orderId);

            /** *********************** */

            /** Customer Address */

            $this->loadModel('CustomerAddresses');
            $this->CustomerAddresses->_customerAddress($data);
            if (!empty($data['view_token'])) { // pos screen customer view
                $customerView = (new PosCustomerView($data['view_token']))->save('order', ['order' => ['id' => $orderId]]);
                $data['signature'] = $customerView->signature;
            }
            if (!empty($data['signature'])) {
                $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
                file_put_contents($directoryPath . 'signature_' . $orderId . '.png', base64_decode(explode(',', $data['signature'])[1]));

                $s3 = new S3();
                $s3->upload([
                    ['path' => $directoryPath . 'signature_' . $orderId . '.png', 'dir' => 'orders/' . 'signature_' . $orderId . '.png'],
                ]);
                RentMy::deleteFile($directoryPath, 'signature_' . $orderId . '.png');
            }
            if (!empty($data['custom_values'])) {
                $customValue = json_decode($data['custom_values'], true);
                RentMy::addModel(['OrderAdditionalInfo']);
                RentMy::$Model['OrderAdditionalInfo']->add($customValue, $orderId, 'order');
            }

            //for cart store implementation

            // $OrderAdditionaInfo = $this->OrderAdditionalInfo->find()
            // ->where(['content_id' => $data['cart_token'], 'checkout_type' => 'cart'])
            // ->toArray();

            // foreach($OrderAdditionaInfo as $additionalInfo){
            //     $additionalInfo->checkout_type = 'order';
            //     $additionalInfo->content_id = $order->id;
            //     $this->OrderAdditionalInfo->save($additionalInfo);
            // }

            /** ***** ******** ***** */
        }

        return $order;
    }

    private function _payment($data = array(), $options = null, $responseText = null)
    {
        $this->add_model(array('Payments', 'Carts'));
        if (!empty($data)) {

            $pData = array();
            $pData['status'] = $data['payment_method'] == 'Unpaid' ? 1 : in_array($data['payment_method'], ['Authorized']) ? 2 : 1;
//            if (!empty($data['is_cash_register'])) {
//                  $pData['status'] = empty($data['amount']) ? 0 : 1;// additional payment will be shown in list now
//            }

            $pData['order_id'] = empty($data['order_id']) == true ? 0 : $data['order_id'];
            $pData['store_id'] = empty($data['store_id']) == true ? 0 : $data['store_id'];
            $pData['type'] = empty($data['type']) == true ? 0 : $data['type'];
            $pData['payment_amount'] = empty($data['amount']) == true ? 0 : $data['amount'];
            $pData['payment_method'] = empty($data['payment_method']) == true ? null : $data['payment_method'];
            $pData['is_captured'] = (!empty($data['payment_method']) && ($data['payment_method'] == 'Captured')) ? 1 : 0;
            $pData['payment_gateway'] = empty($data['payment_gateway_name']) == true ? null : $data['payment_gateway_name'];
            $pData['gateway_id'] = empty($data['gateway_id']) ? '' : $data['gateway_id'];
            $pData['transaction_id'] = empty($data['transaction_id']) == true ? 0 : $data['transaction_id'];
            $pData['note'] = empty($data['note']) == true ? 0 : $data['note'];
            $pData['response_text'] = empty($data['response_text']) == true ? 0 : $data['response_text'];
            $pData['terminal'] = empty($data['terminal']) == true ? '' : $data['terminal'];
            $pData['terminal_id'] = empty($data['terminal_id']) == true ? '' : $data['terminal_id'];
            $pData['content_id'] = empty($data['content_id']) == true ? '' : $data['content_id'];

            if (!empty($data['amount_tendered']))
                $pData['amount_tendered'] = $data['amount_tendered'];

            if (!empty($data['change_amount']))
                $pData['change_amount'] = $data['change_amount'];

            $payment = $this->Payments->patchEntity($this->Payments->newEntity(), $pData);
            if ($this->Payments->save($payment)) {
                if ($payment['status'] != 0){
                    RentMy::addNotificationQueue('payment_received', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'PaymentReceived', 'mobile' => $data['mobile'] ?? null, 'order_id' => $payment['order_id'], 'content_id' => $payment['order_id'], 'source' => RentMy::$token->source, 'payment_count' => 1]);
                }
                // payment log
                //$action_name = !empty($data['action_name']) ? $data['action_name'] : NULL;
                // = json_encode(array('amount' => $data['amount'], 'order_id' => $data['order_id']));
                // $response_text = empty($responseText) ? json_encode($payment) : $responseText;

                // $this->paymentLog($data['order_id'], 'Payments', $payment->id, 1, $options, $action_name, $response_text, $api_request);


                $this->_updateQuantity($data['cart_id']);
                //now clearing the cart
                $orderData = $this->Orders->get($data['order_id']);
                //$this->_getOrderStatus($data);
                $this->Payments->updateOrderPaymentStatus($data['order_id']);
                // recurring payments
                $cart_options = json_decode($data['cart']['options'], true); // recurring options.
                if (!empty($cart_options['recurring']['duration_type'])) {
                    $rental = ['before_rental', 'after_rental'];
                    if ((RentMy::$storeConfig['arb']['active']) && (in_array(RentMy::$storeConfig['arb']['store_active'], $rental))) {
                        RentMy::addModel(['OrderRecurring']);
                        $data['payment_id'] = $payment->id;
                        RentMy::$Model['OrderRecurring']->addFromCart($data['order_id'], $data);
                        if (($data['payment_gateway_name'] == 'Stripe')) {
                            RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'Recurring', 'content_id' => $payment->id, 'user_id' => RentMy::$token->cutomer_id, 'user_type' => 'customer', 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]);

                            // RentMy::addQueue('Order', ['type' => 'Recurring', 'content_id' => $payment->id, 'user_id' => RentMy::$token->cutomer_id, 'user_type' => 'customer', 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]); // add recurring payment
                        }
                    }
                }
                RentMy::addQueue('Order', ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$store->id, 'order_id' => $payment->order_id, 'source' => RentMy::$token->source]); // add order log queue
                $this->Carts->clearCart($data['cart_id']);
                if (!empty($data['email'])) {
                    if ($data['order_source'] != 'POS') {
                        RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);
                    }
                }
                $payment = array('success' => true, 'order_id' => $data['order_id']);
                $order = array('success' => true, 'data' => ['uid' => $orderData->uuid]);
                $data = array(
                    'availability' => array('success' => true),
                    'order' => $order,
                    'payment' => $payment,
                );
                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['instore']]);

                $this->apiResponse['data'] = $data;
            } else {
                // payment log
                $response_text = empty($responseText) ? json_encode($payment) : $responseText;
                $this->paymentLog($data['order_id'], 'Payments', null, 2, $options, null, $response_text);


                $payment = array('success' => false, 'message' => 'Payment submission problem.');
                $order = array('success' => true, 'data' => []);
                $data = array(
                    'availability' => array('success' => true),
                    'order' => $order,
                    'payment' => $payment,
                );
                Log::warning('-------store ID: ' . $this->parseToken->store_id . '--------- Response Data: ' . json_encode($data), ['scope' => ['instore']]);

                $this->apiResponse['data'] = $data;
            }
        } else {
            $this->apiResponse['message'] = 'Method Not Allowed';
        }

    }

    public function cartinfo()
    {
        $this->add_model(array('OrderAdditionalInfo'));
        if (!empty($data['custom_values']) && !empty($data['cart_token'])) {
            $customValue = json_decode($data['custom_values'], true);
            $customFields = $customValue;
            foreach ($customValue as $key => $cValues) {
                if ($cValues['type'] == 2) {
                    $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
                    if (!file_exists($directoryPath . $cValues['field_values'])) {
                        unset($customFields[$key]);
                        continue;
                    }
                    $s3 = new S3();
                    $s3->upload([
                        ['path' => $directoryPath . $cValues['field_values'], 'dir' => 'orders/' . $data['order_id'] . '/' . $cValues['field_values']],
                    ]);
                    RentMy::deleteFile($directoryPath, $cValues['field_values']);
                }

                $orderAdditionalInfo = $this->OrderAdditionalInfo->newEntity();
                $orderAdditionalInfo->checkout_type = 'cart';
                $orderAdditionalInfo->field_id = $cValues['id'];
                $orderAdditionalInfo->field_type = $cValues['type'];
                $$orderAdditionalInfo->content_id = $data['cart_token'];
                if ($cValues['collect_signature']) {
                    $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
                    file_put_contents($directoryPath . 'signature_' . $orderAdditionalInfo->id . $cValues['field_values'] . '.png', base64_decode(explode(',', $data['signature'])[1]));

                    $s3 = new S3();
                    $s3->upload([
                        ['path' => $directoryPath . 'signature_' . $orderAdditionalInfo->id . $cValues['field_values'] . '.png', 'dir' => 'orders/signature/'],
                    ]);
                    RentMy::deleteFile($directoryPath, 'signature_' . $orderAdditionalInfo->id . $cValues['field_values'] . '.png');
                    $cValues['signature'] = 'signature_' . $orderAdditionalInfo->id . $cValues['field_values'] . '.png';
                }
                $orderAdditionalInfo->content = json_encode($cValues);
                $this->OrderAdditionalInfo->save($orderAdditionalInfo);
            }
        }
    }

    /**
     * Update quantity only for buy order
     * @param quantity_id
     */
    private function _updateQuantity($cartId)
    {
        $this->add_model(array('VariantsProducts', 'Quantities'));
        $cartItem = $this->CartItems->find('all')->where(['cart_id' => $cartId])->toArray();
        $pCount = array();
        foreach ($cartItem as $item) {
            if (($item->rental_type == 'buy') && ($item->product_type != 2)) {
                $pCount[$item->quantity_id] = 0;
            }
        }
        foreach ($cartItem as $item) {
            if (($item->rental_type == 'buy') && ($item->product_type != 2)) {
                $pCount[$item->quantity_id] = empty($pCount[$item->quantity_id]) == true ? $item->quantity : ($pCount[$item->quantity_id] + $item->quantity);
            }
        }
        if (!empty($pCount)) {
            foreach ($pCount as $k => $v) {
                $quantity = $this->Quantities->get($k);
                if (!empty($quantity)) {
                    $newQuantity = $quantity->quantity - $v;
                    $quantity['quantity'] = !is_numeric($newQuantity) ? 0 : $newQuantity;
                    $this->Quantities->save($quantity);
                }
            }

        }
        return;
    }

    private function _addDeliveryDetails($data)
    {
        $data['delivery'] = $data['delivery'] ?? '';

        if (empty($data['delivery']) && !empty($data['pickup'])) {
            $this->loadModel('Locations');
            $location = $this->Locations->get($data['pickup']);
            $data['delivery'] = array(
                'pickup' => $location->id,
                'return_to' => $data['return_to'] ?? '',
                'charge' => 0.00,
                'tax' => 0.00,
                'charge_by' => 'in-store',
                'location' => [
                    'id' => $location->id,
                    'pickup_from' => $location->name,
                    'delivery_to' => $location->name,
                ]
            );
        } else {
            $shipping_response = [];
            if ($data['shipping_method'] == 1) { // instore pickup
                $data['delivery'] = array(
                    'charge' => 0.00,
                    'tax' => 0.00,
                    'pickup' => !empty($data['pickup']) ? $data['pickup'] : $data['delivery']['id'],
                    'return_to' => $data['return_to'] ?? '',
                    // 'charge_by' => 'in-store',
//                'location' => [
//                    'id' => $location->id,
//                    'pickup_from' => $location->name,
//                    'delivery_to' => $location->name,
//                ]
                );
            } elseif ($data['shipping_method'] == 2) {
                //$data['delivery']['location']['delivery_to'] = $this->_getShippingAddress($data);
            } elseif ($data['shipping_method'] == 4) { // create shipment on
                $response = (new Shipping())->createShipmentFromOrder($data);
                if (!empty($response['shipments'][0])) {
                    $data['delivery'] = [
                        "method" => 4,
                        'charge_by' => 'shipping',
                        "shipment_id" => $response['shipments'][0]['shipment_id'],
                        "service_code" => $response['shipments'][0]['service_code'],
                        "rate_id" => $data['delivery']['rate_id'],
                        "service_name" => $data['delivery']['service_name'],
                        "ship_date" => $data['delivery']['ship_date'],
                        "carrier_id" => $data['delivery']['carrier_id'],
                        'delivery_date' => $data['delivery']['delivery_date'],
                        'delivery_days' => $data['delivery']['delivery_days'],
                        'charge' => $data['delivery']['charge'],
                        'handling_amount' => $data['delivery']['handling_amount'],
                        'tax' => $data['delivery']['tax'],
                        'two_way' => $data['delivery']['two_way']

                    ];
                    $shipping_response = $response['shipments'][0];
                    $carrier_id = $response['shipments'][0]['carrier_id'];
                }
            } elseif ($data['shipping_method'] == 5) { // Free shipping
                $data['delivery'] = [];
                $carrier_id = '';
                $data['delivery']['charge'] = 0;
                $data['delivery']['tax'] = 0;
            } elseif ($data['shipping_method'] == 6) { // standard shipping
                $carrier_id = '';
                $data['delivery'] = [
                    "method" => 6,
                    'charge_by' => 'Standard shipping',
                    "service_name" => $data['delivery']['carrier_code'],
                    'charge' => $data['delivery']['charge'],
                    'handling_amount' => $data['delivery']['handling_amount'],
                    'tax' => $data['delivery']['tax'],
                    'two_way' => $data['delivery']['two_way']
                ];
            } elseif ($data['shipping_method'] == 7) { //flat shipping
                $carrier_id = '';
                $data['delivery'] = [
                    "method" => 7,
                    'charge_by' => 'Flat shipping',
                    "service_name" => $data['delivery']['carrier_code'],
                    'charge' => $data['delivery']['charge'],
                    'handling_amount' => $data['delivery']['handling_amount'],
                    'tax' => $data['delivery']['tax'],
                    'two_way' => $data['delivery']['two_way']
                ];
            }

        }

        $this->loadModel('DeliveryDetails');
        $deliveryDetails = $this->DeliveryDetails->newEntity();
        $deliveryData = [
            'order_id' => $data['order_id'],
            'config' => json_encode($data['delivery']),
            'shipment_response' => json_encode($shipping_response),
            'method' => $data['shipping_method'],
            'carrier' => $carrier_id,
            'charge_amount' => ($data['delivery']['charge'] + $data['delivery']['tax']),
            'store_id' => RentMy::$store->id
        ];

        if (!empty($data['rent_start']))
            $deliveryData['delivery_date'] = RentMy::toUTC($data['rent_start']);

        $deliveryDetails = $this->DeliveryDetails->patchEntity($deliveryDetails, $deliveryData);
        $this->DeliveryDetails->save($deliveryDetails);


        if ($data['shipping_method'] == 4) {
            $params['partial_shipment'] = [];
            $params['shipment_id'] = $deliveryDetails['id'];
            $params['order_id'] = $deliveryDetails['order_id'];
            RentMy::addModel(['OrderItems']);
            $items = RentMy::$Model['OrderItems']->find()->where(['order_id' => $data['order_id'], 'product_type !=' => 2])->toArray();
            if (!empty($items)) {
                foreach ($items as $item) {
                    $params['partial_shipment'][] = [
                        'order_item_id' => $item->id,
                        'package' => $data['delivery']['package_type'] ?? 'custom_package',
                        'product_id' => $item->product_id,
                        'variants_products_id' => $item->variants_products_id,
                        'quantity' => $item->quantity,
                    ];
                }
            }

            if (!empty($params['partial_shipment'])) {
                $get_weight = (new Shipping())->calculateItemsWeight(['type' => 'order', 'order_id' => $data['order_id']], $params['partial_shipment'] ?? []);
                if (!empty($get_weight['products'])) {
                    $productWeights = new Collection($get_weight['products']);
                }

                RentMy::addModel(['OrderPartialShipments']);
                foreach ($params['partial_shipment'] as $partialShipment) {
                    if (!empty($productWeights))
                        $weights = $productWeights->match(['id' => $partialShipment['product_id'], 'variants_products_id' => $partialShipment['variants_products_id']])->first();
                    $orderPartialShipment = RentMy::$Model['OrderPartialShipments']->find()->where([
                        'shipment_id' => $params['shipment_id'],
                        'order_id' => $params['order_id'],
                        // 'order_item_id' => $params['order_item_id'],
                        'product_id' => $partialShipment['product_id'],
                        'variants_products_id' => $partialShipment['variants_products_id'],
                        'package_code' => $partialShipment['package'],

                    ])->first();

                    if (!empty($orderPartialShipment)) {
                        $orderPartialShipment->quantity += $partialShipment['quantity'];
                    } else {

                        $orderPartialShipment = RentMy::$Model['OrderPartialShipments']->patchEntity(RentMy::$Model['OrderPartialShipments']->newEntity(), [
                            'shipment_id' => $params['shipment_id'],
                            'order_id' => $params['order_id'],
                            'order_item_id' => $partialShipment['order_item_id'],
                            'product_id' => $partialShipment['product_id'],
                            'variants_products_id' => $partialShipment['variants_products_id'],
                            'package_code' => $partialShipment['package'],
                            'quantity' => $partialShipment['quantity'],
                            'weight' => $weights['weight'],
                            'unit' => $weights['unit'],
                        ]);
                    }
                    RentMy::$Model['OrderPartialShipments']->save($orderPartialShipment);
                }
            }
        }


        return;
    }

    private function _getShippingAddress($dAddress)
    {
        $delivery_to = array();
        if (!empty($dAddress['shipping_address1'])) {
            $delivery_to[] = $dAddress['shipping_address1'];
        }
        if (!empty($dAddress['shipping_zipcode'])) {
            $delivery_to[] = $dAddress['shipping_zipcode'];
        }
        if (!empty($dAddress['shipping_city'])) {
            $delivery_to[] = $dAddress['shipping_city'];
        }
        return implode(', ', $delivery_to);
    }

    /**
     * Order payment for CardConnect
     * @param $data
     * @return bool
     */
    private function onlinePayment($data)
    {
        $this->loadComponent('CardConnect');
        if (
            (isset($data['account']) && !empty($data['account'])) &&
            (isset($data['expiry']) && !empty($data['expiry'])) &&
            (isset($data['cvv2']) && !empty($data['cvv2'])) &&
            (isset($data['amount']) && !empty($data['amount'])) &&
            (isset($data['currency']) && !empty($data['currency']))
        ) {
            $cardData = array(
                'account' => $data['account'],
                'expiry' => $data['expiry'],
                'cvv2' => $data['cvv2'],
                'amount' => $data['amount'] * 100,
                'currency' => $data['currency']
            );
            $fName = empty($data['first_name']) ? '' : $data['first_name'];
            $lName = empty($data['last_name']) ? '' : $data['last_name'];
            $customerData = array(
                'name' => $fName . ' ' . $lName,
                'address' => empty($data['address_line1']) ? '' : $data['address_line1'],
                'city' => empty($data['city']) ? '' : $data['city'],
                'region' => empty($data['state_id']) ? '' : $data['state_id'],
                'country' => !empty($data['country_id']) ? $data['country_id'] : NULL
            );
            $response = $this->CardConnect->paymentRequest($cardData, $customerData);
            /*only testing purposes*/
            // return json_decode('{"gsacard":"N","amount":"0.01","resptext":"Approval","acctid":"1","cvvresp":"M","respcode":"000","avsresp":"N","defaultacct":"Y","merchid":"************","token":"****************","authcode":"260305","respproc":"RPCT","profileid":"15720676655846214181","retref":"************","respstat":"A","account":"55XXXXXXXXXX0387"}',true);
            return $response;
        }

        return false;
    }

    /**
     * Conform post order , send email and send sms to customer
     */
    function confirmPOSOrder()
    {
        $this->request->allowMethod('post');
        $data = $this->request->getData();
        if (!empty($data['order_id'])) {
            RentMy::addModel(['Orders', 'Payments', 'OrderAssets', 'AssetLogs']);
            $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $data['order_id']])->first();
            if (!empty($order['email'])) {
                RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);

                //RentMy::addQueue('Order', ['type' => 'OrderEmail', 'order_id' =>$data['order_id'], 'content_id' => $data['order_id'],  'source' => RentMy::$token->source]);
            }
//            if (!empty($data['mobile'])) {
//                $smsConfig = true;
//                if (isset(RentMy::$storeConfig['checkout']['sms'])) {
//                    $smsConfig = (RentMy::$storeConfig['checkout']['sms']) ? true : false;
//                }
//                if ($smsConfig) {
//                    $this->loadComponent('Sms');
//                    $storeName = RentMy::$store->slug;
//                    $orderUid = $this->randomnum(3) . $data['order_id'];
//                    // $domain = 'https://' . $storeName . '.rentmy.co';
//                    // RentMy::getStore($order->store_id);
//                    $domain = RentMy::storeDomain();
//                    $link = $domain . '/order/' . $orderUid;
//                    $msg = "Please click the link below to download your receipt: " . $link;
//                    $this->Sms->sendingSms($data['mobile'], $msg);
//                }
//            }
            if ($data['unpaid_order']) {
                $order->status = 2;
            }

            if ($data['with_customer']) {
                $order->status = 5;
            }

            if (empty($data['with_customer']) && !empty($data['status'])) {
                $order->status = $data['status'];// status
            }

            // recheck payment status on confirm the order
            $paymentObj = RentMy::$Model['Payments']->find();
            $paidTotal = $paymentObj
                ->select([
                    'total' => $paymentObj->func()->sum('payment_amount'),
                ])
                ->where(['order_id' => $data['order_id'], 'status' => 1])
                ->first();
            $order->payment_status = RentMy::$Model['Payments']->updatePaymentStatus($order, $paidTotal['total']);

            RentMy::$Model['Orders']->save($order);
            $this->apiResponse['data'] = ['order_id' => $order->id, 'status' => $order->status];
            $this->apiResponse['message'] = 'Order confirmed successfully';
        }


    }

    /**
     * @param $order_id
     * API - GET /order/%order_id/email
     */
    public function resendEmail($order_id)
    {
        RentMy::addModel(['Orders', 'Templates']);
        $order = RentMy::$Model['Orders']->find()->where(['id' => $order_id])->first();
        //RentMy::dbg($order);
        if (empty($order)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid Order';
            return;
        }

        $options = [];
        $options['recipient'] = [
            'customer' => true
        ];
        $query = $this->request->getQuery();

        if (!empty($query['action'])){
            $options['action_type'] = $query['action'];
            RentMy::addModel(['EmailNotifications']);
            $notificationEvent = RentMy::$Model['EmailNotifications']->find()->where(['store_id' => RentMy::$store->id, 'location' => RentMy::$token->location, 'event' => $query['action']])->first();
            if (!empty($notificationEvent)){
                $template = RentMy::$Model['Templates']->find()
                    ->where(['store_id' => $order->store_id])
                    ->where(['location' => $order->location])
                    ->where(['id' => $notificationEvent->template_id])
                    ->first();

                if (!empty($template['sms_enable'])){
                    RentMy::addModel(['OrderAddresses']);
                    $orderAddress = RentMy::$Model['OrderAddresses']->find()->where(['order_id' => $order_id])->first();
                    if (!empty($orderAddress['mobile'])){
                        $options['mobile'] = $orderAddress['mobile'];
                    }

                }
            }

            if ($query['action'] == 'payment_request' && RentMy::$store->id == 570){
                $orderId = $order->id;
                $customerId = $this->request->getQuery('customer_id');
                $locationId = RentMy::$token->location;
                $storeSlug = RentMy::$store->name;
                $paymentSource = 'online_checkout';

                $queryParams = [
                    'o' => $orderId,
                    's' => $storeSlug,
                    'l' => $locationId,
                    'p' => $paymentSource
                ];

                if ($customerId) {
                    $queryParams['c'] = $customerId;
                }

                $encodedData = UrlHelper::encodeString(http_build_query($queryParams));
                $paymentDomain = 'https://payment.rentmy.co/';
                $uid = $order->uuid;
                // Generating success and cancel URLs dynamically
                $successUrl = urlencode("https://rentmasonbees.com/rentmy-order-complete?uid={$uid}");
                $cancelUrl = urlencode("https://rentmasonbees.com/rentmy-checkout?paymentStatus=failed");


                // Final Payment URL
                $paymentUrl = "{$paymentDomain}payments/{$encodedData}/?success={$successUrl}&cancel={$cancelUrl}";
                $options['payment_url'] = $paymentUrl;

            }
        }
        if (!empty($query['quote_note'])){
            $options['quote_note'] = $query['quote_note'];
        }

        if (!empty($query['quote_emails'])){
            $quoteEmails = array_map('trim', explode(',', $query['quote_emails']));
            $options['quote_emails'] = array_unique(array_merge($quoteEmails, $quoteEmails));
        }
        if (empty($template)){
            $template = RentMy::$Model['Templates']->find()
                ->where(['store_id' => $order->store_id])
                ->where(['location' => $order->location])
                ->where(['type' => 2])
                ->where(['content_type' => 1])
                ->first();
        }

        if (!empty($template))
            $options['template_id'] = $template->id;

        Email::sendOrderEmail($order_id, $options);

        RentMy::addModel(['OrderNotes']);
        try {
            RentMy::$Model['OrderNotes']->addOrderLog('ResendPaymentEmail',
                ['content_id' => $order->id, 'user_id' => RentMy::$token->id, 'order_id' => $order->id, 'source' => RentMy::$token->source, 'store_id'=> $order->store_id]
            );
        }catch (\Throwable $throwable){

        }

        $this->apiResponse['message'] = 'Email sent.';
    }

    /**
     * @param $order_id
     * API - POST /order/%order_id/email/waiver-sharing
     */

    public function sendWaiverSharingEmail($order_id)
    {
        $data = $this->request->getData();
        RentMy::addModel(['Orders']);
        $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $order_id])->first();
        if (empty($order)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid Order';
            return;
        }
        $requiredParam = ['email'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->apiResponse['error'] = Configure::read('message.missing_param');
            return;
        }

        $emails = explode(',', $data['email']);
        foreach ($emails as $email) {
            $email = trim($email);
            if (!empty($email))
                RentMy::addNotificationQueue('waiver_sharing', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'WaiverSharing', 'email' => $email, 'order_id' => $order_id, 'content_id' => $order_id, 'source' => RentMy::$token->source]);
        }
        $this->apiResponse['message'] = 'Email sent successfully';
    }


    public function changeOrderSettings($id, $type)
    {
        $data = $this->request->getData();
        RentMy::addModel(['Orders']);
        $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $id])->first();
        if ($type == 'waiver-sharing') {
            if (isset($data['waiver_sharing'])) {
                $options = !empty($order->options) ? json_decode($order->options, true) : [];
                $options['waiver_sharing'] = !empty($data['waiver_sharing']);
                $options = json_encode($options);
                $orderObj = RentMy::$Model['Orders']->patchEntity($order, ['options' => $options]);
                RentMy::$Model['Orders']->save($orderObj);
                $this->apiResponse['message'] = 'Settings changed';
                return;
            }
        }
    }

    /**
     * @param $order_id
     * API - POST /order/%order_id/email/collect-signature
     */

    public function sendCollectSignatureEmail($order_id)
    {
        $data = $this->request->getData();
        RentMy::addModel(['Orders']);
        $order = RentMy::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $order_id])->first();
        if (empty($order)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid Order';
            return;
        }
        $requiredParam = ['email'];
        if (!$this->array_keys_exist($data, $requiredParam)) {
            $this->apiResponse['error'] = Configure::read('message.missing_param');
            return;
        }

        $email = $data['email'];
        RentMy::addNotificationQueue('collect_signature', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'CollectSignature', 'email' => $email, 'order_id' => $order_id, 'content_id' => $order_id, 'source' => RentMy::$token->source]);
        $this->apiResponse['message'] = 'Email sent successfully';
    }

}
