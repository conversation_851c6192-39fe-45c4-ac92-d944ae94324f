<?php

namespace App\Controller;

use App\Controller\AppController;
use App\Lib\RentMy\RentMy;
use App\Lib\S3;
use App\Services\Product\PackageService;
use Cake\Collection\Collection;
use Cake\Event\Event;
use Cake\Http\Exception\NotFoundException;
use Cake\Http\Exception\UnauthorizedException;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Exception;
use DateTime;
use Cake\Core\Configure;
use App\Lib\ProductAsset;
use Cake\Datasource\ConnectionManager;

class ProductsController extends AppController
{
    private $productAsset;

    public function initialize()
    {
        parent::initialize();
        $this->productAsset = new ProductAsset();

    }

    /**
     * Get List of all product
     * Searching product for admin
     * @param search || boolean
     * @param avd_search || boolean
     */
    public function index()
    {
        $this->request->allowMethod('get');
        $this->add_model(array('VariantsProducts', 'Suppliers', 'Images', 'Prices', 'Stores', 'ProductPrices', 'VariantSets'));

        $data = $this->request->getQuery();

        $order = !empty($data['sort_dir']) ? $data['sort_dir'] : 'ASC';
        $order = ($order == 'DSC') ? 'DESC' : $data['sort_dir'];
        $sort = !empty($data['sort_by']) ? $data['sort_by'] : 'created';
        if ($sort == 'product_name') {
            $orderBy = 'Products.name';
        } elseif ($sort == 'buy_price') {
            $orderBy = 'Products.buy_price';
        } elseif ($sort == 'rent_price') {
            $orderBy = 'Products.rent_price';
        } elseif ($sort == 'created') {
            $orderBy = 'Products.created';
        } elseif ($sort == 'sequence_no') {
            $orderBy = 'Products.sequence_no';
        }

        $location_id = empty($data['location']) ? 0 : $data['location'];
        $pageNo = empty($data['page_no']) ? 1 : $data['page_no'];
        $limit = empty($data['limit']) ? 10 : $data['limit'];
        $offset = ($pageNo - 1) * $limit;

        $conditions = array(
            'VariantsProducts.is_last' => 1,
            'VariantsProducts.status' => 1,
            'Products.status' => 1,
            'Products.store_id' => $this->parseToken->store_id,
            'Qty.location' => $location_id,
        );
        if (isset($data['source'])) {
            if ($data['source'] == 'pos') {
                unset($conditions['Products.type']);
                // ARR wants to hide all inactive products in pos
                //$data['status'] = 1;
                //$conditions = array_merge(array('Products.status IN' => [1, 2, 6]), $conditions);
                $conditions = array_merge(array('Products.status IN' => [1]), $conditions);
            }

        }
        // Advanced Search
        if (!empty($data['avd_search'])) {
            if (isset($data['category_id']) && !empty($data['category_id'])) {
                $categories = explode(',', $data['category_id']);
                $categorySearch = ' (';
                foreach ($categories as $i => $category) {
                    if ($i == 0) {
                        $categorySearch .= "(Products.category_chain_id LIKE '%#" . $category . "#%')";
                    } else {
                        $categorySearch .= "OR (Products.category_chain_id LIKE '%#" . $category . "#%')";
                    }
                }
                $categorySearch .= ') ';
            }
            if (!empty($categorySearch)) {
                $conditions = array_merge(array($categorySearch), $conditions);
            }
            if (!empty($data['product_id'])) {
                $conditions = array_merge(array("Products.id" => $data['product_id']), $conditions);
            }
            if (!empty($data['name'])) {
                $conditions = array_merge(array("Products.name LIKE " => "%" . $data['name'] . "%"), $conditions);
            }
            if (!empty($data['status'])) {
                if (isset($conditions['Products.status']))
                    unset($conditions['Products.status']);

                $status = explode(',', $data['status']);
                $conditions = array_merge(array("Products.status IN" => $status), $conditions);
            } else {
                $conditions = array_merge(array("Products.status !=" => 5), $conditions);
            }
            if (!empty($data['supplier_name'])) {
                $conditions = array_merge(array("Products.supplier_id" => $data['supplier_name']), $conditions);
            }
            if (!empty($data['supplier_id'])) {
                $conditions = array_merge(array("Products.supply_id LIKE '%" . $data['supplier_id'] . "%'"), $conditions);
            }
            if (!empty($data['barcode'])) {
                $conditions = array_merge(array("VariantsProducts.barcode LIKE '%" . $data['barcode'] . "%'"), $conditions);
            }
            if (!empty($data['rental_type'])) {
                $conditions = array_merge(array("Products.purchase_type LIKE '%" . $data['rental_type'] . "%'"), $conditions);
            }
            if (!empty($data['type_id'])) {
                $conditions = array_merge(array("Products.type" => $data['type_id']), $conditions);
            }
            if (!empty($data['image'])) {
                $conditions = array_merge(array("Products.has_default_image" => 0), $conditions);
            }
        } // Quick searrch
        elseif (!empty($data['search'])) {
            $search = $data['search'];
            $search = strtolower($search);
            $str = "%" . $search . "%";
            $conditions = array_merge(array(
                'OR' => array(
                    "Products.id" => intval($search),
                    "Products.name LIKE " => $str,
                    "VariantsProducts.barcode LIKE " => $str,
                    "Products.client_specific_id LIKE " => $str,
                    "Products.supply_id LIKE " => $str,
                    "Products.keyword LIKE " => $str,
                    ["Products.options LIKE " => '%"meta_title":"' . $str],
                    ["Products.options LIKE " => '%"meta_keyword":"' . $str]
                )

            ), $conditions);
        } else {
            $conditions = array_merge(array("VariantsProducts.is_default" => 1), $conditions);
        }

        if (!empty($data['order_by']) && !empty($data['order'])) {
            $order = $data['order'];
            $orderBy = 'Products.' . $data['order_by'];
        }
        $attrArr = ['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'];
        $productsArr = array('Products.id', 'Products.name', 'Products.url', 'Products.sales_tax', 'Products.uuid', 'Products.status', 'Products.type', 'Products.buy_price', 'Products.rent_price', 'Products.variant_set', 'Products.client_specific_id', 'Products.variants_products_id');
        $productsObj = $this->VariantsProducts->find()
            ->select($attrArr)
            ->contain([
                'Products' => function ($q) use ($productsArr) {
                    return $q->select($productsArr);
                }
            ])
            ->contain('Qty', function ($q) use ($location_id) {
                return $q->select(['Qty.id', 'Qty.quantity']);
            })
            ->where($conditions)
            ->group('VariantsProducts.product_id');
        $products = $productsObj
            ->order([$orderBy => $order])
            ->offset($offset)
            ->limit($limit)
            ->toArray();

        if (!empty(RentMy::$storeConfig['inventory']['affiliate']['active'])){
            $productIds = array_column($products, 'product_id');
            if (empty($productIds)) {
                $productIds = [0];
            }

            RentMy::addModel(['Quantities']);
            $productQuantities = RentMy::$Model['Quantities']->find()
                ->select([
                    'product_id',
                    'total_quantity' => 'SUM(quantity)'
                ])
                ->where(['product_id IN' => $productIds])
                ->group('product_id')
                ->toArray();
            $productQuantities = array_column($productQuantities, 'total_quantity', 'product_id');

        }

        $total = $productsObj->count();
        $productIDs = [];
        foreach ($products as $i => $row) {
            $attr_ids[] = $row['product']['variants_products_id'];
            $productIDs[] = $row['product']['id'];
        }
        if (!empty($attr_ids)) {
            $images = $this->Images->image_by_store_plan('admin_product_list', ['variants_products_ids' => $attr_ids])->toArray();
            $productVariants = $this->VariantsProducts->productVariantList($productIDs);
        }
        $variantsSets = $this->VariantSets->getAll();

        foreach ($products as $row) {
            $formated = array();
            $image = '';
            foreach ($images as $mimage) {
                if ($row['product']['variants_products_id'] == $mimage['variants_products_id']) {

                    if (!empty($mimage['image_small'])) {
                        //$directoryPath = WWW_ROOT . DS . 'img' . DS . 'upload' . DS . 'products' . DS . $this->parseToken->store_id . DS . $row['product']['id'] . DS;
                        // if (file_exists($directoryPath . $mimage['image_small']))
                        if ($mimage['status'] == 2) {
                            $image = $mimage['image_small'];
                        }
                    }
                }
            }
            $tags = array();
            $formated['id'] = $row['product']['id'];
            $formated['type'] = $row['product']['type'];
            $formated['uuid'] = $row['product']['uuid'];
            $formated['store_id'] = $row['product']['store_id'];
            $formated['name'] = $row['product']['name'];
            $formated['client_specific_id'] = $row['product']['client_specific_id'];
            $formated['url'] = $row['product']['url'];
            $formated['status'] = $row['product']['status'];
            $formated['price'] = $this->ProductPrices->priceDetails($row);
            // $formated['prices'] = ['buy' => $row['products']['buy_price'], 'rent_price' => $row['product']['rent_price']];
            $formated['variant_set'] = $this->VariantSets->getProductVariantSet($row['product']['variant_set'], $variantsSets);
            $formated['variants'] = $productVariants[$row['product']['id']];
            $formated['image'] = $image;
            $formated['barcode'] = empty($row['barcode']) ? '' : $row['barcode'];
            $formated['supplier']['name'] = empty($row['products_supplier']['supplier_id']) ? '' : $row['products_supplier']['supplier_id'];
            $formated['supplier']['product_supplier_id'] = empty($row['products_supplier']['supply_id']) ? '' : $row['products_supplier']['supply_id'];
            $formated['tag'] = $tags;

            if (!empty(RentMy::$storeConfig['inventory']['affiliate']['active']) && !empty($productQuantities)){
                $formated['quantity'] = $productQuantities[$row['product']['id']];
            }else{
                $formated['quantity'] = empty($row->qty) ? 0 : $row->qty['quantity'];
            }


            $result[] = $formated;
        }
        if (!empty($result)) {
            $this->apiResponse['data'] = $result;
            $this->apiResponse['page_no'] = $pageNo;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = $total;
            $this->apiResponse['total_inventory'] = $this->Products->find()->where(['store_id' => $this->parseToken->store_id])->count();
        } else {
            $this->apiResponse['data'] = [];
            $this->apiResponse['message'] = 'No products found for this search.';
        }
    }

    public function posProducts()
    {
        $this->request->allowMethod('get');
        $data = $this->request->getQueryParams();
        RentMy::addModel(['Products', 'Images', 'ProductPrices', 'VariantsProducts', 'VariantSets', 'Assets']);

        if (!empty($data['search'])) {
            $assets = RentMy::$Model['Assets']->find()->where([
                'serial_no' => trim($data['search']),
                 'current_condition !=' => 7,
                 'current_status !=' => 3,
                'status' => 1,
                'store_id' => RentMy::$store->id,
            ])->first();

            if (!empty($assets)) {
                if (($assets->current_condition != 1) && ($assets->current_status != 1)) {
                    $this->httpStatusCode = 401;
                    $this->apiResponse['message'] = 'This asset is not available now.';
                    return;
                }
                if (!empty($data['token'])) {
                    RentMy::addModel(['Quantities', 'Carts', 'ProductsAvailabilities', 'CartItems', 'Orders', 'Users']);
                    if (!empty($data['rental_type']) && ($data['rental_type'] == 'buy' || $data['rental_type'] == 'rent')) {

                        //check free account & max 10 orders
//                        if (RentMy::$store['store_type'] == 'FREE') {
//                            if (RentMy::$storeConfig['order']['total_order'] >= 10) {
//                                $this->httpStatusCode = 401;
//                                $this->apiResponse['message'] = 'You must subscribe to continue placing orders.';
//                                return;
//                            }
//                        }

                        $cartData = [
                            'token' => $data['token'],
                            'asset_id' => $assets['id'],
                            'product_id' => $assets['product_id'],
                            'variants_products_id' => $assets['variants_products_id'],
                            'location' => $assets['location'],
                            'serial_no' => $assets['serial_no'],
                            'rental_type' => $data['rental_type'],
                            'rental_duration' => 1
                        ];
                        // get cart dates for rent
                        $cart = $this->Carts->find()->where(['uid' => $data['token']])->first();

                        if (!empty($cart['rent_start']) && !empty($cart['rent_end'])) {
                            $cartData['rent_start'] = RentMy::toStoreTimeZone($cart['rent_start'], 'Y-m-d H:i');
                            $cartData['rent_end'] = RentMy::toStoreTimeZone($cart['rent_end'], 'Y-m-d H:i');

                            $quantity = RentMy::$Model['Quantities']->find()
                                ->where(['variants_products_id' => $cartData['variants_products_id']])
                                ->where(['location' => $data['location']])
                                ->first();

                            $cartData['quantity_id'] = $quantity->id;
                            $cartData['location'] = $data['location'];
                            $cartData['store_id'] = RentMy::$token->store_id;

                            // if(empty($quantity->id)){
                            //     $this->httpStatusCode = 401;
                            //     $this->apiResponse['message'] = 'Quantity ID could not found for this location and variants combination.';
                            //     return;
                            // }

                            $assetResponse = RentMy::$Model['CartItems']->updateAssetsItem(
                                [
                                    'cart_id' => $cart->id,
                                    'product_id' => $assets['product_id'],
                                    'variants_products_id' => $assets['variants_products_id'],
                                    'quantity_id' => $quantity->id,
                                    'rental_type' => $data['rental_type'], 'asset_id' => $assets['id'],
                                    'serial_no' => $assets['serial_no']
                                ]
                            );

                            if (!$assetResponse['success']) {
                                $this->httpStatusCode = 401;
                                $this->apiResponse['message'] = 'This asset is already exists in the cart.';
                                return;
                            }
                            $cartData['assets'] = $assetResponse['data'];
                            // price
                            $cartData['price'] = 0.00;
                            if ($cartData['rental_type'] == 'buy') {
                                $price = RentMy::$Model['ProductPrices']->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $cartData['variants_products_id']])->first();
                                $cartData['price'] = $price->price;
                            } else {
                                $price = RentMy::$Model['ProductPrices']->getRentalPriceByDates($cartData['product_id'], $cartData['variants_products_id'], $cartData['rent_start'], $cartData['rent_end']);
                                $cartData['price'] = $price;
                            }

                            $cartData['quantity'] = 1; // without this code cart is not saving for assets.

                        }
                        // add to cart functions
                        $details = RentMy::$Model['Carts']->addToCart($cartData);

                        if (!empty($details)) {
                            $this->apiResponse['data'] = $details;
                            $this->apiResponse['display'] = 'cart';
                            return;
                        }
                        // else {
                        //     $this->httpStatusCode = 401;
                        //     $this->apiResponse['message'] = 'Could not add to cart this asset. Please try again.';
                        //     return;
                        // }
                        //ends
                    }
                }

                // else go below and behave as usual
                $product = RentMy::$Model['Products']->find()->where(['id' => $assets['product_id']])->first();
                $pData = [
                    'asset_id' => $assets['id'],
                    'product_id' => $assets['product_id'],
                    'variants_products_id' => $assets['variants_products_id'],
                    'location' => $assets['location'],
                    'serial_no' => $assets['serial_no'],
                    'uuid' => $product['uuid'],
                    'name' => $product['name'],
                    'type' => $product['type'],
                    'url' => $product['url'],
                    'rental_type' => $data['rental_type'],

                ];
                $this->apiResponse['data'] = $pData;
                $this->apiResponse['display'] = 'product';
                return;
            }
        }


        $sortType = !empty($data['sort_type']) ? $data['sort_type'] : 'DESC';
        $sort = !empty($data['sort']) ? $data['sort'] : 'Products.id';
        if ($sort == 'name') {
            $sort = 'Products.name';
        } elseif ($sort == 'buy_price') {
            $sort = 'Products.buy_price';
        } elseif ($sort == 'rent_price') {
            $sort = 'Products.rent_price';
        } elseif ($sort == 'created') {
            $sort = 'Products.created';
        }

        $page = isset($data['page_no']) ? $data['page_no'] : 1;
        $limit = isset($data['limit']) ? $data['limit'] : 21;
        $offset = ($page - 1) * $limit;
        RentMy::addModel(['Products']);
        $productIds = RentMy::$Model['Products']->find('list', [
            'valueField' => 'id'
        ])->where(['status IN'=>[1, 2], 'store_id'=>RentMy::$store->id])->toList();

        $productFinder = RentMy::$Model['VariantsProducts']->find()
            ->select(['id', 'variant_id', 'set_id', 'barcode', 'chain', 'product_id'])
            ->contain([
                'Products' => function ($q) {
                    return $q->select(['id', 'name', 'variants_products_id', 'uuid', 'url', 'type', 'is_tracked']);
                }
            ])
            ->contain('Qty', function ($q) {
                return $q->select(['Qty.id', 'Qty.quantity']);
            });
        $productFinder->where(['VariantsProducts.is_last' => 1,
            'Products.store_id' => RentMy::$store->id, 'Qty.location' => $data['location'],
            'Products.status IN' => [1, 2, 6]
        ]);

        if (!empty($data['search'])) {
            $strSearch = explode(" ", $data['search']);
            // $str = "%" . $data['search'] . "%";
            for ($i = 0; $i < count($strSearch); $i++) {
                $str = "%" . $strSearch[$i] . "%";
                $productFinder->where([
                    'OR' => [
                        "Products.id" => intval($data['search']),
                        "Products.name LIKE " => $str,
                        "VariantsProducts.barcode LIKE " => $str,
                        "Products.supply_id LIKE " => $str,
                        "Products.client_specific_id LIKE " => $str,
                        "Products.options LIKE " => '%"meta_keyword":"' . "%" . $data['search'] . "%"
                    ]
                ]);
            }
        }
        $productFinder->where(['VariantsProducts.product_id IN' => $productIds]);
        $productFinder->group('VariantsProducts.product_id');
        $productFinder->order([$sort => $sortType]);
        $products = $productFinder->limit($limit)->page($page)->toArray();
        $total = $productFinder->count();

        $productIds = [];
        $collection = new Collection($products);
        $default_variants_products = $collection->extract('product.variants_products_id')->toList();
        $selected_variants_products = $collection->extract('id')->toList();
        $productIds = $collection->extract('product.id')->toList();

        if (!empty($default_variants_products)) {
            $images = RentMy::$Model['Images']->find()->select(['product_id', 'variants_products_id', 'image_small'])->where(['variants_products_id IN' => $default_variants_products]);
            $productVariants = RentMy::$Model['VariantsProducts']->productVariantList($productIds);
        }
        $variantsSets = RentMy::$Model['VariantSets']->getAll();

        $allproducts = $collection->map(function ($item) use ($variantsSets, $productVariants, $images) {
            $image = $images->match(['variants_products_id' => $item['product']['variants_products_id']])->toList();
            return [
                'id' => $item['product']['id'],
                'type' => $item['product']['type'],
                'uuid' => $item['product']['uuid'],
                'store_id' => RentMy::$store->id,
                'name' => $item['product']['name'],
                'url' => $item['product']['url'],
                'price' => RentMy::$Model['ProductPrices']->priceDetails($item),
                //$formated['prices'] = ['buy' => $row['products']['buy_price'], 'rent_price' => $row['product']['rent_price']];
                'variant_set' => RentMy::$Model['VariantSets']->getProductVariantSet($item['product']['variant_set'], $variantsSets),
                'variants' => $productVariants[$item['product']['id']],
                'image' => $image[0]['image_small'],
                'quantity' => empty($item['qty']) ? 0 : $item['qty']
            ];
        })->toArray();

        if (!empty($allproducts)) {
            $this->apiResponse['data'] = $allproducts;
            $this->apiResponse['display'] = 'list';
            $this->apiResponse['page_no'] = $data['page_no'];
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = $total;
        } else {
            $this->apiResponse['data'] = [];
            $this->apiResponse['message'] = 'No products found for this search.';
            $this->httpStatusCode = 401;
        }
    }

    /**
     *  Product listing & Searching for Post
     */
    public function posProducts1()
    {
        $data = $this->request->getQueryParams();
        $location = $data['location'];

        RentMy::addModel(['Products', 'Images', 'ProductPrices', 'Categories', 'VariantsProducts', 'Assets']);

        $sortType = !empty($data['sort_type']) ? $data['sort_type'] : 'DESC';
        $sort = !empty($data['sort']) ? $data['sort'] : 'Products.id';
        if ($sort == 'name') {
            $sort = 'Products.name';
        } elseif ($sort == 'buy_price') {
            $sort = 'Products.buy_price';
        } elseif ($sort == 'rent_price') {
            $sort = 'Products.rent_price';
        } elseif ($sort == 'created') {
            $sort = 'Products.created';
        }
        $categories = [];


        if (!empty($data)) {

            $asset = RentMy::$Model['Assets']->find()->where(['serial_no' => trim($data['search']), 'store_id' => RentMy::$store->id])->first();
            if (!empty($asset)) {

                return;
            }

            $page = isset($data['page_no']) ? $data['page_no'] : 1;
            $limit = isset($data['limit']) ? $data['limit'] : 21;
            $offset = ($page - 1) * $limit;

            $productsFinder = RentMy::$Model['Products']->find()
                ->select(['Products.id', 'Products.name', 'Products.url', 'Products.uuid', 'Products.type',
                    'Products.variants_products_id', 'Products.sales_tax', 'Products.buy_price', 'Products.rent_price',
                    'Quantity.id', 'Quantity.quantity', 'Quantity.available', 'Quantity.location'])
                ->leftJoin(['Quantity' => 'quantities'],
                    [
                        'Products.variants_products_id = Quantity.variants_products_id',
                        'Quantity.location' => $location
                    ],
                    ['Quantity.location' => 'integer']
                );
//            if (!empty($data['tag_id'])) {
//                $tagIds = explode(',', $data['tag_id']);
//                $productsFinder->innerJoinWith('ProductsTags', function ($q) use ($tagIds) {
//                    return $q->where(['ProductsTags.tag_id IN' => $tagIds]);
//                });
//            }
            if (!empty($data['search'])) {
                $str = "%" . $data['search'] . "%";
                $productsFinder->where([
                    'OR' => [
                        "Products.id" => intval($data['search']),
                        "Products.name LIKE " => $str,
                        "VariantsProducts.barcode LIKE " => $str,
                        "Products.supply_id LIKE " => $str,
                        "Products.keyword LIKE " => $str,
                    ]
                ]);
            }
            $productsFinder->where(['Products.status' => 1, 'Products.store_id' => RentMy::$store->id]);

            if (!empty($data['price_min']) && !empty($data['price_max'])) {
                $priceCondition = [
                    "OR" => [
                        ['Products.rent_price >=' => $data['price_min'], 'Products.rent_price <=' => $data['price_max']],
                        ['Products.buy_price >=' => $data['price_min'], 'Products.buy_price <=' => $data['price_max']],
                    ]

                ];
                if (!empty($data['purchase_type'])) {
                    if ($data['purchase_type'] == 'buy') {
                        $priceCondition = array('Products.buy_price >=' => $data['price_min'], 'Products.buy_price <=' => $data['price_max']);
                    } elseif ($data['purchase_type'] == 'rent') {
                        $priceCondition = array('Products.rent_price >=' => $data['price_min'], 'Products.rent_price <=' => $data['price_max']);

                    }
                }
                $productsFinder->where($priceCondition);
            }
            if (!empty($data['purchase_type'])) {
                $productsFinder->where(['Products.purchase_type LIKE' => '%' . $data['purchase_type'] . '%']);
            }

            $productsFinder->order([$sort => $sortType]);
            $products = $productsFinder->limit($limit)->page($page)->toArray();
            $total = $productsFinder->count();


            $collection = new Collection($products);
            $variantProductIds = $collection->extract('variants_products_id')->toList();


            $variantProducts = RentMy::$Model['VariantsProducts']->find()
                ->select(['id', 'price_type', 'chain'])
                ->where(['id IN' => $variantProductIds]);

            $images = RentMy::$Model['Images']->find()
                ->select(['id', 'variants_products_id', 'image_small', 'image_large'])
                ->where(['variants_products_id IN' => $variantProductIds, 'status' => 2]);
            $mProducts = $collection
                ->map(function ($product) use ($variantProducts, $images) {
                    $vp = $variantProducts->match(['id' => $product['variants_products_id']])->first();
                    $image = $images->match(['variants_products_id' => $product['variants_products_id']])->toList();
                    $item = array(
                        'id' => $product['id'],
                        'type' => $product['type'],
                        'name' => $product['name'],
                        'sales_tax' => empty($product['sales_tax']) ? Configure::read('taxValue') : $product['sales_tax'],
                        'url' => $product['url'],
                        'uuid' => $product['uuid'],
                        'default_variant' => array(
                            'variants_products_id' => $product['variants_products_id'],
                            'variant_chain_id' => $vp['chain'],
                            //'variant_chain_name' => $attrName,
                            //'barcode' => $categoryProduct['barcode'],
                            'quantity' => empty($product['Quantity']) ? ['quantity' => 0] : $product['Quantity'],
                        ),
                        'prices' => RentMy::$Model['ProductPrices']->priceDetails($vp),
                        'images' => empty($image) == true ? [] : $image,
                    );
                    return $item;
                });

            $this->apiResponse['data'] = $mProducts->toArray();
            $this->apiResponse['total'] = $total;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['page'] = $page;

        }
    }

    /**
     * @desc Show products listing by category and various attributes
     * @param type $categoryId
     */
    public function userProductsListing()
    {
        $data = $this->request->getData();
        if (isset($data['category_id']) && !empty($data['category_id'])) {
            $categoryId = $data['category_id'];
            $filteringParams = $this->request->getData();
            $childCatIds = $this->Products->Categories->getChildCatIds($categoryId); //since all the products of child category should also be fetched.
            $childCatIds = array_merge($childCatIds, [$categoryId]); //mergin parent category also if any product is directly added in parent category

            $conditionsCategory = ['Categories.id IN' => $childCatIds];
            $conditionsProduct['Products.status'] = 1;
            $conditionsAttributes = [];

            $page = isset($filteringParams['page_no']) ? $filteringParams['page_no'] : 1;
            $limit = isset($filteringParams['limit']) ? $filteringParams['limit'] : 12;

            if (isset($filteringParams['price_range']) && !empty($filteringParams['price_range'])) {
                $prices = explode('-', $filteringParams['price_range']);
                if ($prices) {
                    $conditionsProduct['Products.price >='] = $prices[0];
                    if (isset($prices[1])) {
                        $conditionsProduct['Products.price <='] = $prices[1];
                    }
                }
            }
            if (isset($filteringParams['attributes']) && !empty($filteringParams['attributes'])) {
                $variants = explode(',', $filteringParams['attributes']);
                $variantIds = [];
                if ($variants) {
                    foreach ($variants as $v) {
                        $variantIds[] = $v;
                    }
                    $conditionsAttributes = ['VariantsProducts.id IN' => $variantIds];
                }
            }
            $products = $this->Products->userGetProducts($page, $limit, $conditionsCategory, $conditionsProduct, $conditionsAttributes);
            if ($products) {
                $this->apiResponse['data'] = $products;
            } else {
                $this->apiResponse['data'] = 'No products found!';
            }
        } else {
            $this->apiResponse['message'] = 'Bad Request';
        }
    }

    /**
     * Unused
     * @deprecated
     * Get products by tag, category, name in front section
     */
    public function userAllProducts()
    {
        $this->add_model(array('CategoriesProducts'));
        $data = $this->request->getData();
        $filteringParams = $this->request->getData();
        // if ((isset($data['category_id']) && !empty($data['category_id'])) || (isset($data['tag_id']) && !empty($data['tag_id']))) {
        if (!empty($filteringParams)) {
            $location = empty($filteringParams['location']) == true ? 1 : $filteringParams['location'];

            $conditionsCategory = array();
            $conditionsTag = array();
            $searchParam = true;

            if (isset($filteringParams['category_id']) && !empty($filteringParams['category_id'])) {
                $categoryId = $filteringParams['category_id'];
                $categoryId = explode(',', $categoryId);
                $conditionsCategory = ['Categories.id IN' => $categoryId];
            }
            if (isset($filteringParams['tag_id']) && !empty($filteringParams['tag_id'])) {
                $conditionsTag = ['Tags.id' => $filteringParams['tag_id']];
            }
            $conditionsProduct = array();
            $conditionsProduct = array_merge(array('Products.status' => 1), $conditionsProduct);

            if (isset($filteringParams['search']) && !empty($filteringParams['search'])) {

                $search = $filteringParams['search'];
                $conditionsProduct = array_merge(array(
                    'OR' => array(
                        "Products.id" => intval($search),
                        "Products.name LIKE '%" . $search . "%'",
                        "Products.description LIKE '%" . $search . "%'",
                    )

                ), $conditionsProduct);
                if (!empty($filteringParams['category']) && $filteringParams['category'] != 'all') {
                    $category = $filteringParams['category'];
                    $productId = $this->CategoriesProducts->getProductIdByChain($category);
                    if (!empty($productId)) {
                        $conditionsProduct = array_merge(array('Products.id IN' => $productId), $conditionsProduct);
                    } else {
                        $searchParam = false;
                        $products = [];
                    }
                }

            }

            //$conditionsProduct['Products.status'] = 1;

            $page = isset($filteringParams['page_no']) ? $filteringParams['page_no'] : 1;
            $limit = isset($filteringParams['limit']) ? $filteringParams['limit'] : 20;

            if (isset($filteringParams['price_range']) && !empty($filteringParams['price_range'])) {
                $prices = explode('-', $filteringParams['price_range']);
                if ($prices) {
                    $conditionsProduct['Products.price >='] = $prices[0];
                    if (isset($prices[1])) {
                        $conditionsProduct['Products.price <='] = $prices[1];
                    }
                }
            }
            $conditionsAttributes = [];
            if ((isset($filteringParams['price_min']) && !empty($filteringParams['price_min'])) && (isset($filteringParams['price_max']) && !empty($filteringParams['price_max']))) {
                $conditionsProduct = ['BasePrice.price BETWEEN ' . $filteringParams['price_min'] . ' AND ' . $filteringParams['price_max']];
            }

            if (isset($filteringParams['attributes']) && !empty($filteringParams['attributes'])) {

                $variants = explode(',', $filteringParams['attributes']);

                $variantIds = array();
                if ($variants) {
                    foreach ($variants as $v) {
                        if ($v == 10001 || $v == 10002) {
                            if ($v == 10001) {
                                $conditionsProduct = ['BasePrice.price >' => 0];
                            } else {
                                //$conditionsProduct = ['BasePrice.price >' => 0];
                                $conditionsProduct = array(
                                    'OR' => array(
                                        'BasePrice.hourly_price >' => 0,
                                        'BasePrice.daily_price >' => 0,
                                        'BasePrice.weekly_price	 >' => 0,
                                    )

                                );
                            }

                        } else {
                            $variantIds[] = $v;
                        }
                    }
                    if (!empty($variantIds)) {
                        $conditionsAttributes = ['VariantsProducts.variant_id IN' => $variantIds];
                    }
                }
            }
            if ($searchParam) {
                $products = $this->Products->userGetProducts($page, $limit, $conditionsCategory, $conditionsTag, $conditionsProduct, $conditionsAttributes);
            }
            $total = $this->Products->userGetProducts(1, 100000, $conditionsCategory, $conditionsTag, $conditionsProduct, $conditionsAttributes);

            if (count($products) > 0) {
                $this->apiResponse['data'] = $products;
                $this->apiResponse['total'] = count($total);
                $this->apiResponse['limit'] = $limit;
            } else {
                $this->apiResponse['data'] = [];
            }
        } else {
            $this->apiResponse['message'] = 'Bad Request';
        }
    }


    /**
     * Get product details
     * Used for getting details for admin
     * @param product_id
     * @API - /products/:id/details
     */
    public function details($id = null)
    {
        $this->request->allowMethod('get');
        $this->add_model(array('CategoriesProducts'));
        $product = $this->Products->find()->where(['id' => $id])->first();
        if (empty($product)) {
            throw new NotFoundException();
        }
        if ($this->parseToken->store_id != $product['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'You are not permitted to view details of this product';
            return;
        }
        $product = array();
        $product['basic'] = $this->Products->getProductBasic($id);
        $product['price'] = $this->Products->Prices->getPrices($id);
        $product['variant'] = $this->Products->VariantsProducts->getAttributeList($id);
        $product['related'] = $this->Products->getRelatedProducts($id);
        $product['category'] = $this->CategoriesProducts->getCategories($id);
        $this->apiResponse['data'] = $product;
    }


    /**
     * Available quantity
     * @API - /products/:id/available
     */
    public function available($id = null)
    {
        $this->request->allowMethod('get');
        $this->add_model(array('CategoriesProducts'));
        try {
            $product = $this->Products->find()->where(['id' => $id])->first();
            if ($this->parseToken->store_id != $product['store_id']) {
                $this->httpStatusCode = 401;
                $this->apiResponse['error'] = 'You are not permitted to view availablity of this product';
                return;
            }
            if (!empty($product)) {
                if (!empty($this->request->getData())) {
                    $product['available'] = $this->request->getData('available');
                    $this->Products->save($product);
                }
                $this->httpStatusCode = 200;
                $this->apiResponse['data'] = $product->available;
            } else {
                throw new Exception();
            }
        } catch (Exception $e) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Not Found';
        }
    }

    /**
     * Get product details for admin site
     */
    public function view($vp_id)
    {
        $data = $this->request->getHeaders();
        $queryParams = $this->request->getQueryParams();
        $location = empty($data['Location']) == true ? $queryParams['location'] : $data['Location'][0];
        try {
            $product = $this->Products->view($vp_id, $location, $queryParams);
            if (!empty($product)) {
                $this->apiResponse['data'] = $product;
            } else {
                throw new Exception();
            }
        } catch (Exception $e) {
            $this->apiResponse['message'] = $e->getMessage();
        }
    }

    /**
     * Product Details page
     * @param type $product_uid
     * @API /products/:uid/user/details/
     *
     */
    public function userProductDetails()
    {

        RentMy::addModel(['Products']);
        $header = $this->request->getHeaders();
        $location = !empty($header['Location'][0]) ? $header['Location'][0] : 0;

        $data = $this->request->getQueryParams();
        $data['location'] = empty($location) ? $data['location'] : $location;

        if (!empty(RentMy::$token->location)){
            $data['location'] = RentMy::$token->location;
        }

        $uId = $this->request->getParam('uid');
        $params = $this->request->getQueryParams();
        $view_type = $params['view_type'];
        if (!empty($view_type) && ($view_type == 'id')) {
            $uIdExp = explode('RentMy-', $uId);
            $productFromId = RentMy::$Model['Products']->find()->select(['uuid'])->where(['id' => $uIdExp[1], 'store_id' => RentMy::$store->id])->first();
            $uId = $productFromId->uuid;
        }elseif (!empty($view_type) && ($view_type== 'slug')){
            $productFromSlug = RentMy::$Model['Products']->find()->select(['uuid'])->where(['url' => $uId, 'store_id' => RentMy::$store->id])->first();
            $uId = $productFromSlug->uuid;
        }

        if (empty($uId)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Not Found';
            return;
        }
        $pId = $this->Products->find()->where(['uuid' => $uId, 'store_id' => RentMy::$store->id])->first();
        if (empty($pId)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Not Found';
            return;
        }

        try {
            $product = RentMy::$Model['Products']->getProduct($pId->id, $data);
        }catch (Exception $e) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $e->getMessage();
            return;
        }
        //attributes should be passed here.
        if ($product) {
            $this->apiResponse['data'] = $product;
        } else {
            throw new Exception();
        }

    }

    /**
     * @desc  related products
     * @param type $id - product id
     * @API -
     */
    public function userRelatedProducts($id = null)
    {
        try {


        $this->add_model(array('Images', 'Prices', 'RelatedProducts', 'ProductsAvailabilities', 'VariantsProducts', 'Quantities'));
        if (empty($id)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid product';
            return;
        }
        $data = $this->request->getQueryParams();
        $catProduct [] = $id;
        if (!empty($data['source']) && ($data['source'] == 'cart')) {
            if (!RentMy::$storeConfig['show_related_product_as_addon']) {
                $this->apiResponse['data'] = [];
                return;
            }
            // get cart items id
            $cartItems = TableRegistry::getTableLocator()->get('CartItems')->find()
                ->select(['CartItems.product_id'])
                ->contain(['Carts'])
                ->where(['Carts.uid' => $id])
                ->map(function ($item) {
                    return $item['product_id'];
                })
                ->toarray();
            // get cart items related product ids
            $pid = $this->RelatedProducts->find('all')
                ->select(['id', 'related_product_id'])
                ->limit(6)
                ->where(['product_id IN' => $cartItems])
                ->map(function ($item) {
                    return $item['related_product_id'];
                })
                ->toArray();
            if (empty($pid)) { // related ites are empty ,then we will get some item from cartitems cartegory
                $this->loadModel('CategoriesProducts');
                $productCategory = $this->CategoriesProducts->find('all')
                    ->select(['id', 'category_id', 'product_id'])
                    ->where(['product_id IN' => $cartItems])
                    ->first();

                $product = $this->CategoriesProducts->find('all')
                    ->select(['id', 'product_id'])
                    ->limit(3)
                    ->where(['category_id' => $productCategory['category_id'], 'product_id NOT IN' => $cartItems])
                    ->toArray();
                $pid = array();
                foreach ($product as $p) {
                    $pid[] = $p['product_id'];

                }
            }

        } else {
            $product = $this->RelatedProducts->find('all')
                ->select(['id', 'related_product_id'])
                ->limit(3)
                ->where(['product_id' => $id])
                ->toArray();
            $pid = array();
            $products = array();
            foreach ($product as $p) {
                $pid[] = $p['related_product_id'];
            }

            if (empty($pid)) {
                $this->loadModel('CategoriesProducts');
                $productCategory = $this->CategoriesProducts->find('all')
                    ->select(['id', 'category_id', 'product_id'])
                    ->where(['product_id' => $id])
                    ->first();

                $product = $this->CategoriesProducts->find('all')
                    ->select(['id', 'product_id'])
                    ->limit(3)
                    ->where(['category_id' => $productCategory['category_id'], 'product_id !=' => $id])
                    ->toArray();
                $pid = array();
                foreach ($product as $p) {
                    $pid[] = $p['product_id'];

                }
            }

        }

        if (!empty($pid)) {

            RentMy::addModel(['ProductFieldValues']);
            $productFieldValues = RentMy::$Model['ProductFieldValues']->find('list', [
                'valueField' => 'product_id'
            ])->where([
                'product_id IN' => $pid,
                'store_id' => RentMy::$store->id,
                'value IS NOT' => null,
                'value !=' => ''
            ])->toList();

            $location = RentMy::$token->location;
            $condPAttributes = array();
            $condPAttributes = array_merge(array('is_default' => 1), $condPAttributes);
            $condPAttributes = array_merge(array('is_last' => 1), $condPAttributes);
            $products = $this->Products->find('all')->select(['id', 'available', 'sales_tax', 'deposit_amount', 'store_id', 'url', 'uuid', 'name', 'type', 'Products.status', 'Products.variant_set'])
                ->contain('VariantsProducts', function ($q) use ($condPAttributes, $location) {
                    return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                        ->contain('Qty', function ($q) use ($location) {
                            return $q->select(['Qty.id', 'Qty.location', 'Qty.quantity', 'Qty.variants_products_id'])
                                ->where(['Qty.location' => $location]);
                        })
                        ->where($condPAttributes);
                })
                ->contain('Images', function ($q) use ($id) {
                    return $q->select(['id', 'product_id', 'image_large', 'image_small', 'variants_products_id', 'status']);
                })
                ->contain('ProductsAvailabilities', function ($q) {
                    return $q->select(['ProductsAvailabilities.id', 'ProductsAvailabilities.product_id', 'ProductsAvailabilities.start_date', 'ProductsAvailabilities.end_date', 'ProductsAvailabilities.quantity'])
                        ->where(['ProductsAvailabilities.order_id' => 0]);
                })
                ->where(['Products.id IN' => $pid, 'Products.status' => 1])
                ->map(function ($item) use ($productFieldValues){

                    $item['is_custom_field'] = in_array($item['id'], $productFieldValues);
                    $item['has_variant'] = !($item['variant_set'] == '[1]');
                    if ($item['type'] == 2){
                        $packageService = new PackageService([]);
                        $packageProducts = $packageService->getVariantIds($item);
                        $item['products'] = $packageProducts;
                        foreach ($packageProducts as $packageProduct) {
                            if ($packageProduct['variant_set'] !== '[1]') {
                                $hasOtherVariantSet = true;
                                break;
                            }
                        }

                        if (!empty($hasOtherVariantSet)) {
                            $item['has_variant'] = true;
                        }
                    }

                    return $item;
                })
                ->toArray();
            $products = $this->Products->getProductsPriceImage($products);
        }
        $this->apiResponse['data'] = $products;
        }catch (\Throwable $throwable){
            RentMy::dbgAndEnd($throwable->getMessage());
        }
    }

    /**
     * Add new product
     *
     * @param do_track: false
     * @param do_untrack: false
     * @param driving_license: false
     * @param is_default_weight: true
     * @param is_tracked: 0
     * @param name: "My product"
     * @param status: 2
     * @param type: 1
     * @param weight_unit: "pound"
     */
    public function add()
    {
        $this->request->allowMethod('post');
        $this->add_model(array('ProductsTags', 'Products'));
        RentMy::addModel(['Products']);
        $data = $this->request->getData();


        $product = $this->Products->newEntity();
        if (!empty($data['name'])) {
            $url = $this->seoUrl($data['name']);
            $duplicateProduct = RentMy::$Model['Products']->find()->select(['id', 'url'])->where(['store_id' => RentMy::$store->id, 'url' => $url])->first();
            if (!empty($duplicateProduct)){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = "Another product exists with this title";
                return;
            }
            $data['url'] = $url;

            $data['status'] = 1;
            $data['variant_set'] = empty($data['variant_set']) ? json_encode([1]) : json_encode($data['variant_set']);
            $data['store_id'] = $this->parseToken->store_id;
            $data['user_id'] = $this->parseToken->id;

            $salesTaxValue = $data['sales_tax'];
            unset($data['sales_tax']);

            $options['seo']['meta_title'] = $data['name'];
            $data['options'] = json_encode($options);

            $product = $this->Products->patchEntity($product, $data);
            if ($this->Products->save($product)) {
                $this->_addDefaultVariant($product);
                $this->_addWeight($product->id, $data);
                $tag = $this->request->getData('tags');
                if (!empty($tag)) {
                    foreach ($tag as $tagId) {
                        $tagData = array();
                        $tagObj = $this->ProductsTags->newEntity();
                        $tagData['product_id'] = $product->id;
                        $tagData['tag_id'] = $tagId;
                        $tagData['store_id'] = RentMy::$store->id;
                        $tagObj = $this->ProductsTags->patchEntity($tagObj, $tagData);
                        $this->ProductsTags->save($tagObj);
                    }
                }

                // saving the product custom fields values one by one by loop
                if (!empty($data['custom'])) {
                    $this->addProductCustomFields($data['custom'], $product->id);
                }
                // ends

                // Save sales tax value
                $this->Products->saveSalesTax($product->id, RentMy::$token->location, $salesTaxValue);

                $product = $this->Products->getProductBasic($product->id);
                $product['tags'] = $this->ProductsTags->getTags($product->id);
                $this->apiResponse['data'] = $product;
            } else {
                $this->apiResponse['error'] = 'Please give all required field.';
            }
        } else {
            $this->apiResponse['error'] = 'Please enter product info';
        }
    }

    /**
     * Create new product
     */
    public function firstProduct()
    {

        $this->request->allowMethod('post');
        RentMy::addModel(['ProductsTags', 'Products', 'ProductPrices', 'Quantities', 'Variants', 'VariantsProducts']);
        $data = $this->request->getData();
        $status = [];

        if (empty($data['name'])) {
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Please enter product info';
            return;
        }
        $quatities = array_filter($data['quantity'], function ($quantity){
            return (int)$quantity['quantity'] > 0;
        });

        if (empty($quatities) && ($data['is_tracked'] != 1)){
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Please enter a quantity for this product.';
            return;
        }
        if ((isset($data['price']['price']) && $data['price']['price'] == '') && (isset($data["base_price"]) && $data["base_price"] == '')) {

            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Please enter a price for this product.';
            return;
        }


        $urlTxt = !empty($data['url']) ? $data['url'] : $data['name'];
        $url = RentMy::seoUrl($urlTxt, '', RentMy::$store->id);
        $data['url'] = $url;

        $duplicateProduct = RentMy::$Model['Products']->find()->select(['id', 'url'])->where(['store_id' => RentMy::$store->id, 'url' => $url])->first();
        if (!empty($duplicateProduct)){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Another product exists with this title";
            return;
        }

        $data['status'] = 1;
//        $data['variant_set'] = empty($data['variant_set']) ? json_encode([1]) : json_encode($data['variant_set']);
        $data['ship_own_box'] = $data['ship_own_box'] == true ? 1 : 0;
        $data['variant_set'] = empty($data['variant_set']) ? json_encode([1]) : json_encode($data['variant_set']);
        $data['store_id'] = Rentmy::$store->id;
        $data['user_id'] = Rentmy::$token->id;
        $data['client_specific_id'] = trim($data['client_specific_id']);

        $options = [];
        $options['seo'] = !empty($data['seo']) ? $data['seo'] : [];
        if (empty($options['seo']['meta_title'])) {
            $options['seo']['meta_title'] = $data['name'];
        }


        if (!empty($data['exact_date_time']) && $data['exact_date_time'] == 'exact_date')
            $options['exact_date'] = true;

        if (!empty($data['exact_date_time']) && $data['exact_date_time']=='exact_time')
            $options['exact_time'] = true;

        if (!empty($data['exact_date_time']) && $data['exact_date_time']=='exact_time_with_days' && !empty($data['exact_time_ids']))
            $options['exact_time_with_days'] = true;

        $options['booking'] = !empty($data['booking']);


        $data['options'] = json_encode($options);

        $product = RentMy::$Model['Products']->newEntity();
        $product = RentMy::$Model['Products']->patchEntity($product, $data);
        $product = RentMy::$Model['Products']->save($product);
        if (!$product) {
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Please give all required field.';
            return;
        } else {

            if (!empty($options['exact_time_with_days'])){
                RentMy::addModel(['ReferenceProducts']);
                foreach ($data['exact_time_ids'] as $estId){
                    $referenceProduct = RentMy::$Model['ReferenceProducts']->newEntity();
                    $referenceProduct = RentMy::$Model['ReferenceProducts']->patchEntity($referenceProduct, [
                        'reference_id' => $estId,
                        'reference_type' => 'ExactTime',
                        'product_id' => $product['id']
                    ]);
                    RentMy::$Model['ReferenceProducts']->save($referenceProduct);
                }
            }

            if (!empty($data['sales_tax'])){
                RentMy::$Model['Products']->saveSalesTax($product['id'], RentMy::$token->location, $data['sales_tax']);
                unset($data['sales_tax']);
            }

            $status[] = 'Product Created Successfully.';
        }

        $variant_id = $this->_addDefaultVariant($product);
        if (empty($variant_id)) {
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Could not create product variant and quantity.';
            return;
        } else {
            $status[] = 'Product Variation and Quantity Created Successfully.';
        }

//        if(!empty($data['variant_set'])){
//            $set_array = json_decode($data['variant_set']);
//            $variant_array = [];
//            //gets variant id from the table
//            foreach ($set_array as $set){
//
//            }
//            //then add the returned id to this product_variants table one by one
//        }

        $this->_addWeight($product->id, $data);
        if (!empty($data['tags'])) {
            foreach ($data['tags'] as $tagId) {
                $tagData = array();
                $tagObj = RentMy::$Model['ProductsTags']->newEntity();
                $tagData['product_id'] = $product->id;
                $tagData['tag_id'] = $tagId;
                $tagData['store_id'] = RentMy::$store->id;
                $tagObj = RentMy::$Model['ProductsTags']->patchEntity($tagObj, $tagData);
                RentMy::$Model['ProductsTags']->save($tagObj);
                $status[] = 'Product Tags Created Successfully.';
            }
        }

        // saving the product custom fields values one by one by loop
        if (!empty($data['custom'])) {
            $this->addProductCustomFields($data['custom'], $product->id);
        }
        // ends

        // get variants that are saved previously
        $all_variations = RentMy::$Model['VariantsProducts']->find()->where([
            'status' => 1,
            'is_last' => 1,
            'product_id' => $product->id,
            'store_id' => Rentmy::$store->id,
        ])->toArray();

        if (empty($all_variations)) {
            $this->httpStatusCode = 401;
            $this->apiResponse['message'] = 'Could not fetch variant and quantity.';
            return;
        }

        foreach ($all_variations as $variation) {

            // image adding block
            if (!empty($data['image'])) {
                $productId = $variation->product_id;
                $storeId = Rentmy::$store->id;
                $data['variants_products_id'] = $variation->id;
                $data['product_id'] = $productId;

                // image file uplaod to s3
                $imageName = $data['image'];
                $directoryPath = WWW_ROOT . DS . 'upload' . DS. 'tmp' ;
                $s3 = new S3();

                $largeImage = $this->resizeImage($directoryPath, $imageName, 600);
                $largeWaterMark = $this->createWatermarkImage($directoryPath, $largeImage);
                $smallImage = $this->resizeImage($directoryPath, $imageName, 300);
                $smallWaterMark = $this->resizeImage($directoryPath, $largeWaterMark, 300);
                $s3->upload([
                    ['path' => $directoryPath . DS . $imageName, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $imageName],
                    ['path' => $directoryPath . DS . $largeImage, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $largeImage],
                    ['path' => $directoryPath . DS . $smallImage, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $smallImage],
                    ['path' => $directoryPath . DS . $largeWaterMark, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $largeWaterMark],
                    ['path' => $directoryPath . DS . $smallWaterMark, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $smallWaterMark],
                ]);
                RentMy::deleteFile($directoryPath, $largeImage);
                RentMy::deleteFile($directoryPath, $smallImage);
                RentMy::deleteFile($directoryPath, $imageName);
                RentMy::deleteFile($directoryPath, $largeWaterMark);
                RentMy::deleteFile($directoryPath, $smallWaterMark);


//                $largeImage = $this->resizeImage($directoryPath, $imageName, 600);
//                $smallImage = $this->resizeImage($directoryPath, $imageName, 300);
//                $s3->upload([
//                    ['path' => $directoryPath . DS . $imageName, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $imageName],
//                    ['path' => $directoryPath . DS . $largeImage, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $largeImage],
//                    ['path' => $directoryPath . DS . $smallImage, 'dir' => 'products/' . $storeId . '/' . $productId . '/' . $smallImage],
//                ]);
//                RentMy::deleteFile($directoryPath, $largeImage);
//                RentMy::deleteFile($directoryPath, $smallImage);
//                RentMy::deleteFile($directoryPath, $imageName);
                $image = array(
                    'image_original' => $imageName,
                    'image_large' => $largeImage,
                    'image_small' => $smallImage,
                    'image_large_free' => $largeWaterMark ?? '',
                    'image_small_free' => $smallWaterMark ?? ''
                );
                if ($this->_addImage($data, $image)) {
                    RentMy::$Model['Products']->saveHasImageToProduct($productId);
                    $status[] = 'Product Image Created Successfully.';
                }

                // ends
            }

            // price adding block
            if (isset($data['price']['price']) && ($data['price']['price'] != '')) {
                $label = '';
                if ($data['price']['duration_type'] == 'hourly') {
                    $label = ($data['price']['duration'] > 1) ? 'hours' : 'hour';
                } elseif ($data['price']['duration_type'] == 'daily') {
                    $label = ($data['price']['duration'] > 1) ? 'days' : 'day';
                } elseif ($data['price']['duration_type'] == 'weekly') {
                    $label = ($data['price']['duration'] > 1) ? 'weeks' : 'week';
                } elseif ($data['price']['duration_type'] == 'monthly') {
                    $label = ($data['price']['duration'] > 1) ? 'months' : 'month';
                }
                if($data['price']['price_type'] == 2){
                    $data['price']['duration_type'] ='fixed';
                }
                $priceData = [
                    'store_id' => Rentmy::$store->id,
                    'location' => Rentmy::$token->location,
                    'variants_products_id' => $variation->id,
                    'product_id' => $variation->product_id,
                    'price_type' => $data['price']['price_type'],
                    'duration_type' => $data['price']['duration_type'],
                    'duration' => $data['price']['duration'],
                    'label' => $label,
                    'price' => $data['price']['price'],
                    'flex_duration' => $data['price']['additional_duration'],
                    'max_range' => $data['price']['additional_range'],
                    'flex_price' => $data['price']['additional_price'],
                    'current' => 1
                ];
                $productPrice = RentMy::$Model['ProductPrices']->newEntity();
                $productPrice = RentMy::$Model['ProductPrices']->patchEntity($productPrice, $priceData);
                if (RentMy::$Model['ProductPrices']->save($productPrice)) {
                    $status[] = 'Product Price Created Successfully.';
                }
                if (!empty($data['price']['price_type'])) {
                    $variation->price_type = $data['price']['price_type'];
                    RentMy::$Model['VariantsProducts']->save($variation);
                }
            }

            // quantity adding block

            if (!empty($data['quantity'])) {
                foreach ($data['quantity'] as $qty) {
                    if ($data['is_tracked'] == 1) {
                        $qty['quantity'] = 0;
                    }
                    $quantityData = [
                        'store_id' => Rentmy::$store->id,
                        'variants_products_id' => $variation->id,
                        'location' => $qty['location_id'],
                        'quantity' => $qty['quantity'],
                        'product_id' => $variation->product_id,
                    ];

                    $productQuantity = RentMy::$Model['Quantities']->find()->where([
                        'store_id' => Rentmy::$store->id,
                        'variants_products_id' => $variation->id,
                        'location' => $qty['location_id'],
                        'product_id' => $variation->product_id,
                    ])->first();

                    if (empty($productQuantity)) {
                        $productQuantity = RentMy::$Model['Quantities']->newEntity();
                    }

                    $productQuantity = RentMy::$Model['Quantities']->patchEntity($productQuantity, $quantityData);

                    if (RentMy::$Model['Quantities']->save($productQuantity)) {
                        $status[] = 'Update saved.';
                    }
                }
            }

            // base price adding block
            if (!empty($data['base_price'])) {
                $data['base_price'] = (float)$data['base_price'];
                if (is_numeric($data['base_price'])) {
                    $basePriceData = [
                        'product_id' => $variation->product_id,
                        'base_price' => (float)$data['base_price'],
                    ];
                    $basePrice = RentMy::$Model['ProductPrices']->addBasePrice($variation->id, $basePriceData);

                    if (!empty($basePrice)) {
                        $status[] = 'Base Price Added Successfully.';
                    }
                }
            }

        }


        if (!empty($status)) {
            RentMy::addQueue("ProductSearch", ['type' => 'buyRent', 'product_id' => $product->id]);
            $this->apiResponse['id'] = $product->id;
            $this->apiResponse['message'] = 'Product created successfully.';
            $this->apiResponse['status'] = $status;
        }
    }

    // add custom fields to the product
    private function addProductCustomFields($custom, $product_id)
    {

        RentMy::addModel(['ProductFieldValues']);

        //get old values
        $existing  = RentMy::$Model['ProductFieldValues']->find()
        ->select(['name', 'id', 'value'])
        ->where([
            'store_id' => Rentmy::$store->id,
            'product_id' => $product_id,
        ])->toArray();

        $shouldNotDelete = [];
        foreach ($custom as $custom_field) {
            try {
                if ($custom_field['type'] == 'select'){
                    $values = explode(';', $custom_field['field_values']);
                    if (empty($values[0]))
                        continue;

                    $data = [];
                    foreach ($values as $value){
                        foreach ($existing as $item){
                            if ($item->name == $custom_field['name'] && $item->value == $value) {
                                $shouldNotDelete[] = $item->id;
                                continue(2);
                            }
                        }

                        $data[] = [
                            'product_id' => $product_id,
                            'store_id' => RentMy::$store->id,
                            'field_id' => $custom_field['id'],
                            'label' => $custom_field['label'],
                            'name' => $custom_field['name'],
                            'value' => $value,
                            'price_type'   => 1 //set as default 1=percentage
                        ];
                    }
                    $productFieldValuesEntities= RentMy::$Model['ProductFieldValues']->newEntities($data);
                    RentMy::$Model['ProductFieldValues']->saveMany($productFieldValuesEntities);
                }else {
                    $has = false;
                    foreach ($existing as $item) {
                        if ($item->name == $custom_field['name'] && $item->value == $custom_field['field_values']) {
                            $has = $item->id;
                            break;
                        }
                    }

                    if (!$has) {
                        $data = [
                            'product_id' => $product_id,
                            'store_id' => RentMy::$store->id,
                            'field_id' => $custom_field['id'],
                            'label' => $custom_field['label'],
                            'name' => $custom_field['name'],
                            'value' => $custom_field['field_values'],
                        ];
                        $productFieldValuesEntity = RentMy::$Model['ProductFieldValues']->newEntity();
                        $product_field_values = RentMy::$Model['ProductFieldValues']->patchEntity($productFieldValuesEntity, $data);

                        RentMy::$Model['ProductFieldValues']->save($product_field_values);
                    } else {
                        $shouldNotDelete[] = $has;
                    }
                }
                unset($data);
            } catch (\Exception $e) {

            }
        }

        //find what to delete
        foreach ($existing as $item)
            if (!in_array( $item->id, $shouldNotDelete))
                RentMy::$Model['ProductFieldValues']->delete($item);
    }

    /**
     * Every product must have a variant
     * The default variant is Unassign
     *
     * @param $product
     */
    public
    function _addDefaultVariant($product)
    {
        $this->add_model(array('VariantsProducts', 'Locations'));
        $location = array();

        if($product->type == 2){
            $aData = array(
                'id' => $this->parseToken->location,
                'store_id' => RentMy::$store->id,
                'quantity' => 0
            );
            $location[] = $aData;
        }

        $attrData = array(
            'variant_id' => [1],
            'barcode' => '',
            'cost' => '',
            'purchase_date' => '',
            // 'quantity' => '',
            'location' => $location,
            'default' => false,
            'product_id' => $product->id,
            'store_id' => $product->store_id,
            'set_id' => [1],
        );
        return $this->VariantsProducts->add($product->id, $attrData);
//        return;
    }

    /**
     * Edit product
     *
     * @param product_id
     */
    public function edit($id = null)
    {
        //$this->request->allowMethod('post');
        $data = $this->request->getHeaders();
        $location = empty($data['Location']) == true ? 1 : $data['Location'][0];
        $this->add_model(array('Suppliers', 'ProductsTags', 'Tags'));
        RentMy::addModel(['ExactTimes', 'Products']);
        $data = $this->request->getData();
        $product = RentMy::$Model['Products']->get($id);

        if (RentMy::$store->id != $product['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'Access denied.';
            return;
        }

        if (!empty($data)) {
            try {
                if (!empty($data)) {
                    if (!empty($data['variant_set'])) {
                        $data['variant_set'] = json_encode($data['variant_set']);
                    }
//                    if ($this->parseToken->subscription->account_type == 'FREE') {
//                        $total_products = $this->Products->find()->where(['store_id' => $this->parseToken->store_id])->count();
//                        if ($total_products > 10) {
//                            $product->status = 2;
//                        }
//                    } else if ($this->parseToken->subscription->account_type == 'Silver') {
//                        $total_products = $this->Products->find()->where(['store_id' => $this->parseToken->store_id])->count();
//                        if ($total_products > 50) {
//                            $product->status = 2;
//                        }
//                    }

                    if (!isset($data['url'])){
                        $data['url'] = $product->url;
                    }

                    $data['ship_own_box'] = ($data['ship_own_box']) ? 1 : 0;

                    $duplicateProduct = RentMy::$Model['Products']->find()->select(['id', 'url', 'type'])->where(['store_id' => RentMy::$store->id, 'url' => $data['url'], 'type' => $product->type, 'id !=' => $id])->first();
                    if (!empty($duplicateProduct)){
                        $this->httpStatusCode = 400;
                        $this->apiResponse['message'] = "Another product exists with this title";
                        return;
                    }

                    $options = !empty($product->options)?json_decode($product->options, true):[];
                    if (!empty($data['exact_date_time']) && $data['exact_date_time'] == 'exact_date'){
                        $options['exact_date'] = true;
                        if (isset($options['exact_time'])){
                            unset($options['exact_time']);
                        }
                        if (isset($options['exact_time_with_days'])){
                            unset($options['exact_time_with_days']);
                        }
                    }
                    if (!empty($data['exact_date_time']) && $data['exact_date_time']=='exact_time'){
                        $options['exact_time'] = true;
                        if (isset($options['exact_date'])){
                            unset($options['exact_date']);
                        }
                        if (isset($options['exact_time_with_days'])){
                            unset($options['exact_time_with_days']);
                        }
                    }

                    if (!empty($data['exact_date_time']) && $data['exact_date_time']=='exact_time_with_days' && !empty($data['exact_time_ids'])){
                        $options['exact_time_with_days'] = true;
                        RentMy::addModel(['ReferenceProducts']);


                        $estIdsForThisProduct = RentMy::$Model['ReferenceProducts']->find('list', [
                            'valueField' => 'reference_id'
                        ])->where([
                            'reference_type' => 'ExactTime',
                            'product_id' => $product['id'],
                        ])->innerJoinWith('ExactTimes', function ($q){
                            return $q->where(['ExactTimes.location' => RentMy::$token->location]);
                        })->toList();


                        $deleteAbleEstIds = array_diff($estIdsForThisProduct, (array)$data['exact_time_ids']);
                        if (!empty($deleteAbleEstIds)) {
                            RentMy::$Model['ReferenceProducts']->deleteAll([
                                'reference_type' => 'ExactTime',
                                'product_id' => $product['id'],
                                'reference_id IN' => $estIdsForThisProduct,
                                'reference_id NOT IN' => $deleteAbleEstIds
                            ]);
                        }


                        foreach ($data['exact_time_ids'] as $estId){
                            $referenceProduct = RentMy::$Model['ReferenceProducts']->find()->where([
                                'reference_type' => 'ExactTime',
                                'product_id' => $product['id'],
                                'reference_id' => $estId
                            ])->first();
                            if (!empty($referenceProduct))
                                continue;
                            $referenceProduct = RentMy::$Model['ReferenceProducts']->newEntity();
                            $referenceProduct = RentMy::$Model['ReferenceProducts']->patchEntity($referenceProduct, [
                                'reference_id' => $estId,
                                'reference_type' => 'ExactTime',
                                'product_id' => $product['id']
                            ]);
                            RentMy::$Model['ReferenceProducts']->save($referenceProduct);
                        }


                        if (isset($options['exact_date'])){
                            unset($options['exact_date']);
                        }
                        if (isset($options['exact_time'])){
                            unset($options['exact_time']);
                        }
                    }

                    if (empty($data['exact_date_time'])){
                        if (isset($options['exact_date'])){
                            unset($options['exact_date']);
                        }
                        if (isset($options['exact_time'])){
                            unset($options['exact_time']);
                        }
                        if (isset($options['exact_time_with_days'])){
                            unset($options['exact_time_with_days']);
                        }
                    }

                    $options['booking'] = !empty($data['booking']);
                    $options['enduring_rental'] = !empty($data['enduring_rental']);

                    if ($options['enduring_rental']){
                        RentMy::addModel(['VariantsProducts']);
                        RentMy::$Model['VariantsProducts']->updateAll(['price_type'=>3], ['store_id'=>RentMy::$store->id,'product_id'=>$id]);
                    }

                    if (!empty($data['seo'])){
                        if (isset($data['seo']['meta_title']))
                            $options["seo"]['meta_title'] = $data['seo']['meta_title'];

                        if (isset($data['seo']['meta_keyword']))
                            $options["seo"]['meta_keyword'] = $data['seo']['meta_keyword'];

                        if (isset($data['seo']['meta_description']))
                            $options["seo"]['meta_description'] = $data['seo']['meta_description'];
                    }

                    if (isset($data['excluded_fulfilment_type'])){
                        $options['excluded_fulfilment_type'] = $data['excluded_fulfilment_type'];
                    }

                    $data['options'] = json_encode($options);

                    $this->Products->saveSalesTax($id, RentMy::$token->location, $data['sales_tax']);
                    unset($data['sales_tax']);

                    $product = RentMy::$Model['Products']->patchEntity($product, $data);
                    if (RentMy::$Model['Products']->save($product)) {
                        if (isset($data['do_untrack']) && $data['do_untrack']) {
                            $this->productAsset->productUntrack($id);
                        }
                        if (isset($data['do_track']) && $data['do_track']) {
                            $this->productAsset->productTrack($id);
                        }
                        $this->_addWeight($id, $data);
                        if (!empty($this->request->getData('tags'))) {
                            $this->ProductsTags->deleteAll(['product_id' => $id]);
                            $tag = $this->request->getData('tags');
                            if (isset($tag[0]['id'])) {
                                $tags = [];
                                foreach ($tag as $t) {
                                    if (!empty($t['id'])) {
                                        $tags[] = $t['id'];
                                    }
                                }
                                $tag = $tags;
                            }
                            foreach ($tag as $tagId) {
                                $tagData = array();
                                $tagObj = $this->ProductsTags->newEntity();
                                $tagData['product_id'] = $id;
                                $tagData['tag_id'] = $tagId;
                                $tagObj = $this->ProductsTags->patchEntity($tagObj, $tagData);
                                $this->ProductsTags->save($tagObj);
                            }
                        }

                        // saving the product custom fields values one by one by loop
                        // first delete the previous one and then save again
                        if (!empty($data['custom'])) {
                            $this->addProductCustomFields($data['custom'], $product->id);
                        }
                        // ends

                        $product = RentMy::$Model['Products']->getProductBasic($id);
                        $product['tags'] = $this->ProductsTags->getTags($id);
                        $this->apiResponse['data'] = $product;

                    } else {
                        $this->apiResponse['message'] = 'Method Not Allowed';
                    }
                }
            } catch (Exception $e) {
                $this->apiResponse['message'] = 'Not Found';
            }
        } else {
            $this->add_model(array('Suppliers', 'ProductsTags'));
            try {
                $product = RentMy::$Model['Products']->getProductBasic($id);
                $product['tags'] = $this->ProductsTags->getTags($id);
                if (!empty($product)) {
                    $this->httpStatusCode = 200;
                    $this->apiResponse['data'] = $product;
                } else {
                    throw new Exception();
                }
            } catch (Exception $e) {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Not Found';
            }
        }
    }

    public function _addWeight($pId, $data)
    {
        $this->add_model(array('VariantsProducts'));
        $v_p = $this->VariantsProducts->find()
            ->where(['VariantsProducts.set_id' => 1])
            ->where(['VariantsProducts.product_id' => $pId])
            ->first();
        if (empty($v_p)){
            $variantData = [
                'product_id' => $pId,
                'variant_id' => 1,
                'store_id' => RentMy::$store->id,
                'set_id' => 1,
                'parent_id' => 0,
                'barcode' => $data['barcode'] ?? 0,
                'weight_amount' => $data['weight_unit'] ?? 0,
                'weight_unit' => $data['weight_unit'] ?? '',
                'is_last' => 1 ?? 0,
                'is_default' => 0,
                'chain' => 1,
                'status' => 0
            ];
            $v_p = $this->VariantsProducts->patchEntity($this->VariantsProducts->newEntity(), $variantData);
        }
        $v_p['weight_amount'] = empty($data['weight_amount']) ? 0 : $data['weight_amount'];
        $v_p['weight_unit'] = empty($data['weight_unit']) ? '' : $data['weight_unit'];
        if (isset($data['height']) && isset($data['width']) && isset($data['length']))
            $v_p['volume'] = $data['height'] . 'x' . $data['width'] . 'x' . $data['length'];

        $this->VariantsProducts->save($v_p);
        if (!empty($data['is_default_weight'])) {

            $v_ps = $this->VariantsProducts->find()
                ->where(['VariantsProducts.is_last' => 1])
                ->where(['VariantsProducts.product_id' => $pId])
                ->toArray();
            foreach ($v_ps as $v_p):
                $v_p['weight_amount'] = empty($data['weight_amount']) ? 0 : $data['weight_amount'];
                $v_p['weight_unit'] = empty($data['weight_unit']) ? '' : $data['weight_unit'];
                $this->VariantsProducts->save($v_p);
            endforeach;
        }
        return;
    }

    /**
     * Permanent delete a product
     *
     * @param product_id
     */
    public function deleteForever($id)
    {
        $connection = ConnectionManager::get('default');
        $connection->begin();
        try {
            RentMy::addModel(['Images', 'Products', 'VariantsProducts', 'ProductsTags', 'OrderExchange', 'OrderItems', 'OrderPartialShipments', 'OrderProductOptions']);
            // Delete Product
            $product = RentMy::$Model['Products']->find()->where(['id' => $id])->first();
            $images = RentMy::$Model['Images']->find()->where(['product_id' => $id])->toArray();
            $variants = RentMy::$Model['VariantsProducts']->find()->where(['product_id' => $id])->toArray();
            $tags = RentMy::$Model['ProductsTags']->find()->where(['product_id' => $id])->toArray();

            // Delete Product from Orders
            $orderExchanges = RentMy::$Model['OrderExchange']->find()->where(['product_id' => $id])->toArray();
            $orderItems = RentMy::$Model['OrderItems']->find()->where(['product_id' => $id])->toArray();
            $orderPartialShipments = RentMy::$Model['OrderPartialShipments']->find()->where(['product_id' => $id])->toArray();
            $orderProductOptions = RentMy::$Model['OrderProductOptions']->find()->where(['product_id' => $id])->toArray();

            if(!empty($variants)){
                foreach($variants as $variant) {
                    RentMy::$Model['VariantsProducts']->removeProductVariant($id, $variant['id']);
                }
            }
            if(!empty($tags)){
                foreach($tags as $tag) {
                    RentMy::$Model['ProductsTags']->delete($tag);
                }
            }
            if(!empty($orderExchanges)){
                foreach($orderExchanges as $orderExchange) {
                    RentMy::$Model['OrderExchange']->delete($orderExchange);
                }
            }
            if(!empty($orderItems)){
                foreach($orderItems as $orderItem) {
                    RentMy::$Model['OrderItems']->delete($orderItem);
                }
            }
            if(!empty($orderPartialShipments)){
                foreach($orderPartialShipments as $orderPartialShipment) {
                    RentMy::$Model['OrderPartialShipments']->delete($orderPartialShipment);
                }
            }
            if(!empty($orderProductOptions)){
                foreach($orderProductOptions as $orderProductOption) {
                    RentMy::$Model['OrderProductOptions']->delete($orderProductOption);
                }
            }

            if (RentMy::$Model['Products']->delete($product)) {
                RentMy::$Model['Images']->deleteProductImage($images);

                $connection->commit();
                $this->apiResponse['message'] = 'Product has been deleted successfully.';
            } else {
                $connection->rollback();
                $this->httpStatusCode = 405;
                $this->apiResponse['error'] = 'Method Not Allowed';
            }
        } catch (Exception $e) {
            $connection->rollback();
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Not Found';
        }
    }


    /**
     * delete a product
     * usually soft delete occurred
     * product delete permanently if already soft deleted
     * @param $id
     * @API - delete /products/delete
     */
    public function delete($id)
    {

        $this->request->allowMethod('delete');
        RentMy::addModel(['Products', 'OrderItems', 'Orders']);

        if (empty($id)) {
            //throw new UnauthorizedException('Invalid product id');
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'No data has found. Please enter Product id';
            return;
        }
        try {
            $product = RentMy::$Model['Products']->find()->where(['id' => $id])->first();
            if (RentMy::$store->id != $product['store_id']) {
                //throw new UnauthorizedException('You are not permitted to delete this product');
                $this->apiResponse['error'] = 'You are not permitted to delete this product';
                return;
            }
            if (!empty($product)) {
                if ($product->status == 5) {
                    $getOrders = RentMy::$Model['OrderItems']->find('all', ['fields' => 'order_id'])->contain('Orders')->where(['OrderItems.product_id' => $id, 'Orders.store_id' => RentMy::$store->id, 'Orders.location' => RentMy::$token->location])->toArray();

                    // Alert product's relation to order
                    $params = $this->request->getQueryParams();
                    if(empty($params['force']) && !empty($getOrders)) {
                        $this->httpStatusCode = 405;
                        $this->apiResponse['message'] = "This product is included with following orders";
                        $this->apiResponse['orders'] = $getOrders;
                        return;
                    }
                    // Delete permanently
                    $this->deleteForever($id);

                    // Re-calculate order
                    foreach($getOrders as $order) {
                        $order['id'] = $order['order_id'];
                        RentMy::$Model['Orders']->_updatePriceNQty($order);
                    }

                } else {
                    $product['status'] = 5;
                    if (RentMy::$Model['Products']->save($product)) {
                        $this->apiResponse['message'] = 'Update saved.';
                        return;
                    } else {
                        $this->httpStatusCode = 405;
                        $this->apiResponse['error'] = 'Product could not be deleted. Please try again.';
                        return;
                    }
                }
            } else {
                throw new NotFoundException();
            }
        } catch (\Exception $e) {
            $this->httpStatusCode = 405;
            $this->apiResponse['error'] = 'Product could not be deleted. Please try again.';
        }
    }

    /**
     * Upload Product Image
     * image upload against variant
     * if all is true image upload for all variant of the product
     *
     * @param variants_products_id
     * @param all
     * @return status
     */
    public function mediaUpload($id)
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Images', 'VariantsProducts', 'Products'));
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $baseVarentId = $data['variants_products_id'];
            foreach ($data['file'] as $file) {
                if (!empty($data) && $file['error'] == 0) {
                    $product = $this->Products->get($id);
                    $type = 'products';
                    $productId = $product->id;
                    $storeId = $product->store_id;
                    $image = $this->fileUpload($file, $type, $storeId, $productId);
                    if (!empty($image)) {
                        if ($this->_addImage($data, $image)) {
                            $this->Products->saveHasImageToProduct($data['product_id']);
                            if (!empty($data['all'])) {
                                $vp_ids = explode(',', $data['all']);
                                foreach ($vp_ids as $vp_id) {
                                    $data['variants_products_id'] = $vp_id;

                                    $oldImage = $image;
                                    $image['image_original'] = $vp_id . $image['image_original'];
                                    $image['image_large'] = $vp_id . $image['image_large'];
                                    $image['image_small'] = $vp_id . $image['image_small'];
                                    $image['image_large_free'] = $vp_id . $image['image_large_free'];
                                    $image['image_small_free'] = $vp_id . $image['image_small_free'];


                                    $from_dir = 'products/' . RentMy::$store->id . '/' . $productId . '/';
                                    $to_dir = 'products/' . RentMy::$store->id . '/' . $productId . '/';
                                    $s3 = new s3();
                                    $s3->copyDirectoryFiles($from_dir . $oldImage['image_original'], $to_dir . $image['image_original']);
                                    $s3->copyDirectoryFiles($from_dir . $oldImage['image_large'], $to_dir . $image['image_large']);
                                    $s3->copyDirectoryFiles($from_dir . $oldImage['image_small'], $to_dir . $image['image_small']);
                                    $s3->copyDirectoryFiles($from_dir . $oldImage['image_large_free'], $to_dir . $image['image_large_free']);
                                    $s3->copyDirectoryFiles($from_dir . $oldImage['image_small_free'], $to_dir . $image['image_small_free']);

                                    $this->_addImage($data, $image);
                                }
                                $data['variants_products_id'] = $baseVarentId;
                            }

                          }
                    } else {
                        $this->apiResponse['message'] = 'Not Found';
                    }
                }
            }
        }
    }

    public
    function _addImage($data, $image)
    {
        $imagesProductsObj = $this->Images->newEntity();
        $imagesProducts = array();
        $imagesProducts['product_id'] = $data['product_id'];
        $imagesProducts['store_id'] = RentMy::$store->id;
        $imagesProduct = $this->Products->Images->find('all')
            ->where(['Images.status' => 2])
            ->where(['Images.variants_products_id' => $data['variants_products_id']])
            ->first();
        if (empty($imagesProduct)) {
            $imagesProducts['status'] = 2;
        }
        $imagesProducts['image_original'] = $image['image_original'];
        $imagesProducts['image_large'] = $image['image_large'];
        $imagesProducts['image_small'] = $image['image_small'];
        $imagesProducts['image_large_free'] = $image['image_large_free'];
        $imagesProducts['image_small_free'] = $image['image_small_free'];
        $imagesProducts['variants_products_id'] = $data['variants_products_id'];
        $imagesProductsObj = $this->Images->patchEntity($imagesProductsObj, $imagesProducts);
        if ($this->Images->save($imagesProductsObj)) {
            return true;
        }
        return false;
    }


    /**
     * Get list of images of a product against variant
     *
     * @param variants_products_id
     * @param product_id
     */
    public function images($id, $a_id = null)
    {
        $this->add_model(array('VariantsProducts'));
        RentMy::addModel(['Images']);
        try {
            if ($a_id != null) {
                $imagesProduct = RentMy::$Model['Images']->image_by_store_plan('view', ['variants_products_id' => $a_id])->toArray();
                if (!empty($imagesProduct)) {
                    $this->apiResponse['data'] = $imagesProduct;
                } else {
                    $this->apiResponse['data'] = [];
                }
            } else {
                $this->apiResponse['data'] = [];
            }
        } catch (Exception $e) {
            $this->apiResponse['data'] = [];
        }
    }

    /**
     * make an image as feature for a product
     * a product may has only one feature image
     */
    public
    function featureImage($id)
    {
        $this->add_model(array('Images', 'VariantsProducts'));
        if (!empty($this->request->getData('image_id'))) {
            $imageId = $this->request->getData('image_id');
            $image = $this->Images->get($imageId);
            $check = $this->Images->find()
                ->where(['variants_products_id' => $image->variants_products_id])
                ->where(['status' => 2])
                ->first();
            if ($check) {
                $check['status'] = 1;
                $this->Images->save($check);
            }
            $image['status'] = 2;
            if (!$this->Images->save($image)) {
                $this->apiResponse['message'] = 'Method Not Allowed';
            }
        } else {
            $attr_product = $this->VariantsProducts->find()
                ->where(['is_default' => 1])
                ->where(['product_id' => $id])
                ->first();

            $check = $this->Images->find()->select(['id', 'image_large'])->where(['product_id' => $id])->where(['status' => 2])->where(['variants_products_id' => $attr_product->id])->first();
            if ($check) {
                $this->apiResponse['data'] = $check;
            } else {
                $this->apiResponse['data'] = null;
            }
        }
    }

    /**
     * Product summary
     *
     * @param $id
     */
    public
    function summary($id)
    {
        $this->request->allowMethod('get');
        $this->add_model(array('Products'));
        $product = $this->Products->find()->where(['id' => $id])->first();
        if (empty($product)) {
            throw new NotFoundException();
        }
        if ($this->parseToken->store_id != $product['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'You are not permitted to view summary of this product';
            return;
        }
        if ($id != null) {
            $totalImage = $this->Products->Images->find()->where(['product_id' => $id])->toArray();
            $stock = $this->Products->Quantities->find()->select(['total' => 'SUM(quantity)'])->where(['product_id' => $id])->first();
            $cartTtems = $this->Products->CartItems->find()->select(['total' => 'SUM(quantity)'])->where(['product_id' => $id])->first();
            $orderItems = $this->Products->OrderItems->find()->select(['total' => 'SUM(quantity)'])->where(['product_id' => $id])->first();
            $data = array(
                'total_image' => count($totalImage),
                'total_stock' => $stock->total,
                'total_cart' => $cartTtems->total,
                'total_order' => $orderItems->total,
            );
            $this->apiResponse['data'] = $data;
        }
    }

    /**
     * Get list of category of a product
     */
    public
    function category($id)
    {
        $this->request->allowMethod('get');
        $this->add_model(array('CategoriesProducts', 'Categories'));
        $product = $this->Products->get($id);
        if ($this->parseToken->store_id != $product['store_id']) {
            $this->httpStatusCode = 400;
            $this->apiResponse['error'] = 'You are not permitted to view categories of this product';
            return;
        }
        try {
            $category = $this->CategoriesProducts->getCategories($id);
            if (!empty($category)) {
                $this->apiResponse['data'] = $category;
            } else {
                $this->apiResponse['data'] = [];
            }
        } catch (Exception $e) {
            $this->apiResponse['message'] = $e->getMessage();
        }
    }

    /**
     * Add new Category for a Product
     *
     * @param product_id
     * @param category_chain
     * @param category_chain
     */
    public
    function categoryAdd($productId)
    {
        $this->add_model(array('CategoriesProducts', 'Categories'));
        $status = false;
        if (!empty($this->request->getData())) {
            //pr($this->request->getData());exit;
            $categoryId = $this->request->getData('category_id');
            $categoryChain = $this->request->getData('category_chain');
            $categoryChainObj = explode('>', $categoryChain);
            $catIds = array();
            $catNames = array();
            $parents = $this->Categories->find('path', ['for' => $categoryId]);
            foreach ($parents as $parent) {
                $catIds[] = $parent['id'];
                $catNames[] = $parent['name'];
            }
//            foreach ($categoryChainObj as $k => $v) {
//                $categories = $this->Categories->find()->where(['name' => $v,'store_id'=> $this->parseToken->store_id])->first();
//                $catIds[] = $categories['id'];
//                $catNames[] = $categories['name'];
//            }
            $catChainName = implode('##', $catNames);
            $catChainId = implode('##', $catIds);
            $check = $this->CategoriesProducts->find()->where(["CategoriesProducts.category_chain_id LIKE '%#" . $catChainId . "#%'"])->where(['product_id' => $productId])->first();
            if (!$check) {
                $categoryProductObj = $this->CategoriesProducts->newEntity();
                $categoryProduct['product_id'] = $productId;
                $categoryProduct['category_id'] = $categoryId;
                $categoryProduct['store_id'] = RentMy::$store->id;
                $categoryProduct['category_chain'] = '#' . $catChainName . '#';
                $categoryProduct['category_chain_id'] = '#' . $catChainId . '#';
                $categoryProductObj = $this->CategoriesProducts->patchEntity($categoryProductObj, $categoryProduct);
                if ($result = $this->CategoriesProducts->save($categoryProductObj)) {
                    $status = true;
                }
            }
        }
        if ($status) {
            $this->addCatChainToProduct($productId);
            $this->apiResponse['data'] = $this->CategoriesProducts->getCategory($result->id);
        } else {
            $this->httpStatusCode = 405;
            $this->apiResponse['error'] = 'Method Not Allowed';
        }
    }

    private
    function addCatChainToProduct($id)
    {
        $this->loadModel('CategoriesProducts');
        $product = $this->Products->get($id);
        $pcs = $this->CategoriesProducts->find()->where(['product_id' => $id])->toArray();
        $cat = array();
        foreach ($pcs as $j => $pc) {
            $cat[] = $pc['category_chain_id'];
        }
        $product->category_chain_id = implode(',', $cat);
        $this->Products->save($product);
    }

    /**
     * DEPRECATED
     * Save product's prices
     */
    public
    function price($productId, $a_id = null)
    {
        $this->request->allowMethod('post');
        $this->add_model(array('Variants', 'Prices', 'VariantsProducts'));
        $data = $this->request->getData();
        //pr($data);exit;
        if (!empty($data)) {
            $save = 0;
            if ($a_id != null) {
                if ($this->Prices->_addPrice($data, $productId, $a_id)) {
                    $save = 1;
                    if (isset($data['all'])) {
                        foreach ($data['all'] as $vp_id) {
                            $check = $this->Prices->find()->where(['variants_products_id' => $vp_id])->first();
                            if ($check) {
                                $data['id'] = $check->id;
                            }
                            $this->Prices->_addPrice($data, $productId, $vp_id);
                        }
                    }
                }
            }
            if ($save) {
                //
                $this->ProductPrices->updatePriceToProduct($productId);
                $this->apiResponse['data'] = $this->Products->Prices->getPricesByAttributes($a_id);
            } else {
                $this->apiResponse['error'] = 'Method Not Allowed!';
            }
        } else {
            $this->apiResponse['error'] = 'Method Not Allowed!';
        }
    }

    /** @param $productId
     * @param variant_id
     * @deprecated
     * Get price data of a product's variant
     *
     */
    public function getPrice($productId, $a_id = null)
    {
        $this->add_model(array('ProductPrices', 'VariantsProducts'));
        $vp = $this->VariantsProducts->get($a_id);
        $this->request->allowMethod('get');
        $prices = $this->ProductPrices->priceDetails($vp);
        //$prices = $this->Products->Prices->getPricesByAttributes($a_id);
        if (!empty($prices)) {
            $this->apiResponse['data'] = $prices;
        } else {
            $this->apiResponse['data'] = [];
        }
    }


    /**
     * Saving price data of a product
     * Also saving purchase type to product base on price type rent/buy price
     *
     * @param $productId
     * @param $vp_id
     */
    public function productPrice($productId, $vp_id)
    {
        $this->add_model(array('Variants', 'Products', 'ProductsSettings', 'VariantsProducts', 'ProductPrices'));
        if (!empty($this->request->getData())) {
            $data = $this->request->getData();
            $priceInfo = $this->Products->get($productId);
            //$priceInfo['sales_tax'] = $data['sales_tax'];
            // $priceInfo['deposit_amount'] = $data['deposit_amount'];
            // $priceInfo['ldw_tax'] = $data['ldw_tax'];
            // if ($this->Products->save($priceInfo)) {
            if (isset($data['base_price'])) {
                $this->ProductPrices->addBasePrice($productId, $data['base_price'], $vp_id, $this->parseToken->store_id);
                $this->ProductPrices->updatePriceToProduct($productId);
            }
            $data['id'] = $vp_id;
            //if ($this->_addPurchaseInfo($data)) {
            if (!empty($data['all'])) {
                foreach ($data['all'] as $vpId) {
                    $data['id'] = $vpId;
                    $this->_addPurchaseInfo($data);
                }
            }
            $variant = $this->VariantsProducts->find('all')
                ->select(['id', 'purchase_date', 'cost'])
                ->where(['id' => $vp_id])
                ->first();
            $product = $this->Products->find('all')
                ->select(['id', 'sales_tax', 'deposit_amount', 'ldw_tax'])
                ->where(['Products.id' => $productId])
                ->first();
            $basePrice = $this->ProductPrices->find()->where(['duration_type' => 'base'])->where(['variants_products_id' => $vp_id])->first();
            $product->base_price = !empty($basePrice) ? $basePrice->price : 0;
            //$productsetting = $this->ProductsSettings->find()->select(['value'])->where(['product_id' => $productId])->first();
            $adata = array();
            $adata['product'] = empty($product) == true ? null : $product;
            $adata['variant'] = empty($variant) == true ? null : $variant;
            //$adata['deposit_tax'] = empty($productsetting) == true ? null : $productsetting;
            $this->apiResponse['data'] = $adata;
            //  }
            //} else {
            //    $this->apiResponse['message'] = 'Method Not Allowed!';
            //}
        } else {
            $this->apiResponse['message'] = 'Method Not Allowed!';
        }
    }

    public function _addPurchaseInfo($data)
    {
        $a_Info = $this->VariantsProducts->get($data['id']);
        $a_Info['cost'] = $data['cost'];
        $a_Info['purchase_date'] = empty($data['purchase_date']) == true ? '' : strtotime($data['purchase_date']);
        $this->VariantsProducts->save($a_Info);
        $psetting = $this->ProductsSettings->find()->where(['product_id' => $a_Info['product_id']])->first();
        if (empty($psetting)) {
            $psetting = $this->ProductsSettings->newEntity();
        }
        $psetting['product_id'] = $a_Info['product_id'];
        $psetting['p_key'] = "deposite_tax";
        $psetting['value'] = $data['deposit_tax'] == 0 ? 'false' : 'true';
        $this->ProductsSettings->save($psetting);
        return true;
    }


    /**
     * @param $productId
     * @param $a_id
     * @deprecated
     */
    public function productPrice_old($productId, $a_id)
    {
        $this->add_model(array('VariantsProducts'));
        if (!empty($this->request->getData())) {
            $data = $this->request->getData();
            $priceInfo = $this->VariantsProducts->get($a_id);
            $priceInfo = $this->VariantsProducts->patchEntity($priceInfo, $data);
            if ($this->VariantsProducts->save($priceInfo)) {
                $product = $this->VariantsProducts->find('all')
                    ->select(['id', 'sales_tax', 'deposit_amount', 'deposit_tax', 'cost'])
                    ->where(['id' => $a_id])
                    ->first();
                $this->apiResponse['data'] = $product;
            } else {
                $this->apiResponse['message'] = 'Method Not Allowed!';
            }
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    /**
     * Get all variants of product
     *
     * Saving variants data of a product
     *
     * @param weight_amount
     * @param weight_unit
     * @param variant_id: array
     * @param barcode
     *
     * products/:id/variant
     */
    public function attribute($id)
    {
        $this->add_model(array('Variants', 'VariantsProducts', 'VariantSets', 'Products'));

        if (!empty($this->request->getData('variant_id'))) {
            $data = $this->request->getData();

            $data['store_id'] = $this->parseToken->store_id;
            $status = $this->VariantsProducts->add($id, $data);
            if ($status) {
                // if aggregate quantities is active add queue job
//                if(RentMy::$storeConfig['online_store']['aggregate_quantities']) {
//                    // create Queue Job
//                    $job = RentMy::$Model['Queue.QueuedJobs']->createJob('Quantity',
//                        ['type' => 'aggregate_quantities', 'store_id' => RentMy::$store->id, 'product_id' => $data['product_id'], 'variants_products_id' => $status],
//                        ['reference' => RentMy::$store->id]);
//
//                }

                $this->apiResponse['data'] = array('status' => true);
            } else {
                $message = 'Bad Request';
                $this->apiResponse['data'] = array('status' => false, 'message' => $message);
            }
        } else {
            try {
                $variants = $this->VariantsProducts->getAttributeChain($id);
                if (!empty($variants)) {
                    $this->apiResponse['data'] = $variants;
                    $this->apiResponse['is_tracked'] = $this->Products->get($id)->is_tracked;
                } else {
                    $this->apiResponse['data'] = [];
                    $this->apiResponse['is_tracked'] = $this->Products->get($id)->is_tracked;
                }
            } catch (Exception $e) {
                $this->httpStatusCode = 400;
                $this->apiResponse['data'] = [];
            }
        }
    }


    public function batchAttribute($id)
    {
        $connection = ConnectionManager::get('default');
        $connection->begin();
        try {
            RentMy::addModel(['Variants', 'VariantsProducts', 'VariantSets', 'Products']);
            $data = $this->request->getData();
            if (!empty($data['variants_products'])){
                foreach ($data['variants_products'] as $variant_product){
                    if (!empty($variant_product['variant_id'])){
                        $variant_product['store_id'] = RentMy::$store->id;
                        if (!empty($variant_product['ids'])){
                            RentMy::$Model['VariantsProducts']->edit($variant_product);
                        }else{
                            RentMy::$Model['VariantsProducts']->add($id, $variant_product);
                        }

                    }
                }
            }
            $connection->commit();
            $this->apiResponse['message'] = "Update Successful";
        }catch (\Throwable $exception){
            $connection->rollback();
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
        }
    }
    /**
     * Get all variants of product
     *
     * Update variants data of a product
     *
     * @param weight_amount
     * @param weight_unit
     * @param variant_id: array
     * @param barcode
     *
     * API - POST products/:id/variant/edit
     */
    public function attributeEdit($id)
    {
        $this->add_model(array('Variants', 'VariantsProducts', 'VariantSets'));
        if (!empty($this->request->getData('variant_id'))) {
            $data = $this->request->getData();
            $data['store_id'] = $this->parseToken->store_id;
            $status = $this->VariantsProducts->edit($data);
            if ($status) {
                $this->apiResponse['data'] = array('status' => true);
            } else {
                $message = 'Bad Request';
                $this->apiResponse['data'] = array('status' => false, 'message' => $message);
            }
        } else {
            try {
                $variants = $this->VariantsProducts->getAttributeChain($id);
                if (!empty($variants)) {
                    $this->apiResponse['data'] = $variants;
                } else {
                    $this->apiResponse['data'] = [];
                }
            } catch (Exception $e) {
                $this->apiResponse['data'] = [];
            }
        }
    }

    /**
     * Delete variants of product
     * API - POST products/:product_id/variant/delete
     * @param $id
     */
    public function deleteAttribute($id)
    {

        $variantProductsIds = $this->request->getData('ids');
        if (empty($variantProductsIds)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Variant product ids missing';
            return;
        }
        try {
            RentMy::addModel(['VariantsProducts']);
            $variants_products_id = $variantProductsIds[count($variantProductsIds) - 1];
            $product_id = $id;
            $response = RentMy::$Model['VariantsProducts']->removeProductVariant($product_id, $variants_products_id);
            if (!$response) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = "Variant can't ve deleted. Please try again";
                return;
            }
            $this->apiResponse['message'] = "Variant removed successfully";
            return;
        } catch (Exception $e) {
            $this->apiResponse['message'] = "Variant can't ve deleted. Please try again";
            return;
        }
    }

    /*
     * List of featured product
     */
    public function featured()
    {
        $headerData = $this->request->getHeaders();
        $location = empty($headerData['Location']) == true ? 1 : $headerData['Location'][0];
        $this->add_model(array('Products', 'VariantsProducts', 'Qty', 'ProductsTags', 'Images', 'Prices', 'Quantities', 'ProductPrices'));
        $conditions = array(
            'Products.store_id' => $this->parseToken->store_id,
            'Products.featured' => 1
        );
        $data = $this->request->getQuery();
        $pageNo = empty($data['page_no']) ? 1 : $data['page_no'];
        $limit = (empty($data['limit']) || ($data['limit'] > 12)) ? 12 : $data['limit'];
        $offset = ($pageNo - 1) * $limit;
        if (isset($data['type']) && $data['type'] == 'tag' && !empty($data['id'])) {
            $conditions = array_merge(array('ProductsTags.tag_id' => $data['id']), $conditions);
            $productObj = $this->Products->find()
                ->contain('VariantsProducts', function ($q) {
                    return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                        ->where(['VariantsProducts.is_default' => 1]);
                })
                ->contain('ProductsTags');
        } else if (isset($data['type']) && $data['type'] == 'category' && !empty($data['id'])) {
            $categories = explode(',', $data['id']);
            $categorySearch = ' (';
            foreach ($categories as $i => $category) {
                if ($i == 0) {
                    $categorySearch .= "(Products.category_chain_id LIKE '%#" . $category . "#%')";
                } else {
                    $categorySearch .= "OR (Products.category_chain_id LIKE '%#" . $category . "#%')";
                }
            }
            $categorySearch .= ') ';
            $conditions = array_merge(array($categorySearch), $conditions);
            $productObj = $this->Products->find()
                ->contain('VariantsProducts', function ($q) {
                    return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                        ->where(['VariantsProducts.is_default' => 1]);
                })
                ->contain('ProductsTags');
        } else {
            $productObj = $this->Products->find()
                ->contain('VariantsProducts', function ($q) {
                    return $q->select(['VariantsProducts.id', 'VariantsProducts.product_id', 'VariantsProducts.chain', 'VariantsProducts.barcode', 'VariantsProducts.price_type'])
                        ->where(['VariantsProducts.is_default' => 1]);
                });
        }
        $products = $productObj->offset($offset)
            ->limit($limit)
            ->where($conditions)
            ->toArray();
        $total = $productObj->count();
        foreach ($products as $product) {
            $attr_ids[] = $product->variants_product->id;
        }
        if (!empty($attr_ids)) {
            $images = $this->Images->image_by_store_plan('online_product_list', ['variants_products_ids' => $attr_ids]);
            //$images = $this->Images->find()->select()->where(['variants_products_id IN' => $attr_ids])->toArray();
            $quantities = $this->Quantities->find()->select(['location', 'quantity', 'variants_products_id'])
                ->where(['variants_products_id IN' => $attr_ids])
                ->where(['location' => $location])
                ->toArray();
        }
        foreach ($products as $i => $row) {
            $formated = array();
            $quantity = array();
            foreach ($quantities as $mquantity) {
                if ($row->variants_product->id == $mquantity['variants_products_id']) {
                    $quantity = $mquantity;
                }
            }
            $image = '';
            //$image = $images->match(['variants_products_id' => $product['variants_products_id']])->toList();
            foreach ($images as $mimage) {
                if ($row->variants_product->id == $mimage['variants_products_id']) {
                    if (!empty($mimage['image_small'])) {
                        //$directoryPath = WWW_ROOT . DS . 'img' . DS . 'upload' . DS . 'products' . DS . $this->parseToken->store_id . DS . $row->id . DS;
                        // if (file_exists($directoryPath . $mimage['image_small']))
                        $image = $mimage['image_small'];
                    }
                }
            }
            if (!empty($quantity)) {
                $formated['id'] = $row->id;
                $formated['type'] = $row->type;
                $formated['uuid'] = $row->uuid;
                $formated['name'] = $row->name;
                $formated['url'] = $row->url;
                $formated['status'] = $row->status;
                $formated['price'] = $this->ProductPrices->priceDetails($row->variants_product);
                $formated['image'] = $image;
                $formated['barcode'] = $row->variants_product->barcode;
                $formated['quantity'] = $quantity;
                $formated['default_variant'] = [
                    'variants_products_id' => $quantity['variants_products_id']
                ];

                $result[] = $formated;
            }
        }
        if (!empty($result)) {
            $this->apiResponse['data'] = $result;
            $this->apiResponse['page_no'] = $pageNo;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = $total;
        } else {
            $this->apiResponse['data'] = [];
        }
    }


    /*
     * Related product
     */
    public function related($id = null)
    {
        $this->add_model(array('RelatedProducts'));
        if ($this->request->getData()) {
            $data = $this->request->getData();
            $options = array('RelatedProducts.product_id' => $id);
            $options = array_merge(array('RelatedProducts.related_product_id' => $data['related_product_id']), $options);
            $check = $this->RelatedProducts->find('all')
                ->where($options)
                ->first();
            if (empty($check)) {
                $r_product = $this->RelatedProducts->newEntity();
                $r_data = array();
                $r_data['product_id'] = $id;
                $r_data['related_product_id'] = $data['related_product_id'];
                $r_product = $this->RelatedProducts->patchEntity($r_product, $r_data);
                if ($this->RelatedProducts->save($r_product)) {
                    $products = $this->Products->getRelatedInfo($data['related_product_id']);
                    if (!empty($products)) {
                        $this->apiResponse['data'] = $products;
                    } else {
                        $this->apiResponse['data'] = [];
                    }
                } else {
                    $this->apiResponse['message'] = 'Method Not Allowed';
                }
            } else {
                $this->apiResponse['message'] = 'Already Exist!';
            }
        } else {
            try {
                if ($id != null) {
                    $products = $this->Products->getRelatedProducts($id);
                    if (!empty($products)) {
                        $this->apiResponse['data'] = $products;
                    } else {
                        $this->apiResponse['data'] = [];
                    }
                }
            } catch (Exception $e) {
                $this->apiResponse['error'] = $e->getMessage();
            }
        }
    }

    /*
     * Related Product Search
     */
    public function relatedSearch($id = null)
    {
        if ($id != null) {
            $search = $this->request->getQuery('search');
            $products = $this->Products->find('all')
                ->select(['Products.id', 'Products.name', 'Products.type'])
                ->where(['Products.id !=' => $id])
                ->where(['Products.store_id' => $this->parseToken->store_id])
                ->where(['name LIKE' => '%' . $search . '%'])
                ->toArray();
            $this->apiResponse['data'] = $products;
        }
    }

    /*
     * Product Search
     */
    public function search()
    {
        $data = $this->request->getQuery();
        $location = !empty($data['location']) ? $data['location'] : null;
        $this->add_model(array('CategoriesProducts', 'ProductsSuppliers', 'VariantsProducts'));
        $pageNo = 1;
        $limit = 100;
        $orderBy = 'created';
        $order = 'DESC';
        $tableName = 'Products';
        $orderBy = $tableName . '.' . $orderBy;
        $offset = ($pageNo - 1) * $limit;
        if (!empty($this->request->getQuery('search'))) {
            $search = $this->request->getQuery('search');
            $soruce = $this->request->getQuery('source');
            $search = strtolower($search);
            $options = array("Products.status !=" => 5);
            $options = array_merge(array("VariantsProducts.status" => 1), $options);
            $options = array_merge(array("Qty.location" => $location), $options);
            $options = array_merge(array("Products.store_id" => $this->parseToken->store_id), $options);
            $str = "%" . $search . "%";
            $options = array_merge(array(
                'OR' => array(
                    "Products.id" => intval($search),
                    "Products.name LIKE " => $str,
                    "VariantsProducts.barcode LIKE " => $str,
                    "Products.supply_id LIKE " => $str,
                    "Products.keyword LIKE " => $str
                )

            ), $options);


//            if (!empty($soruce) && in_array($soruce, ['admin', 'pos']))
//                $options['OR'] = array_merge($options['OR'], ["ProductAssets.serial_no" => trim($search)]);

        }


        $selectedArr = array('Products.id', 'Products.type', 'Products.deposit_amount', 'Products.store_id', 'Products.uuid', 'Products.name', 'Products.url', 'Products.variant_set');
        $priceArr = array('BasePrice.id', 'price', 'BasePrice.duration_type', 'BasePrice.duration', 'BasePrice.variants_products_id');
        $products = $this->Products->searchByString($location, $search, $selectedArr, $priceArr, $options, $offset, $limit, $orderBy, $order);

        RentMy::addModel(['Assets']);
        $asset = RentMy::$Model['Assets']->find()->where(['store_id'=>RentMy::$store->id, 'current_status'=>1, 'current_condition'=>1, 'serial_no'=>$search])->first();

        if (!empty($products) || !empty($asset)) {
            $data = array();
            foreach ($products as $product) {
                $a_data = array();
                $a_data['id'] = $product['id'];
                $a_data['deposit_amount'] = $product['deposit_amount'];
                $a_data['url'] = $product['url'];
                $a_data['type'] = $product['type'];
                $a_data['uuid'] = $product['uuid'];
                $a_data['store_id'] = $product['store_id'];
                $a_data['name'] = $product['name'];
                $a_data['price'] = $product['price'];
                $a_data['buy'] = empty($product['price']) ? false : true;
                $a_data['barcode'] = $product['barcode'];
                $a_data['quantity'] = $product['quantity'];
                $a_data['variants_products_id'] = $product['attr_id'];
                $a_data['variant_chain_id'] = $product['chain_id'];
                $a_data['variant_chain_name'] = $product['chain_name'];
                $a_data['variant_set_id'] = json_decode($product['set_id']);
                $a_data['variant_set_name'] = $product['set_name'];
                $a_data['rent'] = $product['rent'];
                $data[] = $a_data;
            }

            RentMy::addModel(['Assets', 'VariantsProducts', 'ProductPrices', 'VariantSets', 'Products']);
            if (!empty($asset)){
                $productAsset = RentMy::$Model['Products']->find()->where([
                    'id' => $asset->product_id
                ])->first();

                $row = RentMy::$Model['VariantsProducts']->find()->where(['VariantsProducts.id'=>$asset->variants_products_id])
                    ->contain(
                        'BasePrice', function ($q){
                        return $q->where(["BasePrice.duration_type LIKE '%" . 'base' . "%'"]);
                    })
                    ->first();
                $priceData = RentMy::$Model['ProductPrices']->find('all')
                    ->where(['variants_products_id' => $row->id])
                    ->where(['price_type' => $row->price_type])
                    ->where(['duration_type IN ' => ['hourly', 'daily', 'weekly', 'monthly', 'fixed']])
                    ->first();

                $formated = [];

                $price = array();
                if (!empty($row->base_price)) {
                    $price = array(
                        'base_price' => $row->base_price['price']
                    );
                }

                $formated['id'] = $productAsset->id;
                $formated['deposit_amount'] = !empty($productAsset->deposit_amount) ? $productAsset->deposit_amount : 0;
                $formated['rent'] = empty($priceData) ? false : true;
                $formated['buy'] = empty($price) ? false : true;
                $formated['url'] = $productAsset->url;
                $formated['type'] = $productAsset->type;
                $formated['uuid'] = $productAsset->uuid;
                $formated['store_id'] = $productAsset->store_id;
                $formated['name'] = $productAsset->name;
                $formated['status'] = $productAsset->status;
                $formated['price'] = $price;
                $formated['barcode'] = $row->barcode;
                $formated['quantity'] = $row->qty;
                $formated['set_id'] = $productAsset->variant_set;
                $formated['variant_set_id'] = $productAsset->variant_set;
                $formated['serial_no'] = $asset->serial_no;
                $formated['asset'] = [
                    'id' => $asset->id,
                    'serial_no' => $asset->serial_no
                ];

                $formated['variants_products_id'] = $row['id'];

                if ($row->chain != '-') {
                    $formated['variant_chain_id'] = $row->chain;
                    $formated['variant_chain_name'] = TableRegistry::getTableLocator()->get('Variants')->getChainAttributeName($row->chain);
                    $formated['variant_set_name'] = RentMy::$Model['VariantSets']->getChainAttributeSetName($productAsset->variant_set);

                    if ($row->chain == 1) {
                        $formated['variant_set_id'] = [1];
                    }
                }
                $formated['attr_id'] = $row->id;

                $data = array_merge($data, [$formated]);
            }

            $this->apiResponse['data'] = $data;
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    /*
     * Front end Product Search by name
     */
    public function searchProduct()
    {
        $data = $this->request->getHeaders();
        $location = RentMy::$token->location;
        RentMy::setDbReadable();
        $this->add_model(array('CategoriesProducts'));
        $status = [1];
        $source = $this->request->getQuery('source');

        if (!empty($source) && ($source == 'admin'))
            $status = [1,2];

        $options = array("Products.status IN" => $status, 'Products.store_id' => $this->parseToken->store_id);
        $cOptions = array();
        $sOptions = array();
        $pageNo = 1;
        $limit = 100;
        $orderBy = 'created';
        $order = 'DESC';
        $tableName = 'Products';
        $orderBy = $tableName . '.' . $orderBy;
        $offset = ($pageNo - 1) * $limit;
        $containArr = array();
        $selectedArr = array('Products.id', 'Products.type', 'Products.store_id', 'Products.uuid', 'Products.name', 'Products.url', 'Products.variants_products_id');
        $priceArr = array('ProductPrices.product_id', 'ProductPrices.price', 'ProductPrices.price_type', 'ProductPrices.duration', 'ProductPrices.variants_products_id');
        $imageArr = array('Images.id', 'Images.product_id', 'Images.image_small');
        $data = $this->request->getQuery();
        if (!empty($this->request->getQuery('search'))) {
            $search = $this->request->getQuery('search');
            $search = strtolower(trim($search));
            $str = "%" . $search . "%";
            $options = array_merge(array(
                'OR' => array(
                    ["Products.id" => intval($search)],
                    ["Products.name LIKE " => $str],
                    ["Products.supply_id LIKE " => $str],
                    ["Products.client_specific_id LIKE " => $str],
                    ["Products.options LIKE " => '%"meta_title":"' . $str],
                    ["Products.options LIKE " => '%"meta_keyword":"' . $str]
                )

            ), $options);
            if (!empty($data['category_id'])) {
                $categories = explode(',', $data['category_id']);
                $categorySearch = ' (';
                foreach ($categories as $i => $category) {
                    if ($i == 0) {
                        $categorySearch .= "(Products.category_chain_id LIKE '%#" . $category . "#%')";
                    } else {
                        $categorySearch .= "OR (Products.category_chain_id LIKE '%#" . $category . "#%')";
                    }
                }
                $categorySearch .= ') ';
                $options = array_merge(array($categorySearch), $options);
            }


            if ($source == 'admin'){

                $options = [
                    'store_id' => RentMy::$store->id,
                    'location' => RentMy::$token->location,
                    'OR' => array(
                        "id" => intval($search),
                        "name LIKE " => $str,
                        "supply_id LIKE " => $str,
                        "keyword LIKE " => $str,
                        "client_specific_id LIKE " => $str,
                    )
                ];

                RentMy::addModel(['ProductsView', 'Assets']);

                $products = RentMy::$Model['ProductsView']->find()
                    ->where($options)
                    ->offset($offset)
                    ->limit($limit)
                    ->group('id')
                    ->map(function ($product) use($search){
                        $product['asset_id'] = $product['serial_id'];
                        if (strtolower($product->serial_no) != strtolower(trim($search)))
                            $product['asset_id'] = $product['serial_no'] = '';
                        $product['default_variant'] = [
                            'variants_products_id' => $product['variants_products_id']
                        ];
                        $product['asset'] = [
                            'id' => $product['asset_id'],
                            'serial_no' => $product['serial_no']
                        ];
                        return $product;
                })->toArray();
                $asset = RentMy::$Model['Assets']->find()->where(['store_id'=>RentMy::$store->id, 'current_status'=>1, 'current_condition'=>1, 'serial_no'=>$search])->first();
                if (!empty($asset)){

                    $productAsset = RentMy::$Model['ProductsView']->find()->where([
                        'store_id' => RentMy::$store->id,
                        'location' => RentMy::$token->location,
                        'id' => $asset->product_id
                    ])->first();

                    $productAsset['default_variant'] = [
                        'variants_products_id' => $productAsset['variants_products_id']
                    ];

                    $productAsset['asset'] = [
                        'id' => $asset['id'],
                        'serial_no' => $asset['serial_no']
                    ];

                    $products = array_merge($products, [$productAsset]);
                }
                $this->apiResponse['data'] = $products;
                return;

            }

            $products = $this->Products->searchByName($location, $selectedArr, $priceArr, $imageArr, $containArr, $options, $cOptions, $sOptions, $offset, $limit, $orderBy, $order, $search);

        }
        $this->apiResponse['data'] = $products;
    }

    /*
     * Related Product Remove
     */
    public function relatedRemove($id = null, $rId = null)
    {
        $this->add_model(array('RelatedProducts'));
        try {
            $options = array('RelatedProducts.product_id' => $id);
            $options = array_merge(array('RelatedProducts.related_product_id' => $rId), $options);
            $check = $this->RelatedProducts->find('all')->where($options)->first();
            if ($check) {
                if ($this->RelatedProducts->delete($check)) {
                    $this->httpStatusCode = 200;
                } else {
                    $this->httpStatusCode = 405;
                    $this->apiResponse['error'] = 'Method Not Allowed';
                }
            } else {
                $this->httpStatusCode = 404;
                $this->apiResponse['error'] = 'Not Found';
            }
        } catch (Exception $e) {
            $this->httpStatusCode = 404;
            $this->apiResponse['error'] = 'Not Found';
        }
    }

    /*
    * Get product Tag
    */
    public function tags($id)
    {
        $this->request->allowMethod('get');
        $this->add_model(array('ProductsTags', 'Tags', 'Products'));
        $product = $this->Products->get($id);
        if ($this->parseToken->store_id != $product['store_id']) {
            $this->httpStatusCode = 401;
            $this->apiResponse['error'] = 'You are not permitted to view this product tags';
            return;
        }
        try {
            $category = $this->ProductsTags->getTags($id);
            if (!empty($category)) {
                $this->apiResponse['data'] = $category;
            } else {
                $this->apiResponse['data'] = [];
            }
        } catch (Exception $e) {
            $this->apiResponse['data'] = $e->getMessage();
        }
    }

    public function excel()
    {
        $data = $this->request->getData();
        $this->add_model(array('Suppliers', 'Prices', 'Tags', 'ProductsTags', 'Images', 'Variants', 'VariantSets', 'VariantsProducts', 'Locations', 'Quantities'));
        $productCount = 0;
        if (!empty($data)) {
            $uid = $this->parseToken->id;
            $storeid = $this->parseToken->store_id;
            if (!empty($_FILES['file']['name'])) {
                $dir = WWW_ROOT . 'img' . DS . 'upload' . DS . 'excel' . DS;
                $file_name = time() . '_' . $this->randomnum(7) . '_' . str_replace(" ", "", $_FILES['file']['name']);
                move_uploaded_file($_FILES['file']['tmp_name'], $dir . $file_name);
                $file = $dir . $file_name;
                $sheetData = $this->formatExcel($file);
                $indices = array();
                foreach ($sheetData as $i => $row) {
                    if ($i == 1) {
                        $head = $row;
                        foreach ($row as $ix => $field) {
                            $field = str_replace(" ", "_", $field);
                            $indices[strtolower($field)] = $ix;
                        }
                    } else {
                        $empData = array();
                        foreach ($indices as $field => $col) {
                            $empData[$field] = $row[$col];
                        }
                        $empData['user_id'] = $uid;
                        $empData['store_id'] = $storeid;
                        $empData['variant_set'] = json_encode([1]);
                        $product = array();
                        $supplier_id = $this->Suppliers->saveSupplier($empData);
                        if (!empty($supplier_id)) {
                            $product = $this->Products->saveProduct($empData, $supplier_id);
                            if (!empty($product)) {
                                $product->variant_set = json_encode([1]);
                                $this->_addDefaultVariant($product);
                            }
                        } else {
                            $product = $this->Products->saveProduct($empData, null);
                            if (!empty($product)) {
                                $product->variant_set = json_encode([1]);
                                $this->_addDefaultVariant($product);
                            }
                        }

                        $location_id = $this->Locations->saveLocation($empData);
                        if ($product['id'] != 0) {
                            $empData['id'] = $product['id'];
                        }
                        //pr($empData);exit;
                        if (isset($empData['variants']) && !empty(trim($empData['variants']))) {
                            $empData['variants'] = trim($empData['variants']);
                            $variant_set = explode(',', $empData['variants']);
                            $empData['variant_values'] = trim($empData['variant_values']);
                            $variant_values = explode(',', $empData['variant_values']);
                            $variant_set_ids = [];
                            $variant_values_ids = [];
                            foreach ($variant_set as $i => $set) {
                                $variantSet = $this->VariantSets->find()->select('id')
                                    ->where(["slug LIKE '%" . $set . "%'"])
                                    ->orWhere(["name LIKE '%" . $set . "%'"])
                                    ->first();
                                if (empty($variantSet)) {
                                    $variantSet = $this->VariantSets->newEntity();
                                }
                                $variant_set_data['name'] = $set;
                                $variant_set_data['store_id'] = $empData['store_id'];
                                $variant_set_data['slug'] = $set;
                                $variant_set_data['status'] = 1;
                                $variantSet = $this->VariantSets->patchEntity($variantSet, $variant_set_data);
                                if ($this->VariantSets->save($variantSet)) {
                                    $variant_set_ids[] = $variantSet->id;
                                    $variantValue = $this->Variants->find()->select('id')->where(["name LIKE '%" . $variant_values[$i] . "%'"])->first();
                                    if (empty($variantValue)) {
                                        $variantValue = $this->Variants->newEntity();
                                    }
                                    $variant_value['name'] = $variant_values[$i];
                                    $variant_value['store_id'] = $empData['store_id'];
                                    $variant_value['variant_set_id'] = $variantSet->id;
                                    $variant_value['status'] = 1;
                                    $variantValue = $this->Variants->patchEntity($variantValue, $variant_value);
                                    if ($this->Variants->save($variantValue)) {
                                        $variant_values_ids[] = $variantValue->id;
                                    }
                                }
                            }
                            $pro = $this->Products->get($empData['id']);
                            $pro->variant_set = '[' . implode(',', $variant_set_ids) . ']';
                            $this->Products->save($pro);
                            $a = $variant_values_ids;
                            $b = $variant_set_ids;

                            $attrData = array(
                                'variant_id' => $a,
                                'barcode' => $empData['barcode'],
                                'cost' => $empData['purchase_cost'],
                                'purchase_date' => '',
                                'quantity' => $empData['quantity'],
                                'location' => '',
                                'default' => false,
                                'product_id' => $empData['id'],
                                'store_id' => $empData['store_id'],
                                'set_id' => $b
                            );

                            $status = $this->VariantsProducts->add($empData['id'], $attrData);
                            if ($status) {
                                $variant_product_id = $this->VariantsProducts->find()->where(['product_id' => $empData['id']])->last();
                            }
                        } else {
                            if (!empty($empData['id'])) {
                                $variant_product_id = $this->VariantsProducts->find()->where(['product_id' => $empData['id']])->last();
                            }
                        }
                        if (!empty($variant_product_id['id']) && !empty($empData['id'])) {
                            $this->Images->saveImage($empData, $empData['id'], $variant_product_id['id']);
                            $this->Prices->savePrice($empData, $variant_product_id['id']);
                            if ($location_id) {
                                $this->Quantities->addQuantity($empData, $location_id, $variant_product_id['id']);
                            } else {
                                $location = $this->Locations->find()->where(['is_online' => 1, 'status' => 1, 'store_id' => $empData['store_id']])->first();
                                $this->Quantities->addQuantity($empData, $location['id'], $variant_product_id['id']);
                            }
                            $productCount++;
                        }
                    }
                }
            }

            if ($productCount) {
                $this->apiResponse['data'] = $productCount;
            } else {
                $this->apiResponse['data'] = $productCount;
            }

        }
    }

    private function formatExcel($file)
    {
        $sheet_data = array();
        require_once ROOT . DS . 'vendor' . DS . 'leaperdev' . DS . 'phpexcel' . DS . 'PHPExcel.php';
        require_once ROOT . DS . 'vendor' . DS . 'leaperdev' . DS . 'phpexcel' . DS . 'PHPExcel' . DS . 'IOFactory.php';
        $excel_obj = \PHPExcel_IOFactory::createReaderForFile($file);
        $excel_obj->setReadDataOnly(true);
        $obj_excel = $excel_obj->load($file);
        $worksheet = $obj_excel->getSheet(0);
        foreach ($worksheet->getRowIterator() as $i => $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            foreach ($cellIterator as $j => $cell) {
                $sheet_data[$i][$j] = $newValue = strip_tags($cell->getValue());
            }
        }
        return $sheet_data;
    }


    /**
     * Delete array of product data
     */
    public function deleteAll()
    {
        // $this->add_model('Products');
        RentMy::addModel(['OrderItems', 'Orders', 'Products']);
        $data = $this->request->getData();

        $getOrders = RentMy::$Model['OrderItems']->find('all', [
            'fields' => [
                'orders' => 'GROUP_CONCAT(DISTINCT OrderItems.order_id)',
                'Products.id',
                'Products.name'
            ]])
        ->contain(['Orders', 'Products'])
        ->whereInList('OrderItems.product_id', $data['product_id'])
        ->where(['Orders.store_id' => RentMy::$store->id, 'Orders.location' => RentMy::$token->location])
        ->group(['Products.id'])
        ->toArray();

        if(!empty($getOrders)) {
            $this->httpStatusCode = 405;
            $this->apiResponse['orders'] = $getOrders;
            return;
        }

        if (!empty($data['product_id'])) {
            foreach ($data['product_id'] as $productId) {
//                $entity = $this->Products->find()->where(['id' => $productId])->first();
//                if ($entity) {
//                    $this->_destroyAllProductData($productId);
//                    $this->Products->delete($entity);
//                }
                $this->deleteForever($productId);
            }
            $this->apiResponse['message'] = 'Data Successfully Deleted';
        } else {
            $this->apiResponse['error'] = Configure::read('message.missing_param');
        }
    }

    function _destroyAllProductData($pId)
    {
        $joinTables = [
            'related_products',
            'quantities',
            'products_tags',
            'products_settings',
            'products_details',
            'categories_products',
            'VariantsProducts',
            'images',
            'prices',
        ];
        foreach ($joinTables as $table) {
            TableRegistry::get($table)->deleteAll(['product_id' => $pId]);
        }
        return;
    }


    /**
     * @deprecated  - need to remove
     */
    public function inventorySummary()
    {
        $this->request->allowMethod('get');
        $this->add_model(['Products', 'Locations']);
        $data = $this->request->getQueryParams();
        if (!empty($data['product'])) {
            $products = $this->Products->find()->where(['store_id' => $this->parseToken->store_id])->toArray();
        } else if (!empty($data['location'])) {
            $products = $this->Locations->find()->where(['store_id' => $this->parseToken->store_id])->toArray();
        }
        if (!empty($products)) {
            $this->apiResponse['data'] = count($products);
        } else {
            $this->apiResponse['data'] = 0;
        }
    }

    /**
     * Import products from isbndb
     * @API - POST /products/books/create
     */
    public function importFromIsbnDB()
    {
        $data = $this->request->getData();
        // save products
        RentMy::addModel(['Products', 'VariantsProducts', 'Locations', 'ProductPrices', 'Images', 'ProductFields', 'ProductFieldValues']);
        $productData = [
            'name' => $data['title'],
            'url' => RentMy::seoUrl($data['title']),
            'status' => 1,
            'variant_set' => json_encode([1]),
            'store_id' => RentMy::$store->id,
            'user_id' => RentMy::$token->id,
            'description' => $data['synopsys']
        ];

        $newproduct = RentMy::$Model['Products']->newEntity();
        RentMy::$Model['Products']->patchEntity($newproduct, $productData);
        RentMy::$Model['Products']->save($newproduct);
        $product_id = $newproduct->id;

        // save variant products & quantity
        $location = array();
        $locations = RentMy::$Model['Locations']->find()->where(['store_id' => RentMy::$store->id])->toArray();
        foreach ($locations as $aLocation) {
            $aData = array(
                'id' => $aLocation->id,
                'store_id' => RentMy::$store->id,
                'quantity' => 0,
                'available' => 0
            );
            $location[] = $aData;
        }
        $attrData = array(
            'variant_id' => [1], 'barcode' => '',
            'cost' => '', 'purchase_date' => '',
            'location' => $location, 'default' => false, 'barcode' => $data['isbn13'],
            'product_id' => $newproduct->id,
            'store_id' => $newproduct->store_id,
            'set_id' => [1],

        );
        RentMy::$Model['VariantsProducts']->add($newproduct->id, $attrData);
        $vp = RentMy::$Model['VariantsProducts']->find()->where(['product_id' => $newproduct->id])->first();
        $vp->price_type = 3;
        RentMy::$Model['VariantsProducts']->save($vp);
        // save custom fields
        $product_fields = RentMy::$Model['ProductFields']->find()
            ->where([
                'ProductFields.store_id' => Rentmy::$store->id,
            ])
            ->toArray();
        foreach ($product_fields as $field) {
            if ($field['name'] == 'publish-date') {
                $value = $data['date_published'];
            } else {
                $value = $data[$field['name']];
            }
            if (!empty($value)) {
                $customFieldData = [
                    'product_id' => $product_id,
                    'store_id' => RentMy::$store->id,
                    'field_id' => $field['id'],
                    'label' => $field['label'],
                    'name' => $field['name'],
                    'value' => $value,
                ];
                $product_field_values = RentMy::$Model['ProductFieldValues']->newEntity();
                $product_field_values = RentMy::$Model['ProductFieldValues']->patchEntity($product_field_values, $customFieldData);
                RentMy::$Model['ProductFieldValues']->save($product_field_values);
            }
        }

        // save image
        if (!empty($data['image'])) {
            $directoryPath = WWW_ROOT . 'upload';
            $fileContent = file_get_contents($data['image']);
            $fileExt = explode('.', $data['image']);
            $imageName = $this->randomnum(6) . '_' . time() . '_' . $this->randomnum(6) . '.' . $fileExt[count($fileExt) - 1];
            file_put_contents($directoryPath . DS . $imageName, $fileContent);

            $largeImage = $this->resizeImage($directoryPath, $imageName, 600);
            $smallImage = $this->resizeImage($directoryPath, $imageName, 300);

            // upload s3
            $s3 = new S3();
            $s3->upload([
                ['path' => $directoryPath . DS . $imageName, 'dir' => 'products/' . RentMy::$store->id . '/' . $product_id . '/' . $imageName],
                ['path' => $directoryPath . DS . $largeImage, 'dir' => 'products/' . RentMy::$store->id . '/' . $product_id . '/' . $largeImage],
                ['path' => $directoryPath . DS . $smallImage, 'dir' => 'products/' . RentMy::$store->id . '/' . $product_id . '/' . $smallImage],
            ]);
//             remove file from server .
            RentMy::deleteFile($directoryPath, $imageName);
            RentMy::deleteFile($directoryPath, $largeImage);
            RentMy::deleteFile($directoryPath, $smallImage);

            $imgName = array(
                'image_original' => $imageName,
                'image_large' => $largeImage,
                'image_small' => $smallImage
            );
            $imagesProducts = [
                'product_id' => $product_id,
                'store_id' => RentMy::$store->id, 'status' => 2,
                'image_original' => $imageName,
                'image_large' => $largeImage,
                'image_small' => $smallImage,
                'variants_products_id' => $vp->id
            ];
            $imageObj = RentMy::$Model['Images']->newEntity($imagesProducts);
            RentMy::$Model['Images']->save($imageObj);
        }
        $this->apiResponse = ['data' => ['product_id' => $product_id], 'message' => 'Product imported successfully'];


    }

    /**
     * Clone any product
     * This is transactional, if there is any error occurs in any section,
     * full transaction reverted.
     * @API POST products/copy
     */
    public function copy()
    {
        $data = $this->request->getData();
        if (empty($data['product_id'])) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid product id';
        }
        RentMy::addModel(['Products']);
        // copy product
        $product = RentMy::$Model['Products']->find()->where(['id' => $data['product_id']])->first();
        if (empty($product->id)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid product id';
        }
        RentMy::addModel(['ProductPackages', 'VariantsProducts', 'Quantities', 'Images', 'ProductPrices', 'CategoriesProducts', 'RelatedProducts', 'Assets', 'ProductFields', 'ProductFieldValues', 'Addons']);

        $cloned_product_id = RentMy::$Model['Products']->getConnection()->transactional(
            function () use ($product) {
                // RentMy::dbg($product);
                $oldProductId = $product['id'];
                // Step 1 : save product
                unset($product['id']);
                unset($product['uuid']);
                $product->name = $product->name . ' (Copy)';
                $product->url = RentMy::seoUrl($product->name, '', RentMy::$store->id);

                $cproduct = RentMy::$Model['Products']->newEntity($product->toArray());
                RentMy::$Model['Products']->save($cproduct);
                $product_id = $cproduct->id;
                // Step 2 : for package copy package products
                if ($product->type == 2) {
                    $productPackages = RentMy::$Model['ProductPackages']->find()->where(['package_id' => $oldProductId, 'store_id' => RentMy::$store->id])->toArray();
                    if (!empty($productPackages)) {
                        foreach ($productPackages as $productPackage) {
                            unset($productPackage['id']);
                            $productPackageData[] = [
                                'store_id' => RentMy::$store->id, 'product_id' => $productPackage['product_id'], 'package_id' => $product_id, 'quantity' => $productPackage['quantity']
                            ];
                        }
                        $entities = RentMy::$Model['ProductPackages']->newEntities($productPackageData);
                        RentMy::$Model['ProductPackages']->saveMany($entities);

                    }
                }
                // Step 3: variants products
                $variants = [];
                $variantProducts = RentMy::$Model['VariantsProducts']->find()->where(['product_id' => $oldProductId])->toArray();
                if (!empty($variantProducts)) {
                    foreach ($variantProducts as $variantProduct) {
                        $variants[$variantProduct->id] = ['old_parent' => $variantProduct->parent_id];
                        $old_vp_id = $variantProduct->id;
                        unset($variantProduct->id);
                        unset($variantProduct->lft);
                        unset($variantProduct->rght);
                        $variantProduct->product_id = $product_id;
                        $vp = RentMy::$Model['VariantsProducts']->newEntity($variantProduct->toArray());
                        RentMy::$Model['VariantsProducts']->save($vp);
                        $variants[$old_vp_id]['new_id'] = $vp->id;
                        $variants_products_id = $vp->id;
                        if ($vp->is_last == 1) {
                            // Step 4 : quantities
                            $quantities = RentMy::$Model['Quantities']->find()->where(['variants_products_id' => $old_vp_id])->toArray();
                            // if (!empty($quantities)) {
                            foreach ($quantities as $quantity) {
                                unset($quantity->id);
                                $quantity->product_id = $product_id;
                                $quantity->variants_products_id = $variants_products_id;
                                $newQuantity = RentMy::$Model['Quantities']->newEntity($quantity->toArray());
                                RentMy::$Model['Quantities']->save($newQuantity);
                            }
                            //}
                            // Step 5 : pricing
                            $prices = RentMy::$Model['ProductPrices']->find()->where(['variants_products_id' => $old_vp_id])->toArray();
                            if (!empty($prices)) {
                                foreach ($prices as $price) {
                                    unset($price->id);
                                    $price->product_id = $product_id;
                                    $price->store_id = RentMy::$store->id;
                                    $price->variants_products_id = $variants_products_id;
                                    $newPrice = RentMy::$Model['ProductPrices']->newEntity($price->toArray());
                                    RentMy::$Model['ProductPrices']->save($newPrice);
                                }
                            }
                            // Step 6 : image
                            $images = RentMy::$Model['Images']->find()->where(['variants_products_id' => $old_vp_id])->toArray();
                            if (!empty($images)) {
                                $cproduct->has_default_image = 1;
                                foreach ($images as $image) {
                                    unset($image->id);
                                    $image->product_id = $product_id;
                                    $image->variants_products_id = $variants_products_id;
                                    $image->store_id = RentMy::$store->id;
                                    $newImage = RentMy::$Model['Images']->newEntity($image->toArray());
                                    RentMy::$Model['Images']->save($newImage);

                                    $s3 = new S3();
                                    $from_dir = 'products/' . RentMy::$store->id . '/' . $oldProductId . '/';
                                    $to_dir = 'products/' . RentMy::$store->id . '/' . $product_id . '/';
                                    if ($s3->exist($from_dir . $newImage['image_original'])) {
                                        $s3->copyDirectoryFiles($from_dir . $newImage['image_original'], $to_dir . $newImage['image_original']);
                                    }
                                    if ($s3->exist($from_dir . $newImage['image_large'])) {
                                        $s3->copyDirectoryFiles($from_dir . $newImage['image_large'], $to_dir . $newImage['image_large']);
                                    }
                                    if ($s3->exist($from_dir . $newImage['image_small'])) {
                                        $s3->copyDirectoryFiles($from_dir . $newImage['image_small'], $to_dir . $newImage['image_small']);
                                    }
                                    if ($s3->exist($from_dir . $newImage['image_large_free'])) {
                                        $s3->copyDirectoryFiles($from_dir . $newImage['image_large_free'], $to_dir . $newImage['image_large_free']);
                                    }
                                    if ($s3->exist($from_dir . $newImage['image_small_free'])) {
                                        $s3->copyDirectoryFiles($from_dir . $newImage['image_small_free'], $to_dir . $newImage['image_small_free']);
                                    }

                                }
                            }
                            // Step 7 : Asset
                            if ($product->is_tracked) {
                                $assets = RentMy::$Model['Assets']->find()->where(['variants_products_id' => $old_vp_id])->toArray();
                                foreach ($assets as $asset) {
                                    unset($asset->id);
                                    $asset->serial_no = $asset->serial_no . '_Copy';
                                    $asset->product_id = $product_id;
                                    $asset->variants_products_id = $variants_products_id;
                                    $asset->quantity_id = RentMy::$Model['Quantities']->find()->where(['variants_products_id' => $variants_products_id, 'location' => $asset->location_id])
                                        ->map(function ($quantity) {
                                            return $quantity['id'];
                                        })
                                        ->first();
                                    $newAsset = RentMy::$Model['Assets']->newEntity($asset->toArray());
                                    //RentMy::dbg($newAsset);
                                    RentMy::$Model['Assets']->save($newAsset);
                                }
                            }
                        }
                    }
                }

                //Step 8 :  categories
                $categories = RentMy::$Model['CategoriesProducts']->find()->where(['product_id' => $oldProductId])->toArray();
                foreach ($categories as $category) {
                    unset($category->id);
                    $category->product_id = $product_id;
                    $newCategory = RentMy::$Model['CategoriesProducts']->newEntity($category->toArray());
                    RentMy::$Model['CategoriesProducts']->save($newCategory);
                }
                //Step 9 :  related items
                $relatedProducts = RentMy::$Model['RelatedProducts']->find()->where(['product_id' => $oldProductId])->toArray();
                foreach ($relatedProducts as $relatedProduct) {
                    unset($relatedProduct->id);
                    $relatedProduct->product_id = $product_id;
                    $newRelated = RentMy::$Model['RelatedProducts']->newEntity($relatedProduct->toArray());
                    RentMy::$Model['RelatedProducts']->save($newRelated);
                }
                // Step 10 : set variant products parent ids
                $variantProducts = RentMy::$Model['VariantsProducts']->find()->where(['product_id' => $product_id, 'parent_id !=' => 0])->toArray();
                foreach ($variantProducts as $variantProduct) {
                    foreach ($variants as $key => $variant) {
                        if ($variantProduct->id == $variant['new_id']) {
                            $oldParent_id = $variant['old_parent'];
                            $variantProduct->parent_id = $variants[$oldParent_id]['new_id'];
                            RentMy::$Model['VariantsProducts']->save($variantProduct);
                        }
                    }
                }
                // Step 10: set default variant id in products table
                $default = RentMy::$Model['VariantsProducts']->find()->where(['product_id' => $product_id, 'is_last' => 1])->order(['is_default' => 'DESC'])->first();
                $cproduct->variants_products_id = $default->id;
                RentMy::$Model['Products']->save($cproduct);

                // Step 11 : custom fields.
                $fields = RentMy::$Model['ProductFields']->find()->where(['products' => $oldProductId])->toArray();
                foreach ($fields as $field) {
                    unset($field['id']);
                    $field->products = $product_id;
                    $newField = RentMy::$Model['ProductFields']->newEntity($field->toArray());
                    RentMy::$Model['ProductFields']->save($newField);

                }

                $fieldValues = RentMy::$Model['ProductFieldValues']->find()->where(['product_id' => $oldProductId])->toArray();
                foreach ($fieldValues as $fieldValue) {
                    unset($fieldValue['id']);
                    $fieldValue->product_id = $product_id;
                    $newFieldValues = RentMy::$Model['ProductFieldValues']->newEntity($fieldValue->toArray());
                    RentMy::$Model['ProductFieldValues']->save($newFieldValues);

                }
                //RentMy::dbg($fieldValues);exit();
                // Step 12 : required addons
                $addons = RentMy::$Model['Addons']->find()->where(['item_id' => $oldProductId])->toArray();
                foreach ($addons as $addon) {
                    unset($addon['id']);
                    $addon->item_id = $product_id;
                    $addon->uid = time();
                    $newAddon = RentMy::$Model['Addons']->newEntity($addon->toArray());
                    RentMy::$Model['Addons']->save($newAddon);

                }
                // Step 13 : exact date time

                return $product_id;

            }
        );
        if (!empty($cloned_product_id)) {
            $this->apiResponse = ['data' => ['id' => $cloned_product_id], 'message' => 'Product(s) copied.'];
        } else {
            $this->apiResponse = ['message' => RentMy::getSiteLinkContent('message.error_something_wrong', 'Something went wrong')];
        }

    }

    /**
     * @param $product_id
     */
    public function saveSettings($product_id)
    {
        if (empty($product_id)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid product';
        }
        RentMy::addModel(['ProductsSettings']);
        $data = $this->request->getData();
        $settings = RentMy::$Model['ProductsSettings']->find()->where(['product_id' => $product_id, 'p_key' => $data['key']])->first();
        $settingsData = [
            'product_id' => $product_id,
            'store_id' => RentMy::$store->id,
            'p_key' => $data['key'],
            'value' => $data['value']
        ];
        if (empty($settings)) {
            $settings = RentMy::$Model['ProductsSettings']->newEntity();
        }
        $settings = RentMy::$Model['ProductsSettings']->patchEntity($settings, $settingsData);
        if (RentMy::$Model['ProductsSettings']->save($settings)) {
            $this->apiResponse['message'] = 'Update saved.';
        }
    }

    /**
     * @POST /product/settings/:id/batch
     * The function `saveBatchSettings` saves batch settings for a product in PHP, handling validation
     * and error handling.
     *
     * @param product_id The `saveBatchSettings` function is responsible for saving batch settings for
     * a specific product. It takes the `product_id` as a parameter to identify the product for which
     * the settings are being saved.
     *
     * @return either a success message 'Update saved.' if the settings are successfully saved, or an
     * error message from the caught exception if there is an error during the process.
     */
    public function saveBatchSettings($product_id)
    {
        if (empty($product_id)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid product';
        }
        RentMy::addModel(['ProductsSettings']);
        $data = $this->request->getData();

        try {

            foreach ($data['settings'] as $setting) {
                $settings = RentMy::$Model['ProductsSettings']->find()->where(['product_id' => $product_id, 'p_key' => $setting['key']])->first();
                $settingsData = [
                    'product_id' => $product_id,
                    'store_id' => RentMy::$store->id,
                    'p_key' => $setting['key'],
                    'value' => $setting['value']
                ];
                if (empty($settings)) {
                    $settings = RentMy::$Model['ProductsSettings']->newEntity();
                }
                $settings = RentMy::$Model['ProductsSettings']->patchEntity($settings, $settingsData);
                RentMy::$Model['ProductsSettings']->save($settings);
            }
            $this->apiResponse['message'] = 'Update saved.';
        }catch (\Exception $e) {
            $this->httpStatusCode = 500;
            $this->apiResponse['message'] = $e->getMessage();
            return;
        }

    }

 /**
  * @GET /product/settings/:id/batch
  * This PHP function retrieves batch settings for a specific product based on provided keys.
  *
  * @param productId The `getBatchSettings` function retrieves settings for a specific product based on
  * the provided `productId`. It first checks for any specified keys in the query parameters and then
  * constructs conditions to fetch the settings from the database table `ProductsSettings`.
  */
    public function getBatchSettings($productId)
    {
        $query = $this->request->getQueryParams();
        $keys = [];
        if (!empty($query['keys'])){
            $keys = explode(',', $query['keys']);
        }
        RentMy::addModel(['ProductsSettings']);

        $conditions = [
            'product_id' => $productId,
        ];

        if (!empty($keys)){
            $conditions['p_key IN'] = $keys;
        }
        $settings = RentMy::$Model['ProductsSettings']->find()->select(['p_key', 'value'])->where($conditions)->toArray();
        $this->apiResponse['data'] = $settings;

    }

    /**
     * @param $product_id
     * @param $key
     */
    public function getSettings($product_id, $key)
    {
        if (empty($product_id)) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid product';
        }
        RentMy::addModel(['ProductsSettings']);
        $data = $this->request->getData();
        $settings = RentMy::$Model['ProductsSettings']->find()->where(['product_id' => $product_id, 'p_key' => $key])->first();
        $this->apiResponse['data'] = $settings;
    }

    /**
     * @API POST: /api/products/bulk/edit
     * @param $product_ids array - required
     * @param $tags array - not required
     * @param $tax int - not required [tax class id]
     * @param $vendor int - not required
     * @param $status int - not required
     * @param $featured int - not required
     */
    public function bulkEdit()
    {
        $data = $this->request->getData();
        $productIds = $data['product_ids'] ?? [];
        $updateAll = $data['update_all'] ?? false;
        if (empty($data['location']))
            $data['location'] = RentMy::$token->location;


        if (empty($productIds) && !$updateAll) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Please select product(s) to continue.';
            return;
        }


        if (empty($data['tags']) && empty($data['tax']) && empty($data['vendor']) &&
            empty($data['status']) && empty($data['featured']) && empty($data['exact_date_time']) && empty($data['sequence_no'])) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Please select some options to update.';
            return;
        }

        RentMy::addModel(['Products']);

        //check if it needs to have queue service
        if ($updateAll || count($productIds) > 250) {
            if ($updateAll)
                $totalProducts = RentMy::$Model['Products']->find()->where(['store_id' => RentMy::$store->id])->count();
            else
                $totalProducts = count($productIds);

            $tags = $data['tags'] ?? [];
            if ($totalProducts > 250 && !empty($tags)) {
                $data['type']='BulkEdit';
                RentMy::addQueue('Products', $data);
                $this->apiResponse['message'] = 'Products will be updated soon using background process.';
                return;
            }
        }

        //else run instant update process
        $hasEdited = RentMy::$Model['Products']->bulkEdit($data);
        $message = 'Update saved.';
        if ($hasEdited == false) {
            $this->httpStatusCode = 400;
            $message = "Bulk edit isn't working now. Please contact with admin";
        }

        $this->apiResponse['message'] = $message;
    }

    /**
     * @POST /products/:id/duplicate-check/:type
     * @return mixed
     */
    public function duplicateChecking($id, $type)
    {
        RentMy::addModel(['Products']);
        $data = $this->request->getData();

        $duplicate = false;
        $message = "";
        switch ($type){
            case 'url':
                if (empty($data['url'])){
                    $this->httpStatusCode = 400;
                    $this->apiResponse['message'] = "required field missing";
                    return;
                }
                $product = RentMy::$Model['Products']->find()->select(['id', 'url', 'name'])->where(['url' => $data['url'], 'id !=' => $id])->first();

                if (!empty($product)){
                    $duplicate = true;
                    $message = "Same url exists with ". $product->name . "(" . $product['id'] . ")";
                }

                break;
            default:
                break;

        }

        $this->apiResponse["data"] = [
            "duplicate" => $duplicate,
            "message" => $message
        ];
    }

    /**
     * @POST /products/check-url
     * @return void
     */
    public function urlDuplicateCheck()
    {
        $data = $this->request->getData();
        $response = [];

        if (empty($data['name']) && empty($data['url'])){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "required field missing";
            return;
        }

        $urlTxt = !empty($data['url']) ? $data['url'] : $data['name'];
        $url = RentMy::seoUrl($urlTxt, '', RentMy::$store->id);
        $response['url'] = $url;

        $this->apiResponse['data'] = $response;
    }


    public function variantSort()
    {
        try {


        RentMy::addModel(['VariantsProducts']);
        $data = $this->request->getData();

        if (!empty($data['variants'])) {
            RentMy::addModel(['VariantsProducts']);
            foreach ($data['variants'] as $key => $variant){
                $fields = RentMy::$Model['VariantsProducts']->find()->where(['id IN' => $variant['ids']])->toArray();

                foreach ($fields as $field) {
                    $field['sequence_no'] = $variant['sequence_no'];
                    RentMy::$Model['VariantsProducts']->save($field);
                }
            }
            $this->apiResponse['message'] = 'You change has been updated.';
            return;
        }

        $this->apiResponse['message'] = 'Please select product fields.';
        return;
        }catch (\Throwable $throwable){
            RentMy::dbgAndEnd($throwable->getMessage());
        }
    }

}

