<?php

namespace App\Controller;

use App\Exception\MissingParamsException;
use App\Lib\Atrium;
use App\Lib\Payment\AuthorizeNet\AuthorizeNet;
use App\Lib\Payment\ConvenuPay;
use App\Lib\Payment\DeltaPay;
use App\Lib\Payment\Empyrean;
use App\Lib\Payment\FreedomPay;
use App\Lib\Payment\goMerchant\goMerchant;
use App\Lib\Payment\goMerchant\Vault;
use App\Lib\Payment\Midtrans;
use App\Lib\Payment\Nelnet;
use App\Lib\Payment\Pax;
use App\Lib\Payment\Payment;
use App\Lib\Payment\Square;
use App\Lib\Payment\Stripe\Charge;
use App\Lib\Payment\Stripe\Customer;
use App\Lib\Payment\Stripe\Stripe;
use App\Lib\Payment\Stripe\StripeIntent;
use App\Lib\Payment\Stripe\Terminal;
use App\Lib\Payment\Touchnet;
use App\Lib\Payment\Transafe;
use App\Lib\Payment\ValorPay\ValorPay;
use App\Lib\RentMy\PosCustomerView;
use App\Lib\RentMy\RentMy;
use App\Lib\S3;
use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\Http\Exception\NotFoundException;
use Cake\Http\Exception\UnauthorizedException;
use Cake\I18n\Number;
use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use App\PaymentGateway\AuthorizeDOTNET;
use App\PaymentGateway\StripePayment;
use App\PaymentGateway\PayPalPayment;
use Cake\Event\Event;
use Cake\Utility\Hash;
use Exception;
use RestApi\Routing\Exception\MissingTokenException;
use Throwable;
use App\Lib\Payment\ValorPay\Terminal as ValorpayTerminal;

class PaymentsController extends AppController
{
    private $currency, $storeId, $userId;

    /**
     * Initialization hook method.
     */
    public function initialize()
    {
        parent::initialize();
    }

    public function beforeFilter(Event $event)
    {
        parent::beforeFilter($event);
    }


    /**
     * @param $order_id
     * @param $content_type
     * @param null $content_id
     * @param $status
     * @param $options
     * @param null $action_name
     * @param null $response_text
     * @param null $api_request
     * @deprecatd
     */
    private function paymentLog($order_id, $content_type, $content_id = null, $status, $options, $action_name = null, $response_text = null, $api_request = null)
    {
        $logData = array(
            'user_id' => RentMy::$token->id,
            'store_id' => RentMy::$store->id,
            'order_id' => $order_id,
            'content_type' => $content_type,
            'status' => $status,
            'options' => $options,
            'action_name' => $action_name,
            'response_text' => $response_text,
            'api_request' => $api_request,
        );

        $event = new Event('Model.PaymentLogs.PaymentLogs', $this, ['logData' => $logData]);
        $this->getEventManager()->dispatch($event);
    }

    /**
     * @param $data
     * @return int
     * @deprecated
     */
    private function _getPaymentType($data)
    {
        $payment_type = 4;
        if ($data['type'] == 1 || $data['type'] == "Online") {
            $payment_type = (isset($data['swipe']) && ($data['swipe'] == true)) ? 3 : 2;
        } else {
            if (!empty($data['gateway_id'])) {
                $payment_type = 5;
            }
        }
        return $payment_type;
    }

    /**
     * after order payment  method for cash and additional
     */
    public function add()
    {
        if (!empty($data = $this->request->getData())) {
            $data['store_id'] = $this->parseToken->store_id;
            $payment = $this->Payments->newEntity();
            $payment = $this->Payments->patchEntity($payment, $data);
            if ($this->Payments->save($payment)) {
                $this->apiResponse['data'] = $this->Payments->view($payment->id);
                RentMy::logToGenius([
                    "event" => "payment_created",
                    "status" => "success",
                    "description" => 'Payment created',
                    "value" => 'Payment ID: ' . $payment->id,
                    "custom_content" => json_encode([
                        'payment' => $payment,
                    ])
                ]);
            } else {
                $this->apiResponse['error'] = 'Please give all required field.';
            }
        } else {
            $this->apiResponse['error'] = 'Method Not Allowed';
        }
    }

    /**
     * After order payment from store admin & POS
     * @param $orderId
     * @param $type 1= online 2= offline ( cash payment), 3= additional payment
     */
    public function payment($orderId, $type)
    {
        RentMy::addModel(['ProductsAvailabilities','OrderItems','Products', 'Orders', 'StoreTerminals']);
        $this->add_model(array('Payments', 'Customers', 'PaymentGateways','Orders'));

        $data = $this->request->getData();

        $data['type'] = $type;
        $data['store_id'] = RentMy::$store->id;
        $data['user_id'] = RentMy::$token->id;
        $data['currency'] = RentMy::getCurrency();
        $data['order_id'] = $orderId;
        $requiredParam = ['amount'];

        // Check availability for items in the quote

        $order = RentMy::$Model['Orders']->find()->where(['id'=> $orderId])->first();

        if (empty($data['customer_id']) && !empty($order['customer_id']))
            $data['customer_id'] = $order['customer_id'];

        if ($order->type == 2){
            $availabilityCheck = RentMy::$Model['Orders']->availabilityFromOrder($data['order_id']);
            if (!$availabilityCheck['is_available']){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = $availabilityCheck['message'];
                return;
            }
        }
        //  if (!$this->array_keys_exist($data, $requiredParam)) {
        //     ec
        //      throw new MissingParamsException(Configure::read('message.missing_param'));
        // }

        if ($data['type'] != 3) {
            if (!empty($data['payment_amount'])) {
                $data['amount'] = $data['payment_amount'];
            }
            if (empty($data['amount']) || $data['amount'] <= 0) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = 'Please enter a valid amount.';
                return;
            }
        }

        $status = false;
        if ($data['type'] == 1) {  //online payment
            $data['capture'] =true;
            $data['payment_method'] = 'Captured';
            if($order['type']==2 || in_array($data['payment_source'], ['online_checkout'])){
                $data['capture'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? false : true;
                $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
            }
            // If customer don't have any email or
            $isCustomerCheck = !in_array($data['payment_gateway_name'], ['Square', 'ValorPay', 'ValorPayCardPresent']);
            if (empty($data['customer_id']) && $isCustomerCheck) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = 'Missing customer id';
                return;
            }
            // get customer details
            $customer = $this->Customers->find()->where(['Customers.id' => $data['customer_id']])->contain('primaryAddresses')->first();
            RentMy::addModel(['OrderAddresses']);
           $orderAdress = RentMy::$Model['OrderAddresses']->find()->where(['order_id'=>$orderId])->first();
           $data['zipcode'] = $orderAdress['zipcode'];
           $data['address_line1'] = $orderAdress['address_line1'];
           $data['email'] = $orderAdress['email'];
           $data['mobile'] = $orderAdress['mobile'];
           $data['payment_amount'] = $data['amount'];
           $data['first_name'] = $orderAdress['first_name'];
           $data['last_name'] = $orderAdress['last_name'];
           $data['city'] = $orderAdress['city'];
           $data['state'] = $orderAdress['state'];

            if (empty($customer) && $isCustomerCheck) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = 'Missing customer id';
                return;
            }
            $data['customer'] = $customer;
            //processing payment
            $getPayment = $this->processOnlinePayment($data);
            if (!$getPayment['success']) {
                $this->httpStatusCode = 403;
                $this->apiResponse['message'] = $getPayment['message'];
                //$this->paymentLog($orderId, 'Payments', null, 2, $getPayment['message']);
                return;
            }


            if ($getPayment['success']) {
                $payment = $getPayment['data'];
                if ($data['payment_gateway_name'] == 'ValorPayCardPresent') {
                    $data['capture'] = $getPayment['capture'] ?? $data['capture'];
                    $data['payment_method'] = !empty($data['capture']) ? "Captured": 'Authorized';
                }
                $data['is_captured'] = $data['capture'];
                $data['transaction_id'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['retref'] : $getPayment['transaction_id'];
                $data['response_text'] = json_encode($payment);
                $data['content_id'] = ($data['payment_gateway_name'] == 'PAX') ? 3 : 2;
                $data['action_name'] = ($data['payment_gateway_name'] == 'PAX') ? 'card-swipe' : 'card-entry';
                $data['log_option'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['resptext'] : 'Approval';
                $status = $this->Payments->create($orderId, $data);


                if (!empty($getPayment['deposit_payment'])) {

                    $depositPayment = $data;
                    $payment = $getPayment['deposit_payment']['data'];
                    $depositPayment['payment_amount'] = $getPayment['deposit_payment']['amount'];
                    $depositPayment['is_captured'] = false;
                    $depositPayment['note'] = "Deposit Payment";
                    $depositPayment['transaction_id'] = $getPayment['deposit_payment']['transaction_id'];
                    $depositPayment['response_text'] = json_encode($payment);
                    $depositPayment['payment_method'] = !empty($getPayment['deposit_payment']['payment_method']) ? $getPayment['deposit_payment']['payment_method'] : 'Authorized';
                    $depositPayment['amount'] = $getPayment['deposit_payment']['amount'];

                    $status = $this->Payments->create($orderId, $depositPayment);

                }

                if (!empty($getPayment['customerRef']))
                    $this->Customers->saveCustomerRef($customer->id, $getPayment['customerRef']);
            }


        } elseif ($data['type'] == 3) {
            $gateway = (new Payment())->getStorePaymentConfig($data['payment_gateway_name']);
            if($gateway['data']['type']=='redirect'){
                if($data['payment_gateway_name'] =='nelnet') {
                    $orderName = !empty($order->first_name) ? $order->first_name.$order->last_name : RentMy::$store->name;
                    $nelNet = new Nelnet($gateway['config']);
                    $response = $nelNet->paymentUrl([
                        'amount' => $data['amount'],
                        'orderNumber' => $orderId,
                        'orderName' => $orderName,
                        'orderDescription' => RentMy::$store->id,
                        'redirectTo' => 'OnlineStoreGuestPayment'
                    ]);
                    $data['transaction_id'] = $response['hash'];
                    $data['payment_method'] = 'Unpaid';
                    $data['status'] = 0;
                }

                if($data['payment_gateway_name'] =='FreedomPay') {
                    RentMy::addModel(['TaxLookup']);
                    $tax = RentMy::$Model['TaxLookup']->find()
                        ->select(['total_tax' =>   RentMy::$Model['TaxLookup']->find()->func()->sum('total_tax')])
                        ->where(['content_id' => $order->id, 'type' => 'order'])->first();
                    if (!empty($tax))
                        $data['tax'] = $tax->total_tax;

                    $freedomPay = new FreedomPay($gateway['config']);
                    $getPayment = $freedomPay->sale([
                        'paymentKey' => $data['payment_key'],
                        'amount' => $data['amount'],
                        'tax' => $data['tax'] ?? '0.00',
                        'order_id' => $orderId,
                        'mobile' => $order->mobile,
                        'zipcode' =>$order->zipcode,
                        'customer_id' => $data['customer_id'],
                        'name_on_card.' => $order->first_name .' '. $order->last_name,
                        'capture' => !empty(RentMy::$storeConfig['checkout_online_capture'])
                    ]);

                    $data['payment_method'] =  empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                }
                if ($getPayment['success']) {
                    $payment = $getPayment['data'];
                    $data['is_captured'] = $data['capture'];
                    $data['transaction_id'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['retref'] : $getPayment['transaction_id'];
                    $data['response_text'] = json_encode($payment);
                    $data['content_id'] = ($data['payment_gateway_name'] == 'PAX') ? 3 : 2;
                    $data['action_name'] = ($data['payment_gateway_name'] == 'PAX') ? 'card-swipe' : 'card-entry';
                    $data['log_option'] = $data['payment_gateway_name'] == 'CardConnect' ? $payment['resptext'] : 'Approval';

                    $customer = $this->Customers->find()->where(['Customers.id' => $data['customer_id']])->contain('primaryAddresses')->first();
                    if (!empty($getPayment['customerRef']))
                        $this->Customers->saveCustomerRef($customer->id, $getPayment['customerRef']);
                }

            } elseif ($data['payment_gateway_name'] == 'Touchnet'){
                $data['is_captured'] = true;
                $data['transaction_id'] = $data['payment_response']['EXT_TRANS_ID'];
                $data['response_text'] = json_encode($data['payment_response']);
                $data['content_id'] = 2;
                $data['action_name'] = 'card-entry';
                $data['log_option'] = 'Approval';
            } else {
                $paymentConfig = json_decode($gateway['data']->config,true);
                $data['payment_method'] = 'Unpaid';
                $data['content_id'] = 5;
                if (!empty($data['payment_amount'])) {
                    $data['amount'] = $data['payment_amount'];
                } else {
                    $data['amount'] = empty($data['amount']) ? 0 : $data['amount'];
                }
                if(empty($paymentConfig['is_paid'])){ // not real payment
                    $data['amount']=0;
                }
                $data['action_name'] = 'Additional' . '(' . $data['payment_gateway_name'] . ')';
                if (!empty($data['gateway_id'])) {
                    $gateway = $this->PaymentGateways->get($data['gateway_id']);
                    if ($gateway) {
                        $gatewayConfig = json_decode($gateway['config'], true);
                        if ($gatewayConfig['is_paid']) {
                            $data['payment_method'] = 'Paid';
                        }
                    }
                }
            }

            $status = $this->Payments->create($orderId, $data);
            if(!empty($response['redirect_url']))
                $status['redirect_url'] = $response['redirect_url'];

        } else {
            if (!empty($data['gateway_id']) && $data['gateway_id'] != 'undefined') { // atrium
                $gateway = $this->PaymentGateways->get($data['gateway_id']);
                if ($gateway->name == 'Atrium') {
                    $atriumObj = new Atrium();
                    $data['amount'] = $data['payment_amount'];
                    $response = $atriumObj->capture($data);
                    if ($response['approved']) {
                        $data['payment_method'] = 'Paid';
                        $data['action_name'] = $gateway->name;
                        $data['content_id'] = 7;
                        $data['note'] = '';
                        $status = $this->Payments->create($orderId, $data);
                    } else {
                        $status = false;
                    }

                }
            } else {
                // cash payment
                $data['payment_method'] = 'Cash';
                $data['content_id'] = 4;
                $data['action_name'] = 'Cash';
                $data['change_amount'] = !empty($data['amount_tendered']) ? $data['amount_tendered'] - $data['amount'] : "";
                $status = $this->Payments->create($orderId, $data);
            }
        }
        RentMy::addModel(['ProductsAvailabilities', 'Orders']);
        $order = RentMy::$Model['Orders']->get($data['order_id']);
        if ($status) {
            // for quote order - after payment update
            // product availabilities, and order type = 1, order status = processing

            if ($data['quote']) {
                if ($order['type'] == 2) {
                    RentMy::$Model['ProductsAvailabilities']->updateAll(['status' => 1], ['order_id' => $data['order_id']]);
                    $order = RentMy::$Model['Orders']->get($data['order_id']);
                    $order->status = 3;
                    $order->type = 1;
                    RentMy::$Model['Orders']->save($order);
                    RentMy::$Model['Orders']->updateBuyItemsQuantityForQuote($order->id);
                    $queueData = ['type' => 'AcceptQuote', 'order_id' => $order->id, 'content_id' => $order->id, 'user_type' => 'customer', 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source];
                    if ($order['rent_end'])
                        $queueData['rent_end'] = $order['rent_end']->format('Y-m-d H:i:s');

                    if ($order['rent_start'])
                        $queueData['rent_start'] = $order['rent_start']->format('Y-m-d H:i:s');

                    RentMy::addNotificationQueue('order_quote_accept', RentMy::$store->id, ['location'=>RentMy::$token->location], $queueData);

                    $this->apiResponse = ['message' => 'Quote accepted & transaction completed successfully'];
                    if(!empty($status['redirect_url'])){
                        $this->apiResponse['redirect_url'] = $status['redirect_url'];
                    }
                    return;
                }

            }

            if ($order->type == 2){
                $review = isset(RentMy::$storeConfig['checkout']['review_quote'])?RentMy::$storeConfig['checkout']['review_quote']:false;

                if (!$review)
                    RentMy::$Model['Orders']->acceptQuote($data['order_id']);
            }

            if ($order->status == 19){
                $order->status = 3;
                RentMy::$Model['Orders']->save($order);
            }

            if (!empty($data['signature'])) {
                $directoryPath = WWW_ROOT . 'upload' . DS . 'tmp' . DS;
                $s3 = new S3();
                $s3dir ='orders/' . 'signature_' . $orderId . '.png';
                if (!$s3->exist($s3dir)){
                    file_put_contents($directoryPath . 'signature_' . $orderId . '.png', base64_decode(explode(',', $data['signature'])[1]));

                    $s3->upload([
                        ['path' => $directoryPath . 'signature_' . $orderId . '.png', 'dir' => $s3dir],
                    ]);
                    RentMy::deleteFile($directoryPath, 'signature_' . $orderId . '.png');
                }
            }

            RentMy::addModel(['Payments']);
            $paymentCount = RentMy::$Model['Payments']->find()->where(['order_id' => $orderId, 'status' => 1])->count();

            if (RentMy::$store->id == 3497 && $paymentCount == 1)
                RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location' => RentMy::$token->location], ['type' => 'OrderEmail', 'mobile' => $data['mobile'] ?? null, 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'source' => RentMy::$token->source]);

            $queueData = [
                'type' => 'PaymentReceived',
                'mobile' => $data['mobile'] ?? null,
                'order_id' => $data['order_id'],
                'content_id' => $data['order_id'],
                'source' => RentMy::$token->source,
                'payment_count' => $paymentCount,
            ];

            RentMy::addNotificationQueue('payment_received', RentMy::$store->id, ['location' => RentMy::$token->location], $queueData);

            RentMy::logToGenius([
                "account" => @$order['event_location'],
                "event" => "payment_created",
                "status" => "success",
                "description" => @$order['event_location'] . ' payment created',
                "value" => 'Order ID: ' . $order['id'],
                "custom_content" => json_encode([
                    'data' => $queueData,
                    'order' => $order,
                ]),
                "ref2" => @$order->customer_id,
            ]);

            $options = !empty($order->options) ? json_decode($order->options, true) : [];
            $options['terms_and_condition_accepted'] = isset($data['terms_and_condition_accepted']) ? $data['terms_and_condition_accepted']: true;
            $order['options'] = json_encode($options);
            RentMy::$Model['Orders']->save($order);
            $this->apiResponse = ['message' => 'Payment completed.'];
            if(!empty($status['redirect_url'])){
                $this->apiResponse['redirect_url'] = $status['redirect_url'];
            }
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse = ['message' => 'Transaction fail!.'];
        }
    }


    /**
     * @param array $data
     * @param null $options
     * @param null $responseText
     * @return bool
     * @deprecated
     */
    private function _payment($data = array(), $options = null, $responseText = null)
    {
        $this->add_model(array('Payments', 'Carts'));
        if (!empty($data)) {
            $pData = array();
            $pData['status'] = in_array($data['payment_method'], ['Authorized']) ? 2 : 1;
            $pData['order_id'] = empty($data['order_id']) == true ? 0 : $data['order_id'];
            $pData['store_id'] = empty($data['store_id']) == true ? 0 : $data['store_id'];
            $pData['type'] = empty($data['type']) == true ? 0 : $data['type'];
            $pData['payment_amount'] = empty($data['amount']) == true ? 0 : $data['amount'];
            $pData['payment_method'] = empty($data['payment_method']) == true ? null : $data['payment_method'];
            $pData['payment_gateway'] = empty($data['payment_gateway_name']) == true ? null : $data['payment_gateway_name'];
            $pData['transaction_id'] = empty($data['transaction_id']) == true ? 0 : $data['transaction_id'];
            $pData['note'] = empty($data['note']) == true ? 0 : $data['note'];
            $pData['response_text'] = empty($data['response_text']) == true ? 0 : $data['response_text'];
            $pData['terminal'] = empty($data['terminal']) == true ? '' : $data['terminal'];
            $pData['terminal_id'] = empty($data['terminal_id']) == true ? '' : $data['terminal_id'];
            $pData['content_id'] = empty($data['content_id']) == true ? '' : $data['content_id'];
            $payment = $this->Payments->newEntity();
            $payment = $this->Payments->patchEntity($payment, $pData);
            if ($this->Payments->save($payment)) {
                // payment log
                $action_name = !empty($data['action_name']) ? $data['action_name'] : NULL;
                $api_request = json_encode(array('amount' => $data['amount'], 'order_id' => $data['order_id']));
                $response_text = empty($responseText) ? json_encode($payment) : $responseText;

                $this->paymentLog($data['order_id'], 'Payments', $payment->id, 1, $options, $action_name, $response_text, $api_request);
                return true;
            } else {
                // payment log
                $response_text = empty($responseText) ? json_encode($payment) : $responseText;
                $this->paymentLog($data['order_id'], 'Payments', null, 2, $options, null, $response_text);

                return false;
            }
        } else {
            return false;
        }

    }

    public function processOnlinePayment($data)
    {
        try {
            if ($data['payment_gateway_name'] != '' && $data['payment_gateway_name'] != 'CardConnect') {

                $param = [
                    'amount' => $data['amount'],
                    'store_id' => $data['store_id'],
                    'description' => 'Payment For order #' . $data['order_id'],
                    'currency' => $data['currency'],
                ];

                $card = !empty($data['card']) ? $data['card'] : [];

                $payment = [];
                switch ($data['payment_gateway_name']) {

                    case "Stripe":
                        $param['account'] = $data['account'] ?? '';
                        $config = (new Payment())->getStorePaymentConfig('Stripe');

                        $stripe = new StripeIntent('PaymentIntent', $config['config']);
                        $paymentIntent = $stripe->confirmIntend($data['account']['id']);

                        $payment = [
                            'success' => true,
                            'transaction_id' => $paymentIntent->id,
                            'data' => $paymentIntent,
                            'stripe_payment_payment_intent_id' => $paymentIntent->id,
                        ];

                        $data['stripe_payment_method'] = $paymentIntent->payment_method;
                        $data['action_name'] = 'card-entry';
                        $data['store_id'] = RentMy::$store->id;

                        if ((!empty($data['paytype']) && ($data['paytype'] == 'stripe_token'))) {
                            $payment['customerRef'] = ['gateway' => 'Stripe', 'ref' => $data['account']['customer'], 'payment_method' => $data['stripe_payment_method']];
                        } else {
                            try {
                                $payment['customerRef'] = $stripe->createCustomer(RentMy::$store->id, $data);

                                (new Customer('PaymentIntent', $config['config']))->addSourceForCustomerIntent($payment['customerRef']['ref'], $paymentIntent->payment_method);
                            }catch (\Throwable $throwable) {
                                RentMy::saveLogFile('alert', "storeId ==> " . RentMy::$store->id . " ==> OrderId".$data['order_id'], " ==> ".$throwable->getMessage(), ['payment']);
                            }
                        }

                        if ((RentMy::$storeConfig['checkout_online_capture'] && RentMy::$storeConfig['checkout']['separate_deposit_amount']) && empty($data['account']['requires_action']) && !empty($data['deposit_amount'])) {
                            $depositIntent = $stripe->intent([
                                'payment_method_id' => $paymentIntent['payment_method'],
                                'amount' => $data['deposit_amount'],
                                'capture' => false,
                                'store_id' => RentMy::$store->id,
                                'customer' => !empty($payment['customerRef']['ref']) ? $payment['customerRef']['ref']: null,
                                'confirm' => true
                            ]);
                            $depositIntent = $stripe->getIntent($depositIntent['id']);
                            $payment['deposit_payment'] = [
                                'capture' => false,
                                'payment_method' => 'Authorized',
                                'success' => true,
                                'amount' => $data['deposit_amount'],
                                'transaction_id' => $depositIntent->id,
                                'data' => $depositIntent,
                                'stripe_payment_payment_intent_id' => $depositIntent->id,
                            ];
                        }

                        break;
                    case "StripeCardPresent":
                        $stripeIntent = $data['response_text'] ?? [];
                        $payment = [
                            'success' => !empty($stripeIntent),
                            'transaction_id' => $stripeIntent['id'] ?? null,
                            'data' => $stripeIntent,
                            'stripe_payment_payment_intent_id' => $stripeIntent['id'] ?? null,
                        ];

                        if (!empty($stripeIntent['charges']['data'][0]['payment_method_details']['card_present']['generated_card'])){
                            try {
                                $config = (new Payment())->getStorePaymentConfig('Stripe');

                                $stripe = new StripeIntent('PaymentIntent', $config['config']);
                                $payment['customerRef'] = $stripe->createCustomer(RentMy::$store->id, $data);

                                (new Customer('PaymentIntent', $config['config']))->addSourceForCustomerIntent($payment['customerRef']['ref'], $stripeIntent['charges']['data'][0]['payment_method_details']['card_present']['generated_card']);

                            }catch (\Throwable $throwable){

                            }

                        }
                        break;
                    case "goMerchant":
                        $config = (new Payment())->getStorePaymentConfig('goMerchant');
                        $goMerchantObj = new goMerchant($config['config']);
                        if (!empty($data['paytype']) && ($data['paytype'] == 'goMerchant_token')) {
                            RentMy::addModel(['Customers']);
                            $vaultKey = RentMy::$Model['Customers']->getGatewayRef($data['customer']['email'], 'goMerchant');
                            $data['recurring'] = [
                                'vaultKey' => $vaultKey,
                                'vaultId' => $data['account']
                            ];

                            if ($data['capture'])
                                $payment = $goMerchantObj->saleUsingVault($data);
                            else
                                $payment = $goMerchantObj->authorizeUsingVault($data);

                        } else {
                            $vault = (new Vault($config['config']))->addCard($data);
                            $data['vaultKey'] = $vault['vaultKey'] ?? '';
                            if ($data['capture']) {
                                $payment = $goMerchantObj->sale($data);
                            } else {
                                $payment = $goMerchantObj->authorize($data);
                            }
                        }
                        break;
                    case "Empyrean":
                        $config = (new Payment())->getStorePaymentConfig('Empyrean');
                        $paymentObj = new Empyrean($config['config']);
                        $data['capture'] = true;
                        $data['token'] = $data['order_id'];
                        $payment = $paymentObj->sale($data);
                        break;
                    case "Square":
                        $config = (new Payment())->getStorePaymentConfig('Square');
                        $paymentObj = new Square($config['config']);
                        $data['capture'] = true;
                        $data['cardToken'] = $data['account'];
                        $data['note'] = 'Order: ' . $data['order_id'];
                        $payment = $paymentObj->sale($data);
                        break;
                    case "Midtrans":
                        $config = (new Payment())->getStorePaymentConfig('Midtrans');
                        $paymentObj = new Midtrans($config['config']);
                        $data['capture'] = true;
                        $data['cardToken'] = $data['account'];
                        $data['note'] = 'Order: ' . $data['order_id'];
                        $payment = $paymentObj->sale($data);
                        break;
                    case "PayPal":
                        $payPalPayment = new PayPalPayment($param, $card);
                        $data['payment_method'] = 'Authorized';
                        $data['action_name'] = 'card-entry';
                        $payment = $payPalPayment->authorize();

                        break;

                    case "Authorize.Net":
                        $config = (new Payment())->getStorePaymentConfig('Authorize.Net');
                        $authorizeNetObj = new AuthorizeNet($config['config']);
                        $data['capture'] = true;
                        $payment = $authorizeNetObj->charge($data);
//                    $authorizeNETPayment = new AuthorizeDOTNET($param, $card);
//                    $data['payment_method'] = 'Authorized';
//                    $data['action_name'] = 'card-entry';
//                    $payment = $authorizeNETPayment->authorize();
                        break;
                    case "PAX":
                        $config = (new Payment())->getStorePaymentConfig('PAX');
                        $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();
                        $config['config']['port'] = $terminal['hsn'];
                        $paxObj = new Pax($config['config']);
                        $data['referenceNumber'] = RentMy::$store->id . 'T' . time();
                        $payment = $paxObj->swipe($data);
                        break;
                    case "FreedomPay":
                        $config = (new Payment())->getStorePaymentConfig('FreedomPay');
                        $data['payment_method'] = 'Captured';
                        $paymentObj = new FreedomPay($config['config']);
                        $_data = $data;
                        $_data['cvv'] = $_data['cvv2'];
                        $_data['card_no'] = $data['account'];
                        $_data['paymentKey'] = ['paymentKeys' => $data['paymentKeys'], 'freedom_pay_session_key' => $data['freedom_pay_session_key']];
                        $payment = $paymentObj->sale($_data);
                        break;
                    case "TransafeCardPresent":
                        $config = (new Payment())->getStorePaymentConfig('TransafeCardPresent');
                        $paymentObj = new Transafe($config['config']);
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = true;
                        $data['payment_method'] = 'Captured';
                        $payment = $paymentObj->saleUsingTerminal($data);
                        break;
                    case "Transafe":
                        $config = (new Payment())->getStorePaymentConfig('Transafe');
                        $paymentObj = new Transafe($config['config']);
                        $data['gateway_id'] = $config['data']['id'] ?? '';
                        $data['action_name'] = 'card-entry';
                        $data['capture'] = true;
                        $data['payment_method'] = 'Captured';

                        $payment = $paymentObj->sale($data);
                        break;

                    case "ConvenuPayCardPresent":
                    case "ConvenuPay":
                        $type = 'sale';
                        $data['payment_method'] = 'Captured';
                        $data['transaction_type'] = $type;
                        $config = (new Payment())->getStorePaymentConfig('ConvenuPay');
                        $terminalNotFound = false;
                        if (isset($config['config']['enable_card_present']) && $config['config']['enable_card_present'] == 1 && !empty($data['swipe'])) {
                            RentMy::addModel(['StoreTerminals']);
                            $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();
                            if (empty($terminal) && empty($terminal->hsn)) {
                                $terminalNotFound = true;
                                $payment['success'] = false;
                                $payment['message'] = "Terminal not found";
                            } else {
                                $data['terminalId'] = (string)$terminal->hsn;
                                $data['payment_by'] = 'terminal';

                            }
                        }
                        if (!$terminalNotFound) {
                            $paymentObj = new ConvenuPay($config['config']);
                            $payment = $paymentObj->sale($data);
                        }


                        break;

                    case "DeltaPay":
                        $data['capture'] = true;
                        $data['payment_token'] = $data['account'];
                        $config = (new Payment())->getStorePaymentConfig("DeltaPay");
                        $paymentObj = new DeltaPay($config['config'], $data);
                        $payment = $paymentObj->createPayment();
                        break;


                    case 'ValorPay':
                        $data['payment_token'] = $data['account'];
                        $authOrSale = 'sale';
                        $config = (new Payment())->getStorePaymentConfig("ValorPay");
                        $paymentObj = new ValorPay($config['config']);
                        $payment = $paymentObj->processSale($data['payment_token'], $data['amount'], $authOrSale, $data);
                        if (!empty($payment['success'])) {
                            try {
                                if ((empty($data['paytype']) || ($data['paytype'] != 'ValorPay_token'))) {
                                    $paymentObj->createCustomer(RentMy::$store->id, $data);
                                }
                            }catch (\Exception $e) {

                            }
                        }
                        break;

                    case "ValorPayCardPresent":
                        $response = $data['response_text'] ?? [];
                        $payment = [
                            'success' => !empty($response),
                            'capture' => (!empty($response['response']['TRAN_METHOD']) && $response['response']['TRAN_METHOD'] == 'SALE') ? 1 : 0,
                            'transaction_id' => $response['response']['TXN_ID'] ?? null,
                            'data' => $response,
                        ];

                        if (!empty($response['response']['TOKEN'])){
                            try {
                                RentMy::addModel(['StoreTerminals']);
                                $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();
                                $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
                                $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
                                $terminalGatewayConfig = [
                                    "appId" => $terminalOptions['app_id'],
                                    "appKey" => $terminalOptions['app_key'],
                                    "epi" => $terminalOptions['epi'],
                                    "apiUrl" => $config['config']['apiUrl'],
                                ];
                                $data['payment_token'] =  $response['response']['TOKEN'];
                                $paymentObj = new ValorPay($terminalGatewayConfig);
                                $paymentObj->createCustomer(RentMy::$store->id, $data);
                            }catch (\Throwable $throwable){

                            }
                        }
                        break;

                    case "Touchnet":
                        $data['capture'] = true;
                        if (!empty($data['payment_response'])) {
                            $payment = $data['payment_response'];
                            $payment['success'] = true;
                        } else {
                            $payment = [];
                            $payment['success'] = false;
                        }
                        break;

                }

                if ($payment['success'] != 1 || isset($payment['redirect'])) {
                    return ['success' => false, 'message' => $payment['message']];
                } else {
                    return $payment;
                }

            } else {
                $payment = $this->onlinePayment($data);
                if (empty($payment['respstat']) || $payment['respstat'] != "A") {
                    $msg = empty($payment['resptext']) ? 'Server not found!' : $payment['resptext'];
                    return ['success' => false, 'message' => $msg];
                } else {
                    return ['success' => true, 'data' => $payment];
                }

            }
        }catch (Exception $exception){
            return ['success' => false, 'message' => $exception->getMessage()];
        }

    }

    /**
     * Cart Connect online payment
     * @param $data
     * @return array|bool
     * @throws Exception
     */
    private function onlinePayment($data)
    {
        $this->loadComponent('CardConnect');
        if (
            (isset($data['account']) && !empty($data['account'])) &&
            (isset($data['expiry']) && !empty($data['expiry'])) &&
            (isset($data['cvv2']) && !empty($data['cvv2'])) &&
            (isset($data['amount']) && !empty($data['amount'])) &&
            (isset($data['currency']) && !empty($data['currency']))
        ) {
            $cardData = array(
                'account' => $data['account'],
                'expiry' => $data['expiry'],
                'cvv2' => $data['cvv2'],
                'amount' => $data['amount'] * 100,
                'currency' => $data['currency']
            );
            $fName = empty($data['first_name']) ? '' : $data['first_name'];
            $lName = empty($data['last_name']) ? '' : $data['last_name'];
            $customerData = array(
                'name' => $fName . ' ' . $lName,
                'address' => empty($data['address_line1']) ? '' : $data['address_line1'],
                'city' => empty($data['city']) ? '' : $data['city'],
                'region' => empty($data['state_id']) ? '' : $data['state_id'],
                'country' => !empty($data['country_id']) ? $data['country_id'] : NULL
            );
            $response = $this->CardConnect->paymentRequest($cardData, $customerData);
            /*only testing purposes*/
            // return json_decode('{"gsacard":"N","amount":"0.01","resptext":"Approval","acctid":"1","cvvresp":"M","respcode":"000","avsresp":"N","defaultacct":"Y","merchid":"************","token":"****************","authcode":"260305","respproc":"RPCT","profileid":"15720676655846214181","retref":"************","respstat":"A","account":"55XXXXXXXXXX0387"}',true);
            return $response;
        }

        return false;
    }


    /**
     * Edit method
     */
    public function edit($id)
    {
        if (!empty($data = $this->request->getData())) {
            $payment = $this->Payments->get($id);
            $payment = $this->Payments->patchEntity($payment, $data);
            if ($this->Payments->save($payment)) {

                $this->apiResponse['data'] = $this->Payments->view($payment->id);
            } else {
                $this->apiResponse['error'] = 'Please give all required field.';
            }
        } else {
            $this->apiResponse['error'] = 'Method Not Allowed';
        }
    }

    public function add_old()
    {
        $pData = $this->request->getData();

        $this->add_model(array('Orders'));
        $payment = $this->Payments->newEntity();
        if ($this->request->is('post')) {
            $orderId = $this->request->getData('order_id');
            $order = $this->Orders->get($orderId);
            $order['status'] = 1;
            $order['payment_date'] = date('Y-m-d');
            if ($this->Orders->save($order)) {
                $payment = $this->Payments->patchEntity($payment, $pData);
                if ($this->Payments->save($payment)) {
                    //now clearing the cart
                    $cartId = $this->request->getData('cart_id');
                    $this->loadModel('Carts');
                    // $this->Carts->clearCart($cartId);

                    $this->apiResponse['data'] = array('id' => $payment->id, 'payment_amount' => $payment->payment_amount);
                } else {
                    $this->apiResponse['error'] = 'Please give all required field.';
                }
            } else {
                $this->httpStatusCode = 405;
                $this->apiResponse['error'] = 'Method Not Allowed';
            }

        } else {
            $this->httpStatusCode = 405;
            $this->apiResponse['error'] = 'Method Not Allowed';
        }
    }

    /*
     * Payment amount
     */
    public function amount($orderId)
    {
        $order = $this->Payments->Orders->get($orderId);
        // TODO: Fix issue causing discount to be applied without coupon
        $totalAmount = ($order->sub_total +$order->product_option_price + $order->tax + $order->delivery_charge) - $order->total_discount;
        $totalDeposit = $order->total_deposit;

        $offlinePaymentMethod = Configure::read('offlinePaymentMethod');
        $data = array(
            'total_amount' => $totalAmount + $totalDeposit,
            'offlinePaymentMethod' => $offlinePaymentMethod,
        );
        $this->apiResponse['data'] = $data;
    }

    /*
     * all payments of an order
     */
    public function all($orderId)
    {
        $payments = $this->Payments->find('all')
            ->select(['id', 'order_id', 'type', 'payment_method', 'payment_amount',
                'note', 'response_text', 'transaction_id', 'content_id', 'created'])
            ->where(['order_id' => $orderId])
            ->where(['status !=' => 0])
            ->map(function ($payment){
                $payment['created'] = RentMy::toStoreTimeZone($payment['created']);
                return $payment;
            })
            ->toArray();
        $paymentContent = Configure::read('paymentContent');
        foreach ($payments as $payment) {
            $payment->content_method = empty($payment->content_id) == true ? '' : $paymentContent[$payment->content_id];
        }
        $this->apiResponse['data'] = $payments;
    }


    /*
     * delete a payment
     * @API - /payments/:id
     * @param $id
     */
    public function delete($id)
    {
        RentMy::addModel(['Payments']);
        $payment = RentMy::$Model['Payments']->find()->where(['id' => $id])->first();

        if (empty($payment)){
            $this->httpStatusCode = 404;
            $this->apiResponse['message'] = 'Payment not found';
            return;
        }

        if ($payment->store_id != RentMy::$store->id) {
            $this->httpStatusCode = 403;
            $this->apiResponse['message'] = 'You are not allowed to delete this payment.';
            return;
        }

        if ($payment['type'] == 1) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'You can not delete this payment.';
            return;
        }
        $orderId = $payment->order_id;
        $paymentId = $payment->id;
        $paymentData = clone $payment;
        if (RentMy::$Model['Payments']->delete($payment)) {
            // adding to queue to add logs or other events
            RentMy::addQueue('Order', ['type' => 'deletePayment', 'order_id' => $orderId, 'store_id' => RentMy::$store->id, 'location'=>RentMy::$token->location, 'content_id' => $paymentId, 'user_id' => RentMy::$token->id, 'source' => RentMy::$token->source, 'payment_data' => $paymentData]);
            // update order payment status
            RentMy::$Model['Payments']->updateOrderPaymentStatus($orderId);

            $this->apiResponse['message'] = 'The payment was removed, and the new amount has been calculated.';
        } else {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Something wrong to delete the payments.';
        }
    }

    /*
     * view a payment
     */
    public function view($id)
    {
        if ($id != null) {
            $payment = $this->Payments->view($id);
            if (!empty($payment)) {
                $this->apiResponse['data'] = $payment;
            }
        }
    }

    public function index()
    {
        $options = array();
        $options = array_merge(array('Payments.store_id' => $this->parseToken->store_id), $options);
        $filteringParams = $this->request->getQuery();
        $page = isset($filteringParams['page_no']) ? $filteringParams['page_no'] : 1;
        $limit = isset($filteringParams['limit']) ? $filteringParams['limit'] : 10;
        if (isset($this->request->getQuery()['order_id']) && !empty(isset($this->request->getQuery()['order_id']))) {
            $orderId = $this->request->query('order_id');
            $options = array_merge(array('Payments.order_id' => $orderId), $options);
        }
        if (isset($this->request->getQuery()['transaction_id']) && !empty(isset($this->request->getQuery()['transaction_id']))) {
            $transactionId = $this->request->query('transaction_id');
            $options = array_merge(array('Payments.transaction_id' => $transactionId), $options);
        }
        if (isset($this->request->getQuery()['amount']) && !empty(isset($this->request->getQuery()['amount']))) {
            $amount = $this->request->query('amount');
            $amount_type = $this->request->query('amount_type');
            if ($amount_type == 1) {
                $options = array_merge(array('Payments.payment_amount >= ' . $amount), $options);

            } else if ($amount_type == 2) {
                $options = array_merge(array('Payments.payment_amount <= ' => $amount), $options);

            } else {
                $options = array_merge(array('Payments.payment_amount =' . $amount), $options);
            }
        }
        if (isset($this->request->getQuery()['transaction_start']) && !empty(isset($this->request->getQuery()['transaction_start'])) && isset($this->request->getQuery()['transaction_end']) && !empty(isset($this->request->getQuery()['transaction_end']))) {
            $satrdate = $satrdate = $this->request->query('transaction_start');
            $enddate = $this->request->query('transaction_end');
            $options = array_merge(array('DATE(Payments.created) BETWEEN ' . "'" . $satrdate . "'" . ' AND ' . "'" . $enddate . "'"), $options);
        }
        $offset = ($page * $limit) - $limit;
        $query = $this->Payments->find('all')
            ->select(['id', 'order_id', 'transaction_id', 'payment_amount', 'payment_method', 'response_text', 'created'])
            ->contain(['Orders' => function ($q) {
                return $q->select(['Orders.id', 'Orders.first_name', 'Orders.last_name'])
//                    ->contain(['OrderItems' => function ($q) {
//                        return $q->select(['OrderItems.id', 'OrderItems.order_id', 'OrderItems.product_id'])
//                            ->contain(['Products' => function ($q) {
//                                return $q->select(['Products.id', 'Products.name'])
//                                    ->contain(['Images'=>function ($q){
//                                        return $q->select(['Images.id','Images.product_id','Images.image_small'])->where(['status'=>2]);
//                                    }]);
//                            }]);
//                    }])
                    ;
            }])
            ->where($options)
            ->offset($offset)
            ->limit($limit)
            ->order(['Payments.created' => 'DESC']);
//pr($query);exit;
        $payments = $query->toArray();

        $total = $this->Payments->find('all')->where($options)->toArray();
        if (!empty($payments)) {
            $this->httpStatusCode = 200;
            $this->apiResponse['data'] = $payments;
            $this->apiResponse['page_no'] = $page;
            $this->apiResponse['limit'] = $limit;
            $this->apiResponse['total'] = count($total);
        } else {
            $this->apiResponse['data'] = [];
        }
    }

    public function viewPdf($filename)
    {

        $this->viewBuilder()
            ->className('Dompdf.Pdf')
            ->layout('Dompdf.default')
            ->options(['config' => [
                'filename' => $filename,
                'render' => 'browser',
            ]]);
    }

    public function paymentLogs($orderId)
    {
        $this->add_model(array('PaymentLogs'));
        $paymentsLogs = $this->PaymentLogs->find('all')->where(['order_id' => $orderId])->toArray();
        $this->apiResponse['data'] = $paymentsLogs;
    }

    /*
     * Sales report
     */
    public function salesReport()
    {
        $data = $this->request->getQuery();
        $pageNo = empty($data['page_no']) ? 1 : $data['page_no'];
        $limit = empty($data['limit']) ? 10 : $data['limit'];
        $offset = ($pageNo - 1) * $limit;
        $options = array();
        $options = array_merge(array('Orders.store_id' => $this->parseToken->store_id), $options);
        if (!empty($data['payment_type'])) {
            $options = array_merge(array("oPayments.type" => $data['payment_type']), $options);
        }
        if (!empty($data['payment_status'])) {
            $options = array_merge(array("oPayments.content_id" => $data['payment_status']), $options);
        }
        if (!empty($data['transaction_type'])) {
            $options = array_merge(array("oPayments.payment_method" => $data['transaction_type']), $options);
        }
        if (!empty($data['terminal'])) {
            $options = array_merge(array("oPayments.terminal_id" => $data['terminal']), $options);
        }
        if (!empty($data['location'])) {
            $options = array_merge(array("Orders.location" => $data['location']), $options);
        }
        if (!empty($data['order_status'])) {
            $options = array_merge(array("Orders.status" => $data['order_status']), $options);
        }
        if (!empty($data['coupon_code'])) {
            $couponObj = TableRegistry::get('Coupons');
            $coupons = $couponObj->find('list')->where(['code LIKE' => "%" . $data['coupon_code'] . "%"])->toArray();
            if (!empty($coupons))
                $options = array_merge(array("Orders.coupon_id IN" => $coupons), $options);
        }
        if (!empty($data['name'])) {
            $name = explode(' ', $data['name']);
            $fName = $name[0];
            $lName = $name[count($name) - 1];
            if (count($name) > 1) {
                $options = array_merge(array(
                    'AND' => array(
                        "Orders.first_name" => $fName,
                        "Orders.last_name" => $lName,
                    )
                ), $options);
            } else {
                $options = array_merge(array(
                    'OR' => array(
                        "Orders.first_name" => $fName,
                        "Orders.last_name" => $lName,
                    )
                ), $options);
            }

        }
        if (!empty($data['email'])) {
            $options = array_merge(array("Orders.email" => $data['email']), $options);
        }
        if (!empty($data['date_start']) && !empty($data['date_end'])) {
            $options = array_merge(array(
                'AND' => array(
                    "DATE(Orders.created) >=" => $data['date_start'],
                    "DATE(Orders.created) <=" => $data['date_end'],
                )
            ), $options);
        }

        $paymentContent = Configure::read('paymentContent');
        //$orderStatus = RentMy::getOrderStatus();
        $this->add_model(array('Payments', 'Orders'));
        $orders = $this->Orders->find('all')
            ->select(['Orders.id', 'Orders.created', 'Orders.total_quantity', 'Orders.status', 'Orders.sub_total', 'Orders.tax', 'Orders.total_discount', 'Orders.total_deposit'])
            ->contain('oPayments', function ($q) {
                return $q->select(['oPayments.id', 'oPayments.type', 'oPayments.order_id', 'oPayments.type', 'oPayments.payment_amount', 'oPayments.payment_method', 'oPayments.content_id', 'oPayments.terminal_id']);
            })
            ->order(['Orders.id' => 'DESC'])
            ->group('Orders.id')
            ->offset($offset)
            ->limit($limit)
            ->where($options)
            ->toArray();

        $total = $this->Orders->find('all')
            ->contain('oPayments')
            ->where($options)
            ->count();

        foreach ($orders as $order) {
            $order->status = RentMy::getOrderStatusStr($order->status);
            //$order->status = $orderStatus[$order->status];
            if (!empty($order->o_payment))
                $order->o_payment->content_method = empty($order->o_payment->content_id) == true ? '' : $paymentContent[$order->o_payment->content_id];
            $order->payments = $order->o_payment;
            unset($order->o_payment);
        }
        $this->apiResponse['data'] = $orders;
        $this->apiResponse['page_no'] = $pageNo;
        $this->apiResponse['limit'] = $limit;
        $this->apiResponse['total'] = $total;
    }

    /*
     * Export Sales report
     */
    public function exportSalesReport()
    {
        $data = $this->request->getQuery();
        $options = array();
        $options = array_merge(array('Orders.store_id' => $this->parseToken->store_id), $options);
        if (!empty($data['payment_type'])) {
            $options = array_merge(array("oPayments.type" => $data['payment_type']), $options);
        }
        if (!empty($data['payment_status'])) {
            $options = array_merge(array("oPayments.content_id" => $data['payment_status']), $options);
        }
        if (!empty($data['transaction_type'])) {
            $options = array_merge(array("oPayments.payment_method" => $data['transaction_type']), $options);
        }
        if (!empty($data['terminal'])) {
            $options = array_merge(array("oPayments.terminal_id" => $data['terminal']), $options);
        }
        if (!empty($data['location'])) {
            $options = array_merge(array("Orders.location" => $data['location']), $options);
        }
        if (!empty($data['order_status'])) {
            $options = array_merge(array("Orders.status" => $data['order_status']), $options);
        }
        if (!empty($data['coupon_code'])) {
            $couponObj = TableRegistry::get('Coupons');
            $coupons = $couponObj->find('list')->where(['code LIKE' => "%" . $data['coupon_code'] . "%"])->toArray();
            if (!empty($coupons))
                $options = array_merge(array("Orders.coupon_id IN" => $coupons), $options);
        }
        if (!empty($data['name'])) {
            $name = explode(' ', $data['name']);
            $fName = $name[0];
            $lName = $name[count($name) - 1];
            if (count($name) > 1) {
                $options = array_merge(array(
                    'AND' => array(
                        "Orders.first_name" => $fName,
                        "Orders.last_name" => $lName,
                    )
                ), $options);
            } else {
                $options = array_merge(array(
                    'OR' => array(
                        "Orders.first_name" => $fName,
                        "Orders.last_name" => $lName,
                    )
                ), $options);
            }

        }
        if (!empty($data['email'])) {
            $options = array_merge(array("Orders.email" => $data['email']), $options);
        }
        if (!empty($data['date_start']) && !empty($data['date_end'])) {
            $options = array_merge(array(
                'AND' => array(
                    "DATE(Orders.created) >=" => $data['date_start'],
                    "DATE(Orders.created) <=" => $data['date_end'],
                )
            ), $options);
        }

        $paymentContent = Configure::read('paymentContent');
        //$orderStatus = RentMy::getOrderStatus();
        $this->add_model(array('Payments', 'Orders'));
        $orders = $this->Orders->find('all')
            ->select(['Orders.id', 'Orders.created', 'Orders.total_quantity', 'Orders.status', 'Orders.sub_total', 'Orders.tax', 'Orders.total_discount', 'Orders.total_deposit'])
            ->contain('oPayments', function ($q) {
                return $q->select(['oPayments.id', 'oPayments.type', 'oPayments.order_id', 'oPayments.type', 'oPayments.payment_amount', 'oPayments.payment_method', 'oPayments.content_id', 'oPayments.terminal_id']);
            })
            ->order(['Orders.id' => 'DESC'])
            ->group('Orders.id')
            ->where($options)
            ->toArray();
        foreach ($orders as $order) {
            $order->status = RentMy::getOrderStatusStr($order->status);
            //$order->status = $orderStatus[$order->status];
            if (!empty($order->o_payment))
                $order->o_payment->content_method = empty($order->o_payment->content_id) == true ? '' : $paymentContent[$order->o_payment->content_id];
            $order->payments = $order->o_payment;
            unset($order->o_payment);
        }

        /* export data */
        $this->viewBuilder()->autoLayout(false);
        $this->autoRender = false;
        Configure::write('debug', false);
        require_once ROOT . DS . 'vendor' . DS . 'leaperdev' . DS . 'phpexcel' . DS . 'PHPExcel.php';
        require_once ROOT . DS . 'vendor' . DS . 'leaperdev' . DS . 'phpexcel' . DS . 'PHPExcel' . DS . 'IOFactory.php';
        $objPHPExcel = new \PHPExcel();
        $tmparray = array("ID", "Created", "Order Id", "Quantity", "Order Status", "Payment Status", "Grand Total", "Paid Amount", "Deposit", "Tax", "Discount");
        $sheet = array($tmparray);
        foreach ($orders as $i => $res) {
            $content_method = empty($res->payments->content_method) ? '' : '(' . $res->payments->content_method . ')';
            $grandTotal = ($res->sub_total + $res->tax + $res->total_deposit) - $res->total_discount;
            $sheet[$i + 1][0] = $i + 1;
            $sheet[$i + 1][1] = date("Y-m-d H:i", strtotime($res->created));
            $sheet[$i + 1][2] = $res->id;
            $sheet[$i + 1][3] = $res->total_quantity;
            $sheet[$i + 1][4] = $res->status;
            $sheet[$i + 1][5] = $res->payments->payment_method . $content_method;
            $sheet[$i + 1][6] = number_format((float)$grandTotal, 2, '.', '');
            $sheet[$i + 1][7] = number_format((float)$res->payments->payment_amount, 2, '.', '');
            $sheet[$i + 1][8] = number_format((float)$res->total_deposit, 2, '.', '');
            $sheet[$i + 1][9] = number_format((float)$res->tax, 2, '.', '');
            $sheet[$i + 1][10] = number_format((float)$res->total_discount, 2, '.', '');
        }
        $worksheet = $objPHPExcel->getActiveSheet();
        foreach ($sheet as $row => $columns) {
            foreach ($columns as $column => $data) {
                $worksheet->setCellValueByColumnAndRow($column, $row + 1, $data);
            }
        }
        $objPHPExcel->getActiveSheet()->getStyle("A1:I1")->getFont()->setBold(true);
        $objPHPExcel->setActiveSheetIndex(0);
        $filename = time() . ".xlsx";
        header('Access-Control-Allow-Origin: *');
        header('Content-type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        //$name = WWW_ROOT . 'img' . DS . 'upload' . DS . 'excel' . DS . $filename;
        //$objWriter->save($name);
        $objWriter->save('php://output');
    }


    /**
     * This function is used for getting paid payment list, all payment list, logs and total & amount
     * @param is_paid | boolean default true
     * @param list | boolean default false
     * @param log | boolean default false
     * @param memo | boolean default true
     * @param $order_id
     */
    public function summary($order_id)
    {
        RentMy::addModel(['Payments', 'Refunds', 'Orders']);
        $store_id = RentMy::$store->id;
        $order = RentMy::$Model['Orders']->find()
            ->select(['id', 'sub_total', 'tax', 'status', 'delivery_charge', 'delivery_tax', 'total_deposit', 'return_charge',
                'additional_charge', 'total_discount', 'product_option_price', 'total_price'])
            ->where(['id' => $order_id, 'store_id' => $store_id])
            ->first();

        if (empty($order->id)) {
            throw new MissingParamsException('Required field missing');
            return;
        }

        $data = $this->request->getQueryParams();
        $listAll = 1;//isset($data['list']) ? $data['list'] : 0;
        $memo = 1;//isset($data['meme']) ? $data['memo'] : 1;

        $allPayments = [];
        $releasedAmount = 0.00;
        $paymentContent = Configure::read('paymentContent');
        if ($listAll) {
            $allPayments = RentMy::$Model['Payments']->find()
                ->select(
                    [
                        'id', 'order_id', 'type', 'payment_method', 'payment_amount', 'note', 'payment_gateway', 'response_text', 'is_captured', 'is_void',
                        'transaction_id', 'content_id', 'status', 'created'
                    ])
                ->where(['order_id' => $order_id, 'store_id' => $store_id, 'status IN' => [1, 2]])
                ->map(function ($item) use ($paymentContent) {
                    $item['content_method'] = empty($item['content_id']) == true ? '' : $paymentContent[$item['content_id']];
                    $item['payment_method'] = ($item['content_id'] == 5) ? $item['payment_gateway'] : $item['payment_method'];
                    $refundedJson = json_decode($item['response_text'], true);
                    $item['released'] = !empty($refundedJson['amount_refunded']) ? number_format(($refundedJson['amount_refunded'] / 100), 2,'.','') : 0;
                    $item['is_refund'] = true;
                    $item['created'] = RentMy::toStoreTimeZone($item->created, 'm-d-Y h:i A');
                    if ($item['type'] == 1 && $item['is_captured'] == 0) {
                        $item['is_refund'] = false;
                    } elseif (($item['type'] == 3) && ($item['status'] == 2)) {
                        $item['is_refund'] = false;
                    }
                    return $item;
                })
                ->toArray();
            $releasedAmount = (new Collection($allPayments))->sumOf(function ($item) {
                return $item['released'];
            });
            $releasedAmount;
        }
        if ($memo) {
            $paymentSummary = RentMy::$Model['Payments']->paymentSummary($order);
            $bookingAmount = empty(RentMy::$storeConfig['payments']['booking']) ? 100 : RentMy::$storeConfig['payments']['booking'];
            $booking = (float)number_format((($bookingAmount * $paymentSummary['grand_total']) / 100), 2,'.','');
            $summary = [
                'total' => $paymentSummary['grand_total'],
                'order_total' => $paymentSummary['order_total'],
                'paid' => $paymentSummary['paid'],
                'due' => $paymentSummary['due'],
                'deposit' => $paymentSummary['deposit'],
                'booking' => number_format($booking, 2, '.', ''),
                'released' => number_format(($paymentSummary['released'] + $releasedAmount), 2, '.', ''),
                'amount_tendered' => $paymentSummary['amount_tendered'],
                'amount_change' =>   $paymentSummary['amount_change'],
                'collected_amount' =>   $paymentSummary['collected_amount'],
                'deposit_refunded' =>   $paymentSummary['deposit_refunded'],
            ];
        }
        $response = ['list' => $allPayments, 'memo' => $summary];
        if (!empty($data['view_token'])) { // pos screen customer view
            (new PosCustomerView($order_id))->savePayment($response);
        }
        $response['enduring_rental'] = RentMy::$Model['Orders']->isRecurring($order_id);
        $this->apiResponse['data'] = $response;

    }

    /**
     * Card swipe Transactions
     * @API POST '/payments/swipe'
     */
    public function swipe()
    {

        try {

        $this->request->allowMethod(['post']);
        $data = $this->request->getData();

        $requiredFields = ['gateway_id', 'terminal_id', 'amount'];
        $required = RentMy::requiredKeyExist($data, $requiredFields);
        if (!empty($required)) {
            throw new MissingParamsException('Required field missing');
            return;
        }

        RentMy::addModel(['PaymentGateways', 'StoreTerminals']);

        $gateway = RentMy::$Model['PaymentGateways']->find()->where(['id' => $data['gateway_id']])->first();

        if (empty($gateway)) {
            throw new NotFoundException('Invalid request');
            return;
        }

        $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();
        if (empty($terminal)) {
            throw new NotFoundException('Invalid request');
            return;
        }


        if ($gateway['online_type'] == 'swipe' && $gateway['name'] == 'PAX') {
            $config = (new Payment())->getStorePaymentConfig('Pax');
            $config['config']['port'] = $terminal['hsn'];
            $paxObj = new Pax($config['config']);
            $data['referenceNumber'] = RentMy::$store->id . 'T' . time();
            $data['type'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'auth' : 'sale';
            $response = $paxObj->swipe($data);
            if ($response['success']) {
                $this->apiResponse['data'] = $response['data'];
                return;
            } else {
                throw new NotFoundException('Transaction failed');
                return;
            }
        }

        if ($gateway['online_type'] == 'swipe' && $gateway['name'] == 'TransafeCardPresent') {
            $config = (new Payment())->getStorePaymentConfig('TransafeCardPresent');
            $paymentObj = new Transafe($config['config']);
            $data['action_name'] = 'card-entry';
            $data['capture'] = !empty(RentMy::$storeConfig['checkout_online_capture']);
            $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
            $response = $paymentObj->saleUsingTerminal($data);
            if ($response['success']) {
                $this->apiResponse['data'] = $response['data'];
                return;
            } else {
                throw new NotFoundException('Transaction failed');
                return;
            }
        }

        if ($gateway['name'] == 'ConvenuPay') {
            if (empty($terminal->hsn)){
                return [
                    'success' => false,
                    'message' => 'Terminal miss configured. Make sure you configured u_device and u_devicetype correctly.',
                    'transaction_id' => '',
                    'data' => []
                ];
            }

            $config = (new Payment())->getStorePaymentConfig('ConvenuPay');
            $paymentObj = new ConvenuPay($config['config']);
            if (isset($config['config']['enable_card_present']) && $config['config']['enable_card_present'] == 1){
                $data['action_name'] = 'card-entry';
                $data['terminalId'] = (string)$terminal->hsn;
                $data['payment_by'] = 'terminal';
                $data['capture'] = !empty(RentMy::$storeConfig['checkout_online_capture']);
                $data['payment_method'] = empty(RentMy::$storeConfig['checkout_online_capture']) ? 'Authorized' : 'Captured';
                $response = $paymentObj->sale($data);
                if ($response['success']) {
                    $this->apiResponse['data'] = $response['data'];
                    return;
                } else {
                    $this->httpStatusCode = 400;
                    $this->apiResponse['message'] = $response['message']??'Transaction failed';
                    return;
                }
            }else{
                throw new Exception('ConvenuPay Card present not activated');
            }

        }

        if($gateway['name'] == "Stripe"){

            $config = (new Payment())->getStorePaymentConfig('Stripe');
            $terminalObj = new Terminal("Terminal", $config['config']);

            $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
//            if($terminalOptions['terminal_reader']){
//                $this->apiResponse['data']['reader'] = $terminalObj->getReader($terminalOptions['terminal_reader']);
//            }
//
//            $terminalToken = $terminalObj->createTerminalConnectionToken();
//            if (!$terminalToken['success']){
//                $this->httpStatusCode = 400;
//                $this->apiResponse = ['message' => $terminalToken['message']];
//                return;
//            }

            $stripe = new StripeIntent('PaymentIntent',$config['config']);

            $postData['capture'] = $data['capture'] ?? true;

            $postData['amount'] = $data['amount'];
            $postData['payment_method_type'] = 'card_present';

            $postData['currency'] = RentMy::getCurrency();

            $intent = !empty($postData['intent_id']) ? $stripe->updateIntent($postData['intent_id'], $postData) : $stripe->intent($postData);

            $sale = $terminalObj->sale([
                "reader_id" => $terminalOptions['terminal_reader'],
                "payment_intent" => $intent->id,
            ]);

            if (!$sale['success']){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = $sale['message']??'Transaction failed';
                return;
            }

            $this->apiResponse['data'] = $intent;
            return;

        }

        if($gateway['name'] == "ValorPayCardPresent"){

            if( !isset($data['amount']) || empty($data['amount']) || $data['amount'] < 0.1 ) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'The amount cannot be less than 0.10.';
                return;
            }

            $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
            $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
            $terminalGatewayConfig = [
                "appId" => $terminalOptions['app_id'],
                "appKey" => $terminalOptions['app_key'],
                "epi" => $terminalOptions['epi'],
                "apiUrl" => $config['config']['apiUrl'],
            ];

            if (empty($terminalGatewayConfig['appId']) || empty($terminalGatewayConfig['appKey']) || empty($terminalGatewayConfig['epi']) || empty($terminalGatewayConfig['apiUrl'])) {
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = 'Terminal miss configured';
                return;
            }


            $valorPayTerminal = new \App\Lib\Payment\ValorPay\Terminal($terminalGatewayConfig);
            $transactionId = time();
            $terminalCode = empty(RentMy::$storeConfig['checkout_online_capture']) ? 3: 1;
            $sale = $valorPayTerminal->initiateSale($config['config']['channel_id'], $data['amount'], $transactionId, $terminalCode, $data);

            if (!$sale['success']){
                $this->httpStatusCode = 400;
                $this->apiResponse['message'] = $sale['message'] ?? 'Transaction failed';
                $this->apiResponse['data'] = $sale['data'] ?? [];
                return;
            }


            $responseData = !empty($sale['data']['response']) ? $sale['data']['response'] : $sale['data'];
            $responseData['request_transaction_id'] = $transactionId;
            $this->apiResponse['data'] = $responseData;
            return;
        }

        throw new NotFoundException('Transaction failed');

        }catch (\Throwable $exception){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $exception->getMessage();
        }
    }

    /**
     * Step 1 : find payments summary & and order details  .
     * Step 2 : check single payment by card & authorized - capture order total and release deposit
     * Step 3 : check single payment by card & captured
     * Step 3.1 : if paid amount > 0  & due < 0 -- refund payment using single payment.
     * Step 3.2 : other show collect payment tab
     * Step 4: Multiple payments - if paid amount > 0 && due < 0 -- refund using first payment reference
     */
    public function paymentSummary()
    {

        $this->request->allowMethod(['post']);
        RentMy::addModel(['Payments', 'Orders', 'Refunds']);
        $data = $this->request->getData();

        $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
        // payment summary
        $payment = RentMy::$Model['Payments']->paymentSummary($order);

        $bookingType = empty(RentMy::$storeConfig['payments']['type']) ? 'percent' : RentMy::$storeConfig['payments']['type'];
        if ($bookingType == 'percent') {
            $bookingConfig = empty(RentMy::$storeConfig['payments']['booking']) ? 100 : RentMy::$storeConfig['payments']['booking'];
            $bookingAmount = (float)number_format((($bookingConfig * $payment['grand_total']) / 100), 2,'.','');
            $pickupConfig = empty(RentMy::$storeConfig['payments']['pickup']) ? 100 : RentMy::$storeConfig['payments']['pickup'];
            $pickupAmount = (float)number_format((($pickupConfig * $payment['grand_total']) / 100), 2,'.','');
            $pickupAmount = ($pickupAmount > $payment['due']) ? $payment['due'] : $pickupAmount;
            $returnConfig = empty(RentMy::$storeConfig['payments']['return']) ? 100 : RentMy::$storeConfig['payments']['return'];
            $returnAmount = (float)number_format((($returnConfig * $payment['grand_total']) / 100), 2,'.','');
            $returnAmount = ($returnAmount > $payment['due']) ? $payment['due'] : $returnAmount;
        } else {
            $bookingConfig = empty(RentMy::$storeConfig['payments']['booking']) ? 0 : RentMy::$storeConfig['payments']['booking'];
            $bookingAmount = (float)number_format($bookingConfig, 2, '.', '');
            $pickupConfig = empty(RentMy::$storeConfig['payments']['pickup']) ? 0 : RentMy::$storeConfig['payments']['pickup'];
            $pickupAmount = (float)number_format($pickupConfig, 2, '.', '');
            $pickupAmount = ($pickupAmount > $payment['due']) ? $payment['due'] : $pickupAmount;
            $returnConfig = empty(RentMy::$storeConfig['payments']['return']) ? 0 : RentMy::$storeConfig['payments']['return'];
            $returnAmount = (float)number_format($returnConfig, 2, '.', '');
            $returnAmount = ($returnAmount > $payment['due']) ? $payment['due'] : $returnAmount;
        }


        $amount = 0;
        if ($payment['due'] > 0) { // show payment page if due amount > 0
            $type = 'payment';
            $amount = $payment['due'];
        } elseif ($payment['due'] < 0) {  // show refund page if due amount < 0
            $type = 'refund';
        } else { // mark order as completed .
            $type = 'completed';
//            $olderStatus = $order->status;
//            $order->status = 11;
//            RentMy::$Model['Orders']->save($order);
//            RentMy::addNotificationQueue('ChangeStatus', RentMy::$store->id, [], ['type' => 'ChangeStatus', 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'user_id' => RentMy::$token->id, 'old_status' => $olderStatus, 'new_status' => $order->status, 'source' => RentMy::$token->source]);

            //RentMy::addQueue('Order', ['type' => 'ChangeStatus', 'order_id' => $data['order_id'], 'content_id' => $data['order_id'], 'user_id' => RentMy::$token->id, 'old_status' => $olderStatus, 'new_status' => $order->status, 'source' => RentMy::$token->source]); // add order log queue
        }
        $this->apiResponse['data'] = [
            'page' => $type,
            'amount' => $amount,
            'booking_amount' => number_format($bookingAmount, 2, '.', ''),
            'pickup_amount' => number_format($pickupAmount, 2, '.', ''),
            'return_amount' => number_format($returnAmount, 2, '.', ''),
            'due' => $payment['due'],
            'paid' => $payment['paid'],
            'auth_amount' => $payment['auth_amount'],
            'grand_total' => $payment['grand_total'],
            'order_total' => $payment['order_total'],
            'deposit' => $payment['deposit'],
            'released' => $payment['released'],
            'amount_tendered' => $payment['amount_tendered'],
            'amount_change' => $payment['amount_change'],
            'collected_amount' => $payment['paid'] - $payment['released'],
        ];

    }

    /**
     * Nel net payment authentication
     */
    public function authenticate()
    {
        $postdata = $this->request->getData();
        $getparams = $this->request->getQuery();
        RentMy::saveLogFile('alert', ' --  payment get data : ' . json_encode($getparams), ['payment']);
        RentMy::saveLogFile('alert', ' --  payment post data : ' . json_encode($postdata), ['payment']);

        RentMy::addModel(['Payments', 'Orders', 'Contents']);
        if (!empty($getparams['userChoice12']) && ($getparams['userChoice12'] == 'cancel')) {
            $getparams['orderNumber'] = $getparams['userChoice11'];
        }
        $order = RentMy::$Model['Orders']->find()->where(['id' => $getparams['orderNumber']])->first();
        $options = json_decode($order['options'], true);

        $store_id = $order->store_id;
        RentMy::getStore($store_id, RentMy::$token->location);
        $store_domain = RentMy::storeDomain();

        $contents = RentMy::$Model['Contents']->find()
            ->select(['id', 'type', 'tag', 'contents', 'status', 'label'])
            ->where(['store_id' => $store_id, 'status' => 1, 'tag' => 'site_specific'])
            ->first();
        $content = json_decode($contents->contents, true);
        $successMessage = empty($content['checkout_payment']['lbl_success_confirm_order']) ? Configure::read('site_specific_contents.checkout_payment.lbl_success_confirm_order') : $content['checkout_payment']['lbl_success_confirm_order'];
        $errorMessage = empty($content['checkout_payment']['lbl_cancel_confirm_order']) ? Configure::read('site_specific_contents.checkout_payment.lbl_cancel_confirm_order') : $content['checkout_payment']['lbl_cancel_confirm_order'];
        if (!empty($getparams['transactionStatus']) && ($getparams['transactionStatus'] != 1)) {
            $referrer = $options['referrer'] . '/' . $order->uuid . '?message=' . $errorMessage . '&status=' . $getparams['transactionStatus'];
            echo '<script>';
            echo 'location.href="' . $referrer . '"';
            echo '</script>';
            exit();
        }
        if (!empty($getparams['userChoice12']) && ($getparams['userChoice12'] == 'cancel')) {
            if (!empty($getparams['userChoice13']) && ($getparams['userChoice13'] == 'OnlineStoreGuestPayment'))  {
                $referrer = RentMy::storeDomain(false, true) . '/order/' . $order->uuid . '/quote/accept?order_id=' . $order->id .
                    '&customer_id=' . $order->customer_id . '&action=payment_request' .
                    '&status=error&message=payment cancelled';
            } else {
                $referrer = $options['referrer'] . '/' . $order->uuid . '?status=error&message=' . $errorMessage;
            }

            echo '<script>';
            echo 'location.href="' . $referrer . '"';
            echo '</script>';
            exit();
        }

        $payment = RentMy::$Model['Payments']->find()->where(['order_id' => $getparams['orderNumber']])->order(['id' => 'DESC'])->first();

        if (!empty($getparams['userChoice1']) && ($getparams['userChoice1'] == 'OnlineStoreGuestPayment')) {
            $referrer = RentMy::storeDomain(false, true) . '/order/' . $order->uuid . '/quote/accept?action=payment_request&';
        } else {
            $referrer = $options['referrer'] . '/' . $order->uuid . '?';
        }
        if (!empty($payment)) {
            $payment->status = 1;
            $payment->transaction_id = $getparams['transactionId'];
            $payment->payment_method = 'Paid';
            if (RentMy::$Model['Payments']->save($payment)) {
                RentMy::addQueue('Order', ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => '', 'user_type' => 'customer', 'order_id' => $order->id, 'source' => 'online']); // add order log queue
                RentMy::$Model['Payments']->updateOrderPaymentStatus($order->id); //
                $referrer .= 'message=' . $successMessage . '&status=' . $getparams['transactionStatus'];
            } else {
                $referrer .= 'message=' . $errorMessage . '&status=' . $getparams['transactionStatus'];
            }
            echo '<script>';
            echo 'location.href="' . $referrer . '"';
            echo '</script>';

        }
        exit();
    }

    public function nelnet()
    {
        $nelNet = new Nelnet();
        $nelNet->setRedirectUrl('https://clientapi.rentmy.co/api/payments/authenticate');
        //$nelNet->setRedirectUrl('https://testecommerce.montana.edu/honors/confirm.php');
        $response = $nelNet->doPayment([
            'amount' => 20000,
            'orderName' => 'RentMy Support',
            'orderDescription' => 'Test order #2001'
        ]);
        //RentMy::dbg($response);
        exit();
    }


    /**
     * @API: POST: /gateway/freedom-pay/init
     * {
            "address_1": "",
            "city": "",
            "first_name": "",
            "first_last": "",
            "postal_code": "",
            "email": "",
            "mobile": "",
            "amount": "",
            "cart_id": ""
        }
     * @return mixed
     */
    public function initFreedomPay()
    {
        $this->request->allowMethod(['post']);

        $paymentMethod = $this->request->getParam('paymentMethod');

        $data = $this->request->getData();
        $config = (new Payment())->getStorePaymentConfig('FreedomPay');

//        $iFrame = (new FreedomPay([
//            'es_key' => 'WQWFRQJKDM7KM7J8HQPT38KVPW96JKR4',
//            'store_id' => '1520443977',
//            'terminal_id' => '2522786975',
//            'is_live' => false,
//        ]))->initFrame($data);

        $iFrame = (new FreedomPay($config['config']))->initFrame($data, $paymentMethod);
        $this->apiResponse['data'] = $iFrame;
    }

    public function stripeIntent()
    {
        $postData = $this->request->getData();
        $source = !empty($postData['source'])?$postData['source']:'';
        $postData['source'] = 'online';
        $requiredFields = ['store_id', 'amount', 'source'];
        $required = RentMy::requiredKeyExist($postData, $requiredFields);
        if (!empty($required)) {
            throw new MissingParamsException('Required field missing');
            return;
        }

        if (!empty($postData['store_id']))
            RentMy::getStore($postData['store_id'], RentMy::$token->location);

        if (in_array($source, ['pos', 'admin'])){
            $postData['source'] = $source;
        }

        if (in_array(RentMy::$token->source, ['pos', 'admin'])){
            $postData['source'] = RentMy::$token->source;
        }

        $config = (new Payment())->getStorePaymentConfig('Stripe');
        $stripe = new StripeIntent('PaymentIntent',$config['config']);

        $postData['capture'] = isset($postData['capture'])?$postData['capture']:true;

        if ($postData['source'] == 'online'){
            $postData['capture'] = !empty(RentMy::$storeConfig['checkout_online_capture']);

            if (!empty($postData['order_id'])){
                RentMy::addModel(['Orders', 'Payments']);
                $order = RentMy::$Model['Orders']->find()->where(['id' => $postData['order_id']])->first();
                $totalDeposit = $order->total_deposit;
                if (($postData['capture'] && !empty(RentMy::$storeConfig['checkout']['separate_deposit_amount']))) {
                    $depositPayment = RentMy::$Model['Payments']->find()->where(['order_id' => $postData['order_id'], 'note LIKE' => "%Deposit%", 'payment_gateway' => 'Stripe'])->first();
                    $paymentSummary = RentMy::$Model['Payments']->paymentSummary($order);
                    if (empty($depositPayment) && ($postData['amount'] > $totalDeposit) && $paymentSummary['due'] > $totalDeposit){
                        $postData['amount'] = $postData['amount'] - $totalDeposit;
                        $isDeposit = true;
                    }
                }


            }

        }


        $postData['currency'] = RentMy::getCurrency();
        if (strtolower($postData['currency']) == 'eur'){
            $postData['confirm'] = true;
        }
       // $postData['setup_future_usage'] = 'on_session';
        $intent = !empty($postData['intent_id']) ? $stripe->updateIntent($postData['intent_id'], $postData) : $stripe->intent($postData);
        if (!empty($isDeposit) && !empty($totalDeposit)){
            $intent['deposit_amount'] = $totalDeposit;
        }
        $intent['payment_amount'] = $postData['amount'];

        $this->apiResponse['data'] = $intent;
    }

    public function checkValorPaySwipeStatus()
    {
        $data = $this->request->getData();
        if (!$this->array_keys_exist($data, ['transaction_id', 'terminal_id'])){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Required field missing";
            return;
        }

        RentMy::addModel(['StoreTerminals']);
        $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();

        $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
        $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
        $terminalGatewayConfig = [
            "appId" => $terminalOptions['app_id'],
            "appKey" => $terminalOptions['app_key'],
            "epi" => $terminalOptions['epi'],
            "apiUrl" => $config['config']['apiUrl'],
        ];

        $valorTerminal = new \App\Lib\Payment\ValorPay\Terminal($terminalGatewayConfig);
        $response = $valorTerminal->status($data['transaction_id']);

        $responseData = $response['data'] ?? [];
        $statusCode = $responseData['statusCode'] ?? null;
        $errorCode = $responseData['error_no'] ?? null;

        $paymentStatus = 'processing';
        $pendingStatusCodes = ['VC05', 'VC07', 'VC08'];
        $failStatusCodes = ['VC01', 'VC02', 'VC03', 'VC04', 'VC09', 'VC12'];
        // Handle based on error_code
        if (in_array($errorCode, $failStatusCodes)) {
            // VC09 = TXN NOT FOUND -> fail, probably bad ID or expired
            $paymentStatus = 'failed';
        }

        if (!empty($response['success'])){
            $paymentStatus = 'succeeded';
        }

        if ($paymentStatus == 'processing' && !empty($data['is_last'])) {
            // Last poll attempt and still not successful: Cancel
            $cancelResponse = $valorTerminal->cancel($config['config']['channel_id'], $data['transaction_id']);
            $paymentStatus = 'failed';
        }

        $this->apiResponse['data']['payment_response'] = $response['data'] ?? [];
        $this->apiResponse['data']['payment_status'] = $paymentStatus;
    }

    public function checkStripeSwipeStatus()
    {
        $data = $this->request->getData();

        if (!$this->array_keys_exist($data, ['stripe_intent_id', 'terminal_id'])){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = "Required field missing";
            return;
        }

        RentMy::addModel(['StoreTerminals']);
        $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $data['terminal_id']])->first();

        $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
        $config = (new Payment())->getStorePaymentConfig('Stripe');
        $terminalObj = new Terminal("Terminal", $config['config']);
        $stripe = new StripeIntent('PaymentIntent', $config['config']);

        $intent = $stripe->getIntent($data['stripe_intent_id']);

        if ($intent->status != 'succeeded' && !empty($data['is_last'])){
            $reader = $terminalObj->cancel($terminalOptions['terminal_reader']);
        }
        $reader = $terminalObj->getReader($terminalOptions['terminal_reader']);
        $paymentStatus = 'processing';
        if ($reader->action->status == 'failed'){
            $paymentStatus = 'failed';
        }elseif ($reader->action->process_payment_intent->payment_intent != $data['stripe_intent_id']) {
            $paymentStatus = 'failed';
        }


        if ($intent->status == 'succeeded'){
            $paymentStatus = "succeeded";
        }

        $this->apiResponse['data']['payment_status'] = $paymentStatus;
        $this->apiResponse['data']['payment_intent'] = $intent;

    }

    public function transafeIFrameAttr()
    {
        $iFrameHost = $this->request->getQuery('client_host');
        if (empty($iFrameHost)) {
            throw new MissingParamsException('Required field missing');
            return;
        }

        $config = (new Payment())->getStorePaymentConfig('Transafe');
        $this->apiResponse['data'] = (new Transafe($config['config']))->getFrameAttrHtml($iFrameHost);
    }

    /**
     * @return void
     */
    public function touchnet(){
        $data = $this->request->getData();
        $config = (new Payment())->getStorePaymentConfig('Touchnet');
        $response = (new Touchnet($config['config']))->generateSecureLinkTicket($data);
        $this->apiResponse['data'] = $response;
    }

    /**
     * @param $orderId
     * @param $redirect = redirect url
     * @return void
     */
    public function touchnetPayment($orderId, $redirect){
        RentMy::addModel(['Payments', 'Orders']);
        $paymentData = $this->request->getData();

        $success = 1;
        $message = '';
        $order = RentMy::$Model['Orders']->find()->where(['id'=>$orderId])->first();
        if (!empty($paymentData)){

            $data = $this->request->getData();
            RentMy::getStore($order->store_id, $order->location);
            $config = (new Payment())->getStorePaymentConfig('Touchnet', $order->location);
            $response = (new Touchnet($config['config']))->authorizeAccount($data);
            if ($response['success']){
                $response['data']['session_identifier'] = $data['session_identifier'];
                $data['is_captured'] = 1;
                $data['store_id'] = $order->store_id;
                $data['order_id'] = $order->id;
                $data['transaction_id'] = $data['session_identifier'];
                $data['response_text'] = json_encode($response['data']);
                $data['content_id'] = 2;
                $data['payment_gateway_name'] = 'Touchnet';
                $data['payment_amount'] = $data['amount'] = $paymentData['pmt_amt'];
                $data['action_name'] = 'card-entry';
                $data['log_option'] = 'Approval';
                $data['type'] = 1;
                $data['payment_method'] = 'Captured';
                $data['status'] = 1;
                $payment =  RentMy::$Model['Payments']->create($order->id, $data);
                $message = "Transaction has been completed successfully";
            }else{
                $success = 0;
                $message = !empty($response['message']) ? $response['message'] : "Payment failed!";
                try {
                    RentMy::addModel(['OrderNotes']);
                    $order['currency_format'] = RentMy::$storeConfig['currency_format'];
                    $errorMsg = RentMy::currencyFormat($order, $paymentData['pmt_amt']) . ' payment failed';
                    if (!empty($response['message'])){
                        $errorMsg .= ' because of ' . strtolower($response['message']);
                    }
                    RentMy::$Model['OrderNotes']->addOrderLog('PaymentFailed',
                        ['content_id' => $order->id, 'user_id' => '', 'order_id' => $order->id, 'source' => 'Payment', 'store_id'=> $order->store_id, 'error_message'=>$errorMsg]
                    );
                }catch (\Throwable $throwable){

                }
            }

        }
        $url = str_replace("@", "/", $redirect);

        $url .= '?success='.$success . '&message='.$message;

        $this->redirect($url);

    }

    /**
     * @POST /payments/webhook/:type
     * @here type=Stripe
     * @return void
     */
    public function webHook($type)
    {
        $data = $this->request->getData();
        if (strtolower($type) == 'stripe'){
            $stripeData = $data['data']['object'];
            switch ($data['type']){
                case 'refund.updated':
                    try {


                    RentMy::addModel(['Payments', 'Orders', 'Refunds']);
                    // check already refunded

                    $stripeIntentId = $stripeData['payment_intent'];
                    $payment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $stripeIntentId, 'payment_method' => 'Captured'])->first();

                    if (!empty($payment['is_void']))
                        return;

                    if ($payment['payment_method'] != 'Captured'){
                        return;
                    }

                    if (!empty($stripeData['metadata']['request_from']) && $stripeData['metadata']['request_from'] == 'RentMy')
                        return;


                    $checkRefund = RentMy::$Model['Refunds']->find()->where(['gateway_refund_id' => $stripeData['id']])->first();
                    if (!empty($checkRefund))
                        return;

                    if (!empty($payment)){
                        $refundData = [
                            'order_id' => $payment->order_id,
                            'amount' => $stripeData['amount'] / 100,
                            'payment_id' => $payment->id,
                            'store_id' => $payment->store_id,
                            'gateway_refund_id' => $stripeData['id']
                        ];
                        $order = RentMy::$Model['Orders']->find()->where(['id' => $payment->order_id])->first();
                        if (!empty($order)){
                            $refundData['location'] = $order->location;
                            $refundData['store_id'] = $order->store_id;
                        }

                        $refundData['source'] = 'Stripe';
                        RentMy::$Model['Refunds']->add($refundData);
                        RentMy::addModel(['Payments']);
                        RentMy::$Model['Payments']->updateOrderPaymentStatus($payment['order_id']);

                        RentMy::saveLogFile('alert', "Refunded from Stripe ==> " . json_encode($refundData), ['payment']);

                    }
                    }catch (\Throwable $throwable){
                        RentMy::saveLogFile('alert', "Refunded from Stripe error ==> " . $throwable->getMessage(), ['payment']);

                    }
                    break;

                case 'refund.failed':
                    if (!empty($stripeData['metadata']['request_from']) && $stripeData['metadata']['request_from'] == 'RentMy')
                        return;
                    RentMy::addModel(['Payments', 'Orders', 'Refunds']);
                    $refundId = $stripeData['id'];
                    $refund = RentMy::$Model['Refunds']->find()->where(['gateway_refund_id' => $refundId])->first();
                    $orderId = $refund->order_id;
                    $order = RentMy::$Model['Orders']->find()->where(['id' => $orderId])->first();
                    if (!empty($order)){
                        $location = $order->location;
                        $storeId = $order->store_id;
                    }
                    RentMy::$Model['Refunds']->delete($refund);
                    RentMy::addModel(['Payments']);
                    RentMy::$Model['Payments']->updateOrderPaymentStatus($orderId);

                    $queueData = ['type' => 'RefundCanceled', 'user_id' => RentMy::$token->id, 'order_id' => $orderId, 'source' => 'Stripe'];
                    if (!empty($storeId)){
                        $queueData['store_id'] = $storeId;
                    }

                    if (!empty($location)){
                        $queueData['location'] = $location;
                    }
                    $queueData['amount'] = $stripeData['amount'] / 100;
                    $queueData['content_id'] = '';
                    RentMy::saveLogFile('alert', "Refunded Canceled from stripe ==> " . json_encode($queueData), ['payment']);
                    RentMy::addQueue('Order', $queueData); // add order log queue
                    break;

                case 'charge.captured':
                    RentMy::addModel(['Payments', 'Orders', 'Refunds']);
                    $stripeIntentId = $stripeData['payment_intent'];

                    $checkPayment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $stripeIntentId, 'payment_method' => 'Captured', 'status' => 1])->first();
                    // check payment already captured
                    if (!empty($checkPayment)){
                        return;
                    }
                    $payment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $stripeIntentId])->first();
                    $orderId = $payment->order_id;
                    $order = RentMy::$Model['Orders']->find()->where(['id' => $orderId])->first();

                    RentMy::getStore($order->store_id, $order->location);
                    $config = (new Payment())->getStorePaymentConfig('Stripe', $order->location);
                    $intent = (new StripeIntent('Payment', $config['config']))->getIntent($stripeIntentId);
                    $payment->status = 0;
                    $payment->is_captured = 1;
                    RentMy::$Model['Payments']->save($payment);

                    $capturedData = [
                        'store_id' => $payment->store_id,
                        'order_id' => $payment->order_id,
                        'type' => $payment->type, 'content_id' => $payment->content_id,
                        'payment_gateway' => $payment->payment_gateway, 'gateway_id' => $payment->gateway_id,
                        'terminal' => $payment->terminal, 'terminal_id' => $payment->terminal_id,
                        'payment_method' => 'Captured',
                        'transaction_id' => $stripeIntentId,
                        'payment_amount' => $stripeData['amount_captured'] / 100,
                        'status' => 1,
                        'is_captured' => 1,
                        'response_text' => json_encode($intent)
                    ];
                    $payment = RentMy::$Model['Payments']->newEntity();
                    $payment = RentMy::$Model['Payments']->patchEntity($payment, $capturedData);
                    if (RentMy::$Model['Payments']->save($payment)) {
                        RentMy::addNotificationQueue('create_order', RentMy::$store->id, ['location'=>RentMy::$token->location], ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$token->id, 'order_id' => $payment->order_id, 'source' => 'Stripe']);
                    }
                    RentMy::$Model['Payments']->updateOrderPaymentStatus($orderId);
                    break;

                case 'payment_intent.canceled':
                    RentMy::addModel(['Payments', 'Orders', 'Refunds']);
                    $stripeIntentId = $stripeData['id'];

                    $checkVoid = RentMy::$Model['Payments']->find()->where(['transaction_id' => $stripeIntentId, 'is_void' => 0, 'status' => 0])->first();
                    // check payment already voided
                    if (!empty($checkVoid)){
                        return;
                    }
                    $payment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $stripeIntentId])->first();

                    $orderId = $payment->order_id;
                    $order = RentMy::$Model['Orders']->find()->where(['id' => $orderId])->first();

                    RentMy::getStore($order->store_id, $order->location);

                    $payment->status = 0;
                    $payment->is_void = 1;
                    if (RentMy::$Model['Payments']->save($payment)) {
                        RentMy::$Model['Payments']->updateOrderPaymentStatus($orderId);
                        RentMy::addNotificationQueue('create_order', $order->store_id, ['location'=>$order->location], ['type' => 'Payments', 'content_id' => $payment->id, 'user_id' => RentMy::$token->id, 'order_id' => $payment->order_id, 'source' => 'Stripe']);
                    }
                    break;

                case 'payment_intent.succeeded':
                    RentMy::addModel(['Payments', 'Orders', 'Refunds', 'ApplicationFee', 'OrderAddresses']);
                    $stripeIntentId = $stripeData['id'];
                    $maxAttempts = 5;
                    $attempt = 0;
                    $payment = null;

                    while ($attempt < $maxAttempts) {
                        $payment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $stripeIntentId])->first();

                        if (!empty($payment)) {
                            break;
                        }

                        sleep(2); // Wait for 2 seconds before retrying
                        $attempt++;
                    }
                    if (empty($payment)){
                        return;
                    }
                    $order = RentMy::$Model['Orders']->find()->where(['id' => $payment->order_id])->first();
                    $orderAddress = RentMy::$Model['OrderAddresses']->find()->where(['order_id' => $order->id])->first();

                    RentMy::getStore($order->store_id, $order->location);

                    $config = (new Payment())->getStorePaymentConfig('Stripe', $order->location);
                    $balanceTransaction = (new StripeIntent('Payment', $config['config']))->getBalanceTransaction($stripeIntentId);
                    $stripeProcessFee = 0;
                    if ($balanceTransaction['success']){
                        $applicationFees = $balanceTransaction['data']['fee_details'];
                        $stripeFees = array_filter($applicationFees, function($fee) {
                            return $fee['type'] === 'stripe_fee';
                        });

                        $stripeFees = array_values($stripeFees);
                        if (!empty($stripeFees))
                            $stripeProcessFee = $stripeFees[0]['amount'];
                    }

                    $applicationFee = RentMy::$Model['ApplicationFee']->newEntity();
                    $applicationFeeData = [
                        'order_id' => $payment->order_id,
                        'store_id' => $order->store_id,
                        'location' => $order->location,
                        'customer_id' => $order->customer_id ?? null,
                        'customer_name' => (!empty($orderAddress->first_name) || !empty($orderAddress->last_name)) ? $orderAddress->first_name . ' ' . $orderAddress->last_name : null,
                        'customer_email' => $orderAddress->email ?? null,
                        'customer_company' => $orderAddress->company ?? null,
                        'payment_id' => $payment->id,
                        'payment_amount' => $stripeData['amount_received'] / 100,
                        'application_fee_amount' => $stripeData['application_fee_amount'] / 100,
                        'stripe_process_fee_amount' => $stripeProcessFee / 100,
                        'stripe_payment_intent_id' => $stripeData['id'],
                        'payout_amount' => $payoutAmount ?? null,
                        'payout_date' => $payoutDate ?? null,
                        'payment_status' => 'paid',
                    ];
                    $applicationFee = RentMy::$Model['ApplicationFee']->patchEntity($applicationFee, $applicationFeeData);
                    RentMy::$Model['ApplicationFee']->save($applicationFee);
                    break;
            }
        }
    }


    public function cancelReader($terminalId, $gateway)
    {

        if ( empty($terminalId) || empty($gateway) ){
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Missing terminal id or gateway';
            return;
        }
        $gateway = strtolower($gateway);
        RentMy::addModel(['StoreTerminals']);
        $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $terminalId])->first();

        if( empty($terminal) ) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = 'Invalid terminal';
            return;
        }

        try {
            switch ($gateway) {

                case 'valorpay':
                    $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];
                    $config = (new Payment())->getStorePaymentConfig('ValorPayCardPresent');
                    $terminalGatewayConfig = [
                        "appId" => $terminalOptions['app_id'],
                        "appKey" => $terminalOptions['app_key'],
                        "epi" => $terminalOptions['epi'],
                        "apiUrl" => $config['config']['apiUrl'],
                    ];

                    $channelId = $config['config']['channel_id'];

                    if (empty($terminalGatewayConfig['appId']) || empty($terminalGatewayConfig['appKey']) || empty($terminalGatewayConfig['epi']) || empty($terminalGatewayConfig['apiUrl'])) {
                        $this->httpStatusCode = 400;
                        $this->apiResponse['message'] = 'Terminal miss configured';
                        return;
                    }

                    $terminalObj = new ValorpayTerminal($terminalGatewayConfig);

                    $reader = $terminalObj->cancel($channelId);
                    break;

                case 'stripe':
                    RentMy::addModel(['StoreTerminals']);
                    $terminal = RentMy::$Model['StoreTerminals']->find()->where(['id' => $terminalId])->first();

                    $terminalOptions = !empty($terminal['options']) ? json_decode($terminal['options'], true) : [];

                    $config = (new Payment())->getStorePaymentConfig('Stripe');
                    $terminalObj = new Terminal("Terminal", $config['config']);

                    $reader = $terminalObj->cancel($terminalOptions['terminal_reader']);
                    break;

            }

            if( isset($reader) ) {

                if (!$reader['success']) {
                    $this->httpStatusCode = 400;
                    $this->apiResponse = ['message' => $reader['message']];
                    return;
                }

                $this->apiResponse['message'] = "Transaction cancelled successfully.";
                $this->apiResponse['data'] = $reader['data'];
                return;

            }

        } catch (\Throwable $th) {
            $this->httpStatusCode = 400;
            $this->apiResponse['message'] = $th->getMessage();
            return;
        }


        $this->httpStatusCode = 400;
        $this->apiResponse['message'] = 'Bad Request';
        return;


    }


}
