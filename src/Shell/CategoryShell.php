<?php

namespace App\Shell;

use App\Lib\RentMy\RentMy;
use Cake\Console\Shell;
use Cake\Datasource\ConnectionManager;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;

class CategoryShell extends Shell
{
    private $Categories;

    public function initialize()
    {
        parent::initialize();
        $this->Categories = TableRegistry::get('Categories');
    }

    public function main()
    {

    }

    public function exitingCategoryMetaDataMapping()
    {
        RentMy::addModel(['Categories', 'Stores']);
        $storeIds = RentMy::$Model['Stores']->find('list', [
            'valueField' => 'id'
        ])->order(['id' => "DESC"])->toList();
        foreach ($storeIds as $storeId) {
            $categories = RentMy::$Model['Categories']->find()->select(['id', 'url'])->where(['store_id' => $storeId])->toArray();
            foreach ($categories as $category) {
                $category = RentMy::$Model['Categories']->find()->where(['id' => $category->id])->first();
                $options = !empty($category->options) ? json_decode($category->options, true) : [];
                if (empty($options["seo"])) {
                    $options["seo"] = [
                        "meta_title" => $category->name,
                        "meta_description" => strip_tags($category->description),
                    ];
                }
                $category->options = json_encode($options);
                RentMy::$Model['Categories']->save($category);
            }
            $this->out("category meta data updated for ==> store id:" . $storeId);
        }
    }

    /**
     * Updates the parent_id of a category node
     */
    public function updateParent($childId, $parentId = null)
    {
        $child = $this->Categories->get($childId);

        // Set new parent_id (null makes it a root node)
        $child->parent_id = $parentId ?: null;

        if ($this->Categories->save($child)) {
            $this->out("Parent updated successfully for category ID {$childId}.");
        } else {
            $this->err("Failed to update parent for category ID {$childId}.");
        }
    }

    public function resetAndUpdateParentIds($storeId)
    {
        // Step 1: Set all parent_id to NULL using raw SQL
        $connection = ConnectionManager::get('default');
        $connection->execute('UPDATE categories SET parent_id = NULL WHERE parent_id=0 AND store_id = :store_id', [
            'store_id' => $storeId,
        ]);
        $this->out("✅ All parent_id values set to NULL for store #$storeId.");

        // Step 2: Update parent_id using CakePHP logic
        $this->Categories->find()->where(['store_id' => $storeId])->each(function ($category) {
            if (empty($category->parent_id)) {
                $this->updateParent($category->id);
            }

        });

        $connection->execute('UPDATE categories SET parent_id = 0 WHERE parent_id=NULL AND store_id = :store_id', [
            'store_id' => $storeId,
        ]);
        $this->out("✅ Parent ID update completed.");
    }


    /**
     * Recovers the tree structure (recalculates lft/rght)
     */
    public function recoverTree()
    {
        try {
            if ($this->Categories->recover()) {
                $this->out("Tree recovery successful.");
            } else {
                $this->err("Tree recovery failed.");
                // Look for broken parent_ids
                $isBroken = $this->checkBrokenParents();
                if ($isBroken) {
                    $this->err("⚠️ Broken parent_id references found. Attempting to fix...");
                    $this->fixInvalidParents();
                    $this->out("✅ Invalid parent_id references fixed.");
                } else {
                    $this->out("No broken parent_id references found.");
                }
            }
        } catch (\Exception $e) {
            $this->err("Exception during recovery: " . $e->getMessage());
        }

    }


    public function checkBrokenParents()
    {
        $connection = ConnectionManager::get('default');

        $query = "
        SELECT c.id, c.parent_id
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.parent_id IS NOT NULL AND p.id IS NULL
    ";

        $results = $connection->execute($query)->fetchAll('assoc');

        if (empty($results)) {
            $this->out("✅ No broken parent_id references found.");
            return false;
        } else {
            $this->err("⚠️ Found categories with invalid parent_id:");
            foreach ($results as $row) {
                $this->err(" - Category ID {$row['id']} has invalid parent_id: {$row['parent_id']}");
            }
            return true;
        }
    }

    public function fixInvalidParents()
    {
        $affected = $this->Categories->query()
            ->update()
            ->set(['parent_id' => null])
            ->where(['parent_id' => 0])
            ->execute()
            ->rowCount();

        $this->out("Fixed {$affected} rows where parent_id was 0.");
    }

    public function restoreToZero()
    {
        $affected = $this->Categories->query()
            ->update()

            ->set(['parent_id' => 0])
            ->where(['parent_id IS NULL'])
            ->execute()
            ->rowCount();

        $this->out("Restores {$affected} rows where parent_id was NUll.");
    }



    public function recover()
    {
        $isBroken = $this->checkBrokenParents();
        if ($isBroken) {
            $this->err("Broken parent_id references found. Attempting to fix...");
            $this->fixInvalidParents();
            $this->out("Invalid parent_id references fixed.");
        } else {
            $this->out("No broken parent_id references found.");
        }
        // In a shell
//        $this->Categories = TableRegistry::getTableLocator()->get('Categories');
        $this->Categories->recover('parent');

        $this->restoreToZero();
    }

}
