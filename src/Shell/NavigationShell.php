<?php

namespace App\Shell;

use App\Lib\RentMy\RentMy;
use Cake\Console\Shell;

class NavigationShell extends Shell
{

    public function addBlogToNavigation()
    {
        RentMy::addModel(['Stores', 'Navigations']);
        $stores = RentMy::$Model['Stores']->find('list', [
            'valueField' => 'id',
        ])->toList();
        RentMy::$Model['Navigations']->deleteAll([
            'content_url' => 'blog'
        ]);
        $page = ['content_url' => 'blog', 'label' => 'Blog', 'content_type' => 'Page', 'type' => 'header', 'status' => 1, 'sequence_no' => 4];
        foreach ($stores as $storeId){
            RentMy::addModel(['Locations', 'Contents', 'Stores']);
            $locations = RentMy::$Model['Locations']->find('list', [
                'valueField' => 'id',
            ])->where(['store_id'=>$storeId])->toList();
            foreach ($locations as $location){
                $content = RentMy::$Model['Navigations']->find()->where(['location'=>$location, 'store_id' => $storeId, 'content_url' => $page['content_url']])->first();
                $data['store_id'] = $storeId;
                $data['content_id'] = 0;
                $data['content_type'] = $page['content_type'];
                $data['label'] = $page['label'];
                $data['content_url'] = $page['content_url'];
                $data['status'] = $page['status'];
                $data['sequence_no'] = $page['sequence_no'];
                $data['type'] = $page['type'];
                $data['location'] = $location;
                $content = RentMy::$Model['Navigations']->newEntity();
                $content = RentMy::$Model['Navigations']->patchEntity($content, $data);
                RentMy::$Model['Navigations']->save($content);
            }
            $this->out("blog navigation updated for store $storeId");
        }


    }

    public function blogNavigationLabelRestore()
    {
        RentMy::addModel(['Navigations', 'Stores']);
        $storeIds = RentMy::$Model['Stores']->find('list', [
            'valueField' => 'id'
        ])->order(['id' => "DESC"])->toList();
        foreach ($storeIds as $storeId) {
            $navigation = RentMy::$Model['Navigations']->find()->where(['store_id' => $storeId, 'content_url' => 'blog', 'label' => 'Catalog'])->first();
            if (!empty($navigation)){
                $navigation->label = 'Blog';
                if (RentMy::$Model['Navigations']->save($navigation)) {
                    $this->out("Navigation label updated for store id: " . $storeId);
                } else {
                    $this->out("Failed to update Navigation label for store id: " . $storeId);
                }
            }
        }
    }
}
