<?php

namespace App\Shell;

use App\Controller\AppController;
use App\Lib\Payment\Payment;
use App\Lib\Payment\Stripe\Invoice;
use App\Lib\RentMy\RentMy;
use Cake\Collection\Collection;
use Cake\Console\Shell;
use Cake\Core\Configure;
use Cake\Core\Exception\Exception;
use Cake\Filesystem\Folder;
use Queue\Model\QueueException;

class  CustomerShell extends Shell
{
    public function main()
    {

//        $this->loadModel('Customers');
//        $this->loadModel('Orders');
//        $orders = $this->Orders->find()->order(['id' => 'ASC'])->toArray();
//        $appController = new AppController();
//        foreach ($orders as $order) {
//            $customer = $this->Customers->find()
//                ->where(['email' => $order->email, 'store_id' => $order->store_id])
//                ->first();
//            if (empty($customer)) {
//                if (!empty($order->email)) {
//                    $data = [
//                        'store_id' => $order->store_id,
//                        'first_name' => $order->first_name,
//                        'last_name' => $order->last_name,
//                        'phone' => $order->phone,
//                        'mobile' => $order->mobile,
//                        'email' => $order->email,
//                        'password' => md5($appController->randomnum(10)),
//                        'social_type' => 'Email',
//                        'status' => 1
//                    ];
//                    $customer = $this->Customers->newEntity();
//                    $customer = $this->Customers->patchEntity($customer, $data);
//                    $this->Customers->save($customer);
//                    $order->customer_id = $customer->id;
//                    $this->Orders->save($order);
//                }
//
//            } else {
//                $order->customer_id = $customer->id;
//                $this->Orders->save($order);
//            }
//            print_r("<pre>");
//            print_r('order : ' . $order->id . '...' . 'Customer:' . $customer->id);
//            print_r("</pre>");
//        }
        $this->campEasyLLc();
    }

    /** Save all customer address to customer_address table */
    function address()
    {
        $this->loadModel('Customers');
        $this->loadModel('Orders');
        $this->loadModel('CustomerAddresses');
        $orders = $this->Orders->find()->order(['id' => 'ASC'])->toArray();
        foreach ($orders as $order) {
            if (!empty($order->customer_id)) {
                $cus_address = $this->CustomerAddresses->find()
                    ->where(['customer_id' => $order->customer_id])
                    ->count();
                if (empty($cus_address)) {
                    $this->CustomerAddresses->_customerAddress($order);
                }
            }
        }
    }

    /** Save existing order address to orders_address db table */
    function order()
    {
        $this->loadModel('Orders');
        $this->loadModel('OrderAddresses');
        $orders = $this->Orders->find()->where()->order(['id' => 'ASC'])->toArray();
        foreach ($orders as $order) {
            $address = $this->OrderAddresses->find()->where(['order_id' => $order->id])->first();
            if (empty($address)) {
                $data = [
                    'order_id' => $order->id,
                    'store_id' => $order->store_id,
                    'city' => $order->city,
                    'state' => $order->state,
                    'zipcode' => $order->zipcode,
                    'country' => $order->country,
                    'address_line1' => $order->address_line1,
                    'phone' => $order->phone,
                    'mobile' => $order->mobile,
                    'email' => $order->email,
                    'shipping_address1' => $order->shipping_address1,
                    'shipping_address2' => $order->shipping_address2,
                    'shipping_city' => $order->shipping_city,
                    'shipping_state' => $order->shipping_state,
                    'shipping_zipcode' => $order->shipping_zipcode,
                    'shipping_first_name' => $order->shipping_first_name,
                    'shipping_last_name' => $order->shipping_last_name,
                    'shipping_mobile' => $order->shipping_mobile,
                    'shipping_country' => $order->shipping_country
                ];
                $address = $this->OrderAddresses->newEntity();
                $address = $this->OrderAddresses->patchEntity($address, $data);
                $this->OrderAddresses->save($address);

            }
        }

    }

    function country()
    {
        Configure::write('debug', 2);
        $this->loadModel('Orders');
        $this->loadModel('OrderAddresses');
        $orders = $this->Orders->find()->where()->order(['id' => 'ASC'])->toArray();
        foreach ($orders as $order) {
            $address = $this->OrderAddresses->find()->where(['order_id' => $order->id])->first();
            if (!empty($address)) {
                //pr($address);
                $address->country = $order->country_id;
                echo '..........';
                pr($address->country . '...' . $order->country_id . '...' . $order->id);
                echo '............';
                $this->OrderAddresses->save($address);
            }
        }

    }


    function funtimeCustomers()
    {
        $store_id = 577;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . 'customers.xls';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }
            return $values;
        });
        $sheetData->groupBy('email')
            ->each(function ($cusData, $key) use ($store_id) {
                if (!empty($key)) { // email exist
                    // get customer ID from email
                    $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($key), 'store_id' => $store_id])->first();
                    // get existing address
                    if (!empty($customer)) {
//                        $count = RentMy::$Model['CustomerAddresses']->find()->where(['store_id' => $store_id, 'customer_id' => $customer->id])->count();
                        // add first address as Primary
                        //RentMy::dbg($cusData);
                        foreach ($cusData as $i => $cus) {
                            if (!empty($cus['address']) && !empty(!empty($cus['city'])) && !empty(!empty($cus['state']))) {
                                $mobile = str_replace('(', '', $cus['home']);
                                $mobile = str_replace(')', '', $mobile);
                                $mobile = str_replace('-', '', $mobile);

                                $phone = str_replace('(', '', $cus['cell']);
                                $phone = str_replace(')', '', $phone);
                                $phone = str_replace('-', '', $phone);


                                $customerAddress = [
                                    'customer_id' => $customer->id,
                                    'store_id' => $store_id,
                                    'is_primary' => ($i == 0) ? 1 : 0,
                                    'type' => ($i == 0) ? 'Primary' : 'Shipping',
                                    'city' => $cus['city'] ?? '',
                                    'zipcode' => $cus['zip'] ?? '',
                                    'state' => strtoupper($cus['state']) ?? '',
                                    'country' => 'US',
                                    'mobile' => $mobile ?? '',
                                    'phone' => $phone ?? '',
                                    'address_line1' => $cus['address'] ?? ''
                                ];
                                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                                $this->out('customer_id:' . $customer->id);
                            }
                        }

                        // save other address as Shipping
                    }
                } else {

                }
                // RentMy::dbg($key);
            });
        //   $sheetData->groupBy('email');
        //  RentMy::dbg($sheetData->toArray());

        //  exit();
        //$collection = new Collection($sheet);
        //RentMy::dbg($sheet);

    }

    /**
     * without email
     */
    function funtimeECustomers()
    {

        $store_id = 577;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . 'customer_without_email.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }
            return $values;
        });
        $sheetData->groupBy('name')
            ->each(function ($cusData, $key) use ($store_id) {
                if (!empty($key)) { // email exist
                    // get customer ID from email
                    $customer = RentMy::$Model['Customers']->find()->where(['first_name' => trim($key), 'store_id' => $store_id])->first();
                    // get existing address
                    if (!empty($customer)) {
//                        $count = RentMy::$Model['CustomerAddresses']->find()->where(['store_id' => $store_id, 'customer_id' => $customer->id])->count();
                        // add first address as Primary
                        //RentMy::dbg($cusData);
                        foreach ($cusData as $i => $cus) {
                            if (!empty($cus['address']) && !empty(!empty($cus['city'])) && !empty(!empty($cus['state']))) {
                                $mobile = str_replace('(', '', $cus['home']);
                                $mobile = str_replace(')', '', $mobile);
                                $mobile = str_replace('-', '', $mobile);

                                $phone = str_replace('(', '', $cus['cell']);
                                $phone = str_replace(')', '', $phone);
                                $phone = str_replace('-', '', $phone);


                                $customerAddress = [
                                    'customer_id' => $customer->id,
                                    'store_id' => $store_id,
                                    'is_primary' => ($i == 0) ? 1 : 0,
                                    'type' => ($i == 0) ? 'Primary' : 'Shipping',
                                    'city' => $cus['city'] ?? '',
                                    'zipcode' => $cus['zip'] ?? '',
                                    'state' => strtoupper($cus['state']) ?? '',
                                    'country' => 'US',
                                    'mobile' => $mobile ?? '',
                                    'phone' => $phone ?? '',
                                    'address_line1' => $cus['address'] ?? ''
                                ];
                                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                                $this->out('customer_id:' . $customer->id);
                            }
                        }

                        // save other address as Shipping
                    }
                } else {

                }
            });


    }

    function funtimeCustomersName()
    {
        $store_id = 577;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customers = RentMy::$Model['Customers']->find()->where(['store_id' => $store_id])->toArray();
        foreach ($customers as $customer) {
            $exp_name = explode(' ', $customer['first_name']);
            $customer->first_name = $exp_name[0];
            $lastnameArray = [];
            foreach ($exp_name as $j => $e) {
                if ($j > 0) {
                    $lastnameArray[] = trim($e);
                }
            }
            if (!empty($lastnameArray)) {
                $customer->last_name = trim(implode(' ', $lastnameArray));
            }
            RentMy::$Model['Customers']->save($customer);
            $this->out($customer->id);
        }
    }

    public function laundry()
    {
        $store_id = 589;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '589_customer.xls';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }

        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            if (!empty($values[0])) {
                foreach ($values as $i => $value) {
                    $values[$indices[$i]] = trim($value);
                    unset($values[$i]);
                }
                return $values;
            }
        });
        $customers = $sheetData->toArray();
        foreach ($customers as $customer) {
            if (!empty($customer['email'])) {
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    'phone' => $customer['other_phone'],
                    'mobile' => str_replace('-', '', $customer['phone']),
                    'email' => $customer['email'],
                    'password' => md5(RentMy::randomNum(8)),
                    'social_type' => 'email',
                    'status' => 1,
                    'optional' => json_encode([
                        'customer_number' => $customer['customer_number'],
                        'active' => $customer['active'],
                        'emailstatement' => $customer['emailstatement'],
                        'current_balance' => $customer['current_balance'],
                        'ss#' => $customer['ss#'],
                        'previous_address' => $customer['previous_address'],
                        'customer_type' => $customer['customer_type'],
                        'automatic_credit_card' => $customer['automatic_credit_card'],
                        'bad_address' => $customer['bad_address'],
                        'firstinstalldate' => $customer['firstinstalldate'],
                        'apartment_complex2' => $customer['apartment_complex2'],
                        'deposit' => $customer['deposit'],
                        'branch' => $customer['branch'],
                        'new_customer_number' => $customer['new_customer_number'],
                        'insurance' => $customer['insurance'],
                        'textenabled' => $customer['textenabled'],
                        'ipaddress' => $customer['ipaddress'],
                        'carrieremail' => $customer['carrieremail'],
                        'branchid' => $customer['branchid']

                    ])
                ];
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['city'],
                    'zipcode' => $customer['zip/postal_code'],
                    'state' => strtoupper($customer['state']),
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['phone']),
                    'phone' => $customer['other_phone'],
                    'address_line1' => $customer['street'],
                    'address_line2' => $customer['suite'],
                    'fax' => $customer['fax']
                ];
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out('customer_id:' . $customer_id);
            }
        }
    }

    /**
     * Camp Easy LLC ( id : 2424)
     */
    public function campEasyLLc()
    {
        $store_id = 2424;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '2424_customers_import.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", trim($field));
            $indices[$ix] = strtolower($field);
        }

        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            if (!empty($values[0])) {
                foreach ($values as $i => $value) {
                    $values[$indices[$i]] = trim($value);
                    unset($values[$i]);
                }
                return $values;
            }
        });
        $customers = $sheetData->toArray();
        foreach ($customers as $customer) {

            if (!empty($customer['email'])) {
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    'phone' => $customer['phone'],
                    'mobile' => str_replace('-', '', $customer['cell']),
                    'email' => $customer['email'],
                    'password' => md5(RentMy::randomNum(8)),
                    'social_type' => 'email',
                    'status' => 1,
                    'optional' => json_encode([
                        'emergency_contact' => $customer['emergency_contact'] ?? '',
                        'rv_name' => $customer['rv_name'] ?? '',
                        'year' => $customer['year'] ?? '',
                        'make' => $customer['make'] ?? '',
                        'model' => $customer['model'] ?? '',
                        'plate' => $customer['plate'] ?? '',
                        'length' => $customer['length'] ?? '',
                        'amp' => $customer['amp'] ?? '',
                        'address' => $customer['address'] ?? '',
                    ])
                ];
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;

                $customerAddr = explode(',', trim($customer['address']));
                if (empty($customer['address']) || count($customerAddr) == 0)
                    continue;

                if (count($customerAddr) == 3) {
                    $customer['address_line1'] = $customerAddr[0];
                    $customer['city'] = $customerAddr[1];
                    if (!empty($customerAddr[2])) {
                        $stateAndZip = explode(' ', trim($customerAddr[2]));
                        if (count($stateAndZip) == 2) {
                            $customer['state'] = $stateAndZip[0];
                            $customer['zipcode'] = $stateAndZip[1];
                        } else {
                            $customer['state'] = $stateAndZip[0];
                        }
                    }
                } else {
                    $customer['address_line1'] = $customerAddr[0];
                }


                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['city'] ?? '',
                    'zipcode' => $customer['zipcode'] ?? '',
                    'state' => strtoupper($customer['state'] ?? ''),
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['cell']),
                    'phone' => str_replace('-', '', $customer['phone']),
                    'email' => $customer['email'],
                    'address_line1' => $customer['address_line1'] ?? '',
                ];

                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out('customer_id:' . $customer_id);
            }
        }
    }


    /** Load carpio customers */
    public function carpio()
    {
        $store_id = 1117;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '1117_customer.xls';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }

        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            if (!empty($values[0])) {
                foreach ($values as $i => $value) {
                    $values[$indices[$i]] = trim($value);
                    unset($values[$i]);
                }
                return $values;
            }
        });
        $customers = $sheetData->toArray();
        foreach ($customers as $customer) {
            if (!empty($customer['email'])) {
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    // 'phone' => $customer['other_phone'],
                    'mobile' => str_replace('-', '', $customer['phone_number']),
                    'company' => $customer['company_name'],
                    'email' => $customer['email'],
                    'password' => md5('Royal'),
                    'social_type' => 'email',
                    'status' => 1,
                    'optional' => json_encode([
                        'job_title' => $customer['job_title'],
                        'industry' => $customer['industry'],
                        'regional_manager' => $customer['regional_manager']
                        //  'last_activity_date' => $customer['last_activity_date']

                    ])
                ];
                // RentMy::dbg($customerData);
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['city'],
                    'state' => strtoupper($customer['state/region']),
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['phone_number']),
                    'address_line1' => $customer['street_address'],
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out('customer_id:' . $customer_id);
            }
        }
    }

    /** Load carpio customers */
    public function shallWePlay()
    {
        $store_id = 431;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '431_customer.xls';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }

        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            if (!empty($values[0])) {
                foreach ($values as $i => $value) {
                    $values[$indices[$i]] = trim($value);
                    unset($values[$i]);
                }
                return $values;
            }
        });
        $customers = $sheetData->toArray();
        foreach ($customers as $customer) {
            if (!empty($customer['email'])) {
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    // 'phone' => $customer['other_phone'],
                    'mobile' => str_replace('-', '', $customer['phone']),
                    'company' => $customer['business_name'],
                    'email' => $customer['email'],
                    'password' => md5('shall_we_play2022'),
                    'social_type' => 'email',
                    'status' => 1,
                    'optional' => json_encode([
                        'user_id' => $customer['user_id'],
                        'id' => $customer['id'],
                        'note' => $customer['note'],
                        'driving_license_expiry_date' => $customer['driving_license_expiry_date'],
                        'customer_driving_license' => $customer['customer_driving_license'],
                        'customer_dateofbirth' => $customer['customer_dateofbirth'],
                        'tax_exception' => $customer['tax_exception']

                    ])
                ];
                // RentMy::dbg($customerData);
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['billing_city'],
                    'state' => strtoupper($customer['billing_state']),
                    'zipcode' => $customer['billing_zip'],
                    'address_line1' => $customer['billing_street'],
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['phone']),
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out('customer_id:' . $customer_id);
            }
        }
    }

    /** Load carpio customers */
    public function laundry591()
    {
        $store_id = 591;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '591_customers.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }

        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            if (!empty($values[0])) {
                foreach ($values as $i => $value) {
                    $values[$indices[$i]] = trim($value);
                    unset($values[$i]);
                }
                return $values;
            }
        });
        $customers = $sheetData->toArray();
        // print_r("<pre>");print_r($customers);print_r("</pre>");exit();
        foreach ($customers as $customer) {
            if (!empty($customer['email'])) {
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    'phone' => $customer['other_phone'],
                    'mobile' => str_replace('-', '', $customer['phone']),
                    'email' => $customer['email'],
                    'password' => md5('shall_we_play2022'),
                    'social_type' => 'email',
                    'status' => $customer['status'],
                    'company' => $customer['companyname'],
                    'optional' => json_encode([
                        'emailstatement' => $customer['emailstatement'],
                        'current_balance' => $customer['current_balance'],
                        'ss#' => !empty($customer['ss#']) ? $customer['ss#'] : '',
                        'previous_address' => $customer['previous_address'],
                        'customer_type' => $customer['customer_type'],
                        'automatic_credit_card' => $customer['automatic_credit_card'],
                        'bad_address' => $customer['bad_address'],
                        'firstinstalldate' => $customer['firstinstalldate'],
                        'apartment_complex2' => !empty($customer['apartment_complex2']) ? $customer['apartment_complex2'] : '',
                        'branch' => !empty($customer['branch']) ? $customer['branch'] : '',
                        'new_customer_number' => !empty($customer['new_customer_number']) ? $customer['new_customer_number'] : '',
                        'insurance' => !empty($customer['insurance']) ? $customer['insurance'] : '',
                        'textenabled' => !empty($customer['textenabled']) ? $customer['textenabled'] : '',
                        'ipaddress' => !empty($customer['ipaddress']) ? $customer['ipaddress'] : '',
                        'contractdatestamp' => !empty($customer['contractdatestamp']) ? $customer['contractdatestamp'] : '',
                        'carrierEmail' => !empty($customer['carrieremail']) ? $customer['carrieremail'] : '',
                        'branchId' => !empty($customer['branchid']) ? $customer['branchid'] : '',
                        'ownerBid' => !empty($customer['ownerbid']) ? $customer['ownerbid'] : '',
                        'companyPhone' => !empty($customer['companyphone']) ? $customer['companyphone'] : '',
                        'communityZip' => !empty($customer['communityzip']) ? $customer['communityzip'] : '',
                        'washer_al#' => !empty($customer['washer_al##']) ? $customer['washer_al##'] : ''

                    ])
                ];
                // RentMy::dbg($customerData);
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['city'],
                    'state' => strtoupper($customer['state']),
                    'zipcode' => $customer['zip'],
                    'address_line1' => $customer['street'],
                    'address_line2' => $customer['suite'],
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['phone']),
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out('customer_id:' . $customer_id);
            }
        }
    }

    /** Load carpio customers */
    public function tikestoybox()
    {
        $store_id = 2101;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '2101_customers.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];
        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }

        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            if (!empty($values[0])) {
                foreach ($values as $i => $value) {
                    $values[$indices[$i]] = trim($value);
                    unset($values[$i]);
                }
                return $values;
            }
        });
        $customers = $sheetData->toArray();
        // print_r("<pre>");print_r($customers);print_r("</pre>");exit();
        foreach ($customers as $customer) {
            if (!empty($customer['email'])) {
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    // 'phone' => $customer['other_phone'],
                    'mobile' => self::parsePhone($customer['phone']),
                    'email' => $customer['email'],
                    'password' => md5('RentMy2022'),
                    'social_type' => 'email',
                    'status' => 1,
                    // 'company' => $customer['companyname'],

                ];
                // RentMy::dbg($customerData);
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['city'],
                    'state' => strtoupper($customer['state']),
                    'zipcode' => $customer['zip'],
                    'address_line1' => $customer['billing_address'],
                    'country' => 'US',
                    'mobile' => self::parsePhone($customer['phone'])
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Shipping',
                    'city' => $customer['delivery_city'],
                    'state' => strtoupper($customer['delivery_state']),
                    'zipcode' => $customer['delivery_zip'],
                    'address_line1' => $customer['delivery_address'],
                    'country' => 'US',
                    'mobile' => self::parsePhone($customer['phone'])
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out('customer_id:' . $customer_id);
            }
        }
    }

    /** Load carpio customers */
    public function americanRiver2067()
    {
        ini_set('memory_limit', '4024M');
        $store_id = 2067;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '2067_customers.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];

        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {

            //  if (!empty($values[0])) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }
            return $values;
            //  }
        });
        $customers = $sheetData->toArray();
        //   print_r("<pre>");print_r($customers);print_r("</pre>");exit();
        foreach ($customers as $key => $customer) {
            //if($key==5) exit();
            //$customer['email'] = $customer;
            //$customer['email'] = ($key - 1) . '@americanriverraftrentals.com';
            if (!empty($customer['email'])) {
                $exp_name = explode(' ', $customer['company_name']);
                $firstname = $exp_name[0];
                unset($exp_name[0]);
                $lastname = (count($exp_name) > 0) ? implode(" ", $exp_name) : $exp_name[1];
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $firstname,
                    'last_name' => $lastname,
                    //'phone' => $customer['other_phone'],
                    'mobile' => str_replace('-', '', $customer['phone']),
                    'email' => $customer['email'],
                    'password' => md5($customer['email']),
                    'social_type' => 'email',
                    'status' => 1,
                    'optional' => json_encode([
                        'key' => $customer['key'],
                        'num' => $customer['num']
                    ])
                ];
                // RentMy::dbg($customerData);
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $exp_city = explode(',', $customer['city']);
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $exp_city[0],
                    'state' => strtoupper($exp_city[0]),
                    'zipcode' => $customer['postal_code'],
                    'address_line1' => $customer['street'],
                    // 'address_line2' => $customer['suite'],
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['phone']),
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out($key . ': ' . 'customer_id:' . $customer_id);
            }
        }
    }

    /** Load carpio customers */
    public function campEZ()
    {
        ini_set('memory_limit', '4024M');
        $store_id = 2424;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '2424_customers.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];

        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);
        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {

            //  if (!empty($values[0])) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }
            return $values;
            //  }
        });
        $customers = $sheetData->toArray();
        //   print_r("<pre>");print_r($customers);print_r("</pre>");exit();
        foreach ($customers as $key => $customer) {
            //if($key==5) exit();
            //$customer['email'] = $customer;
            //$customer['email'] = ($key - 1) . '@americanriverraftrentals.com';
            if (!empty($customer['email'])) {
                //$exp_name = explode(' ', $customer['company_name']);
                //$firstname = $exp_name[0];
                // unset($exp_name[0]);
                // $lastname = (count($exp_name) > 0) ? implode(" ", $exp_name) : $exp_name[1];
                $customerData = [
                    'store_id' => $store_id,
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    'phone' => str_replace('-', '', $customer['cell_phone']),
                    'mobile' => str_replace('-', '', $customer['home_phone']),
                    'email' => $customer['email'],
                    'password' => md5(RentMy::randomNum(5) . $customer['email']),
                    'social_type' => 'email',
                    'status' => 1
                ];
                // RentMy::dbg($customerData);
                $cusObj = RentMy::$Model['Customers']->find()
                    ->where(['store_id' => $store_id, 'email' => trim($customer['email'])])
                    ->first();
                if (empty($cusObj)) {
                    $cusObj = RentMy::$Model['Customers']->newEntity();
                }
                $cusObj = RentMy::$Model['Customers']->patchEntity($cusObj, $customerData);
                RentMy::$Model['Customers']->save($cusObj);
                $customer_id = $cusObj->id;
                $exp_city = explode(',', $customer['city']);
                $customerAddress = [
                    'customer_id' => $customer_id,
                    'store_id' => $store_id,
                    'is_primary' => 1,
                    'type' => 'Primary',
                    'city' => $customer['city'],
                    'state' => strtoupper($customer['state']),
                    'zipcode' => $customer['zip'],
                    'address_line1' => $customer['address'],
                    // 'address_line2' => $customer['suite'],
                    'country' => 'US',
                    'mobile' => str_replace('-', '', $customer['home_phone']),
                ];
                //RentMy::dbg($customerAddress);
                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                $this->out($key . ': ' . 'customer_id:' . $customer_id);
            }
        }
    }

    private function parsePhone($phone)
    {
        $phone = str_replace('-', '', $phone);
        $phone = str_replace('(', '', $phone);
        $phone = str_replace(')', '', $phone);
        $phone = str_replace(' ', '', $phone);
        return $phone;
    }

    private function formatExcel($file, $sheetIndex = 0)
    {
        $sheet_data = array();
        require_once ROOT . DS . 'vendor' . DS . 'leaperdev' . DS . 'phpexcel' . DS . 'PHPExcel.php';
        require_once ROOT . DS . 'vendor' . DS . 'leaperdev' . DS . 'phpexcel' . DS . 'PHPExcel' . DS . 'IOFactory.php';
        $excel_obj = \PHPExcel_IOFactory::createReaderForFile($file);
        $excel_obj->setReadDataOnly(true);
        $obj_excel = $excel_obj->load($file);
        $worksheet = $obj_excel->getSheet($sheetIndex);
        foreach ($worksheet->getRowIterator() as $i => $row) {
            //if ($i < 10) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            foreach ($cellIterator as $j => $cell) {
                $sheet_data[$i][$j] = $newValue = strip_tags($cell->getValue());
            }
            // }
        }
        return $sheet_data;
    }


    /**
     * Shell script to import customer excel file using ftp
     * File name must be in the format of store_id_file_name
     */
    public function jobUploadFtp()
    {
        RentMy::addModel(['Queue.QueuedJobs']);
        $ftp_path = WWW_ROOT . 'upload' . DS . 'excel' . DS . 'ftp';
        $dir = new Folder($ftp_path);
        $files = $dir->find('.*\.xls|.*\.xlsx', true);
        RentMy::dbg($files);
        foreach ($files as $file) {
            $explode = explode('_', $file);
            if (!empty($explode[0])) {
                $store_id = $explode[0];
                RentMy::getStore($store_id);
                $job = RentMy::$Model['Queue.QueuedJobs']->createJob('ImportCustomer', ['type' => 'import_ftp', 'file' => $file, 'store_id' => RentMy::$store->id],
                    ['reference' => RentMy::$store->id]);
            }

        }
    }


    public function importFtp()
    {
        $jobId = 84716;
        $store_id = 1550;
        RentMy::addModel(['Queue.QueuedJobs']);
        $job = RentMy::$Model['Queue.QueuedJobs']->get($jobId);
        $data = unserialize($job->data);

        RentMy::getStore($data['store_id']);
        try {
            $settings = RentMy::$storeConfig;
            RentMy::addModel(['CustomerAddresses', 'Customers']);
            echo $ftp_path = WWW_ROOT . 'upload' . DS . 'excel' . DS . 'ftp' . DS;
            RentMy::dbg($data);
            if (empty($data['file'])) {
                $data['failure_message'] = 'Missing Filename';
                // $this->failedJob($jobId, $data);
                return false;
            } else if (!(file_exists($ftp_path . $data['file']))) {
                $data['failure_message'] = 'Uploaded file missing.';
                //$this->failedJob($jobId, $data);
                return false;
            } else {
                $customersExcel = $ftp_path . $data['file'];
                $sheetData = $this->formatExcel($customersExcel);
                $indices = [];

                foreach ($sheetData[1] as $ix => $field) {
                    $field = str_replace(" ", "_", $field);
                    $indices[$ix] = strtolower($field);
                }
                unset($sheetData[1]);
                $sheetData = new Collection($sheetData);
                $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
                    foreach ($values as $i => $value) {
                        $values[$indices[$i]] = trim($value);
                        unset($values[$i]);
                    }
                    return $values;
                });

                $sheetData->groupBy('email')
                    ->each(function ($cusData, $key) use ($store_id) {
                        if (!empty($key)) { // email exist
                            // get customer ID from email
                            $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($key), 'store_id' => $store_id])->first();
                            if (empty($customer)) {
                                $customer = RentMy::$Model['Customers']->newEntity();
                            }
                            foreach ($cusData as $i => $cus) {
                                $phone = str_replace('(', '', $cus['phone']);
                                $phone = str_replace(')', '', $phone);
                                $phone = str_replace('-', '', $phone);
                                $customer_data = [
                                    'first_name' => $cus['first_name'],
                                    'last_name' => $cus['last_name'],
                                    'phone' => $phone,
                                    'email' => $cus['email'],
                                    'password' => md5('MSU' . time()),
                                    'social_type' => 'sso',
                                    'store_id' => RentMy::$store->id,
                                    'status' => 1,
                                    'optional' => json_encode([
                                        'role' => $cus['role'],
                                        'member' => $cus['member'],
                                        //'gid' => str_replace('-', '', $cus['gid'])
                                        'gid' => $cus['id']

                                    ])
                                ];
                                //RentMy::dbg($customer_data);
                                $customer = RentMy::$Model['Customers']->patchEntity($customer, $customer_data);
                                RentMy::$Model['Customers']->save($customer);
                                $this->out('customer id : ' . $customer->id);
                            }

                        }
                    });
            }

        } catch (\Exception $e) {

        }
    }

    /**
     * Blue Skies Drone Rental
     */
    function blueskiesdron()
    {
        $store_id = 1279;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . 'Blue_Skies_customer_list.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];

        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);

        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }
            return $values;
        });

        $sheetData->groupBy('email')
            ->each(function ($cusData, $key) use ($store_id) {
                if (!empty($key)) { // email exist
                    // get customer ID from email
                    $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($key), 'store_id' => $store_id])->first();
                    // new entry if not exist
                    if (empty($customer)) {
                        $info = $cusData[0];
//                        RentMy::dbg($info);
                        $mobile = str_replace('(', '', $info['phone']);
                        $mobile = str_replace(')', '', $mobile);
                        $mobile = str_replace('-', '', $mobile);

                        $phone = str_replace('(', '', $info['other_phone']);
                        $phone = str_replace(')', '', $phone);
                        $phone = str_replace('-', '', $phone);

                        $data = [
                            'type' => 1,
                            'store_id' => $store_id,
                            'first_name' => $info['first_name'],
                            'last_name' => $info['last_name'],
                            'mobile' => $mobile ?? '',
                            'phone' => $phone ?? '',
                            'email' => $info['email'],
                            'password' => (new \Cake\Auth\DefaultPasswordHasher)->hash('blueskies'),

                        ];

                        $newCustomer = RentMy::$Model['Customers']->newEntity($data);
                        $customer = RentMy::$Model['Customers']->save($newCustomer);
                        // $this->out('new customer_id:' . $customer->id);
                    }
                    // store address
                    if (!empty($customer)) {
                        // add first address as Primary
                        foreach ($cusData as $i => $cus) {
                            if (!empty($cus['street']) && !empty(!empty($cus['city'])) && !empty(!empty($cus['state']))) {
                                $mobile = str_replace('(', '', $cus['phone']);
                                $mobile = str_replace(')', '', $mobile);
                                $mobile = str_replace('-', '', $mobile);

                                $phone = str_replace('(', '', $cus['other_phone']);
                                $phone = str_replace(')', '', $phone);
                                $phone = str_replace('-', '', $phone);


                                $customerAddress = [
                                    'customer_id' => $customer->id,
                                    'store_id' => $store_id,
                                    'is_primary' => ($i == 0) ? 1 : 0,
                                    'type' => ($i == 0) ? 'Primary' : 'Others',
                                    'city' => $cus['city'] ?? '',
                                    'zipcode' => $cus['zip/postal_code'] ?? '',
                                    'state' => $cus['state'] ?? '',
                                    'country' => 'US',
                                    'mobile' => $mobile ?? '',
                                    'phone' => $phone ?? '',
                                    'address_line1' => $cus['street'] ?? ''
                                ];
                                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                                $this->out('customer_id:' . $customer->id);
                            }
                        }
                    }
                }
            });

    }
    /**
     *
     */
    function amuzing()
    {
        $store_id = 3695;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . 'amuzing_customer_list.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];

        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);

        $sheetData = new Collection($sheetData);
        $sheetData = $sheetData->map(function ($values, $key) use ($indices) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }
            return $values;
        });

        $sheetData->groupBy('email')
            ->each(function ($cusData, $key) use ($store_id) {
                if (!empty($key)) { // email exist
                    // get customer ID from email
                    $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($key), 'store_id' => $store_id])->first();
                    // new entry if not exist
                    if (empty($customer)) {
                        $info = $cusData[0];
//                        RentMy::dbg($info);
                        $mobile = str_replace('(', '', $info['phone']);
                        $mobile = str_replace(')', '', $mobile);
                        $mobile = str_replace('-', '', $mobile);


                        $data = [
                            'type' => 1,
                            'store_id' => $store_id,
                            'first_name' => $info['first_name'],
                            'last_name' => $info['last_name'],
                            'mobile' => $mobile ?? '',
                            'company'=> $info['company'],
                            'email' => $info['email'],
                            'password' => (new \Cake\Auth\DefaultPasswordHasher)->hash(rand(10)),

                        ];

                        $newCustomer = RentMy::$Model['Customers']->newEntity($data);
                        $customer = RentMy::$Model['Customers']->save($newCustomer);
                        // $this->out('new customer_id:' . $customer->id);
                    }
                    // store address
//                    if (!empty($customer)) {
//                        // add first address as Primary
//                        foreach ($cusData as $i => $cus) {
//                            if (!empty($cus['street']) && !empty(!empty($cus['city'])) && !empty(!empty($cus['state']))) {
//                                $mobile = str_replace('(', '', $cus['phone']);
//                                $mobile = str_replace(')', '', $mobile);
//                                $mobile = str_replace('-', '', $mobile);
//
//                                $phone = str_replace('(', '', $cus['other_phone']);
//                                $phone = str_replace(')', '', $phone);
//                                $phone = str_replace('-', '', $phone);
//
//
//                                $customerAddress = [
//                                    'customer_id' => $customer->id,
//                                    'store_id' => $store_id,
//                                    'is_primary' => ($i == 0) ? 1 : 0,
//                                    'type' => ($i == 0) ? 'Primary' : 'Others',
//                                    'city' => $cus['city'] ?? '',
//                                    'zipcode' => $cus['zip/postal_code'] ?? '',
//                                    'state' => $cus['state'] ?? '',
//                                    'country' => 'US',
//                                    'mobile' => $mobile ?? '',
//                                    'phone' => $phone ?? '',
//                                    'address_line1' => $cus['street'] ?? ''
//                                ];
//                                $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
//                                RentMy::$Model['CustomerAddresses']->save($cusAddress);
//                                $this->out('customer_id:' . $customer->id);
//                            }
//                        }
//                    }
                }
            });

    }
    /**
     *
     */
    function mountainthreads()
    {
        $store_id = 3893;
        RentMy::addModel(['CustomerAddresses', 'Customers']);
        $customersExcel = WWW_ROOT . 'upload' . DS . 'excel' . DS . '3893_customer_list.xlsx';
        $sheetData = $this->formatExcel($customersExcel);
        $indices = [];

        foreach ($sheetData[1] as $ix => $field) {
            $field = str_replace(" ", "_", $field);
            $indices[$ix] = strtolower($field);
        }
        unset($sheetData[1]);

        $sheetData = new Collection($sheetData);
        // Counter for generating fallback emails
        $fallbackEmailCounter = 1;
        $sheetData = $sheetData->map(function ($values, $key) use ($indices, &$fallbackEmailCounter) {
            foreach ($values as $i => $value) {
                $values[$indices[$i]] = trim($value);
                unset($values[$i]);
            }

            // Fallback email generation
            if (empty($values['email'])) {
                $values['email'] = $fallbackEmailCounter . '@mountainthreads.com';
                $fallbackEmailCounter++;
            }
            
            return $values;
        });

        $sheetData->groupBy('email')
            ->each(function ($cusData, $key) use ($store_id) {

                if (!empty($key)) { // email exist
                    // get customer ID from email
                    $customer = RentMy::$Model['Customers']->find()->where(['email' => trim($key), 'store_id' => $store_id])->first();

                    $info = $cusData[0];
                    $mobile = str_replace('(', '', $info['primary_number']);
                    $mobile = str_replace(')', '', $mobile);
                    $mobile = str_replace('-', '', $mobile);

                    $data = [
                        'type' => 1,
                        'store_id' => $store_id,
                        'first_name' => $info['first_name'],
                        'last_name' => $info['last_name'],
                        'status'=> $info['active'],
                        'mobile' => $mobile ?? '',
                        'company'=> $info['business_name'],
                        'email' => trim($key),
                        'tax_exempt' => $info['tax_exempt'],
                        'password' => (new \Cake\Auth\DefaultPasswordHasher)->hash(rand(10)),
                        'optional' => json_encode([
                            'marketing_calls'=> $info['marketing_calls'],
                            'unsubscribed'=>$info['unsubscribed'],
                            'phone_number_type' => $info['phone_number_type'],

                            'service_street_address' => $info['service_street_address'],
                            'service_city' => $info['service_city'],
                            'service_state' => $info['service_province'],
                            'service_country' => $info['service_country'],
                            'service_zipcode' => $info['service_postal_code'],
                            'age_range' => $info['age_range'],
                            'gender' => $info['gender'],
                            'bio' => $info['bio'],
                            'source' => $info['source'],
                            'last_serviced' => $info['last_serviced'],
                            'next_interaction' => $info['next_interaction'],
                            'property' => $info['property'],
                            'quotes' => $info['quotes'],
                            'orders' => $info['orders'],
                            'receivables' => $info['receivables'],
                            'service_discount' => $info['service_discount'],
                            'retail_discount' => $info['retail_discount'],
                        ])

                    ];

                    // new entry if not exist or Update it
                    if (empty($customer)) {
                        $newCustomer = RentMy::$Model['Customers']->newEntity($data);
                    } else {
                        $newCustomer = RentMy::$Model['Customers']->patchEntity($customer, $data);
                    }
                    $customer = RentMy::$Model['Customers']->save($newCustomer);

                   //  store address
                    if (!empty($customer)) {
                        // add first address as Primary
                        foreach ($cusData as $i => $cus) {
                            if (!empty($cus['billing_street_address']) && !empty(!empty($cus['billing_city'])) && !empty(!empty($cus['billing_province']))) {
                                $mobile = str_replace('(', '', $cus['primary_number']);
                                $mobile = str_replace(')', '', $mobile);
                                $mobile = str_replace('-', '', $mobile);

                                $phone = str_replace('(', '', $cus['other_phone']);
                                $phone = str_replace(')', '', $phone);
                                $phone = str_replace('-', '', $phone);


                                $customerAddress = [
                                    'customer_id' => $customer->id,
                                    'store_id' => $store_id,
                                    'is_primary' => ($i == 0) ? 1 : 0,
                                    'type' => ($i == 0) ? 'Primary' : 'Others',
                                    'city' => $cus['billing_city'] ?? '',
                                    'zipcode' => $cus['billing_postal_code'] ?? '',
                                    'state' => $cus['billing_province'] ?? '',
                                    'country' => $cus['billing_country'] ?? '',
                                    'mobile' => $mobile ?? '',
                                    'phone' => $phone ?? '',
                                    'address_line1' => $cus['billing_street_address'] ?? ''
                                ];

                                $address = $cusAddress = RentMy::$Model['CustomerAddresses']->find()->where(['customer_id' => $customer->id])->first();
                                if (empty($address)) {
                                    $cusAddress = RentMy::$Model['CustomerAddresses']->newEntity($customerAddress);
                                } else {
                                    $cusAddress = RentMy::$Model['CustomerAddresses']->patchEntity($address, $customerAddress);
                                }
                                RentMy::$Model['CustomerAddresses']->save($cusAddress);
                                $this->out('customer_id:' . $customer->id);
                            }
                        }
                    }
                }
            });

    }
    function updateGateways($gateway)
    {
        RentMy::addModel(['PaymentGateways', 'Customers', 'CustomerGateway']);
        $customers = RentMy::$Model['Customers']->find()
            ->where(['gateway LIKE' => '%'.$gateway.'%'])
            ->order(['id'=>'ASC'])
            ->toArray();

        foreach ($customers as $customer) {
            $gateways = json_decode($customer['gateway'], true);
            foreach ($gateways as $gateway) {
                if ($gateway['gateway'] == 'DeltaPay') {
                    $paymentGateway = RentMy::$Model['PaymentGateways']->find()
                        ->where(['store_id' => $customer['store_id'], 'name' => $gateway['gateway']])
                        ->first();
                    $data = [
                        'store_id' => $customer['store_id'],
                        'customer_id' => $customer['id'],
                        'gateway_id' => $paymentGateway['id'],
                        'gateway_name' => $gateway['gateway'],
                    //    'token' => $gateway['token'],
                        'customer_ref' => $gateway['ref'],
                       // 'card_no' => $gateway['card_no']
                    ];
                    $vaultData = self::getDeltaDefaultBillingId($data['customer_ref']);
                    if(!empty($vaultData['billing_id'])) {
                        $data['token'] = $vaultData['billing_id'];
                        $data['card_type'] = $vaultData['card_type'];
                        $data['card_no'] = $vaultData['card_no'];
                        RentMy::$Model['CustomerGateway']->addNewGateway($customer['id'],$data);
                        $this->log('Customer_id:' . $customer['id']);
                       // RentMy::dbg($data);
                    }else {
                        $this->log('Not found - Customer_id:' . $customer['id']);
                    }
                }
                elseif ($gateway['gateway'] == 'Transafe'){
                    $paymentGateway = RentMy::$Model['PaymentGateways']->find()
                        ->where(['store_id' => $customer['store_id'], 'name' => $gateway['gateway']])
                        ->first();

                    if(!empty($gateway['vaultKey']) && !empty( $gateway['ref'])) {
                        $data = [
                            'store_id' => $customer['store_id'],
                            'customer_id' => $customer['id'],
                            'gateway_id' => $paymentGateway['id'],
                            'gateway_name' => $gateway['gateway'],
                            'customer_ref' => $gateway['ref'],
                            'card_no' => $gateway['card'],
                            'token' => $gateway['vaultKey']
                        ];
                        RentMy::$Model['CustomerGateway']->addNewGateway($customer['id'],$data);
                        $this->log('Customer_id:' . $customer['id']);
                    }else {
                        $this->log('Not found - Customer_id:' . $customer['id']);
                    }
                }
            }

        }
    }

    function getDeltaDefaultBillingId($customer_vault)
    {
        // get customer details
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://mydeltaps.transactiongateway.com/api/query.php',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('security_key' => 'wjZ5x7Ahc8a2kxBVAj54n2ds4StmduEd',
                'report_type' => 'customer_vault',
            //    'customer_vault_id' => '125528037'
               'customer_vault_id' => $customer_vault

            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $customer = json_decode(json_encode(simplexml_load_string($response)), true);
        $customerDetails = $customer['customer_vault']['customer'];
        if (empty($customerDetails))
            return;
        // update customer

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://mydeltaps.transactiongateway.com/api/transact.php',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('security_key' => 'wjZ5x7Ahc8a2kxBVAj54n2ds4StmduEd',
                'firstname' => $customerDetails['first_name'],
                'lastname' => $customerDetails['last_name'],
                'customer_vault_id' => $customerDetails['customer_vault_id'],
                'customer_vault' => 'update_customer'),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $data = explode('&', $response);
        $response = [];
        for ($i = 0; $i < count($data); $i++) {
            $rdata = explode("=", $data[$i]);
            $response[$rdata[0]] = $rdata[1];
        }
     //    RentMy::dbg($customerDetails);
        // RentMy::dbgAndEnd($response);
        if (!empty($response['billing_id'])) {
            return [
                'billing_id' => $response['billing_id'],
                'card_type' => $customerDetails['cc_type'],
                'card_no' => $customerDetails['cc_number']
            ];
        }

    }

    public function reCalculateTotalPaid($storeId = 2960){
        RentMy::addModel(['Customers']);
        $customers = RentMy::$Model['Customers']->find()->where(
            [
                'Customers.store_id' => $storeId,
                'subscription_id !=' => '',
                'total_paid IS NULL'
            ]
        )->contain(['CustomerPlans'])->toArray();

        foreach ($customers as $customer){
            $customer->total_paid = $customer['customer_plan']['amount'];
            RentMy::$Model['Customers']->save($customer);
        }

    }


    public function additionalChargeToCustomer($storeId, $location, $customerId){
        RentMy::getStore($storeId, $location);
        RentMy::addModel(['Customers']);
        $customer = RentMy::$Model['Customers']->find()->where(['id'=>$customerId])->first();

        if ($customer->gateway_customer_id){
            $gateway = (new Payment())->getStorePaymentConfig('Stripe', $location);
            $invoiceObj = new Invoice('Membership', $gateway['config']);
            $invoiceObj->addInvoiceItem($customer->gateway_customer_id, [
                'amount' => 10,
                'description' => "additional charge $10"
            ]);
        }
    }
}
