<?php


namespace App\Lib;


use App\Lib\RentMy\RentMy;
use App\Lib\RentMy\Shipping;
use Cake\Core\Configure;
use Cake\Http\Client;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;

/**
 * Class Shipengine
 * @package App\Lib
 */
class Shipengine
{

    public $config;
    private $http;
    private $baseUrl = 'https://api.shipengine.com/v1/';

    /**
     * Shipengine constructor.
     * @param $config
     */
    public function __construct($config)
    {
        $this->config = $config;
        $this->http   = new Client([
            'headers' => ['api-key' => $this->config['api-key']]
        ]);
    }

    /**
     * Get shipping rates
     * @param $data
     * @return Client\Response
     */
    public function getShippingRates($data)
    {
        $api_data = [
            "shipment" => [
                "validate_address" => "no_validation",
                'ship_to' => $this->formatShipTo($data),
                "ship_from" => $this->formatShipFrom($data),
            ],
            "rate_options" => [
                "carrier_ids" =>
                    $this->config['carrier_id']

            ]
        ];
        if (!empty($data['rental_date'])) {
            $api_data['shipment']['ship_date'] = $data['rental_date'];
        }

        $packages                                  = $this->formatPackages($data, $data['carrierServices']);
        $api_data['shipment']['packages']          = $packages['packages'];
        $api_data['rate_options']['package_types'] = $packages['package_types'];
        $api_data['rate_options']['service_codes'] = $packages['service_codes'];

        RentMy::saveLogFile('alert', 'store ID: ' . RentMy::$token->store_id . '-- Shipengine Rate Request: ' . json_encode($api_data), ['fedex']);
        return $this->http->post($this->baseUrl . 'rates', json_encode($api_data), ['type' => 'json']);
    }

    /**
     * Customer Address
     * @param $data
     * @return array
     */
    public function formatShipTo($data)
    {
        $data['address']['shipping_state'] = (new Shipping())->stateNameToCode($data['address']['shipping_state']);
        return [
            "name" => $data['address']['shipping_first_name'] . ' ' . $data['address']['shipping_last_name'],
            "phone" => $data['address']['shipping_mobile'],
            "company_name" => $data['address']['company'],
            "address_line1" => $data['address']['shipping_address1'],
            "address_line2" => $data['address']['shipping_address2'],
            "city_locality" => $data['address']['shipping_city'],
            "state_province" => strtoupper($data['address']['shipping_state']),
            "postal_code" => $data['address']['shipping_zipcode'],
            "country_code" => empty($data['address']['shipping_country']) ? 'US' : strtoupper(trim($data['address']['shipping_country']))

        ];
    }

    /**
     * Ship from data will be store location details .
     * @param $data
     * @return array
     */
    public function formatShipFrom($data)
    {
        $data['store_location']['state'] = (new Shipping())->stateNameToCode($data['store_location']['state']);
        return [
            "address_line1" => $data['store_location']['address'],
            "city_locality" => $data['store_location']['city'],
            "state_province" => strtoupper($data['store_location']['state']),
            "postal_code" => $data['store_location']['zipcode'],
            "country_code" => strtoupper($data['store_location']['country']),
            "name" => RentMy::$store['slug'],//$data['store']['s_user']['first_name'] . ' ' . $data['store']['s_user']['last_name'],
            "phone" => $data['store_location']['phone'],
            "company_name" => RentMy::$store['slug'],
            "address_residential_indicator" => "yes"
        ];

    }

    /**
     * @param $data
     * @param $services
     * @return \array[][]
     */
    public function formatPackages(array $data, array $services = []): array
    {
        //init format so never not isset happens
        $packageData = array(
            'packages' => [],
            'service_codes' => [],
            'package_types' => []
        );

        //if shop only configured with weight shipping
        if (!isset($data['boxes'])) {
            $packageData['packages'][] = array(
                "weight" => [
                    "value" => $data['packages']['weight'],
                    "unit" => $data['packages']['unit']
                ]
            );
            return $packageData;
        }

        //when weight and volume configured to the shop
        if (isset($data['boxes']['list']) && !empty($data['boxes']['list'])) {
            foreach ($data['boxes']['list'] as $package) {
                $_packages = $package['package_format'];
                unset($_packages['package_code']);
                unset($_packages['products']);
                //if standard package founded
                if (!$package['is_custom']) {
                    unset($_packages['dimensions']);
                    $packageData['package_types'][] = $package['package_format']['package_code'];
                    $packageData['service_codes']   = $services;
                }
                $packageData['packages'][] = $_packages;
            }
            //make sure remove duplicate package_types
            $packageData['package_types'] = array_unique($packageData['package_types']);
        }

        if (empty($packageData['packages']))
            $packageData['packages'][] = array(
                "weight" => [
                    "value" => $data['packages']['weight'],
                    "unit" => $data['packages']['unit']
                ]
            );

        return $packageData;
    }

    /**
     * @param $date
     * @param $ship_from
     * @param $ship_to
     * @param $package
     * @param array $packages
     * @return array
     */
    public function getRatesForNewShippment($date, $ship_from, $ship_to, $package, $packages = [])
    {
        $api_data = [
            "shipment" => [
                "validate_address" => "no_validation",
                'ship_to' => $ship_to,
                "ship_from" => $ship_from,
                "packages" => [
                    [
                        "weight" => [
                            "value" => $package['weight']??0,
                            "unit" => $package['unit'] ?? '',
                        ]
                    ]
                ]
            ],
            "rate_options" => [
                "carrier_ids" => $this->config['carrier_id']
            ]
        ];
        if (!empty($date))
            $api_data['shipment']['ship_date'] = $date;


        if (empty($packages)) {
            if (!empty($package['package_types']))
                $api_data['rate_options']['package_types'] = $package['package_types'];

            if (!empty($package['service_codes']))
                $api_data['rate_options']['service_codes'] = $package['service_codes'];
        }else{
            $api_data['shipment']['packages'] = $packages['packages'];
            $api_data['shipment']['items'] = $packages['items'];
            $api_data['rate_options']['package_types'] = [];
            $api_data['rate_options']['service_codes'] = [];
        }

        $response = $this->http->post($this->baseUrl . 'rates', json_encode($api_data), ['type' => 'json']);
        if (!empty($response->json['errors'][0]['message'])) {
            return ['success' => false, 'message' => $response->json['errors'][0]['message']];
        } else if (!empty($response->json['rate_response']['errors'][0]['message'])) {
            return ['success' => false, 'message' => $response->json['rate_response']['errors'][0]['message']];
        } else {
            return ['success' => true, 'data' => $response->json];
        }
    }

    /**
     * Get shipping rates
     * @param $data
     * @return Client\Response
     */
    public function createShipments($data, $service_code)
    {
        $shipFrom              = $this->formatShipFrom($data);
        $shipTo                = $this->formatShipTo($data);
        $packages              = $this->formatPackages($data);

        $api_data['shipments'] = [
            [
                "validate_address" => "no_validation",
                "service_code" => $service_code,
                "confirmation" => "none",
                "insurance_provider" => "none",
                "ship_to" => $shipTo,
                "ship_from" => $shipFrom,
                "packages" => $packages['packages'],
                'ship_date' => $data['rental_date']

            ]
        ];
        if(!empty($data['delivery']['carrier_id'])){
            $api_data['shipments'][0]['carrier_id'] = $data['delivery']['carrier_id'];
        }
        return $this->http->post($this->baseUrl . 'shipments', json_encode($api_data), ['type' => 'json']);

    }

    /**
     * Create shipment from admin after order
     * @param $date
     * @param $service_code
     * @param $ship_to
     * @param $ship_from
     * @param $packages
     * @return array
     */
    public function createShipmentsAfterOrder($date,$carrier_id, $service_code, $ship_to, $ship_from, $packages)
    {
        $sPackages = [];
        if (empty($packages['packages'])) {
            foreach ($packages as $package) {
                $sPackages[] = [
                    'weight' => [
                        'value' => $package['weight'],
                        'unit' => 'pound',
                    ],
                    'dimensions' => [
                        "unit" => "inch",
                        "length" => $package['length'],
                        "width" => $package['width'],
                        "height" => $package['height']
                    ]
                ];
            }
        }

        $apiData = [
            "validate_address" => "no_validation",
            "service_code" => $service_code,
            "carrier_id" => $carrier_id,
            "confirmation" => "none",
            "insurance_provider" => "none",
            "ship_to" => $ship_to,
            "ship_from" => $ship_from,
            "packages" => $sPackages,
            'ship_date' => $date
        ];

        if (!empty($packages['packages']))
            $apiData['packages'] = $packages['packages'];

        if (!empty($packages['items']))
            $apiData['items'] = $packages['items'];

        $api_data['shipments'][] = $apiData;

        $response = $this->http->post($this->baseUrl . 'shipments', json_encode($api_data), ['type' => 'json']);
        if (!empty($response->json['errors'][0]['message'])) {
            return ['success' => false, 'message' => $response->json['errors'][0]['message']];
        }
        return ['success' => true, 'data' => $response->json];

    }

    public function formatReturnTo()
    {

    }

    /**
     * Get carrier list
     * @return Client\Response
     */
    function carrierList()
    {
        $response = $this->http->get($this->baseUrl . 'carriers', ['type' => 'json']);
        return $response->json;
    }

    /**
     * @param $carrier_id
     * @return bool|mixed
     */
    function carrierService($carrier_id)
    {
        $response = $this->http->get($this->baseUrl . 'carriers/' . $carrier_id . '/services', ['type' => 'json']);
        return $response->json;
    }

    /**
     * @param $carrier_id
     * @return bool|mixed
     */
    function carrierPackages($carrier_id)
    {
        $response = $this->http->get($this->baseUrl . 'carriers/' . $carrier_id . '/packages', ['type' => 'json']);
        return $response->json;
    }

    /**
     * Create Label from shipment id
     * @param $shipment_id
     * @return array
     */
    function createLabelFromShippingId($shipment_id)
    {
        $api_data = [
            "validate_address" => "no_validation",
            "label_format" => "pdf",
            "label_layout" => "4x6",
            "label_download_type" => "url"
        ];
        $response = $this->http->post($this->baseUrl . 'labels/shipment/' . $shipment_id, json_encode($api_data), ['type' => 'json']);
        if (!empty($response->json['errors'][0]['message'])) {
            $msg = $response->json['errors'][0]['message'];
            return ['success' => false, 'message' => $msg];
        }

        return ['success' => true, 'data' => $response->json];
    }

    /**
     * @param $label_id
     * @return array
     */
    function createReturnLabel($label_id)
    {
        $api_data = [
            "charge_event" => "carrier_default",
            "label_format" => "pdf",
            "label_layout" => "4x6",
            "label_download_type" => "url"
        ];
        $response = $this->http->post($this->baseUrl . 'labels/' . $label_id . '/return', json_encode($api_data), ['type' => 'json']);
        if (!empty($response->json['errors'][0]['message'])) {
            $msg = $response->json['errors'][0]['message'];
            return ['success' => false, 'message' => $msg];
        }

        return ['success' => true, 'data' => $response->json];
    }


    /**
     * @param $shipment_id
     * @return array|null
     */
    function shippingDetails($shipment_id)
    {
        $response = $this->http->get($this->baseUrl . 'shipments/' . $shipment_id, ['type' => 'json']);
        return $response->getJson();
    }

    /**
     * @param $shipment_id
     * @param $data
     * @return mixed
     */
    function updateShipping($shipment_id, $data)
    {
        $response = $this->http->put($this->baseUrl . 'shipments/' . $shipment_id, json_encode($data), ['type' => 'json']);
        return $response->getJson();
    }

    /**
     * @param $shipment_id
     * @param $carriers
     * @param array $options
     * @return array
     */
    function getRatesFromShipment($shipment_id, $carriers, $options = [])
    {
        $api_data = [
            'shipment_id' => $shipment_id,
            "rate_options" => [
                "carrier_ids" =>
                    $carriers

            ]

        ];
        $response = $this->http->post($this->baseUrl . 'rates', json_encode($api_data), ['type' => 'json']);
        if (!empty($response->json['errors'][0]['message'])) {
            //Log::error("Response: " . json_encode($response->json), 'fedex');
            $msg = $response->json['errors'][0]['message'];
            return ['success' => false, 'message' => $msg];
        }

        return ['success' => true, 'data' => $response->json];
    }

    /**
     * this function will void a shipment
     * @param $labelId
     * @return array
     */
    public function void($labelId): array
    {
        $response = $this->http->put($this->baseUrl . "labels/$labelId/void");

        if (empty($response->json['approved'])) {
            //Log::error("Response: " . json_encode($response->json), 'fedex');
            $msg = $response->json['message'];
            return ['success' => false, 'message' => $msg];
        }

        return ['success' => true, 'message' => 'This label has been voided.'];
    }
}


