<?php


namespace App\Lib\RentMy;

use App\Controller\AppController;
use App\Lib\S3;
use App\Lib\Services\GoogleServices;
use Cake\Collection\Collection;
use Cake\Core\Configure;

use Cake\I18n\Number;
use Cake\I18n\Time;
use Cake\Network\Request;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use Cake\Utility\Hash;
use Cake\Log\Log;
use Http\Message\Authentication\QueryParam;


class RentMy
{
    /*
     * Readable connection config name is declared in config/app.php
     */
    const ReadableConnection = 'slave1';
    // public static $isReadableConnection = false;

    public static $storeSetting;
    public static $storeConfig;
    public static $storeContents;
    public static $store;
    public static $token;
    public static $Model;
    public static $storeUser;
    private static $recalCount = 0;
    private static $nextDateCount = 0;
    public static $orderStatus;
    public static $location;
    public static $withDisabledOrderStatus;

    const MIMES = ['png', 'jpg', 'jpeg', 'doc', 'pdf', 'ppt', 'docx', 'pptx', 'docx', 'xls', 'xlsx', 'csv'];

    public function __construct()
    {

    }


    /**
     * @param $storeId
     */
    public static function getStore($storeId, $location = '')
    {
        self::addModel(['Stores']);
        self::$store = self::$Model['Stores']->get($storeId);
        if (empty($location)) {
            self::addModel(['Locations']);
            $defaultLocation = self::$Model['Locations']->find()->where(['store_id' => $storeId, 'is_online' => 1, 'status' => 1])->first();
            $location = $defaultLocation->id;
        }
        self::$storeSetting = json_decode(self::$store['settings'], true)[$location] ?? [];
        self::$storeConfig = json_decode(self::$store['config'], true)[$location] ?? [];
    }

    /**
     * Save location based store config
     * @param $storeId
     * @param $config
     * @return bool
     */
    public static function saveStoreConfig($storeId,$config){
        self::addModel(['Stores']);
        $store = self::$Model['Stores']->get($storeId);
        $store_config = json_decode($store->config, true);
        $location = self::$token->location;
        if (empty($location)) {
            self::addModel(['Locations']);
            $defaultLocation = self::$Model['Locations']->find()->where(['store_id' => $storeId, 'status' => 1])->first();
            $location = $defaultLocation->id;
        }
        $store_config[$location] = $config;
        $store->config = json_encode($store_config);

        if(self::$Model['Stores']->save($store)){
            return true;
        }else{
            return false;
        }
    }
    /**
     * set slave db connection for all loaded model
     */
    public static function setDbReadable()
    {
        //   self::$isReadableConnection = true;
        foreach (self::$Model as &$model) {
            $model->setConnection(\Cake\Datasource\ConnectionManager::get(self::ReadableConnection));
        }
    }

    /**
     * Set master (default) config for all loaded models
     */
    public static function setDbWritable()
    {
        // self::$isReadableConnection = false;
        foreach (self::$Model as &$model) {
            $model->setConnection(\Cake\Datasource\ConnectionManager::get('default'));
        }
    }

    /**
     * Return Store Current Timezone
     */
    public static function getTimeZone()
    {
        $timezone = !empty(self::$storeSetting['timezone']) ? self::$storeSetting['timezone'] : Configure::read('TIMEZONE');
        return $timezone;
    }

    /**
     * Return Store currency Config
     */
    public static function getCurrency()
    {
        return (!empty(self::$storeConfig) && !empty(self::$storeConfig['currency_format']['code'])) ? self::$storeConfig['currency_format']['code'] : 'USD';

    }

    /**
     * Return Start time condition
     * @return boolean
     */
    public static function getStartTime()
    {
        return !isset(self::$storeConfig['show_start_time']) ? 1 : self::$storeConfig['show_start_time'];
    }

    /**
     * Return End time condition for show/hide condition
     * @return boolean
     */
    public static function getEndTime()
    {
        return !isset(self::$storeConfig['show_end_time']) ? 1 : self::$storeConfig['show_end_time'];
    }

    /**
     * Return the show/hide condition for end date
     * @return boolean
     */
    public static function getEndDate()
    {
        return !isset(self::$storeConfig['show_end_date']) ? 1 : self::$storeConfig['show_end_date'];
    }

    /**
     * Return the rental day options 24 or calendar
     * @return boolean
     */
    public static function getRentalDayOption()
    {
        return !isset(self::$storeConfig['rental_day']) ? 24 : self::$storeConfig['rental_day'];
    }

    /**
     * show/hide option for rental price
     * @return boolean
     */
    public static function getRentalPriceOption()
    {
        return !isset(self::$storeConfig['rental_price_option']) ? 1 : self::$storeConfig['rental_price_option'];
    }

    /**
     * Convert any date time to UTC
     * @param $datetime
     * @param string $format
     * @return string
     */
    public static function toUTC($datetime, $format = 'Y-m-d H:i:s')
    {
        return (new \Cake\I18n\Time($datetime, self::getTimeZone()))->setTimezone('UTC')->format($format);
    }

    /**
     * Convert any date to Store Timezone
     * @param $datetime
     * @param string $format
     * @return mixed
     */
    public static function toStoreTimeZone($datetime, $format = 'Y-m-d H:i:s', $tz = false)
    {
        if ($tz === false)
            $tz = self::getTimeZone();

        return (new \Cake\I18n\Time($datetime, 'UTC'))->setTimezone($tz)->format($format);
    }

    /**
     * @param $datetime
     * @param bool $rental
     * @param bool $hideTime
     * @param bool $hideFormatText
     * @return string
     */
    public static function format_date($datetime, $rental = false, $hideTime = false, $hideFormatText = false)
    {
        $allFormatsDate = [
            'MM/DD/YYYY' => 'm/d/Y',
            'DD/MM/YYYY' => 'd/m/Y',
            'DD MMM YYYY' => 'd M Y',
            'YYYY-MM-DD' => 'Y-m-d'
        ];
        $allFormatsTime = 'h:i A';

        if (!empty($datetime)) {
            if ($rental) {
                $date_format = !empty(self::$storeConfig['date_format']) ? $allFormatsDate[self::$storeConfig['date_format']] : 'd M Y';
                $date = Time::parse($datetime)->format($date_format) . ' ';
                if (RentMy::$storeConfig['show_start_time']) {
                    $date .= Time::parse($datetime)->format($allFormatsTime);
                }
                return $date;
            } else {
                if ($hideTime) {
                    $format = !empty(self::$storeConfig['date_format']) ? $allFormatsDate[self::$storeConfig['date_format']] : 'd M Y';
                } else {
                    $format = !empty(self::$storeConfig['date_format']) ? $allFormatsDate[self::$storeConfig['date_format']] . ' ' . $allFormatsTime : 'm/d/Y h:i A';
                }
                if ($hideFormatText) {
                    $datetime = Time::parse($datetime)->format($format);
                    return $datetime;
                } else {
                    if (Time::parse($datetime)->isToday()) {
                        $datetime = 'Today ' . Time::parse($datetime)->format('h:i A');
                    } else if (Time::parse($datetime)->isYesterday()) {
                        $datetime = 'Yesterday ' . Time::parse($datetime)->format('h:i A');
                    } elseif (Time::parse($datetime)->isTomorrow()) {
                        $datetime = 'Tomorrow ' . Time::parse($datetime)->format('h:i A');
                    } else {
                        $datetime = Time::parse($datetime)->format($format);
                    }
                    return $datetime;
                }
            }
        } else {
            return $datetime;
        }
    }

    /**
     * @param $datetime
     * @return string
     */
    public static function toStoreTimeZoneByConfig($datetime)
    {
        //$time = (new \Cake\I18n\Time($datetime,self::getTimeZone()))->$format($format);
        if (self::$storeConfig['show_start_time']) {
            $format = 'm-d-Y h:i A';
        } else {
            $format = 'm-d-Y';
        }
        return (new \Cake\I18n\Time($datetime, 'UTC'))->setTimezone(self::getTimeZone())->format($format);
        //return $time;
    }


    /**
     * Set store config
     * @param $storeId
     * @param $config
     */

    //need location based constrain decision
    public static function setConfig($storeId, $config)
    {
        self::addModel(['Stores']);
        $store = self::$Model['Stores']->get($storeId);
        $store->config = json_encode($config, true);
        if (self::$Model['Stores']->save($store)) {
            self::$store = $store;
            self::$storeConfig = $config;
        }

    }

    /**
     * Check current Store plan & inventory count
     * Free user will show water mark and can add unlimited products.
     * Free store can have 10 products and will not show watermark and after 10 watermark will show
     * Start UP store can have 100 products and will not show watermark and after 50 watermark will show
     * SMB store can have 1000 products and will not show watermark and after 1500 watermark will show
     * @param $store
     * @return bool
     */
    public static function checkPaidStoreType($store, $options = array())
    {
        $storePlan = strtoupper($store->store_type);
        $additional_inventory = empty($options['additional_inventory']) ? 0 : $options['additional_inventory'];
        $newInventory = true;
        $storeConfig = json_decode($store['config'], true);
        // when branding config is disabled

//        if(!isset($storeConfig['footer']['show_copyright'])){
//            return false;
//        }
//        if($storeConfig['footer']['show_copyright'] == false){
//            return true;
//        }
        if ($storePlan != 'FREE') {
            RentMy::addModel(['Products']);
            $productCount = RentMy::$Model['Products']->find()->where(['store_id' => $store->id, 'status' => 1])->count();
            $productCount += $additional_inventory;
            if (($storePlan == 'STARTER') && ($productCount > 10)) {
                $newInventory = false;
            } elseif (($storePlan == 'BRONZE') && ($productCount > 50)) {
                $newInventory = false;
            } elseif (($storePlan == 'SILVER') && ($productCount > 1500)) {
                $newInventory = false;
            } elseif (($storePlan == 'SMB') && ($productCount > 1000)) {
                $newInventory = false;
            } elseif (($storePlan == 'START UP') && ($productCount > 100)) {
                $newInventory = false;
            } elseif (($storePlan == 'BASIC') && ($productCount > 10)) {
                $newInventory = false;
            } elseif (($storePlan == 'LITE') && ($productCount > 100)) {
                $newInventory = false;
            } elseif (($storePlan == 'STANDARD') && ($productCount > 500)) {
                $newInventory = false;
            } elseif (($storePlan == 'PRO') && ($productCount > 1500)) {
                $newInventory = false;
            }
            $newInventory = true;
        } else {
            if (!empty($store->plan_id)) {
                RentMy::addModel(['Products']);
                $productCount = RentMy::$Model['Products']->find()->where(['store_id' => $store->id, 'status' => 1])->count();
                $productCount += $additional_inventory;
                if ($productCount > 10) {
                    $newInventory = false;
                }
            } else {
                $newInventory = false;
            }
        }
        return $newInventory;

    }

    /**
     * @param $numbers
     * @param $target
     * @return array|mixed
     */
    public function getClosestnumber($numbers, $target)
    {
        foreach ($numbers as $number)
            $smallest[] = ['id' => $number['id'], 'duration' => ($number['duration'] - $target)];

        $nagetives = array_filter($smallest, function ($item) {
            return $item['duration'] <= 0;
        });
        $smallest = !empty($nagetives) ? $nagetives : $smallest;
        //now make all duration positive and order by lowest duration
        $allPositive = array_map(function ($item) {
            $item['duration'] = abs($item['duration']);
            return $item;
        }, $smallest);
        usort($allPositive, function ($a, $b) {
            return $a['duration'] - $b['duration'];
        });

        //return id from this sorted array's zero index
        return $allPositive[0]['id'];
    }

//    /** Get the closes number from any array */
//    public static function getClosestnumber($numbers, $target)
//    {
//        foreach ($numbers as $number) {
//            $smallest[$number['id']] = abs($number['duration'] - $target);
//        }
//        asort($smallest);
//        return key($smallest);
//    }

    public static function saveLogFile($type, $logData, $scope)
    {
        if (!empty($scope)) {
            \Cake\Log\Log::$type($logData, ['scope' => $scope]);
        } else {
            \Cake\Log\Log::$type($logData);
        }

        self::logToGenius([
            "account" => 'rentmy_api',
            "event" => "save_log_file",
            "status" => $scope,
            "description" => $type,
            "value" => '',
            "custom_content" => json_encode($logData)
        ]);


    }

    /**
     * Save logs on db
     * @param array $options
     */
    public static function saveLogDB($action,$request,$response,$options = [])
    {
        RentMy::addModel(['StoreLogs']);
        RentMy::$Model['StoreLogs']->saveToDb($action,$request,$response,$options);

    }

    /**
     * Static function for Load Models
     * RentMy::$Model['modelName'] will be used to get models
     * @param array $models
     * @return \Cake\ORM\Table
     */
    public static function addModel($models = array())
    {
        if (!empty($models)) {
            if (is_array($models)) {
                foreach ($models as $model) {
                    if (empty(self::$Model[$model])) {
                        self::$Model[$model] = TableRegistry::getTableLocator()->get($model);
                    }
                }
            } else {
                if (empty(self::$Model[$models])) {
                    self::$Model[$models] = TableRegistry::getTableLocator()->get($models);
                }
            }
        }

        //listen db connection
//        if (self::$isReadableConnection)
//            self::setDbReadable();
//        else
//            self::setDbWritable();

    }

    /*
    * Random generate code (6 digit)
    */

    /**
     * Random Alpha-Numeric String
     *
     * @param int length
     * @param bool $characters
     * @return string
     * @access public
     */
    public static function randomNum($length, $characters = false)
    {
        $randstr = "";
        srand((double)microtime() * 1000000);
        //our array add all letters and numbers if you wish
        if ($characters) {
            $chars = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');
        } else {
            $chars = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
        }
        for ($rand = 0; $rand <= $length; $rand++) {
            $random = rand(0, count($chars) - 1);
            $randstr .= $chars[$random];
        }
        return $randstr;
    }

    /**
     * @param $date
     * @return string
     */
    public static function getDefaultStartDate($date)
    {
        $rental_day = empty(self::$storeConfig['rental_day']) ? '24' : self::$storeConfig['rental_day'];
        if ($rental_day == 'calendar') {
            $datetime = $date . ' 08:00';
        } else {
            $datetime = $date . ' 12:00';
        }
        return $datetime;
    }

    public static function getDefaultEndDate($date, $sameStartEnd = true)
    {
        $rental_day = empty(self::$storeConfig['rental_day']) ? '24' : self::$storeConfig['rental_day'];
        if ($sameStartEnd) {
            if ($rental_day == 'calendar') {
                $datetime = $date . ' 23:00';
            } else {
                $datetime = $date . ' 12:00';
            }
        } else {
            if ($rental_day == 'calendar') {
//                $datetime = $date . ' 08:00';
                $datetime = $date . ' 23:00';
                $datetime = Time::parse($datetime)->subDays(1)->format('Y-m-d H:i');
            } else {
                $datetime = $date . ' 12:00';
            }
        }
        return $datetime;
    }

    /**
     * @param $date
     * @param string $type = 'start' | 'end'
     * @param bool $recall
     * @param null $queryDate
     * @param bool $default
     * @return string
     */
    public static function getNextAdjustedTime($date, $type = 'start', $recall = false, $queryDate = null, $default = true, $options = [])
    {
        if (!empty($options['current_time'])){
            $currentToStoreTimezone = Time::parse($options['current_time'])->format('Y-m-d H:00');
        }else{
            $currentToStoreTimezone = Time::now(RentMy::$storeConfig['timezone'])->format('Y-m-d H:00');
        }

        $currentTime = RentMy::toUTC($currentToStoreTimezone, 'Y-m-d H:i');
        $week_day = Time::parse($date)->format('N');
        $utc_date_time = self::toUTC($date, 'Y-m-d H:i');
        $utc_date = self::toUTC($date, 'Y-m-d');
        $date_time = Time::parse($date)->format('Y-m-d H:i');

        if (!$recall) {
            $queryDate = $date_time;
        } else
            self::$recalCount++;


        $storeId = self::$store->id;
        $location = self::$token->location;
        self::addModel(['Holidays']);
        $sessionIds = self::$Model['Holidays']->find('list')
            ->where([
                'store_id' => $storeId,
                'location' => $location,
                'type' => 'season',
                'start_date <=' => $utc_date_time,
                'end_date >=' => $utc_date_time,
            ])->toList();

        $where = [
            'store_id' => $storeId,
            'location' => $location,
            'day' => $week_day,
            'is_open' => 1,
            'type' => 'day'
        ];

        if (!empty($sessionIds)){
            $where['type'] = 'season';
            $where['season_id IN'] = $sessionIds;
        }

        $opeCloseHours = self::$Model['Holidays']->find()
            ->where($where)
            ->first();

        if (empty($opeCloseHours)) {
            $nextDayTime = Time::parse($date_time)->addDays(1)->format('Y-m-d H:i');
            if (self::$recalCount > 7) {
                self::$recalCount = 0;
                return $queryDate;
            }
            self::$nextDateCount++;
            return self::getNextAdjustedTime($nextDayTime, $type, true, $queryDate);
        }

        self::$recalCount = 0;

        if (empty($opeCloseHours->start_time) && empty($opeCloseHours->end_time)) {
            if (empty($opeCloseHours->start_time) && $type == 'start' && $default) {
                $parsedDate = Time::parse($date_time)->format('Y-m-d');
                $date_time = self::getDefaultStartDate($parsedDate);
            }
            return $date_time;
        } else {
            if (empty(RentMy::$storeConfig['show_start_time']))
                return $date_time;
        }

        $dateTime = '';
        if ($type == 'start') {

            if ($opeCloseHours['is_prev_day']) {
                $dateTime = Time::parse($date_time)->subDays(1)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['start_time'])->format('H:i');
            } else {
                $dateTime = Time::parse($date_time)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['start_time'])->format('H:i');
            }
            $dateTime = Time::parse($dateTime)->format('Y-m-d H:i');

            $openDateTimeToStoreTimeZone = RentMy::toStoreTimeZone($dateTime, 'Y-m-d H:i');

            if (self::$nextDateCount > 0){
                $date_time = Time::parse($date_time)->format('Y-m-d') . ' ' . Time::parse($openDateTimeToStoreTimeZone)->format('H:i');
            }
            //if start date is greater than closing hour
            if ($opeCloseHours['is_next_day']) {
                $endDateTime = Time::parse($date_time)->addDays(1)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['end_time'])->format('H:i');
            } else {
                $endDateTime = Time::parse($date_time)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['end_time'])->format('H:i');
            }
            $closingTime = $endDateTime = Time::parse($endDateTime)->format('Y-m-d H:i');
            $closingHour = $endDateTimeToStoreTimezone = Time::parse($date_time)->format('Y-m-d') .' '. self::toStoreTimeZone($endDateTime, 'H:i');

            if (($closingHour < $date_time) || ($currentToStoreTimezone > $closingHour)) {
                self::$nextDateCount++;
                $nextDayTime = Time::parse($openDateTimeToStoreTimeZone)->addDays(1)->format('Y-m-d');
                $nextDayTime = self::getDefaultStartDate($nextDayTime);
                return self::getNextAdjustedTime($nextDayTime, $type);
            }

            // if start date is less than opening hour then set opening hour as start date
            if ($date_time < $openDateTimeToStoreTimeZone){
               $date_time = $openDateTimeToStoreTimeZone;
            }
// if current time is greater than opening hour

            if ($currentToStoreTimezone > $date_time) {
                $currentTime = self::toStoreTimeZone($currentTime, 'Y-m-d H:i');
                $newTime = Time::parse($currentTime)->addHours(1)->format('Y-m-d H:00');
//                self::$nextDateCount++;
                return self::getNextAdjustedTime($newTime, $type);
            }
            return $date_time;
        }

        if ($type == 'end') {
            if ($opeCloseHours['is_next_day']) {
                $dateTime = Time::parse($date_time)->addDays(1)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['end_time'])->format('H:i');
            } else {
                $dateTime = Time::parse($date_time)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['end_time'])->format('H:i');
            }

            $endDateTime = Time::parse($dateTime)->format('Y-m-d H:i');
            $endDateTimeToStoreTimezone = self::toStoreTimeZone($endDateTime, 'Y-m-d H:i');

            //if end date is smaller than opening hour
            if ($opeCloseHours['is_prev_day']) {
                $startDateTime = Time::parse($date_time)->subDays(1)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['start_time'])->format('H:i');
            } else {
                $startDateTime = Time::parse($date_time)->format('Y-m-d') . ' ' . Time::parse($opeCloseHours['start_time'])->format('H:i');
            }
            $startDateTimeToStoreTimezone = self::toStoreTimeZone($startDateTime, 'Y-m-d H:i');

            if ($startDateTimeToStoreTimezone > $date_time) {
                $date_time = $startDateTimeToStoreTimezone;
            }

            if ($endDateTimeToStoreTimezone < $date_time){
                $date_time = $endDateTimeToStoreTimezone;
            }

            return $date_time;

        }

        return Time::parse($date_time)->format('Y-m-d H:i');

    }

    public static function getDefaultAdjustedRentaldDate($start_date, $duration = null, $rentType = null, $term = 1)
    {
        $base = $start_date = self::getNextAdjustedTime($start_date);
        $start_date = self::min_date($start_date);
        if ($base != $start_date) {
            $start_date = self::getNextAdjustedTime($start_date);
        }
        $end_date = self::getEndDateFromDuration($start_date, $duration, $rentType, $term);

        $end_date = self::getNextAdjustedTime($end_date, 'end');

        return ['start_date' => $start_date, 'end_date' => $end_date];

    }

    /**
     * Get end date using hourly, daily, weekly, monthly duration
     * this function is used find the end date for pricing list
     * @param $rentStart
     * @param null $duration
     * @param null $rentType
     * @param int $term
     * @return false|float|int|string
     */
    public static function getEndDateFromDuration($rentStart, $duration = null, $rentType = null, $term = 1)
    {
        $rentEnd = '';
        $reduceSecond = 0;
        if ($duration && $rentType) {
            $startTimeInSeconds = strtotime($rentStart);
            if ($rentType == 'hourly') {
                $rentEnd = $startTimeInSeconds + (($duration * 3600 * $term) - $reduceSecond);
            } else if ($rentType == 'daily') {
                $rentEnd = $startTimeInSeconds + (($duration * 24 * 3600 * $term) - $reduceSecond);
            } else if ($rentType == 'weekly') {
                $term = (7 * $term);
                $rentEnd = $startTimeInSeconds + (($duration * 24 * 3600 * $term) - $reduceSecond);
            } else if ($rentType == 'monthly') {
                $term = (30 * $term);
                $rentEnd = $startTimeInSeconds + (($duration * 24 * 3600 * $term) - $reduceSecond);
            } else {
                $rentEnd = strtotime($rentStart);
            }
        }
        if ($rentEnd) { //now formatting
            $rentEnd = date('Y-m-d H:i', $rentEnd);
        }
        return $rentEnd;
    }

    /**
     * Returns lead & lag date time.
     * @param string $type
     * @param $date
     */
    public static function leadlag($type = 'lead', $date)
    {
        $data = self::$storeConfig['order'];
        $return_date = $date;
        if ($type == 'lead') {
            if (!empty($data['lead_time_type']) && !empty($data['lead_time_value'])) {
                if ($data['lead_time_type'] == 'D') {
                    $return_date = Time::parse($date)->subDays($data['lead_time_value'])->format('Y-m-d H:i');
                }
                if ($data['lead_time_type'] == 'H') {
                    $return_date = Time::parse($date)->subHours($data['lead_time_value'])->format('Y-m-d H:i');
                }
            }

        } elseif ($type == 'sum_lead') { // used when lead time will be calculated with current time in cart
            if (!empty($data['lead_time_type']) && !empty($data['lead_time_value'])) {
                if ($data['lead_time_type'] == 'D') {
                    $return_date = Time::parse($date)->addDays($data['lead_time_value'])->format('Y-m-d H:i');
                }
                if ($data['lead_time_type'] == 'H') {
                    $return_date = Time::parse($date)->addHours($data['lead_time_value'])->format('Y-m-d H:i');
                }
            }
        } else {
            if (!empty($data['lag_time_type']) && !empty($data['lag_time_value'])) {
                if ($data['lag_time_type'] == 'D') {
                    $return_date = Time::parse($date)->addDays($data['lag_time_value'])->format('Y-m-d H:i');
                }
                if ($data['lag_time_type'] == 'H') {
                    $return_date = Time::parse($date)->addHours($data['lag_time_value'])->format('Y-m-d H:i');
                }
            }
        }
        return $return_date;
    }

    /**
     * This function returns the possible start date after calculating lead time
     * @param $date
     * @return string
     */
    public static function min_date($date)
    {
        $data = self::$storeConfig['order'];
        $return_date = $date;
        if (!empty($data['lead_time_type']) && !empty($data['lead_time_value'])) {
            if ($data['lead_time_type'] == 'D') {
                $return_date = Time::parse($date)->addDays($data['lead_time_value'])->format('Y-m-d H:i');
            }
            if ($data['lead_time_type'] == 'H') {
                $return_date = Time::parse($date)->addHours($data['lead_time_value'])->format('Y-m-d H:i');
            }
        }
        return $return_date;
    }

    /**
     * This function used to get time difference between to dates in ms
     * @param $start
     * @param $end
     * @return false|int
     */
    public function timeDifference($start, $end)
    {
        $diff = strtotime($end) - strtotime($start);
        return $diff;
    }

    /**
     * Format Start and end date
     * @param $start_date
     * @param $end_date
     * @param $product
     * @return array
     */
    public static function formatRentalDates($start_date, $end_date, $product = [])
    {
        $isExactTime = false;
        if (!empty($product))
            $isExactTime = !empty($product['options']) && isset($product['options']['exact_time']) ? $product['options']['exact_time'] : false;

        $optionPrice = self::getRentalPriceOption();
        $rent_start = $start_date;
        $rent_end = $end_date;

        $timeDifference = self::timeDifference($rent_start, $rent_end);
        $days_between = floor(abs($timeDifference) / 86400);

        $disable_default_time = self::$token->disable_default_time ?? false;
        if ($disable_default_time) { // used on project nano , where default dates/times are set from main application
            $rent_start = Time::parse($start_date)->format('Y-m-d H:i:00');
            $rent_end = Time::parse($end_date)->format('Y-m-d H:i:00');
        } // when exact time is activated
        elseif (!$isExactTime) {
            if (empty(self::$storeConfig['show_start_time'])) {
                $start = Time::parse($start_date)->format('Y-m-d');
                $end = Time::parse($end_date)->format('Y-m-d');
                $rent_start = self::getDefaultStartDate($start);
                $rent_end = self::getDefaultEndDate($end);
            } // when only start date is enabled & rental options are disabled
            elseif (empty(self::$storeConfig['show_end_date']) && (empty(self::$storeConfig['show_start_time'])) && empty(self::$storeConfig['rental_price_option'])) {
                $end = Time::parse($end_date)->format('Y-m-d');
                $rent_end = self::getDefaultEndDate($end);
            } // when only start date & time is enabled & rental options are disabled.
            elseif (empty(self::$storeConfig['show_end_date']) && (!empty(self::$storeConfig['show_start_time'])) && empty(self::$storeConfig['rental_price_option']) && ($days_between > 0)) {
                $rental_day = empty(self::$storeConfig['rental_day']) ? '24' : self::$storeConfig['rental_day'];
                $end = Time::parse($end_date)->format('Y-m-d');
                if ($rental_day == 'calendar') {
                    $rent_end = $end . ' ' . '23:00';
                } else {
                    $rent_end = Time::parse($rent_start)->addDays(1)->format('Y-m-d H:i');
                }
            }
        }

        $timeDifference = self::timeDifference($rent_start, $rent_end);
        if ($timeDifference == 0) { // if time difference is 0 , then add one hour with end time so that price will not show 0
            $rent_end = Time::parse($rent_end)->addHour(1)->format('Y-m-d H:00');
        }
        return ['start_date' => $rent_start, 'end_date' => $rent_end];
    }

    /** Get start and end date from exact date calucation from store config */
    public static function extactDates($product_id = '')
    {
//        $dateTime = self::$storeConfig['datetime'];
//        if (empty($dateTime['exact_start_date'])) {
//            return [];
//        };

        // fetch all or product specific exact dates .
        RentMy::addModel(['ExactTimes']);
        $exactDates = RentMy::$Model['ExactTimes']->find()->where(['store_id' => RentMy::$store->id, 'content_type' => 2, 'content_id' => $product_id, 'date IS NOT NULL'])->toArray();
        if (empty($exactDates)) {
            $exactDates = RentMy::$Model['ExactTimes']->find()->where(['store_id' => RentMy::$store->id, 'content_type' => 1, 'date IS NOT NULL'])->toArray();
        }
        $fetched = [];


        foreach ($exactDates as $dates) {
            $fetched = $dates;
            $fetched['duration_values'] = json_decode($fetched['duration_values'], true);
        }
        if (empty($fetched)) {
            return [];
        };

        if (empty($fetched['duration_type']) || empty($fetched['duration_values'])) {
            return [];
        }

        $startDate = Time::parse($fetched['date'])->format('Y-m-d H:i');
        $durationValue = $fetched['duration_values'][0];
        if ($fetched['duration_type'] == 'hour') {
            $endDate = Time::parse($startDate)->addHours($durationValue)->format('Y-m-d H:i');
        } elseif ($fetched['duration_type'] == 'day') {
            $endDate = Time::parse($startDate)->addDays($durationValue)->format('Y-m-d H:i');
        } elseif ($fetched['duration_type'] == 'week') {
            $endDate = Time::parse($startDate)->addWeeks($durationValue)->format('Y-m-d H:i');
        } elseif ($fetched['duration_type'] == 'month') {
            $endDate = Time::parse($startDate)->addMonths($durationValue)->format('Y-m-d H:i');
        }
        return ['start_date' => $startDate, 'end_date' => $endDate];

    }


    public static function exactTimeItemChecker($addToCartRequest)
    {
        if (!empty($addToCartRequest['exact_times']))
            $addToCartRequest['exact_times_id'] = $addToCartRequest['exact_times']['id'];

        if (empty($addToCartRequest['exact_times_id']) || empty($addToCartRequest['rent_start']))
            return $addToCartRequest;

        self::addModel(['Orders', 'ExactTimes']);

        $exactTime = self::$Model['ExactTimes']->find()->where(['store_id' => RentMy::$store->id, 'id' => $addToCartRequest['exact_times_id'], 'time IS NOT NULL'])
            ->first();

        $maxBooking = $exactTime->max_booking;
        if (empty($maxBooking) || $maxBooking <= 0)
            return $addToCartRequest;

        $rentStart = Time::parse($addToCartRequest['rent_start'])->format('Y-m-d H:i:s');
        self::addModel(['Orders', 'Carts']);

        $str = '"exact_times_option":{"id":' . $exactTime->id;
        $usedLimit = self::$Model['Orders']->find()->where([
            'rent_start' => $rentStart,
            'store_id' => self::$store->id,
            "options LIKE" => "%". $str . "%"
        ])->count();

        $where = [
            'rent_start' => $rentStart,
            'store_id' => self::$store->id,
            'order_id is NULL',
            "options LIKE '%" . 'exact_times_option":{"id":"' . $exactTime->id . '"' . "%'"
        ];

        if (!empty($addToCartRequest['token']))
            $where['uid !='] = $addToCartRequest['token'];

        $usedLimit += self::$Model['Carts']->find()->where($where)->count();
        $isExceeded = $usedLimit >= $maxBooking;
        if (!$isExceeded)
            return $addToCartRequest;

        $errorMsg = 'This start time is no longer available. Choose an alternate start time';
        $storeContent = self::getStoreContents();
        if (!empty($storeContent[0]['contents']['cart']['exact_times_max_booking_msg_error']))
            $errorMsg = $storeContent[0]['contents']['cart']['exact_times_max_booking_msg_error'];

        $addToCartRequest['exact_times_limit'] = [
            'message' => $errorMsg,
            'canAdd' => false,
        ];

        return $addToCartRequest;
    }

    /**
     * Fetch exact times list for any product or store products.
     * It is used and added with product details
     * @param string $startDate
     * @param string $product_id
     * @return array
     */
    public static function exactTimes($startDate = '', $product_id = '', $location = '')
    {
//        $dateTime = self::$storeConfig['datetime'];
//        if (empty($dateTime['exact_start_time'])) {
//            return [];
//        };

        if (empty($location))
            $location = RentMy::$token->location;

        // fetch all or product specific exact dates .
        if (!empty($startDate)){
            $startDate = Time::parse($startDate)->format('Y-m-d');
        }else{
            $startDate = Time::now()->format('Y-m-d');
        }

        RentMy::addModel(['ExactTimes']);
        $exactTimes = RentMy::$Model['ExactTimes']->find()
            ->where(['store_id' => RentMy::$store->id, 'content_type' => 2, 'content_id' => $product_id, 'time IS NOT NULL'])
            ->andWhere([
                'OR' => [
                    [
                        'start_date IS NULL',
                        'end_date IS NULL'
                    ],
                    [
                        "DATE_FORMAT(ExactTimes.start_date,'%Y-%m-%d') <=" => $startDate,
                        "DATE_FORMAT(ExactTimes.end_date,'%Y-%m-%d') >=" => $startDate
                    ]
                ]
            ])
            ->toArray();

        if(!empty($product_id)){
            RentMy::addModel(['ReferenceProducts', 'Products']);
            $day = Time::parse($startDate)->format('N');
            $options = [];
            $product = RentMy::$Model['Products']->find()->select(['id', 'options'])->where(['id' => $product_id])->first();
            $options = !empty($product->options) ? json_decode($product->options, true) : [];
            $options['exact_time_ids'] = RentMy::$Model['ReferenceProducts']->find('list', [
                'valueField' => 'reference_id'
            ])->where([
                'reference_type' => 'ExactTime',
                'product_id' => $product_id,
            ])->innerJoinWith('ExactTimes', function ($q) use ($location){
                return $q->where(['ExactTimes.location' => $location]);
            })->toList();

            if (!empty($options['exact_time_with_days']) && !empty($options['exact_time_ids'])){

                $exactTimes = RentMy::$Model['ExactTimes']->find()
                    ->where(['store_id' => RentMy::$store->id, 'location' => $location, 'id IN' => $options['exact_time_ids'], 'time IS NOT NULL'])
                    ->andWhere([
                        'OR' => [
                            [
                                'start_date IS NULL',
                                'end_date IS NULL'
                            ],
                            [
                                "DATE_FORMAT(ExactTimes.start_date,'%Y-%m-%d') <=" => $startDate,
                                "DATE_FORMAT(ExactTimes.end_date,'%Y-%m-%d') >=" => $startDate
                            ]
                        ]
                    ])
                    ->contain(['ExactTimeExceptions' => function ($q) use ($startDate){
                        return $q->select(['id', 'exact_time_id', 'exception_date', 'exception_time', 'type'])
                            ->where(['DATE(exception_date)' => Time::parse($startDate)->format("Y-m-d")]);
                    }])
                    ->map(function ($exactTime) use ($day) {
                        $days = !empty($exactTime['days']) ? json_decode($exactTime['days']) : [];
                        if (!empty($days) && !in_array($day, $days)) {
                            return null;
                        }
                        return $exactTime;
                    })
                    ->filter()
                    ->toArray();

                if (empty($exactTimes))
                    return [];
            }
        }

        if (empty($exactTimes)) {
            $exactTimes = RentMy::$Model['ExactTimes']->find()
                ->where(['store_id' => RentMy::$store->id, 'location' => $location, 'content_type' => 1, 'time IS NOT NULL'])
                ->andWhere([
                    'OR' => [
                        [
                            'start_date IS NULL',
                            'end_date IS NULL'
                        ],
                        [
                            "DATE_FORMAT(ExactTimes.start_date,'%Y-%m-%d') <=" => $startDate,
                            "DATE_FORMAT(ExactTimes.end_date,'%Y-%m-%d') >=" => $startDate
                        ]
                    ]
                ])
                ->contain(['ExactTimeExceptions' => function ($q) use ($startDate){
                return $q->select(['id', 'exact_time_id', 'exception_date', 'exception_time', 'type'])
                    ->where(['DATE(exception_date)' => Time::parse($startDate)->format("Y-m-d")]);
            }])->toArray();
        }
        $fetched = [];

        foreach ($exactTimes as $times) {
            $times['duration_values'] = json_decode($times['duration_values'], true);
            $fetched[] = $times;

        }
        if (empty($fetched)) {
            return ['durations' => [], 'times' => []];
        }


        // set start date
        if (empty($startDate)) {
            $startDate = Time::parse('now')->format('Y-m-d');
        } else {
            $startDate = Time::parse($startDate)->format('Y-m-d');
        }
        // get opening hours
        // $closingTime = '23:00';
        // get seasonal opening hours
        // RentMy::addModel(['Holidays']);
//        $seasonData = RentMy::$Model['Holidays']->find()->where([
//            'store_id' => RentMy::$store->id,
//            'type' => 'season',
//            'start_date <=' => RentMy::toUTC($startDate),
//            'end_date >=' => RentMy::toUTC($startDate),
//        ])->first();
//        $day = date("N", strtotime($startDate));
//        if (!empty($seasonData)) {
//            $holiday = RentMy::$Model['Holidays']->find()
//                ->where([
//                    'store_id' => RentMy::$store->id,
//                    'day' => $day,
//                    'type' => 'day',
//                    'season_id' => $seasonData->id
//                ])
//                ->first();
//        }
//        if (empty($holiday)) {
//            $holiday = RentMy::$Model['Holidays']->find()
//                ->where([
//                    'store_id' => RentMy::$store->id,
//                    'day' => $day,
//                    'type' => 'day'
//                ])->first();
//        }
//        if (!empty($holiday)) {
//            //$openingStartTime = Time::parse($openingStartTime);
//            $closingTime = RentMy::toStoreTimeZone(Time::parse($holiday->end_time));
//            $closingTime = Time::parse($closingTime)->subMinutes(10);
//        } else {
//            //$openingStartTime = Time::parse($openingStartTime);
//            $closingTime = Time::parse($closingTime)->subMinutes(10);
//        }

        //
        $durationValues = $fetched['duration_values'];
        $durations = [];
        //RentMy::dbg($durationValues);exit();
        $j = 0;

        foreach ($fetched as $i => $durationValue) {
            $times = [];
            // $openTime = $openingStartTime;
            $durationValue['time'] = Time::parse($durationValue['time'])->format("Y-m-d H:i:s");
            if (!empty($durationValue['exact_time_exceptions'])){
                $excludeDates = array_values(array_filter($durationValue['exact_time_exceptions'], function ($exception){
                    return $exception['type'] == 'excluded';
                }));

                if (!empty($excludeDates))
                    continue;

                $exceptionDates = array_values(array_filter($durationValue['exact_time_exceptions'], function ($exception){
                    return $exception['type'] == 'excepted';
                }));
                if (!empty($exceptionDates))
                    $durationValue['time'] = Time::parse($exceptionDates[0]['exception_time'])->format("Y-m-d H:i:s");

            }


            if ($durationValue['duration_type'] == 'hour') {
                foreach ($durationValue['duration_values'] as $d) {
                    $hours = floor($d);
                    $minutes = ceil(($d - $hours) * 60);
                    if (empty($hours))
                        $hours = '';
                    $label = ($hours > 1) ? $hours . ' Hours' : $hours . ' Hour';
                    if (!empty($minutes))
                        $label .= ' ' .(($minutes > 1) ? $minutes . ' Minutes' : $minutes . ' Minute');

                    $durations[$d]['id'] = $durationValue['id'];
                    $durations[$d]['value'] = $d;
                    $durations[$d]['dvalue'] = $d;
                    $durations[$d]['type'] = 'hour';
                    $durations[$d]['times'][] = strtotime($durationValue['time']);//Time::parse($durationValue['time'])->format('h:i A');;
                    $durations[$d]['label'] = $label;
                    $durations[$d]['values'][] = ['id' => $durationValue['id'], 'value' => strtotime($durationValue['time'])];

                }

            } elseif ($durationValue['duration_type'] == 'day') {
                foreach ($durationValue['duration_values'] as $d) {

                    $days = floor($d);
                    $hours = ceil(($d - $days) * 24);
                    if (empty($days))
                        $days = '';

                    $label = ($days > 1) ? $days . ' Days' : $days . ' Day';
                    if (!empty($hours))
                        $label .= ' ' . (($hours > 1) ? $hours . ' Hours' : $hours . ' Hour');

                    $dValue = $d * 24;
                    $durations[$dValue]['id'] = $durationValue['id'];
                    $durations[$dValue]['value'] = $d;
                    $durations[$dValue]['dvalue'] = $dValue;
                    $durations[$dValue]['type'] = 'day';
                    $durations[$dValue]['label'] = $label;
                    $durations[$dValue]['times'][] = strtotime($durationValue['time']);//Time::parse($durationValue['time'])->format('h:i A');;
                    $durations[$dValue]['values'][] = ['id' => $durationValue['id'], 'value' => strtotime($durationValue['time'])];
                }
            } elseif ($durationValue['duration_type'] == 'week') {
                foreach ($durationValue['duration_values'] as $d) {

                    $weeks = floor($d);
                    $days = ceil(($d - $weeks) * 7);
                    if (empty($weeks))
                        $weeks = '';
                    $label = ($weeks > 1) ? $weeks . ' Weeks' : $weeks . ' Week';
                    if (!empty($days))
                        $label .= ' ' . (($days > 1) ? $days . ' Days' : $days . ' Day');

                    $dValue = $d * 24 * 7;
                    $durations[$dValue]['id'] = $durationValue['id'];
                    $durations[$dValue]['value'] = $d;
                    $durations[$dValue]['dvalue'] = $dValue;
                    $durations[$dValue]['type'] = 'week';
                    $durations[$dValue]['label'] = $label;
                    $durations[$dValue]['times'][] = strtotime($durationValue['time']);//Time::parse($durationValue['time'])->format('h:i A');;
                    $durations[$dValue]['values'][] = ['id' => $durationValue['id'], 'value' => strtotime($durationValue['time'])];
                }
            } elseif ($durationValue['duration_type'] == 'month') {
                foreach ($durationValue['duration_values'] as $d) {

                    $months = floor($d);
                    $days = ceil(($d - $months) * 30);
                    if (empty($months))
                        $months = '';
                    $label = ($months > 1) ? $months . ' Months' : $months . ' Month';
                    if (!empty($days))
                        $label .= ' ' . (($days > 1) ? $days . ' Days' : $days . ' Day');

                    $dValue = $d * 24 * 7 * 30;
                    $durations[$dValue]['id'] = $durationValue['id'];
                    $durations[$dValue]['value'] = $d;
                    $durations[$dValue]['dvalue'] = $dValue;
                    $durations[$dValue]['type'] = 'month';
                    $durations[$dValue]['label'] = $label;
                    $durations[$dValue]['times'][] = strtotime($durationValue['time']);//Time::parse($durationValue['time'])->format('h:i A');;
                    $durations[$dValue]['values'][] = ['id' => $durationValue['id'], 'value' => strtotime($durationValue['time'])];
                }
            }
        }
        $i = 0;

        foreach ($durations as $d) {
            $eDuration['durations'][$i] = $d;
            $i++;
        }
        $eDurationDurations = !empty($eDuration['durations']) ? $eDuration['durations'] : [];
        $sortedDurations = (new Collection($eDurationDurations))->sortBy('dvalue', 'SORT_ASC')->toArray();
        $valuesData = [];

        $j = 0;
        foreach ($sortedDurations as $i => $duration) {
            $sDuration[$j] = $duration;
            $times = array_unique(Hash::sort($duration['times'], '', 'asc'));
            unset($sDuration[$j]['times']);
            foreach ($times as $time) {
                $sDuration[$j]['times'][] = Time::parse($time)->format('h:i A');
            }
            unset($sDuration[$j]['values']);
            $values = $duration['values'];
            $temp = [];
            foreach ($values as $key => $value) {
                if (in_array($value['value'], $temp)) {
                    continue;
                }
                $temp[] = $value['value'];
                $value['value'] = Time::parse($value['value'])->format('h:i A');
                $sDuration[$j]['values'][] = $value;
                $valuesData[] = $value;
            }
            unset($sDuration[$j]['dvalue']);
            $j++;
        }

        $eDuration['durations'] = !empty($sDuration) ? $sDuration : [];
        $eDuration['times'] = [];
        foreach ($fetched as $i => $time) {

            $estTime = Time::parse($time['time'])->format('h:i A');
            if (!empty($time['exact_time_exceptions'])){
                $excludeDates = array_values(array_filter($time['exact_time_exceptions'], function ($exception){
                    return $exception['type'] == 'excluded';
                }));

                if (!empty($excludeDates))
                    continue;

                $exceptionDates = array_values(array_filter($time['exact_time_exceptions'], function ($exception){
                    return $exception['type'] == 'excepted';
                }));
                if (!empty($exceptionDates))
                    $estTime = Time::parse($exceptionDates[0]['exception_time'])->format("h:i A");

            }
            $eDuration['times'][] = $estTime;

        }
        $eDuration['times'] = array_values(array_unique($eDuration['times']));

        return $eDuration;

    }


    /**
     * Create seourl from any string
     * @param $string
     * @return string
     */
    public static function seoUrl($string, $id = '', $storeId='')
    {

        //Unwanted:  {UPPERCASE} ; / ? : @ & = + $ , . ! ~ * ' ( )
        $string = strtolower(trim($string));
        //Strip any unwanted characters
        $string = preg_replace("/[^a-z0-9_\s-]/", "", $string);
        //Clean multiple dashes or whitespaces
        $string = preg_replace("/[\s-]+/", " ", $string);
        //Convert whitespaces and underscore to dash
        $string = preg_replace("/[\s_]/", "-", $string);

        if (!empty($storeId)){
            $string = self::generateUniqueUrl($string, $storeId, $id);
        }

        return trim($string);
    }

    private static function generateUniqueUrl($slug, $storeId, $id = '')
    {
        RentMy::addModel(['Products']);
        if (!empty($id)){
            $product = RentMy::$Model['Products']->get($id);
            $duplicateUrlProduct = RentMy::$Model['Products']->find()->select(['id', 'url'])->where(['store_id' => $storeId, 'url' => $slug, 'id !=' => $product->id])->first();
        }else{
            $duplicateUrlProduct = RentMy::$Model['Products']->find()->select(['id', 'url'])->where(['store_id' => $storeId, 'url' => $slug])->first();
        }
        if (!empty($duplicateUrlProduct)){
            $slugPieces = explode("-", $slug);
            $lastItem = end($slugPieces);

            $slugPieces = explode("-", $slug);
            $lastItem = end($slugPieces);

            if (is_numeric($lastItem)) {
                // Increment the numeric suffix
                $slug = implode("-", array_slice($slugPieces, 0, -1)) . "-" . ($lastItem + 1);
            } else {
                // Append "-1" if the last piece is not numeric
                $slug .= "-1";
            }
            return self::generateUniqueUrl($slug, $storeId, $id);
        }
        return $slug;
    }

    /**
     * Debug
     */
    public static function dbg($var)
    {
        print_r("<pre>");
        print_r($var);
        print_r("</pre>");
    }

    /**
     * Debug and die process
     */
    public static function dbgAndEnd($var)
    {
        self::dbg($var);
        exit;
    }

    /**
     * Check required fields
     * @param array $keys
     * @param array $arr
     * @return bool
     */
    public static function array_keys_exists(array $keys, array $arr)
    {
        return !array_diff_key(array_flip($keys), $arr);
    }

    /**
     * Checked required fields
     * @param array $data
     * @param array $requiredFields
     * @return array
     */
    public static function requiredKeyExist($data = [], $requiredFields = [])
    {
        $postKeys = array_keys($data);
        $missingFields = array_diff($requiredFields, $postKeys);
        $error = [];
        if (!empty($missingFields)) {
            foreach ($missingFields as $missingField) {
                $error[] = $missingField . ' is required.';
            }
        }
        return $error;
    }


    /*
     *  processing model error
     *  globally used
     * @param $error array
     */
    public static function errorFormat($errors): string
    {
        $res = '';
        if (is_array($errors)) {
            foreach ($errors as $field => $validationMessage) {
                if (is_array($validationMessage[key($validationMessage)])) {
                    $arr = $validationMessage[key($validationMessage)];
                    $r = array_map(function ($item) {
                        return $item;
                    }, $arr);

                    $res = $field . ' : ' . key($r) . ' -> ' . end($r);
                    return $res;

                } else {
                    $msg = $validationMessage[key($validationMessage)];
                }
                $res .= $field . ' : ' . $msg;
                break;
            }
        } else {
            $res = $errors;
        }

        return $res;
    }

    /*
     * current time in APP's timezone
     * Globally Used
     */
    public static function currentTime($is_unix = false, $tz = null)
    {
        return $is_unix == true ? strtotime(Time::parse('now')->format('Y-m-d H:i:s')) : Time::parse('now')->format('Y-m-d H:i:s');
    }


    /*
     * removes array keys from an array
     */
    public static function filter(array $arr, array $removable)
    {
        foreach ($arr as $item => $value) {
            if (in_array($item, $removable)) {
                unset($arr[$item]);
            }
        }
        return $arr;
    }

    /*
     * sending email
     */
    public static function Email(array $options, $transport = null)
    {
        $template = isset($options['template']) ? $options['template'] : 'default';
        $email = new \Cake\Mailer\Email();
        $attachment = '';
        $replyTo = [];
        $options['from_name'] = $replyTo['name'] = env('EMAIL_FROM_NAME', 'RentMy');
        $options['from_email'] = $replyTo['email'] =   env('EMAIL_FROM_EMAIL', '<EMAIL>');

        if (!empty(RentMy::$store->slug)){
            $options['from_name'] = RentMy::$store->slug;
        }

        if (!empty($options['replyTo']))
            $replyTo = $options['replyTo'];

        $bcc = $options['bcc'] ?? '';

        if (isset($options['attachment'])) {
            $attachment = $options['attachment'];
        }
        foreach ($options as $key => $value) {
            if (isset($key) && isset($value)) {
                $email->setViewVars(array($key . '' => $value));
            }
        }

        if (empty($options['to']) && !empty($bcc)) {
            $options['to'] = implode(',', $bcc);
            $bcc = [];
        }

        if (!in_array(strtolower(env('APP_ENV')), ['production', 'prod', ''])) {
            $devEmail = Configure::read('admin_email');
            $options['to'] = $bcc = [];
            $options['to'][] = $devEmail['to'];
            $bcc[] = $devEmail['bcc'];

            if (!empty($options['order'])) {
                $devEmails = Configure::read('order_emails');
                if (!empty($devEmails))
                    foreach ($devEmails as $_email)
                        $options['to'][] = $_email;
            }
        }

        $options['to'] = self::emailRecipientFilter($options['to']);
        $bcc = self::emailRecipientFilter($bcc);

        $options['subject'] = ucwords($options['subject']);

        $smtp = RentMy::$storeConfig['smtp'] ?? [];
        $options['client_smtp'] = $options['client_smtp'] ?? true; // disable store smtp config when send to admin email
        if (!empty($smtp) && $smtp['active'] && ($options['client_smtp'])) {

            if (isset($smtp['smtp_gateway']) && $smtp['smtp_gateway'] == 'gmail') {
                $options['template'] = $template;
                $options['bcc'] = $bcc;
                return (new GoogleServices($smtp))->sendEmail($options);
            }

            if (!empty($smtp['host']) && !empty($smtp['port']) && !empty($smtp['username']) && !empty($smtp['password'])) {
                $smtp['tls'] = !($smtp['tls'] == 'false');
                \Cake\Mailer\TransportFactory::drop('customSMTP');
                \Cake\Mailer\TransportFactory::setConfig('customSMTP', [
                    'host' => $smtp['host'],
                    'port' => $smtp['port'],
                    'username' => $smtp['username'],
                    'password' => $smtp['password'],
                    'tls' => !empty($smtp['tls']),
                    'className' => 'Smtp'
                ]);
                $transport = 'customSMTP';
                $options['from_name'] = $replyTo['name'] = $smtp['name'];
                $options['from_email'] = $replyTo['email'] = $smtp['email'];

                if (!empty($smtp['bcc'])) {
                    if (!empty($bcc)) {
                        if (!is_array($bcc)) {
                            $bcc = [$bcc];
                        }
                    } else {
                        $bcc = [];
                    }
                    array_push($bcc, $smtp['bcc']);
                }
            }
        }

        if (empty($transport)) {
            $email->setTransport('Leaping');
        } else {
            $email->setTransport('customSMTP');
        }

        try {
            if (!empty($attachment))
                $email->setEmailFormat('html')
                    ->setTo($options['to'])
                    ->setFrom([$options['from_email'] => $options['from_name']])
                    ->setReplyTo($replyTo['email'], $replyTo['name'])
                    ->setSubject($options['subject'])
                    ->addAttachments($attachment)
                    ->viewBuilder()->setTemplate($template);
            else
                $email->setEmailFormat('html')
                    ->setTo($options['to'])
                    ->setFrom([$options['from_email'] => $options['from_name']])
                    ->setReplyTo($replyTo['email'], $replyTo['name'])
                    ->setSubject($options['subject'])
                    ->viewBuilder()->setTemplate($template);

            if (!empty($bcc))
                $email->setBcc($bcc);

            $status = $email->send();

            if ($status)
                return true;
            else
                return false;

        } catch (\Exception $exception) {
            RentMy::saveLogFile('error', '-Email config Error, Issue: ' . $exception->getMessage(), ['notification.email']);
            $message = 'SMTP configuration not working. ' . $exception->getMessage();
            $titles = 'From: <EMAIL>' . "\r\n" .
                'Reply-To: <EMAIL>' . "\r\n" .
                'X-Mailer: PHP/' . phpversion();
            mail(Configure::read('admin_email.to'), 'RentMy:: SMTP Error', $message, $titles);
            return false;
        }
    }

    private static final function emailRecipientFilter($emails)
    {
        if (empty($emails))
            return '';

        if (is_array($emails) || is_object($emails)) {
            $toMails = [];
            foreach ($emails as $to) {
                if ($to == '<EMAIL>')
                    continue;

                $toMails[] = $to;
            }
            return $toMails;
        }

        return str_replace('<EMAIL>', '', $emails);
    }

    /*
     * delete a directory
     */
    public static function deleteDirectory($dir)
    {
        if (is_dir($dir)) {
            $objects = scandir($dir);

            foreach ($objects as $object) {
                if ($object != '.' && $object != '..') {
                    if (filetype($dir . '/' . $object) == 'dir') {
                        self::deleteDirectory($dir . '/' . $object);
                    } else {
                        unlink($dir . '/' . $object);
                    }
                }
            }

            reset($objects);
            rmdir($dir);
        }
    }

    /*
     * Pagination wrapper
     * params :
     * $columns  []; // columns will be shown,if empty then all
     * $finder  Query
     * $order  ['field'=>asc / desc ]
     *
     */
    public static function paginationWrapper(Request $request, Query $finder, array $columns, array $order, $Limit = 10)
    {
        $get_params = $request->getQuery();

        $pageNo = empty($get_params['page_no']) ? 1 : $get_params['page_no'];
        $limit = empty($get_params['limit']) ? 10 : $get_params['limit'];
        $offset = ($pageNo - 1) * $limit;
        if (!empty($columns)) {
            $resource = $finder
                ->select($columns)
                ->offset($offset)
                ->order($order)
                ->limit($limit);
        } else {
            $resource = $finder
                ->offset($offset)
                ->order($order)
                ->limit($limit);
        }

        return (object)[
            'resource' => $resource,
            'page_no' => $pageNo,
            'limit' => $limit
        ];
    }


    /*
     * get request params except  pagination Params
     */
    public static function getQueryParams(Request $request)
    {
        $params = $request->getQueryParams();
        //  unset($params['limit']);
        //  unset($params['page_no']);
        return $params;
    }

    public static function trimString(string $string): string
    {
        $replaced = preg_replace('/[^a-zA-Z0-9-_\.]/', ' ', $string);
        $replacedMultiple = preg_replace('/\s+/', ' ', $replaced);
        return trim($replacedMultiple);
    }

    public static function keys_are_equal($array1, $array2): bool
    {
        return !array_diff_key($array1, $array2) && !array_diff_key($array2, $array1);
    }


    /**
     * Upload file .
     * @param $file
     * @param $id
     * @param $type
     * @param bool $dir
     * @param null $max_size
     * @param array $mimes
     * @return array|string
     */

    public static function uploadFile($file, $id, $type, $dir = false, $max_size = null, $mimes = [])
    {
        $mimes = empty($mimes) ? self::MIMES : $mimes;
        $ext = pathinfo($file['name'], PATHINFO_EXTENSION);

        if ($file['error'] != 0) {
            return "Invalid file.File upload failed";
        }

        $size = $file['size'] / (1024 * 1024); //MB

        if (!is_null($max_size) && $max_size < $size) {
            return "Max size exceeded.Try less than " . $max_size . " MB";
        }

        if (!in_array($ext, $mimes)) {
            return "Unsupported file extension";
        }

        // $mimeType = mime_content_type($file['tmp_name']);

        $directory_root = ROOT . DS . 'webroot' . DS . 'upload' . DS . $type . DS;
        $filename = $id . '_' . self::GUID() . '.' . $ext;

        if ($dir == true) {
            $filename = self::GUID() . '.' . $ext;
            if (!is_dir($directory_root . DS . $id)) {
                $directory = $directory_root . DS . $id . DS;
                mkdir($directory, 0755, true);
            } else {
                $directory = $directory_root . DS . $id . DS;
            }
            $directoryPath = DS . 'upload' . DS . $type . DS . $id . DS . $filename;
        } else {
            $directory = $directory_root;
            $directoryPath = DS . 'upload' . DS . $type . DS . $filename;
        }


        try {
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            if (move_uploaded_file($file['tmp_name'], $directory . $filename)) {
                return [
                    'path' => $directoryPath,
                    'file_name' => $filename
                ];
            }

        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Delete any existing file
     * @param $base
     * @param $url
     * @param null $type
     * @return bool
     */
    public static function deleteFile($base, $url, $type = null)
    {
        $file = $base . DS . $url;
        if (is_file($file) && @unlink($file)) {
            return true;
        } else if (is_file($file)) {
            return false;
        } else {
            return false;
        }
    }


    // checks for a password that is at least one capital character and one number and length in 8 character
    public static function checkPassword($new_password, $repeat_password = null)
    {
        if (strlen($new_password) < 8) {
            return "Password length should be minimum 8 character";
        }
        if (preg_match('/^[A-Za-z0-9]*([A-Z][A-Za-z0-9]*\d|\d[A-Za-z0-9]*[A-Z])[A-Za-z0-9]*$/', $new_password) === 0) {
            return "Password should contain at least one capital letter and one number";
        }
        if (strcasecmp($new_password, $repeat_password) !== 0 && $repeat_password !== null) {
            return "New password and repeat password must be same";
        }
        return false;
    }

    /*
     * get route name from request
     */
    public static function getRouteName(Request $request): string
    {
        return Hash::get($request->getAttribute('params'), '_name') ?? '';
    }

    //    create a unique custom GUID
    public static function GUID()
    {
        if (function_exists('com_create_guid') === true) {
            return trim(com_create_guid(), '{}');
        }

        return sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535));
    }

    public static function checkFileType($filename)
    {
        $allowed = ['gif', 'png', 'jpg', 'jpeg', 'bmp', 'webm'];
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        if (!in_array(strtolower($ext), $allowed)) {
            return 'application';
        } else {
            return 'image';
        }
    }

    /**
     * Check the logged in customer is VP or not
     * @param array $options
     */
    public static function checkVP($options = [])
    {
        $isVp = false;
        if (isset(self::$storeConfig['client']) && self::$storeConfig['client']['active']) {// check client port is active
            if ((!empty(self::$token->customer_id)) && (self::$token->customer_type == 2)) {
                $isVp = true;
            }
        }
        return $isVp;
    }

    /**
     * Check the logged in customer is VP or not
     * @param array $options
     */
    public static function formatPricing($pricingData, $options = [])
    {
        $isVp = false;
        if (isset(self::$storeConfig['client']['active']) && self::$storeConfig['client']['active']) {// check client port is active
            if ((!empty(self::$token->customer_id)) && (self::$token->customer_type == 2)) {
                $isVp = true;
            }
        }
        $prices = [];
        if (!empty($pricingData)) {
            $pricingCollection = new Collection($pricingData);
            if ($isVp) {
                $prices = $pricingCollection->filter(function ($price) {
                    return $price['is_vp'] == 1;
                })->toArray();
                if (empty($prices)) {
                    $prices = $pricingCollection->filter(function ($price) {
                        return $price['is_vp'] == 0;
                    })->toArray();
                }
            } else {
                $prices = $pricingCollection->filter(function ($price) {
                    return $price['is_vp'] == 0;
                })->toArray();
            }
            $prices = array_values($prices);
        } else {
            $prices = $pricingData;
        }

        if (isset($options['custom_fields']) && !empty($options['custom_fields'])) {
            RentMy::addModel(['OrderProductOptions']);
            RentMy::$Model['OrderProductOptions']->calculatePricing($prices, $options['custom_fields']);
        }

        return $prices;
    }

    /**
     * Add queue
     * @param $type
     * @param $data
     * @param int $priority
     * @param array $options
     */
    public static function addQueue($type, $data, $priority = 5, $options = [])
    {
        RentMy::addModel(['Queue.QueuedJobs']);
        $data['store_id'] = empty($data['store_id']) ? RentMy::$store->id : $data['store_id'];
        $data['location'] = empty($data['location']) ? RentMy::$token->location : $data['location'];

        $_options = ['reference' => $data['store_id'], 'priority' => $priority];
        if (is_array($options))
            foreach ($options as $key => $value)
                $_options[$key] = $value;

        RentMy::$Model['Queue.QueuedJobs']->createJob($type, $data, $_options);
    }

    /**
     * Get store domain name
     * @param bool $fromWP
     * @return string
     */
    public static function storeDomain($fromWP = false, $isForPayment = false): string
    {
        $storeName = RentMy::$store->name;
        $store_domain = RentMy::$store->domain;
        $wildDomain = env('DOMAIN', '.rentmy.shop');

        if ($isForPayment)
            return 'https://' . $storeName . $wildDomain;

        if ($fromWP)
            $store_domain = null;

        return !empty($store_domain) ? 'https://' . $store_domain : 'https://' . $storeName . $wildDomain;
    }

    /**
     * @param $permission
     * @param string|array $ability
     * @return bool
     */
    public static function canAccess($permission, $ability)
    {
        //@todo check if the store has enabled/disabled for access permission
        if ('store has disabled permission access control' == 'true')
            return true;

        $where = [
            'user_id' => RentMy::$token->id,
            'permission' => $permission,
        ];

        if (is_string($ability))
            $ability = [$ability];

        $invalidAbility = false;
        foreach ($ability as $item) {
            if (!in_array($item, ['edit', 'delete', 'view', 'create']))
                $invalidAbility = $item;

            $where['can_' . $item] = 1;
        }

        if ($invalidAbility) {
            header("Content-Type: application/json");
            http_response_code(500);
            $apiResponse = [
                'error' => "$invalidAbility is the invalid access request for permission: $permission",
                'status' => 'NOK'
            ];
            die(json_encode($apiResponse));
        }

        self::addModel(['permissions']);
        $hasPermission = self::$Model['permissions']->find()->where($where)->count();
        if ($hasPermission > 0)
            return true;

        //if permission has been revoked or no access then terminate the php process
        header("Content-Type: application/json");
        http_response_code(403);
        $apiResponse = [
            'error' => 'Access forbidden!',
            'status' => 'NOK'
        ];
        die(json_encode($apiResponse));
    }

    /**
     * @todo need add logic
     */
    public static function getUserTypeString()
    {
        return 'driver';
    }

    public static function userAccessLocations($tokenData = [])
    {

        if (!empty($tokenData)) {
            $userId = $tokenData['user_id'];
            $role_id = $tokenData['user_type_id'];
        } else {
            $userId = RentMy::$token->id;
            $role_id = RentMy::$token->user_type_id;
        }

        if (in_array($role_id, [1, 2, 3, 4]))
            return false;

        RentMy::addModel(['StoresUsers']);
        if (!self::$storeUser) {
            self::$storeUser = RentMy::$Model['StoresUsers']->find()
                ->where(['user_id' => $userId])
                ->map(function ($user) {
                    $user->user_locations = json_decode($user->user_locations);
                    return $user;
                })
                ->first();
        }

        if (!empty(self::$storeUser->user_locations)) {
            if (!self::$storeUser->location_id || !in_array(self::$storeUser->location_id, self::$storeUser->user_locations)) {
                RentMy::$Model['StoresUsers']->updateAll(['location_id' => self::$storeUser->user_locations[0]], ['id' => self::$storeUser->id]);
                self::$storeUser->location_id = self::$storeUser->user_locations[0];
                RentMy::$token->location_id = self::$storeUser->location_id;
            }
            return self::$storeUser->user_locations;
        }

        return [];
    }

    /**
     * This function is used to add email & sms notifications queue for order only
     * Step 1 : Find the order event name from order status id
     * Step 2 : Find all the notifications for the selected event
     * Step 3 : find the receipts & templates for the notifications
     * Step 4 : set queue when notification trigger immediately
     * Step 5 : set queue when notification trigger before or after .
     * @param $event
     * @param $storeId
     * @param array $options
     * @param array $order
     * @TODO  payment triggers not included yet.
     */

    public static function addNotificationQueue($event, $storeId, $options = [], $order = [])
    {
        RentMy::addModel(['EmailNotifications']);
        //$options['location'] = !empty($options['location']) ? $options['location'] : (!empty(RentMy::$token->location) ? RentMy::$token->location : '');
        if (empty($options['location'])) {
            $options['location'] = !empty(RentMy::$token->location) ? RentMy::$token->location : '';
        }

        if (isset($order['new_status'])) {

            //when old status and new status same skip throwing notifications
            if (isset($order['old_status']) && $order['new_status'] == $order['old_status'])
                return;


            $emailNotification = RentMy::$Model['EmailNotifications']->find()
                ->where(['store_id' => $storeId, 'location' => $options['location'], 'event'=>'order_changed', 'OR' => [
                    ['trigger_on LIKE' => '%order_status":"'. $order['new_status'] .'%'],
                    ['trigger_on LIKE' => '%order_status":'.$order['new_status'].'%']
                ]])->first();

            if (empty($emailNotification)){
                switch ($order['new_status']) {
                    case 1:
                        $event = 'order_cancel';
                        break;
                    case 11:
                        $event = 'order_complete';
                        break;
                    case 5:
                        $event = 'order_with_customer';
                        break;
                    default:
                        RentMy::addQueue('Order', $order); // add order log queue
                        return;
//                case 1:
//                    $event = 'order_amount_paid';
//                    break;
//                case 1:
//                    $event = 'order_amount_authorized';
//                    break;
//                case 1:
//                    $event = 'order_amount_captured';
//                    break;
                }
            }


        }
        self::addModel(['EmailNotifications', 'Users', 'Templates']);

        if ($event == 'daily_digest'){
            $notificationConfigs = RentMy::$Model['EmailNotifications']->find()
                ->where(['store_id' => $storeId])
                ->where(['event' => $event])
//            ->where(['status' => 1])
                ->map(function ($config) {
                    $config->trigger_on = json_decode($config->trigger_on);
                    $config->recipient = json_decode($config->recipient, true);
                    return $config;
                })
                ->toArray();
        }else{
                $where =['EmailNotifications.store_id' => $storeId, 'EmailNotifications.location' => $options['location'], 'EmailNotifications.event' => $event];
                if (!empty($emailNotification) && 'ChangeStatus' == $event){
                    unset($where['EmailNotifications.event']);
                    $where = array_merge($where, ['EmailNotifications.id' => $emailNotification->id]);
                }
                $notificationConfigs = RentMy::$Model['EmailNotifications']->find()
                    ->where($where)
                    ->where(['ReferenceProducts.id IS NULL'])
                    ->contain(['ReferenceProducts'])
                    ->notMatching('ReferenceProducts')
                    ->map(function ($config) {
                        $config->trigger_on = json_decode($config->trigger_on);
                        $config->recipient = json_decode($config->recipient, true);
                        return $config;
                    })
                    ->toArray();

            if (!empty($order['order_id'])){
                RentMy::addModel(['OrderItems', 'ReferenceProducts']);
                $productIdsInOrder = RentMy::$Model['OrderItems']->find('list', [
                    'valueField' => 'product_id'
                ])->where(['order_id' => $order['order_id']])->toList();

                if (!empty($productIdsInOrder)){
                    $notificationConfigsForSpecificProducts = RentMy::$Model['EmailNotifications']->find()
                        ->where($where)
                        ->contain(['ReferenceProducts'])
                        ->matching('ReferenceProducts', function ($q) use ($productIdsInOrder) {
                            return $q->where(['ReferenceProducts.product_id IN' => $productIdsInOrder]);
                        })

                        ->map(function ($config) {
                            $config->trigger_on = json_decode($config->trigger_on);
                            $config->recipient = json_decode($config->recipient, true);
                            return $config;
                        })
                        ->toArray();
                }

                if (!empty($notificationConfigsForSpecificProducts))
                    $notificationConfigs = $notificationConfigsForSpecificProducts;

            }

        }

        if (empty($notificationConfigs))
            return;

        if ($event == 'payment_received'){
            $notificationConfigs = array_values(array_filter($notificationConfigs, function ($config) use ($order) {
                return ($order['payment_count'] == 1 && !empty($config->recipient['first_payment'])) ||
                    (empty($config->recipient['first_payment']));
            }));

            }

        if (isset($options['store']))
            $options['store'] = json_decode(json_encode($options['store']), true);

        foreach ($notificationConfigs as $notificationConfig) {
            if (!$notificationConfig->template_id)
                continue;

            $template = RentMy::$Model['Templates']->find()->select(['title', 'sms_enable', 'email_enable'])->where(['id' => $notificationConfig->template_id])->first();

            if (!$template->sms_enable && !$template->email_enable && ($event != 'daily_digest'))
                continue;

            $taskManager = 'Notification';  // normal notification Queue type
            $data = [
                'event' => $event,
                'type' => $event,
                'store_id' => $storeId,
                'options' => $options,
                'location' => $options['location']
            ];

            if (!empty($options['mobile'])) $data['mobile'] = $options['mobile'];

            if (!empty($order)) {
                $taskManager = 'Order';   // order notification queue type
                if ($event != 'shipment_created') {
                    $data = $order;
                }
            }

            $data['template_id'] = $notificationConfig->template_id;
            $data['recipient'] = ['customer' => false, 'bcc' => []];
            if (!empty($notificationConfig->recipient)) {
                $data['recipient']['customer'] = $notificationConfig['recipient']['customer'] ?? false;
                if (!empty($notificationConfig->recipient['store_users'])) {
                    $storesUsers = self::$Model['Users']->find()
                        ->select(['email' => 'Users.email'])
                        ->where(['Users.id IN' => $notificationConfig->recipient['store_users']])
                        ->toArray();

                    $recipients = [];
                    foreach ($storesUsers as $storesUser)
                        $recipients[] = $storesUser->email;

                    $data['recipient']['bcc'] = $recipients;
                }

                if (!empty($notificationConfig->recipient['reply_to'])) {
                    $storesUser = self::$Model['Users']->find()
                        ->where(['Users.id' => $notificationConfig->recipient['reply_to']])
                        ->first();

                    if (!empty($storesUser)) {
                        $data['recipient']['reply_to'] = [
                            'name' => $storesUser->first_name . ' ' . $storesUser->last_name,
                            'email' => $storesUser->email
                        ];
                    }
                }

                if (!empty($order) && isset($notificationConfig['recipient']['shipping_to_customer']) && $notificationConfig['recipient']['shipping_to_customer']) {
                    self::addModel(['OrderAddresses']);
                    $shipAddresses = self::$Model['OrderAddresses']->find()
                        ->select(['shipping_email'])
                        ->where(['order_id IN' => $order['id'] ?? $order['order_id']]) //mostly order's ID comes with order_id instead ID
                        ->toArray();

                    if (!empty($shipAddresses))
                        foreach ($shipAddresses as $address)
                            if (!empty($address['shipping_email']))
                                $data['recipient']['bcc'][] = $address['shipping_email'];
                }
                if (!empty($order) && !empty($notificationConfig['recipient']['attachments']))
                    $data['recipient']['attachments'] = $notificationConfig['recipient']['attachments'];
            }

            if ((!$data['recipient']['customer'] && empty($data['recipient']['bcc']) && !$template->sms_enable) && $event != 'daily_digest')
                continue;

            $_options = [];
            $_options['notBefore'] = !empty($options['not_before']) ? $options['not_before'] : '';

            if ($notificationConfig->trigger_on->send == 'immediately' && $event != 'daily_digest') {
                if ($event == 'order_rental_reminder_start') {
                    $data['type'] = 'OrderNotify';
                    $data['notification_type'] = 'start';
                    self::addQueue($taskManager, $data);
                    continue;
                }

                if ($event == 'order_rental_reminder_end') {
                    $data['type'] = 'OrderNotify';
                    $data['notification_type'] = 'end';
                    self::addQueue($taskManager, $data);
                    continue;
                }

                self::addQueue($taskManager, $data, 5, $_options);
                continue;
            }

            if ($notificationConfig->trigger_on->send == 'before')
                $type = $notificationConfig->trigger_on->duration_type == 'days' ? 'subDays' : 'subHours';

            if ($notificationConfig->trigger_on->send == 'after')
                $type = $notificationConfig->trigger_on->duration_type == 'days' ? 'addDays' : 'addHours';

            if (isset($type)) {
                if ($event == 'order_rental_reminder_start') {
                    $data['type'] = 'OrderNotify';
                    $data['notification_type'] = 'start';
                    $notBefore = Time::parse($data['rent_start'])->$type((int)$notificationConfig->trigger_on->duration)->format('Y-m-d H:i:s');
                    self::addQueue($taskManager, $data, 5, ['notBefore' => $notBefore]);
                    continue;
                }

                if ($event == 'order_rental_reminder_end') {
                    $data['type'] = 'OrderNotify';
                    $data['notification_type'] = 'end';
                    $notBefore = Time::parse($data['rent_end'])->$type((int)$notificationConfig->trigger_on->duration)->format('Y-m-d H:i:s');
                    self::addQueue($taskManager, $data, 5, ['notBefore' => $notBefore]);
                    continue;
                }

                $notBefore = Time::parse()->$type((int)$notificationConfig->trigger_on->duration)->format('Y-m-d H:i:s');

                if ($event == 'abandoned_cart') {
                    $notBefore = Time::parse($data['created'])->$type((int)$notificationConfig->trigger_on->duration)->format('Y-m-d H:i:s');
                }

                self::addQueue($taskManager, $data, 5, ['notBefore' => $notBefore]);
            }


            if ($event == 'daily_digest'){
                $taskManager = 'Store';
                $data['type'] = 'dailyDigest';
                $data['digest_day'] = Time::parse($options['digest_day'])->format('Y-m-d');
                $data['sending_time'] = $notBefore = Time::parse($options['sending_time'])->format('Y-m-d H:i:s');
                self::addQueue($taskManager, $data, 5, ['notBefore' => $notBefore]);
            }
        }
    }

    public static function getOrderStatus($userRoleId = '', $flatReturn = false, $withDisabled = false)
    {
        if (empty(self::$orderStatus)) {
            self::addModel(['SystemStatus']);
            $allStatus = self::$Model['SystemStatus']->find('threaded')
                ->where(['store_id IN' => [RentMy::$store->id, 0]])
                ->where(['status' => 1, 'enabled' => 1, 'content_type' => 'order'])
                ->where(['reference_id IS NULL'])
                ->order(['sequence' => 'ASC'], true)
                ->map(function ($statuses) {
                    $_status = RentMy::$Model['SystemStatus']->getStoreWiseStatus($statuses);
                    if ($_status['enabled'] == 0)
                        return;

                    $items = [
                        'id' => $_status['content_type_id'] ?? $_status['id'],
                        'label' => $_status['name'],
                        'serial_no' => $_status['sequence'] ?? 0,
                        'color_code' => $_status['color_code'],
                    ];
                    foreach ($statuses['children'] as $status) {
                        $status = RentMy::$Model['SystemStatus']->getStoreWiseStatus($status);
                        if ($status['enabled'] == 0)
                            continue;

                        $items['child'][] = [
                            'id' => $status['content_type_id'] ?? $status['id'],
                            'label' => $status['name'],
                            'serial_no' => $status['sequence'] ?? 0,
                            'color_code' => $status['color_code'],
                        ];
                    }
                    return $items;
                })
                ->toArray();


            foreach ($allStatus as $key => $status) {
                if (empty($status))
                    continue;

                self::$orderStatus[] = $status;
            }
        }

        if (empty(self::$withDisabledOrderStatus) && $withDisabled) {
            self::addModel(['SystemStatus']);
            self::$withDisabledOrderStatus = self::$Model['SystemStatus']->find('threaded')
                ->where(['store_id IN' => [RentMy::$store->id, 'store_id' => 0]])
                ->where(['status' => 1, 'content_type' => 'order'])
                ->where(['reference_id IS NULL'])
                ->order(['sequence' => 'ASC'], true)
                ->map(function ($statuses) {
                    $_status = RentMy::$Model['SystemStatus']->getStoreWiseStatus($statuses);
                    $items = [
                        'id' => $_status['content_type_id'] ?? $_status['id'],
                        'label' => $_status['name'],
                        'serial_no' => $_status['sequence'] ?? 0,
                        'color_code' => $_status['color_code'],
                    ];
                    foreach ($statuses['children'] as $status) {
                        $status = RentMy::$Model['SystemStatus']->getStoreWiseStatus($status);
                        $items['child'][] = [
                            'id' => $status['content_type_id'] ?? $status['id'],
                            'label' => $status['name'],
                            'serial_no' => $status['sequence'] ?? 0,
                            'color_code' => $status['color_code'],
                        ];
                    }
                    return $items;
                })
                ->toArray();
        }

        $allStatus = self::$orderStatus;
        if ($withDisabled)
            $allStatus = self::$withDisabledOrderStatus;

        $serialized = [];
        foreach ($allStatus as $status) {
            $children = [];
            if (array_key_exists('child', $status)) {
                $children = $status['child'];
                unset($status['child']);
            }

            $serialized[] = $status;
            foreach ($children as $child)
                $serialized[] = $child;
        }

        if (!empty($userRoleId)) {
            self::addModel(['Roles']);
            $role = self::$Model['Roles']->find()->select(['system_status_ids'])->where(['id' => $userRoleId])->first();
            $userAllowedStatusIds = json_decode($role->system_status_ids, true);
            if (empty($userAllowedStatusIds))
                return [];

            $userAllowedStatus = [];
            foreach ($serialized as $status)
                if (in_array($status['id'], $userAllowedStatusIds))
                    $userAllowedStatus[] = $status;

            return $userAllowedStatus;
        }

        if ($flatReturn) {
            $flatStatus = [];
            foreach ($serialized as $status)
                $flatStatus[$status['id']] = $status['label'];

            return $flatStatus;
        }

        return self::$orderStatus;
    }

    public static function getOrderStatusStr($statusId)
    {
        $orderStatus = self::getOrderStatus(false, true, true);
        return $orderStatus[$statusId] ?? '';
    }

    /**
     * @param false $online
     * @return array
     */
    public static function getStoreContents($online = false)
    {
        if (!empty(self::$storeContents))
            return self::$storeContents;

        self::addModel(['Contents']);
        $contents = self::$Model['Contents']->find('all')
            ->select(['id', 'type', 'tag', 'contents', 'status', 'label'])
            ->where(['store_id' => self::$store->id, 'status' => 1])
            ->toArray();

        if (!empty($contents)) {
            $contents = self::$Model['Contents']->formatContentData($contents, self::$storeConfig);
            foreach ($contents as $i => $content) {
                if (self::$store['store_type'] == 'FREE') {
                    if ($content['config']['tag'] == 'site_specific') {
                        unset($contents[$i]['contents']['footer']['copyright_text']);
                    }
                }

                if ($online && !empty($content['contents']['cart'])) {
                    foreach ($content['contents']['cart'] as $key => $value) {
                        if (in_array($key, ['lbl_drop_off_pickup', 'lbl_drop_off_move_pickup', 'lbl_drop_off_storage_pickup'])) {
                            $values = explode(',', $value);
                            $value = [];
                            $index = 0;
                            foreach ($values as $str)
                                $value[$index++] = trim($str);

                            $contents[$i]['contents']['cart'][$key] = $value;
                        }
                    }
                }

            }
            self::$storeContents = $contents;
            return $contents;
        }
        return [];
    }

    public static function getMultiStoreDeliveryFlowLabel($flawValue, $name)
    {
        $contents = self::getStoreContents(true);

        $label = '';
        if ($flawValue == 1) {
            $flows = $contents[0]['contents']['cart']['lbl_drop_off_pickup'];
            if ($name == 'drop_off')
                $label = $flows[0] ?? '';

            if ($name == 'pickup')
                $label = $flows[1] ?? '';
        }

        if ($flawValue == 2) {
            $flows = $contents[0]['contents']['cart']['lbl_drop_off_move_pickup'];
            if ($name == 'drop_off')
                $label = $flows[0] ?? '';

            if ($name == 'move')
                $label = $flows[1] ?? '';

            if ($name == 'pickup')
                $label = $flows[2] ?? '';
        }

        if ($flawValue == 3) {
            $flows = $contents[0]['contents']['cart']['lbl_drop_off_storage_pickup'];
            if ($name == 'drop_off')
                $label = $flows[0] ?? '';

            if ($name == 'storage')
                $label = $flows[1] ?? '';

            if ($name == 'pickup')
                $label = $flows[2] ?? '';

        }

        if ($label == '')
            $label = $name;

        return ucwords(str_replace('_', ' ', $label));
    }


    //    Currency Format
    public static function currencyFormat($order = [], $amount = 0, $format = true)
    {
        $currency_format = !empty($order['currency_format']) ? $order['currency_format'] : RentMy::$storeConfig['currency_format'];
        if (empty($currency_format)){
            $currency_format = ['pre' => true, 'post' => true, 'symbol' => '$', 'code' => 'USD', 's' => ''];
        }
        $locale = !empty($currency_format['locale']) ? $currency_format['locale'] : '';

        if ($format) {
            $amount = Number::format($amount, [
                'places' => 2,
                'locale' => $locale
            ]);
        }
        if ($currency_format['pre'] == true && $currency_format['post'] == false) {
            $data = $currency_format['symbol'] . $amount;
        } else if ($currency_format['pre'] == false && $currency_format['post'] == true) {
            $data = $amount . ' ' . $currency_format['code'];
        } else {
            $data = $currency_format['symbol'] . $amount . ' ' . $currency_format['code'];
        }
        return $data;
    }


    /**
     *this function will check barcode config for a store
     * @params $order_id
     * @return boolean
     */

    public static function isBarcode($order_id)
    {
        self::addModel(['Orders']);
        $isActive = false;
        $config = self::$storeConfig;

        if (!empty($config['order']['barcode']['active']))
            $isActive = true;

        if (!empty($config['order']['barcode']) && !empty($config['order']['barcode']['payment_status']) && !empty($config['order']['barcode']['order_status'])) {
            $order = self::$Model['Orders']->find()->where(['store_id' => RentMy::$store->id, 'id' => $order_id])->first();

            if (($config['order']['barcode']['order_status'] == 'all') && ($config['order']['barcode']['payment_status'] == 'all')) {
                $isActive = true;
            } else if ($config['order']['barcode']['order_status'] == 'all') {
                if ($config['order']['barcode']['payment_status'] != $order['payment_status'])
                    $isActive = false;
            } else if ($config['order']['barcode']['payment_status'] == 'all') {
                if ($config['order']['barcode']['order_status'] != $order['status'])
                    $isActive = false;
            } else {
                if (($config['order']['barcode']['order_status'] != $order['status']) || $config['order']['barcode']['payment_status'] != $order['payment_status'])
                    $isActive = false;
            }
        }

        return $isActive;
    }

    public static function engToFrenchDate($str)
    {
//        2277 is JETT Mobility store id
        if (RentMy::$store->id != 2277)
            return $str;
//        $months = [
//            "January"=>"Janvier",
//            "February"=>"Février",
//            "March"=>"Mars",
//            "April"=>"Avril",
//            "May"=>"Peut",
//            "June"=>"Juin",
//            "July"=>"Juillet",
//            "August"=>"Août",
//            "September"=>"Septembre",
//            "October"=>"Octobre",
//            "November"=>"Novembre",
//            "December"=>"Décembre",
//        ];
        $months = [
            "Jan" => "Jan",
            "Feb" => "Fév",
            "March" => "Mars",
            "April" => "Avril",
            "May" => "Peut",
            "June" => "Juin",
            "July" => "Juillet",
            "Aug" => "Août",
            "Sept" => "Septembre",
            "Oct" => "Oct",
            "Nov" => "Nov",
            "Dec" => "Déc",
        ];
        $searchArr = [];
        $replaceArr = [];
        foreach ($months as $eng => $franch) {
            $searchArr[] = $eng;
            $replaceArr[] = $franch;
        }
        $str = str_replace($searchArr, $replaceArr, $str);
        return $str;
    }

    public static function stateToCode($state){
        if (strlen($state) < 3)
            return $state;

        //check if state name matched in database then return code
        self::addModel(['States']);
        $stateEntity = RentMy::$Model['States']->find()->select('code')->where(['name LIKE' => '%' . $state . '%'])->first();
        if (!empty($stateEntity))
            return strtoupper($stateEntity->code);

        //if not matched return full name that passed in
        return $state;
    }

    /**
     * this function will return site specific content
     * @return array
     */
    public static function getSiteLinkContent($keys = '', $default = ''){
        static $cachedContent = null; // Static variable to store cached content

        if ($cachedContent === null) {
            // Content not yet cached, fetch and cache it
            RentMy::addModel(['Contents']);
            $contents = RentMy::$Model['Contents']->find('all')
                ->select(['contents'])
                ->where(['store_id' => RentMy::$store->id, 'location' => RentMy::$token->location, 'type' => 'site-links'])
                ->first();
            $contents = !empty($contents->contents) ? json_decode($contents->contents, true) : '';
            $configContents = Configure::read('site_specific_contents');
            $cachedContent = Hash::merge($configContents, $contents);
        }
        // If keys are provided, return specific content
        if (!empty($keys)){
            $keySet = explode(".", $keys);
            $specificContent = $cachedContent;
            foreach ($keySet as $key){
                if (isset($specificContent[$key])) {
                    $specificContent = $specificContent[$key];
                } else {
                    $specificContent = null; // Key not found, return null
                    break;
                }
            }
            if (empty($specificContent) && !empty($default))
                $specificContent = $default;

            return $specificContent;
        }

        // Return cached content
        return $cachedContent;
    }

    public static function getOrderPrefix(){
        return !empty(self::$storeConfig['order']['order_number_prefix'])?self::$storeConfig['order']['order_number_prefix']:'';
    }
        /**
     * @param $filePath
     * @return string
     */
    public static function makeS3Url($filePath){
        if (empty($filePath)){
            return ;
        }
//        base_url
        $s3Obj = new S3();
        $url = Configure::read('AWSS3.base_url');
        if (empty($url)){
            $url = 'https://s3.us-east-2.amazonaws.com/';
        }
        if (substr($url, -1) != '/'){
            $url = $url . DS;
        }

        $first_character = substr($filePath, 0, 1);

        if ($first_character[0] != '/'){
            $filePath = DS . $filePath;
        }
        return $url . Configure::read('AWSS3.product_bucket') . $filePath;

    }

    /**
     * Log data to Genius Records system
     *
     * This method sends payload data to an external Genius Records API endpoint.
     * It adds the API key from environment variables and makes a POST request.
     *
     * @param array $payload The data to be sent to Genius Records
     * @return array Returns status code and decoded response from the API
     */
    public static function logToGenius($payload)
    {

        try {
            $url = env('GENIUS_RECORDS_URL');
            $key = env('GENIUS_KEY');

            if($url == false || $key == false) {
                return false;
            }

            $payload['key'] = $key;

            if( isset(self::$store->id) && self::$store->id != '' ) {
                $payload['ref1'] = self::$store->id;
            }

            /*
            if( isset(self::$token->id) && self::$token->id != '' ) {
                $payload['ref2'] = self::$token->id;
            }
            */

            if( isset(self::$location->id) && self::$location->id != '' ) {
                $payload['ref3'] = self::$location->id;
            }

            if( empty($payload['account']) && isset(self::$token->source) && self::$token->source != '' ) {
                $payload['account'] = ucfirst(self::$token->source);
            }



            $ch = curl_init();
            $headers  = [
                'Content-Type: application/json',
            ];

            Log::info('logToGenius():$url');
            Log::info(json_encode($url));

            Log::info('logToGenius():$payload');
            Log::info(json_encode($payload));

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec ($ch);

            Log::info('logToGenius():$response');
            Log::info($response);

            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            return [
                'status' => $status,
                'response' => json_decode($response),
            ];
        } catch (\Exception $e) {
            Log::error('logToGenius(): ' . $e->getMessage());
            return [
                'status' => 500,
                'response' => null,
            ];
        }
    }
}
