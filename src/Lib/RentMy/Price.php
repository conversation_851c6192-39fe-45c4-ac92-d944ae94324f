<?php


namespace App\Lib\RentMy;


use Cake\Collection\Collection;
use Cake\Core\Configure;
use Cake\I18n\Time;
use Cake\Utility\Hash;

class Price extends RentMy
{
    private $priceConfig = null;

    /**
     * Get Flat Prize
     * @param @data['product_id'];
     * @param @data['quantity']
     * @param @data['location'];
     * @param @data['rent_start'];
     * @param @data['rent_end'];
     *
     *
     */
    public function getFlatPrice($data)
    {
        self::addModel(['ProductPrices', 'ProductsAvailabilities']);
        $priceType = 3;
        $price = 0;  // 1. set initial price = 0;
        $location = $data['location'] ?? RentMy::$token->location;
        // get price configs
        if (is_null($this->priceConfig)) {
            self::addModel(['PriceAdjustment']);
            $this->priceConfig = self::$Model['PriceAdjustment']->getByProduct($data['product_id']);
        }

        $data['rent_end'] = RentMy::$Model['ProductsAvailabilities']->actualEndDateWithoutEndDateCalculation($data['rent_start'], $data['rent_end']);

        if (!empty(RentMy::$storeConfig['rental_day']) && RentMy::$storeConfig['rental_day'] =='calendar' && (Time::parse($data['rent_start'])->format('Y-m-d') != Time::parse($data['rent_end'])->format('Y-m-d'))) {
            $data['rent_end'] = Time::parse($data['rent_end'])->format('Y-m-d') . ' 23:00';
        }
        //RentMy::dbg($this->priceConfig);
        // 2. get time difference between start & end
        $timeDifference = self::timeDifference($data['rent_start'], $data['rent_end']);
        // 3. get all prices list for this product
        $productPrices = self::$Model['ProductPrices']->find()
            ->where(['variants_products_id' => $data['variants_products_id']])
            ->where(['price_type' => $priceType])
            ->where(['duration_type !=' => 'base'])
            ->where(['location' => $location])
            ->order(['price' => 'DESC'])
            ->toArray();
        $productPrices = RentMy::formatPricing($productPrices, $data);
        if (empty($productPrices)) {
            return $price;
        }

        // 4. get time duration for each product prices
        foreach ($productPrices as $productPrice) {
            $productPrice->price_duration = $productPrice->duration;
            $productPrice->duration = self::priceDuration($productPrice->duration_type, $productPrice->duration);
        }
        // RentMy::dbg($timeDifference);
        // RentMy::dbg($productPrices);
        // 5. get closest lowest option as selected
        $closest = self::getClosestnumber($productPrices, $timeDifference);

        $collection = new Collection($productPrices);
        $selected = $collection->match(['id' => $closest])->first();
        // RentMy::dbg($selected);
        // 6. Find the rest of the time duration
        $restTime = fmod($timeDifference, $selected->duration);

        // 7. if rest time is empty then set full duration prices e.g 3w price, 2 days price
        // promo price - if promo price is empty use the regular price
        if (RentMy::$storeConfig['inventory']['promo_price']) {  // promo price will work when it is activated by suadmin
            $regular_price = !empty($selected->promo_price) ? $selected->promo_price : $selected->price;
        } else {
            $regular_price = $selected->price;
        }
        if (empty($restTime)) {
            $multiplier = ceil($timeDifference / $selected->duration);
            $price += $multiplier * $regular_price;
            // check price adjustment for daily or hourly,if found then check how many days in the adjustment config
            if (in_array($selected->duration_type, ['daily', 'hourly'])) {
                $price += self::priceAdjustment($regular_price, $selected->duration_type,
                    $data['rent_start'], $data['rent_end'], $multiplier);
            }
        }
        // 8. if rest duration exists . next duration must be immediate lower . e.g for month , next selected will be week.
        // for week , it will be days
        else {
            if ($timeDifference - $selected->duration > 0 )
                $nextSelectedTerm = self::getAnotherFlatOptionsTerms($collection, $selected);
            // 9. if next selection is empty, pick the full duration price
            if (!isset($nextSelectedTerm) || $nextSelectedTerm->isEmpty()) {
                $multiplier = ceil($timeDifference / $selected->duration);
                $price += $multiplier * $regular_price;
                if (in_array($selected->duration_type, ['daily', 'hourly'])) {
                    $price += self::priceAdjustment($regular_price, $selected->duration_type,
                        $data['rent_start'], $data['rent_end'], $multiplier);
                }
            }

            // 10. if next duration exist , then pick next duration full price using ceil
            // if next duration full price is greater then previous , then use the previous price
            else {
                $multiplier = floor($timeDifference / $selected->duration);
                $price += $multiplier * $regular_price;
                if ($selected->duration_type == 'daily') {
                    $nextStart_date = Time::parse(Time::parse($data['rent_start'])->timestamp + ($selected->duration * $multiplier))->addHours(1)->format('Y-m-d H:i:00');
                } elseif (in_array($selected->duration_type, ['monthly', 'weekly'])) {
                    $nextStart_date = Time::parse(Time::parse($data['rent_start'])->timestamp + ($selected->duration * $multiplier))->addDays(1)->format('Y-m-d H:i:00');
                } else {
                    $nextStart_date = Time::parse(Time::parse($data['rent_start'])->timestamp + ($selected->duration * $multiplier))->format('Y-m-d H:i:00');
                }

                if (in_array($selected->duration_type, ['daily', 'hourly'])) {
                    $price += self::priceAdjustment($regular_price, $selected->duration_type,
                        $data['rent_start'], $nextStart_date, $multiplier);
                }
                $newSelected = $nextSelectedTerm->first();
                // get price from Rest time from another options
                $multiplier = ceil($restTime / $newSelected->duration);

                if (RentMy::$storeConfig['inventory']['promo_price']) {
                    $new_regular_price = !empty($newSelected->promo_price) ? $newSelected->promo_price : $newSelected->price;
                } else {
                    $new_regular_price = $newSelected->price;
                }
                $extraTimeRate = $multiplier * $new_regular_price;

                if ($extraTimeRate > $price) {
                    $price += $regular_price;
                    $price_for_adjustment = $regular_price;

                } else {
                    $price += $multiplier * $new_regular_price;
                    $price_for_adjustment = $new_regular_price;
                    if (in_array($newSelected->duration_type, ['daily', 'hourly'])) {
                        $price += self::priceAdjustment($price_for_adjustment, $newSelected->duration_type,
                            $nextStart_date, $data['rent_end'], $multiplier);
                    }
                }
                //RentMy::dbg($multiplier);
                // set start date after calculating first combinations
                // RentMy::dbg(Time::parse(($selected->duration*3) + Time::parse($data['rent_start'])->timestamp))->format('Y-m-d H:i');


            }
        }
        // 11. find other options prices that have highest durations. if largest duration options provide less prices then pick them.
        $price = self::getLargeOptionsPrices($collection, $selected, $price, $data);
        return $price;
    }

//    /**
//     * @param $data
//     * @param $priceObj //
//     * @param $priceAmount //flat price
//     * @return int
//     * @throws \Exception
//     * Please use this function after complete flat price calculation
//     */
//    private function priceAdjustment($data, $priceObj, $priceAmount)
//    {
//        //let
//        $price = 0;
//        $acceptedDurationTypes = ['daily', 'hourly'];
//
//        //1. check if this store has enabled this feature
//        if (!true) //@todo
//            return $price;
//
//        //2. only work with daily and hourly price types
//        if (!in_array($priceObj->duration_type, $acceptedDurationTypes))
//            return $price;
//
//        //3. if already not load, get price config for this product
//        if (is_null($this->priceConfig)) {
//            self::addModel(['PriceAdjustment']);
//            $this->priceConfig = self::$Model['PriceAdjustment']->getByProduct($data['product_id']);
//        }
//
//        if (empty($this->priceConfig))
//            return $price;
//
//        //4. again check price object with price config
//        if ($priceObj->duration_type != $acceptedDurationTypes[(--$this->priceConfig->duration_type)]) //price config duration type start index from 1 so decreased
//            return $price;
//
//        //5. get unit price
//        $unitPrice = $this->priceConfig->amount_type == 1 ? $priceAmount - ($priceAmount * $this->priceConfig->amount/100) : $this->priceConfig->amount;
//
//        //6. calculate days from the date range
//        $days = [];
//        for ($date =  new \DateTime($data['rent_start']); $date <= new \DateTime($data['rent_end']); $date->modify('+1 day')) {
//            $day = $date->format('l');
//            if (isset($days[$day]))
//                $days[$day]++;
//            else
//                $days[$day] = 1;
//        }
//
//        //5. now calculate price for each day
//        foreach ($days as $day => $count){
//            if (in_array(ucfirst($day), $this->priceConfig->days))
//                $price += $unitPrice * $count;
//        }
//
//        return $price;
//    }


    /**
     * @param $price
     * @param $price_duration
     * @param $rent_start
     * @param $rent_end
     * @param $multiplier
     * @return float|int
     */
    function priceAdjustment($price, $price_duration, $rent_start, $rent_end, $multiplier)
    {
        $adjusted_price = 0;
        // check days config exist & super admin access for price adjustments
        if (empty($this->priceConfig->days) && empty(RentMy::$storeConfig['inventory']['price_adjustment']))
            return $adjusted_price;

        // check duration with config duration
        $duration_config = ($this->priceConfig->duration_type == 2) ? 'hourly' : 'daily';
        if (trim($duration_config) != trim($price_duration))
            return $adjusted_price;

        $days = [];
        $unitPrice = $this->priceConfig->amount_type == 1 ? ($price * $this->priceConfig->amount / 100) : $this->priceConfig->amount;
        for ($date = Time::parse($rent_start); $date <= Time::parse($rent_end); $date->addDays(1)) {
            $day = $date->format('l');
            if (isset($days[$day]))
                $days[$day]++;
            else
                $days[$day] = 1;
        }

        //5. now calculate price for each day
        $total_days = 0;
        foreach ($days as $day => $count) {
            if( !is_array($this->priceConfig->days) ) continue;
            if (in_array(ucfirst($day), $this->priceConfig->days)) {
                $total_days += $count;
            }
        }

        if ($duration_config == 'hourly') {
            $adjusted_price = $total_days * $unitPrice * $multiplier;
        } else {
             $lastDay =  Time::parse($rent_end)->format('l');

            if (self::$storeConfig['rental_day'] == '24' && in_array(ucfirst($lastDay), $this->priceConfig->days) && ($total_days > 1))
                $total_days--;

            $adjusted_price = $total_days * $unitPrice;
        }

        return $adjusted_price;

    }

    /**
     * If selected options price is higher than other higher options prices
     * then select the lower option price
     * @param $productPrices
     * @param $selected
     * @param $price
     * @return mixed
     */
    function getLargeOptionsPrices($productPrices, $selected, $price, $data)
    {
        $lowestPrice = $price;
        $prices = $productPrices->filter(function ($productPrice, $key) use ($selected, $price) {
            return
                ($productPrice->id != $selected->id) &&  // not initial selected option
                ($productPrice->duration > $selected->duration) &&  // higher than initial selected option
                ($price > $productPrice->price);  // higher option has  lower price

        });
        if (!$prices->isEmpty()) {
            $lowest = $prices->first();
            $lowestPrice = $lowest->price;
            if (in_array($lowest->duration_type, ['daily', 'hourly'])) {
                $lowestPrice += self::priceAdjustment($lowestPrice, $lowest->duration_type, $data['rent_start'], $data['rent_end'],1);
            }
        }
        return $lowestPrice;
    }

    /**
     * @param $productPrices - all pricing options
     * @param $selected - selected pricing options
     * @return mixed - next selected pricing options
     */
    function getAnotherFlatOptionsTerms($productPrices, $selected)
    {
        $prices = $productPrices->reject(function ($price, $key) use ($selected) {
            return $price->id === $selected->id;
        });
        if (!$prices->isEmpty()) {
            if ($selected->duration_type == 'monthly') {
                $newterm = 'weekly';
            } elseif ($selected->duration_type == 'weekly') {
                $newterm = 'daily';
            } elseif ($selected->duration_type == 'daily') {
                $newterm = 'hourly';
            }
        }
        if (!empty($newterm)) {
            $prices = $productPrices->filter(function ($price, $key) use ($newterm) {
                return $price->duration_type === $newterm;
            });
        }
        return $prices;

    }

    /**
     * Get pricing duration for each term
     * @param $duration_type
     * @param $duration
     * @return float|int
     */
    public function priceDuration($duration_type, $duration)
    {
        $priceDuration = 1;
        if ($duration_type == 'hourly') {
            $priceDuration = $duration * 60 * 60;
        } else if ($duration_type == 'daily') {
            $priceDuration = $duration * 60 * 60 * 24;
        } else if ($duration_type == 'weekly') {
            $priceDuration = $duration * 60 * 60 * 24 * 7;
        } else if ($duration_type == 'monthly') {
            $priceDuration = $duration * 60 * 60 * 24 * 30;
        }
        return $priceDuration;
    }


    /**
     * @param $data
     * @return mixed
     * Step 1 : get time difference of start & end date
     * Step 2 : get time difference for all pricing combinations.
     * Step 3 : get the closes price object from rental time duration
     * Step 4: sort all pricing combination based on time duration asc
     * Step 5 : now get the get price multiplier ceil (time difference / price duration ) e.g input duration 10 days, pricing 3 days , so multiplier will be 3 , for 7 days multiplier will be 2
     * Step 6 : now get the list where price max range & addition pricing needs to be calculated . ie. if input is 10 days , multiplier = 3 & flex price > 0 ,
     *          count those prices where (multiplier - max_range) > 0 , then calculated pricing with addititional pricing
     * step 7 : otherwise price = price * multiplier
     */
    public function getFlexPrice($data)
    {
        RentMy::addModel(['ProductPrices', 'ProductsAvailabilities']);
        $priceType = 4;
        $price = 0;  // 1. set initial price = 0;
        // 2. get time difference between start & end
        $location = $data['location'] ?? RentMy::$token->location;

        $data['rent_end'] = RentMy::$Model['ProductsAvailabilities']->actualEndDateWithoutEndDateCalculation($data['rent_start'], $data['rent_end']);

        if (!empty(RentMy::$storeConfig['rental_day']) && RentMy::$storeConfig['rental_day'] =='calendar' && (Time::parse($data['rent_start'])->format('Y-m-d') != Time::parse($data['rent_end'])->format('Y-m-d'))) {
            $data['rent_end'] = Time::parse($data['rent_end'])->format('Y-m-d') . ' 23:00';
        }

        $timeDifference = self::timeDifference($data['rent_start'], $data['rent_end']);
        if ($timeDifference <= 0)
            $timeDifference = (60 * 60);

        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            $productPrices = RentMy::$Model['ProductPrices']->find()
                ->where(['variants_products_id' => $data['variants_products_id']])
                ->where(['price_type' => 4])
                ->where(['location' => $location])
                ->order(['price' => 'DESC'])
                ->toArray();
            $productPrices = RentMy::formatPricing($productPrices, $data);
            foreach ($productPrices as $productPrice) {
                $productPrice->time_duration = self::priceDuration($productPrice->duration_type, $productPrice->duration);
                $allDurations[] = ['id' => $productPrice->id, 'duration' => $productPrice->time_duration];
            }
            $closest = RentMy::getClosestnumber($allDurations, $timeDifference);
            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
            $allPrice = array();
            foreach ($productPrices as $i => $priceData) {
                $priceDuration = self::priceDuration($priceData->duration_type, $priceData->duration);
                $multiplier = ceil($timeDifference / $priceDuration);
                if (($multiplier - $priceData->max_range > 0) && (!empty($priceData->flex_price))) {
                    $basicPrice = $priceData->price * $priceData->max_range;
                    $basicDuration = $priceDuration * $priceData->max_range;

                    $restDuration = ($timeDifference - $basicDuration);
                    $flexPriceDuration = self::priceDuration($priceData->duration_type, $priceData->flex_duration);
                    $flexMultiplier = $restDuration > 0 ? ceil($restDuration / $flexPriceDuration) : 0;
                    $additionPrice = $priceData->flex_price * $flexMultiplier;

                    $price = $basicPrice + $additionPrice;
                    $allPrice[] = $price;
                } else {
                    $price = ($priceData->price * $multiplier);
                    $allPrice[] = $price;

                }
            }
            return min($allPrice);
        }
    }


    /**
     * @param $data
     * @return mixed
     * Step 1 : get time difference of start & end date
     * Step 2 : get time difference for all pricing combinations.
     * Step 3 : get the closes price object from rental time duration
     * Step 4: sort all pricing combination based on time duration asc
     * Step 5 : now get the get price multiplier ceil (time difference / price duration ) e.g input duration 10 days, pricing 3 days , so multiplier will be 3 , for 7 days multiplier will be 2
     * Step 6 : now get the list where price max range & addition pricing needs to be calculated . ie. if input is 10 days , multiplier = 3 & flex price > 0 ,
     *          count those prices where (multiplier - max_range) > 0 , then calculated pricing with addititional pricing
     * step 7 : otherwise price = price * multiplier
     */
    public function getRecurringPrice($data)
    {
        RentMy::addModel(['ProductPrices', 'ProductsAvailabilities']);
        $priceType = 5;
        $price = 0;  // 1. set initial price = 0;
        // 2. get time difference between start & end
        $location = $data['location'] ?? RentMy::$token->location;

        $data['rent_end'] = RentMy::$Model['ProductsAvailabilities']->actualEndDateWithoutEndDateCalculation($data['rent_start'], $data['rent_end']);

        $timeDifference = self::timeDifference($data['rent_start'], $data['rent_end']);
        if ($timeDifference <= 0)
            $timeDifference = (60 * 60);

        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
            $productPrices = RentMy::$Model['ProductPrices']->find()
                ->where(['variants_products_id' => $data['variants_products_id']])
                ->where(['price_type' => 5])
                ->where(['id' => $data['price_id']])
                ->where(['location' => $location])
                ->order(['price' => 'DESC'])
                ->toArray();
            $productPrices = RentMy::formatPricing($productPrices, $data);
            foreach ($productPrices as $productPrice) {
                $productPrice->time_duration = self::priceDuration($productPrice->duration_type, $productPrice->duration);
                $allDurations[] = ['id' => $productPrice->id, 'duration' => $productPrice->time_duration];
            }
            $closest = RentMy::getClosestnumber($allDurations, $timeDifference);
            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
            $allPrice = array();
            foreach ($productPrices as $i => $priceData) {
                $priceDuration = self::priceDuration($priceData->duration_type, $priceData->duration);
                $multiplier = ceil($timeDifference / $priceDuration);
                if (RentMy::$storeConfig['inventory']['promo_price']) {  // promo price will work when it is activated by suadmin
                    $regular_price = !empty($priceData->promo_price) ? $priceData->promo_price : $priceData->price;
                } else {
                    $regular_price = $priceData->price;
                }
                $price = ($regular_price * $multiplier);
                $allPrice[] = $price;
            }
            return min($allPrice);
        }
    }
    /**
     * @param $data ['rent_start']
     * @param $data ['rent_end']
     * @param $data ['variants_products_id']
     * @return mixed
     */
//    public function getFlexPrice($data)
//    {
//        self::addModel(['ProductPrices']);
//        $priceType = 4;
//        $price = 0;  // 1. set initial price = 0;
//        // 2. get time difference between start & end
//        $timeDifference = self::timeDifference($data['rent_start'], $data['rent_end']);
//        if ($timeDifference <= 0)
//            $timeDifference = (60 * 60);
//
//        if (!empty($data['rent_start']) && !empty($data['rent_end'])) {
//            $productPrices = RentMy::$Model['ProductPrices']->find()
//                ->where(['variants_products_id' => $data['variants_products_id']])
//                ->where(['price_type' => 4])
//                ->order(['price' => 'DESC'])
//                ->toArray();
//            $productPrices = RentMy::formatPricing($productPrices);
//            foreach ($productPrices as $productPrice) {
//                $productPrice->time_duration = self::priceDuration($productPrice->duration_type, $productPrice->duration);
//                $allDurations[] = ['id' => $productPrice->id, 'duration' => $productPrice->time_duration];
//            }
//            $closest = RentMy::getClosestnumber($allDurations, $timeDifference);
//            $productPrices = Hash::sort($productPrices, '{n}.time_duration', 'asc');
//            $allPrice = array();
//            foreach ($productPrices as $i => $priceData) {
//                if ($closest == $priceData->id) {
//                    $priceDuration = self::priceDuration($priceData->duration_type, $priceData->duration);
//                    $multiplier = ceil($timeDifference / $priceDuration);
//                    if ($multiplier - $priceData->max_range > 0) {
//                        $basicPrice = $priceData->price * $priceData->max_range;
//                        $basicDuration = $priceDuration * $priceData->max_range;
//
//                        $restDuration = ($timeDifference - $basicDuration);
//                        $flexPriceDuration = self::priceDuration($priceData->duration_type, $priceData->flex_duration);
//                        $flexMultiplier = $restDuration > 0 ? ceil($restDuration / $flexPriceDuration) : 0;
//                        $additionPrice = $priceData->flex_price * $flexMultiplier;
//                        $price = $basicPrice + $additionPrice;
//                    } else {
//                        $price = ($priceData->price * $multiplier);
//                    }
//                }
//                //$allPrice[] = $price;
//            }
//            //return min($allPrice);
//        }
//        return $price;
//    }
}
