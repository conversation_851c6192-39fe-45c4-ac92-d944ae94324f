<?php


namespace App\Lib;


use App\Lib\RentMy\RentMy;
use Cake\Core\Configure;
use Cake\Http\Client;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;

/**
 * Class Shipengine
 * @package App\Lib
 */
class Serviceobject
{

    public $config;
    private $http;
//    private $baseUrl = 'https://trial.serviceobjects.com/ft/web.svc/JSON/';
//    private $taxType = 'sales'; // use
//    private $licenseKey = 'WS19-BWG1-ZTH3';

    /** production keys */
    private $baseUrl = 'https://sws.serviceobjects.com/ft/web.svc/JSON/';
    private $taxType = 'sales'; // use
    private $licenseKey = 'WS19-YGZ3-FWP2';

    /**
     * Shipengine constructor.
     * @param $config
     */
    public function __construct()
    {
        $this->http = new Client();
    }

    public function getRate($data)
    {
        if ($data['country'] == 'ca') {
            return self::getCanadianTaxInfoByProvince($data);
        } else {
            return self::getBestMatch($data);
        }

    }

    function getCanadianTaxInfoByProvince($data)
    {
        RentMy::addModel(['Tax']);
        $tax = RentMy::$Model['Tax']->find()->where([
            'lookup' => 'Serviceobject',
            //'city' => $data['city'],
            'state' => $data['state'],
            'country' => $data['country']
        ])->first();
        if (!empty($tax)) {
            return ['tax_id' => $tax->id, 'tax_rate' => $tax['tax_rate']];
        } else {
            $postData = [
                'Province' => $data['state'] ? rawurlencode($data['state']) : '',
                'LicenseKey' => $this->licenseKey
            ];
            $get_fields_string = '';
            foreach ($postData as $key => $value) {
                $get_fields_string .= $key . '=' . $value . '&';
            }
            rtrim($get_fields_string, '&');
            $get_fields_string = '?' . $get_fields_string;
            try {
                //echo $this->baseUrl . 'GetCanadianTaxInfoByProvince' . $get_fields_string;
                // https://ws.serviceobjects.com/ft/web.svc/JSON/GetCanadianTaxInfoByProvince?Province=Manitoba&LicenseKey=WS19-YGZ3-FWP2
                $response = $this->http->get($this->baseUrl . 'GetCanadianTaxInfoByProvince' . $get_fields_string, ['type' => 'json']);
                $responseData = $response->getJson();
                if (!empty($responseData)) {
                        $serviceData = [
                            'lookup' => 'Serviceobject',
                            'city' => $data['city'],
                            'county' => $data['county'],
                            'state' => $data['state'],
                            'zipcode' => $data['zipcode'],
                            'country' => $data['country'],
                            'tax_rate' => $responseData['HarmonizedSalesTax'],
                            'state_rate' => $responseData['ProvinceSalesTax'],
                            //'city_rate' => $r['CityRate'],
                            //'county_rate' => $r['CountyRate'],
                            //'city_district_rate' => $r['CityDistrictRate'],
                            //'county_district_rate' => $r['CountyDistrictRate'],
                            //'special_district_rate' => $r['SpecialDistrictRate'],
                            'response' => json_encode($responseData)
                        ];
                        $tax = RentMy::$Model['Tax']->newEntity($serviceData);
                        RentMy::$Model['Tax']->save($tax);
                    return ['tax_id' => $tax->id, 'tax_rate' => $tax['tax_rate']];
                }
            } catch (\Exception $e) {
                //RentMy::dbg($e);
            }
        }
        //RentMy::dbg($response->getJson());
    }

    /**
     * Get shipping rates
     * @param $data
     * @return Client\Response
     */
    public function getBestMatch($data)
    {
        RentMy::addModel(['Tax', 'States']);
        if (!empty($data['state']) && strlen($data['state']) > 2) {
            $state = RentMy::$Model['States']->find()->where(['name LIKE' => $data['state']])->first();
            if (!empty($state)) {
                $data['state'] = $state->code;
            }
        }
        $tax = RentMy::$Model['Tax']->find()->where([
            'lookup' => 'Serviceobject',
            'city' => $data['city'],
            'state' => $data['state'],
            'zipcode'=> $data['zipcode'],
            'country' => $data['country']
        ])->first();
        if (!empty($tax)) {
            return ['tax_id' => $tax->id, 'tax_rate' => $tax['tax_rate']];
        } else {
            $postData = [
                'Address' => $data['address1'] ? rawurlencode($data['address1']) : '',
                'City' => $data['city'] ? rawurlencode($data['city']) : '',
                'State' => $data['state'] ? rawurlencode($data['state']) : '',
                'Zip' => $data['zipcode'] ? rawurlencode($data['zipcode']) : '',
                'TaxType' => $this->taxType,
                'LicenseKey' => $this->licenseKey
            ];
            $get_fields_string = '';
            foreach ($postData as $key => $value) {
                $get_fields_string .= $key . '=' . $value . '&';
            }
            rtrim($get_fields_string, '&');
            $get_fields_string = '?' . $get_fields_string;
            try {
                //echo $this->baseUrl . 'GetBestMatch' . $get_fields_string;
                $response = $this->http->get($this->baseUrl . 'GetBestMatch' . $get_fields_string, ['type' => 'json']);
                $responseData = $response->getJson();
                if ( isset($responseData['TaxInfoItems']) && is_iterable($responseData['TaxInfoItems']) ) {
                    foreach ($responseData['TaxInfoItems'] as $r) {
                        $serviceData = [
                            'lookup' => 'Serviceobject',
                            'city' => $data['city'],
                            'county' => $data['county'],
                            'state' => $data['state'],
                            'zipcode' => $data['zipcode'],
                            'country' => $data['country'],
                            'tax_rate' => $r['TaxRate'],
                            'state_rate' => $r['StateRate'],
                            'city_rate' => $r['CityRate'],
                            'county_rate' => $r['CountyRate'],
                            'city_district_rate' => $r['CityDistrictRate'],
                            'county_district_rate' => $r['CountyDistrictRate'],
                            'special_district_rate' => $r['SpecialDistrictRate'],
                            'response' => json_encode($responseData)
                        ];
                        $tax = RentMy::$Model['Tax']->newEntity($serviceData);
                        RentMy::$Model['Tax']->save($tax);

                    }
                    return ['tax_id' => $tax->id, 'tax_rate' => $tax['tax_rate']];
                }
            } catch (\Exception $e) {
                //RentMy::dbg($e);
            }
        }
        //RentMy::dbg($response->getJson());


    }
}


