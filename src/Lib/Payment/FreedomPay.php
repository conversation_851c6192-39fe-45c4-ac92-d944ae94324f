<?php

namespace App\Lib\Payment;

use App\Lib\RentMy\RentMy;
use Cake\Utility\Xml;

class FreedomPay
{
    private $esKey, $storeId, $terminalId, $baseURL, $baseURLFreeway, $posSyncAttemptNum = 1;

    private $version = '1.5';

    /**
     * @param $config
     */
    public function __construct($config)
    {
        $this->esKey = $config['es_key'];
        $this->storeId = $config['store_id'];
        $this->terminalId = $config['terminal_id'];

        $this->baseURL = 'https://hpc.freedompay.com';
//        $this->baseURLFreeway = 'https://cs.freedompay.com/Freeway/Service.asmx?WSDL';
        $this->baseURLFreeway = 'https://cs12.freedompay.com/Freeway/Service.asmx';
        if (isset($config['is_live']) && (($config['is_live'] == 'false') || ($config['is_live'] == false))) {
            $this->baseURL = 'https://hpc.uat.freedompay.com';
            $this->baseURLFreeway = 'https://cs.uat.freedompay.com/Freeway/Service.asmx';
        }

    }


    /**
     * @param $prams
     * @return array
     * @throws \SoapFault
     */
    public function initFrame($prams = [], $paymentMethod = null)
    {
        if (!empty($prams['test'])) {
            $this->__construct($prams['config']);
            $formattedData = [
                'address_1' => '30 Park ave',
                'amount' => '1',
                'cart_id' => '00000',
                'city' => 'New York',
                'email' => '<EMAIL>',
                'first_last' => 'Rentmy',
                'first_name' => 'Support',
                'mobile' => '',
                'postal_code' => '11096',
            ];
            $prams['amount'] = $formattedData['amount'];
        } else {
            RentMy::addModel(['Orders', 'Carts', 'Customers']);
            if (!empty($prams['type']) && ($prams['type']) == 'cart') {
                $formattedData = [
                    'address_1' => $prams['address']['address_line1'],
                    'amount' => $prams['amount'],
                    'cart_id' => $prams['order_id'],
                    'city' => $prams['address']['city'],
                    'email' => $prams['address']['email'],
                    'first_last' => $prams['address']['first_name'],
                    'first_name' => $prams['address']['last_name'],
                    'mobile' => $prams['address']['mobile'],
                    'postal_code' => $prams['address']['zipcode'],
                ];
                $customer = RentMy::$Model['Customers']->find()->where(['store_id' => RentMy::$store->id, 'email like ' => '%' . $prams['address']['email'] . '%'])
                    ->map(function ($cust) {
                        $gateways = json_decode($cust['gateway'], true);
                        $cust['gateway'] = array_filter($gateways, function ($ref) {
                            return $ref['gateway'] == 'FreedomPay';
                        });
                        $cust['gateway']=array_values($cust['gateway']);
                        return $cust;
                    })
                    ->first();
                if(!empty($customer['gateway'][0]) && !empty($prams['paytype']) && ($prams['paytype']=='freedompay_token')) {
                    $formattedData['TokenInformation'] = [
                        'token' => $customer['gateway'][0]['vaultKey']['card_token'],
                        'exp_month' => sprintf("%02d", $customer['gateway'][0]['vaultKey']['exp_month']),
                        'exp_year' => $customer['gateway'][0]['vaultKey']['exp_year'],
                    ];
                }
                $formattedData['cart_id'] = $prams['order_id']; // cart id is used for unique identification
            } else {
                $order = RentMy::$Model['Orders']->find()->where(['id' => $prams['order_id']])->first();
                $cart = RentMy::$Model['Carts']->find()->where(['order_id' => $prams['order_id']])->first();
                $formattedData = [
                    'address_1' => $order->address_line1,
                    'amount' => $prams['amount'],
                    'cart_id' => $cart->id,
                    'city' => $order->city,
                    'email' => $order->email,
                    'first_last' => $order->first_name,
                    'first_name' => $order->last_name,
                    'mobile' => $order->mobile,
                    'postal_code' => $order->zipcode,
                ];

                $customer = RentMy::$Model['Customers']->find()->where(['store_id' => RentMy::$store->id, 'id' => $order->customer_id])
                    ->map(function ($cust) {
                        $gateways = json_decode($cust['gateway'], true);
                        $cust['gateway'] = array_filter($gateways, function ($ref) {
                            return $ref['gateway'] == 'FreedomPay';
                        });
                        $cust['gateway']=array_values($cust['gateway']);
                        return $cust;
                    })
                    ->first();
                if(!empty($customer['gateway'][0]) && !empty($prams['paytype']) && ($prams['paytype']=='freedompay_token')) {
                    $formattedData['TokenInformation'] = [
                        'token' => $customer['gateway'][0]['vaultKey']['card_token'],
                        'exp_month' =>sprintf("%02d", $customer['gateway'][0]['vaultKey']['exp_month']),
                        'exp_year' => $customer['gateway'][0]['vaultKey']['exp_year'],
                    ];
                }
            }
        }

        switch ($paymentMethod) {
            case 'google':
            case 'GooglePay':
                $apiUrl = 'api/v1.5/controls/init/google';
                $data = [
                    'Button' => [
                        'Integrated' => true,
                    ],
                    'ButtonType' => 'Buy',
                    'CultureCode' => 'en-US',
                    'EsKey' => $this->esKey,
                    'Legal' => [
                        'HideCheckbox' => false,
                        'TextType' => 'DynamicThirdPartyWithCheckbox',
                    ],
                    'StoreId' => $this->storeId,
                    'TerminalId' => $this->terminalId,
                    'ValidationMessageType' => 'Feedback',
                    'WorkflowType' => 'Standard',
                    'UiOptions' => [
                        'Layout' => '',
                        'Style' => '',
                    ],
                    'BillingAddress' => [
                        'Format' => 'Full',
                        'IsPhoneNumberRequired' => true,
                        'IsRequired' => true,
                    ],
                    'ButtonColor' => 'Black',
                    'CurrencyCode' => '',
                    'IsEmailRequired' => true,
                    'ResponseType' => 'Raw',
                    'TotalPrice' => $prams['amount'],
                    'FraudCheck' => [
                        'Enabled' => false,
                        'Kount' => [
                            'DeviceIdentifier' => '',
                        ],
                    ],
                ];
                break;
            case 'apple':
            case 'ApplePay':
                $apiUrl = 'api/v1.5/controls/init/apple';
                $data = [
                    'Button' => [
                        'Integrated' => true,
                    ],
                    'ButtonType' => 'Plain',
                    'CultureCode' => 'en-US',
                    'EsKey' =>  $this->esKey,
                    'Legal' => [
                        'HideCheckbox' => false,
                        'TextType' => 'DynamicThirdPartyWithCheckbox',
                    ],
                    'StoreId' => $this->storeId,
                    'TerminalId' =>  $this->terminalId,
                    'ValidationMessageType' => 'Feedback',
                    'WorkflowType' => 'Standard',
                    'UiOptions' => [
                        'Layout' => '',
                        'Style' => '',
                    ],
                    'AutoFinalizePayment' => true,
                    'TotalPrice' => $prams['amount'],
                    'ButtonColor' => 'Black',
                    'Styles' => '.fp-apple-pay-button{height:50px; width: 200px; border-radius:0 !important;}',
                    'CurrencyCode' => '',
                    'FraudCheck' => [
                        'Enabled' => false,
                        'Kount' => [
                            'DeviceIdentifier' => '',
                        ],
                    ],
                ];
                break;
            case 'paypal':
            case 'PayPal':
                $apiUrl = 'api/v1.5/controls/init/paypal';
                $data = [
                    'Button' => [
                        'Integrated' => true,
                    ],
                    'ButtonType' => '',
                    'CultureCode' => 'en-US',
                    'EsKey' => $this->esKey,
                    'Legal' => [
                        'HideCheckbox' => false,
                        'TextType' => 'DynamicThirdPartyWithCheckbox',
                    ],
                    'StoreId' => $this->storeId,
                    'TerminalId' => $this->terminalId,
                    'ValidationMessageType' => 'Feedback',
                    'WorkflowType' => 'Standard',
                    'UiOptions' => [
                        'Layout' => '',
                        'Style' => '',
                    ],
                    'CurrencyCode' => '',
                    'Intent' => 'Capture',
                    'InvoiceNumber' => $prams['order_id'] ?? rand(100000, 999999),
                    'TotalPrice' => (string) $prams['amount']
                ];
                break;


            default:
                $apiUrl = 'api/v1.5/controls/init';
                $data = [
                    'ButtonType' => '',
                    'EsKey' => $this->esKey,
                    'Legal' => [
                        'HideCheckbox' => false,
                        'TextType' => 'DynamicWithCheckbox',
                    ],
                    'StoreId' => $this->storeId,
                    'TerminalId' => $this->terminalId,
                    'ValidationMessageType' => 'Feedback',
                    'WorkflowType' => 'Standard',
                    'CardIconDisplayType' => 'Dynamic',
                    'PaymentType' => 'Card', //  [‘Card’, ‘CardOnFile’, ‘GiftCard’, ‘RewardCard’, ‘PrivateLabelCard']
                    'Styles' => 'input{color:green;}',
                    'CardNumber' => [
                        'LabelType' => 'Default',
                        'PlaceholderType' => 'EnterCardNumber',
                    ],
                    'ExpirationDate' => [
                        'LabelType' => 'Default',
                        'PlaceholderType' => 'Default',
                        'ValidationType' => 'Required'
                    ],
                    'PostalCode' => [
                        'LabelType' => 'Default',
                        'PlaceholderType' => 'EnterPostalCode',
                        'ValidationType' => 'Required',
                    ],
                    'SecurityCode' => [
                        'LabelType' => 'Default',
                        'PlaceholderType' => 'Cvc',
                        'ValidationType' => 'Required'
                    ],
                    'ConsumerAuthentication' => [
                        'Enabled' => false,
                        'AutoMapToFreewayRequest' => true,
                        'Fields' => [
                            0 => ['key' => 'BillingAddress1', 'value' => $prams['address_1']],
                            1 => ['key' => 'BillingCity', 'value' => $prams['city']],
                            2 => ['key' => 'BillingCountryCode', 'value' => '840'],
                            3 => ['key' => 'BillingFirstName', 'value' => $prams['first_name']],
                            4 => ['key' => 'BillingLastName', 'value' => $prams['first_last']],
                            5 => ['key' => 'BillingPostalCode', 'value' => $prams['postal_code']],
                            6 => ['key' => 'Email', 'value' => $prams['email']],
                            7 => ['key' => 'MobilePhone', 'value' => $prams['mobile']],
                            //  8 => ['key' => 'ChallengeIndicator', 'value' => '04']  // The challenge indicator of 04 is only required for 3ds transactions.
                        ]
                    ],
                    'OrderDetails' => [
                        'TransactionTotal' => $prams['amount'],
                        'OrderNumber' => $prams['cart_id'],
                        // 'OrderDescription' =>  'test',
                        'CurrencyCode' => 840,
                    ]
                ];
                break;
        }

        if (!empty($formattedData['TokenInformation'])) {
            $data['TokenInformation'] = $formattedData['TokenInformation'];
            $data['PaymentType']='CardOnFile';
        }

        RentMy::logToGenius([
            "event" => "initFrame",
            "status" => 'success',
            "description" => 'request',
            "value" => '',
            "custom_content" => json_encode([
                'apiUrl' => $apiUrl,
                'payload' => $data
            ])
        ]);

        $response = $this->sendRequest(
            $apiUrl,
            json_encode($data),
            ['Content-Type: application/json',]
        );

        RentMy::logToGenius([
            "event" => "initFrame",
            "status" => 'success',
            "description" => 'response',
            "value" => '',
            "custom_content" => json_encode($response)
        ]);

        $sessionKey = $this->getSessionTokenFromUrl($response['result'], '?sessionKey=', '"');
        return ['iframe' => $response['result'], 'session_key' => $sessionKey];
    }

    /**
     * @param $string
     * @param $start
     * @param $end
     * @return false|string
     */
    private function getSessionTokenFromUrl($string, $start, $end)
    {
        $string = ' ' . $string;
        $ini = strpos($string, $start);
        if ($ini == 0) return '';
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;
        return substr($string, $ini, $len);
    }

    /**
     * @param $prams
     * @return false|mixed
     * @throws \SoapFault
     */
    private function getToken($prams = [])
    {
        $data = [
            "PaymentFlow" => "Direct",
            "Platform" => "Web",
            "ConsumerAuthentication" => ["Enabled" => false],
            "CultureCode" => "en-US",
            "RequestMessage" => [
                "storeId" => $this->storeId,
                "terminalId" => $this->terminalId,
                "esKey" => $this->esKey,

                "purchaseTotals" => $prams['purchaseTotals'] ?? []
            ]
        ];

        RentMy::logToGenius([
            "event" => "getToken",
            "status" => 'success',
            "description" => 'request',
            "value" => '',
            "custom_content" => json_encode([
                'apiUrl' => 'api/v2/session/initialize',
                'payload' => $data
            ])
        ]);

        $response = $this->sendRequest('api/v2/session/initialize', json_encode($data), ['Content-Type: application/json']);

        RentMy::logToGenius([
            "event" => "getToken",
            "status" => 'success',
            "description" => 'response',
            "value" => '',
            "custom_content" => json_encode($response)
        ]);

        $response = json_decode($response['result'], true);
        return $response['access_token'] ?? false;
    }

    /**
     * @return string
     * @throws \Exception
     */
    private function getPosSyncId(): string
    {
        date_default_timezone_set('UTC');
        $dt = new \DateTime('now', new \DateTimeZone("UTC"));
        $isoTIme = $dt->format('Y-m-d\TH:i:s.') . substr($dt->format('u'), 0, 3) . 'Z';
        $gid = strtolower(sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535)));
        $posIdData = [$isoTIme, $this->storeId, $this->terminalId, $gid];

        return implode(';', $posIdData);
    }

    /**
     * @return string
     * @throws \Exception
     */
    private function getPosSyncAttemptNum()
    {
        return $this->posSyncAttemptNum;  //++; //if needs to update on each request
    }

    /**
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function saleTest($params)
    {
        $this->__construct($params['config']);

        $PaymentData = array(
            'PosSyncAttemptNum' => $this->getPosSyncAttemptNum(),
            'PosSyncId' => $this->getPosSyncId(),
            'RequestMessage' => [
                'storeId' => $this->storeId,
                'terminalId' => $this->terminalId,
                'esKey' => $this->esKey,
                'purchaseTotals' => [
                    'currency' => 'USD',
                    'chargeAmount' => '1',
                ],
                'ccAuthService' => [
                    'transType' => '',
                    'run' => 'true',
                    'commerceIndicator' => 'internet'
                ],
                'card' => [
                    'nameOnCard' => 'Rentmy Support'
                ],
                'items' => [
                    [
                        'productCode' => 'pppppp',
                        'productName' => 'Test Product',
                        'productDescription' => 'Connection Test Product',
                        'unitPrice' => '1',
                        'quantity' => 1,
                        'totalAmount' => '1',
                        'taxIncludedFlag' => 'Y',
                        'saleCode' => 'S',
                    ]
                ],
                'merchantReferenceCode' => 'RNTM-' . RentMy::$store->id . '-' . time(),
                'clientMetadata' => [
                    "sellingSystemName" => RentMy::storeDomain(),
                    "sellingSystemVersion" => "2.1",
                    'sellingMiddlewareName' => 'RentMy',
                    'sellingMiddlewareVersion' => '1.0',
                ],
                'invoiceHeader' => [
                    'invoiceNumber' => '00000', //invoice number = cart id
                ],
            ],
            'PaymentKey' => $params['paymentKeys'][0]
        );

        if (empty($params['freedom_pay_session_key']))
            return ['success' => false, 'message' => 'No token has been generated'];

        $token = $params['freedom_pay_session_key'];

        RentMy::logToGenius([
            "event" => "saleTest",
            "status" => 'success',
            "description" => 'request',
            "value" => '',
            "custom_content" => json_encode([
                'apiUrl' => 'api/v1.5/payments',
                'token' => $token,
                'payload' => $PaymentData
            ])
        ]);

        $response = $this->sendRequest(
            'api/v1.5/payments',
            json_encode($PaymentData),
            ['Content-Type: application/json', "Authorization: Bearer $token"]
        );

        RentMy::logToGenius([
            "event" => "saleTest",
            "status" => 'success',
            "description" => 'response',
            "value" => '',
            "custom_content" => json_encode($response)
        ]);


        $response = json_decode($response['result'], true);
        if ("ACCEPT" != $response['FreewayResponse']['decision'])
            return ['success' => false, 'data' => $response['FreewayResponse'], 'message' => 'Payment failed'];

        if (empty($response['FreewayResponse']))
            return ['success' => false, 'data' => $response, 'message' => 'Payment failed'];

        $response = $response['FreewayResponse'];
        return [
            'success' => true,
            'transaction_id' => $response['requestID'],
            'data' => $response,
            'message' => 'Connection success!'
        ];
    }

    /**
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function sale($params)
    {

        // vault payment
        if (!empty($params['paytype']) && $params['paytype'] == 'freedompay_token') {
            return $this->saleWithFreeway($params);
        }

        $serviceCharge = [];
        $cartId = rand(0, 399);
        if (!empty($params['cart']['id']))
            $cartId = $params['cart']['id'];

        if (!empty($params['first_name']) || !empty($params['last_name']))
            $params['name_on_card'] = $params['first_name'] . ' ' . $params['last_name'];

        if (!empty($params['cart_id'])) {
            $cartId = $params['cart_id'];
            RentMy::addModel(['Carts']);
            $cart = RentMy::$Model['Carts']->find()->select(['id', 'options', 'tax'])->where(['id' => $params['cart_id']])->first();
            if (!empty($cart))
                $cartId = $cart->id;

            $options = json_decode($cart['options'], true);
            if (!empty($options['additional_service_amount']))
                $serviceCharge = ['amount' => $options['additional_service_amount'], 'tax' => $options['additional_service_tax'] ?? 0];

            if (empty($params['tax']))
                $params['tax'] = $cart['tax'];
        }
        // after order payment
        if (!empty($params['order_id'])) {
            RentMy::addModel(['Carts']);
            $cart = RentMy::$Model['Carts']->find()->select(['id', 'options', 'tax'])->where(['order_id' => $params['order_id']])->first();
            if (!empty($cart))
                $cartId = $cart->id;

            $options = json_decode($cart['options'], true);
            if (!empty($options['additional_service_amount']))
                $serviceCharge = ['amount' => $options['additional_service_amount'], 'tax' => $options['additional_service_tax'] ?? 0];

            if (empty($params['tax']))
                $params['tax'] = $cart['tax'];
        }

        //getting customer id
        if (!empty($params['customer']['id']))
            $customerId = $params['customer']['id'];

        if (!empty($params['customer_id']))
            $customerId = $params['customer_id'];

        if (empty($params['name_on_card']) && !empty($params['order_id'])) {
            RentMy::addModel(['Orders']);
            $order = RentMy::$Model['Orders']->find()->select(['id', 'first_name', 'last_name', 'options'])->where(['id' => $params['order_id']])->first();
            $options = json_decode($order['options'], true);
            if (!empty($options['additional_service_amount']))
                $serviceCharge = ['amount' => $options['additional_service_amount'], 'tax' => $options['additional_service_tax'] ?? 0];
            $params['name_on_card'] = $order->first_name . ' ' . $order->last_name;
        }

        $posSyncId = $this->getPosSyncId();
        $PaymentData = array(
            'PosSyncAttemptNum' => $this->getPosSyncAttemptNum(),
            'PosSyncId' => $posSyncId,
            'RequestMessage' => [
                'storeId' => $this->storeId,
                'terminalId' => $this->terminalId,
                'esKey' => $this->esKey,
                'purchaseTotals' => [
                    'currency' => $params['currency'] ?? 'USD',
                    'chargeAmount' => $params['amount'],
                ],
                'ccAuthService' => [
                    'transType' => '',
                    'run' => 'true',
                    'commerceIndicator' => 'internet'
                ],
                'merchantReferenceCode' => 'RNTM-' . RentMy::$store->id . '-' . time(),
                'clientMetadata' => [
                    "sellingSystemName" => RentMy::storeDomain(),
                    "sellingSystemVersion" => "2.1",
                    'sellingMiddlewareName' => 'RentMy',
                    'sellingMiddlewareVersion' => '1.0',
                ],
                'invoiceHeader' => [
                    'invoiceNumber' => '', //invoice number = cart id
                ],
            ],
        );

        //if (!empty($params['tax']))
        $PaymentData['RequestMessage']['purchaseTotals']['taxTotal'] = empty($params['tax']) ? 0.00 : $params['tax'];
        $PaymentData['RequestMessage']['invoiceHeader']['invoiceNumber'] = (string)$cartId;

        RentMy::addModel(['CartItems']);
        $cartItems = RentMy::$Model['CartItems']->find()->contain(['Products'])->where(['cart_id' => $cartId])->toArray();
        $items = [];
        foreach ($cartItems as $item) {
            $_i = [
                'productCode' => (string)$item['product']['id'],
                'productName' => $item['product']['name'],
                'productDescription' => $item['product']['name'],
                'unitPrice' => $item['price'],
                'quantity' => $item['quantity'],
                'totalAmount' => $item['sub_total'],
                'taxIncludedFlag' => $options['taxIncludedFlag'],
                'saleCode' => 'S',
            ];

            if (!empty($item['sales_tax']))
                $_i['taxAmount'] = $item['sales_tax'];

            $items[] = $_i;
        }

        $additionalServiceProductCode = $cartId . RentMy::$store->id;
        if (!empty($serviceCharge)) {
            $_i = [
                'productCode' => $additionalServiceProductCode,
                'productName' => 'Additional Service Charges',
                'productDescription' => 'Additional Service Charges total',
                'unitPrice' => $serviceCharge['amount'],
                'quantity' => 1,
                'totalAmount' => $serviceCharge['amount'],
                'taxIncludedFlag' => empty(RentMy::$storeConfig['tax']['price_with_tax']) ? 'N' : 'Y',
                'saleCode' => 'S',
            ];

            if ($serviceCharge['tax'] > 0)
                $_i['taxAmount'] = $serviceCharge['tax'];

            $items[] = $_i;
        }

        $PaymentData['RequestMessage']['items'] = $items;

        // vault payment
        if (!empty($params['paytype']) && $params['paytype'] == 'freedompay_token') {
            $vault = $params['account'];
            if (empty($vault['card_token']))
                return ['success' => false, 'data' => [], 'message' => 'Empty vault token'];

            if (empty($vault['name_on_card']))
                $vault['name_on_card'] = $params['name_on_card'];

            $PaymentData['RequestMessage']['card'] = [
                'accountNumber' => $vault['card_token'],
                'nameOnCard' => $vault['name_on_card'],
                'expirationMonth' => $vault['exp_month'],
                'expirationYear' => $vault['exp_year'],
                'cardType' => 'token',
            ];
        } elseif (!empty($params['paymentKey'])) {
            $PaymentData['PaymentKey'] = $params['paymentKey']['paymentKeys'][0];
            $PaymentData['RequestMessage']['tokenCreateService'] = [
                "run" => "true",
                "type" => '6',
            ];
            $PaymentData['RequestMessage']['card'] = ['nameOnCard' => $params['name_on_card']];
        } else {
            $PaymentData['RequestMessage']['card'] = [
                'accountNumber' => $params['card_no'],
                'cvNumber' => $params['cvv'],
                'expirationMonth' => $params['expiry_month'],
                'expirationYear' => $params['expiry_year'],
                'nameOnCard' => $params['name_on_card'],
                'cardType' => 'credit',
            ];
            $PaymentData['RequestMessage']['tokenCreateService'] = [
                "run" => "true",
                "type" => '6',
            ];
        }

        $PaymentData['ConsumerAuthentication']['Enabled'] = false;

        if ($params['capture'] || ($params['payment_method'] == 'Captured'))
            $PaymentData['RequestMessage']['ccCaptureService'] = ['run' => 'true'];

        if (!empty($params['paymentKey']['freedom_pay_session_key']))
            $token = $params['paymentKey']['freedom_pay_session_key'];
        else
            $token = $this->getToken([
                "purchaseTotals" => [
                    "chargeAmount" => $params['amount'],
                    "currency" => $params['currency'] ?? 'USD',
                ]
            ]);

        if (!$token)
            return ['success' => false, 'message' => 'No token has been generated'];

//        $response = $this->sendRequest(
//            'api/v1.5/payments',
//            json_encode($PaymentData),
//            ['Content-Type: application/json', "Authorization: Bearer $token"]
//        );



        RentMy::logToGenius([
            "event" => "sale",
            "status" => 'success',
            "description" => 'request',
            "value" => '',
            "custom_content" => json_encode([
                'apiUrl' => 'api/v1.5/payments',
                'token' => $token,
                'payload' => $PaymentData
            ])
        ]);

        // url endcoded formate
        $response = $this->sendRequest(
            'api/v1.5/payments',
            http_build_query($PaymentData),
            ['Content-Type: application/x-www-form-urlencoded', "Authorization: Bearer $token"]
        );

        RentMy::logToGenius([
            "event" => "sale",
            "status" => 'success',
            "description" => 'response',
            "value" => '',
            "custom_content" => json_encode($response)
        ]);



        $response = json_decode($response['result'], true);
        if ("ACCEPT" != $response['FreewayResponse']['decision']) {
            return ['success' => false, 'data' => $response['FreewayResponse'], 'message' => 'Payment failed'];
        } else {
            if (empty($response['FreewayResponse']))
                return ['success' => false, 'data' => $response, 'message' => 'Payment failed'];

            $response = $response['FreewayResponse'];
            $returnResponse = [
                'success' => true,
                'transaction_id' => $response['requestID'],
                'data' => $response
            ];

            if (!empty($vault['name_on_card']))
                $returnResponse['data']['tokenInformation']['nameOnCard'] = $vault['name_on_card'];

            if (!empty($response['tokenInformation']['token'])) {
                $returnResponse['data']['tokenInformation']['nameOnCard'] = $params['name_on_card'];
                $returnResponse['customerRef'] = [
                    'gateway' => 'FreedomPay',
                    'vaultKey' => [
                        'name_on_card' => $params['name_on_card'],
                        'card_mask' => $response['tokenInformation']['accountNumberMasked'],
                        'card_token' => $response['tokenInformation']['token'],
                        'exp_month' => $response['tokenInformation']['cardExpirationMonth'],
                        'exp_year' => $response['tokenInformation']['cardExpirationYear'],
                    ]
                ];
            }

             $this->acknowledgeRequest($posSyncId, $token);
            //   $this->reverseRequest($posSyncId, $token);
            return $returnResponse;
        }

    }


    public function tor($PaymentData){

    }


    /**
     * @param $params
     * @return array
     * @throws \SoapFault
     * @todo now implemented for vault payment need to implement for card
     */
    public function saleWithFreeway($params){
        $serviceCharge = [];

        $cartId = rand(0, 399);
        if (!empty($params['cart']['id']))
            $cartId = $params['cart']['id'];

        if (!empty($params['first_name']) || !empty($params['last_name']))
            $params['name_on_card'] = $params['first_name'] . ' ' . $params['last_name'];

        if (!empty($params['cart_id'])) {
            $cartId = $params['cart_id'];
            RentMy::addModel(['Carts']);
            $cart = RentMy::$Model['Carts']->find()->select(['id', 'options', 'tax'])->where(['id' => $params['cart_id']])->first();
            if (!empty($cart))
                $cartId = $cart->id;

            $options = json_decode($cart['options'], true);
            if (!empty($options['additional_service_amount']))
                $serviceCharge = ['amount' => $options['additional_service_amount'], 'tax' => $options['additional_service_tax'] ?? 0];

            if (empty($params['tax']))
                $params['tax'] = $cart['tax'];
        }
        // after order payment
        if (!empty($params['order_id'])) {
            RentMy::addModel(['Carts']);
            $cart = RentMy::$Model['Carts']->find()->select(['id', 'options', 'tax'])->where(['order_id' => $params['order_id']])->first();
            if (!empty($cart))
                $cartId = $cart->id;

            $options = json_decode($cart['options'], true);
            if (!empty($options['additional_service_amount']))
                $serviceCharge = ['amount' => $options['additional_service_amount'], 'tax' => $options['additional_service_tax'] ?? 0];

            if (empty($params['tax']))
                $params['tax'] = $cart['tax'];
        }

        //getting customer id
        $customerId = '';
        if (!empty($params['customer']['id']))
            $customerId = $params['customer']['id'];

        if (!empty($params['customer_id']))
            $customerId = $params['customer_id'];

        if (empty($params['name_on_card']) && !empty($params['order_id'])) {
            RentMy::addModel(['Orders']);
            $order = RentMy::$Model['Orders']->find()->select(['id', 'first_name', 'last_name', 'options'])->where(['id' => $params['order_id']])->first();
            $options = json_decode($order['options'], true);
            if (!empty($options['additional_service_amount']))
                $serviceCharge = ['amount' => $options['additional_service_amount'], 'tax' => $options['additional_service_tax'] ?? 0];
            $params['name_on_card'] = $order->first_name . ' ' . $order->last_name;
        }


        $merchantReferenceCode = 'RNTM-' . RentMy::$store->id . '-' . time();
        $tax = empty($params['tax']) ? 0.00 : $params['tax'];


//        if (!empty($params['paytype']) && $params['paytype'] == 'freedompay_token') {

            $vault = $params['account'];
            if (empty($vault['card_token']))
                return ['success' => false, 'data' => [], 'message' => 'Empty vault token'];

            if (empty($vault['name_on_card']))
                $vault['name_on_card'] = $params['name_on_card'];

//        }

        RentMy::addModel(['CartItems']);
        $cartItems = RentMy::$Model['CartItems']->find()->contain(['Products'])->where(['cart_id' => $cartId])->toArray();
        $items = '';
        $taxIncludedFlag = empty(RentMy::$storeConfig['tax']['price_with_tax']) ? 'N' : 'Y';

        foreach ($cartItems as $item) {
            $items .= '<item>
                              <productCode>'. (string)$item['product']['id'] .'</productCode>
                              <productName>'. $item['product']['name'] .'</productName>
                              <productDescription>'. $item['product']['name'] .'</productDescription>
                              <quantity>'. $item['quantity'] .'</quantity>
                              <unitPrice>'. $item['price'] .'</unitPrice>
                              <taxAmount>'. $item['sales_tax'] .'</taxAmount>
                              <totalAmount>'. $item['sub_total'] .'</totalAmount>
                              <taxIncludedFlag>'. $options['taxIncludedFlag'] .'</taxIncludedFlag>
                              <saleCode>S</saleCode>
                        </item>';
        }
        $additionalServiceProductCode = $cartId . RentMy::$store->id;
        if (!empty($serviceCharge)) {
            $items .= '<item>
                              <productCode>'. $additionalServiceProductCode .'</productCode>
                              <productName>Additional Service Charges</productName>
                              <productDescription>Additional Service Charges total</productDescription>
                              <quantity>1</quantity>
                              <unitPrice>'. $serviceCharge['amount'] .'</unitPrice>
                              <taxAmount>'. $serviceCharge['tax'] .'</taxAmount>
                              <totalAmount>'. $serviceCharge['amount'] .'</totalAmount>
                              <taxIncludedFlag>'. $taxIncludedFlag .'</taxIncludedFlag>
                              <saleCode>S</saleCode>
                        </item>';
        }

        $posSyncId = $this->getPosSyncId();

        $xml = '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <soap:Body>
                <Submit xmlns="http://freeway.freedompay.com/">
                    <request>
                        <storeId>'.$this->storeId.'</storeId>
                        <terminalId>'.$this->terminalId.'</terminalId>
                        <esKey>'.$this->esKey.'</esKey>
                        <invoiceHeader>
                            <invoiceNumber>'. $cartId .'</invoiceNumber>
                        </invoiceHeader>
                        <merchantReferenceCode>'. $merchantReferenceCode .'</merchantReferenceCode>';

        if ($params['capture'] || ($params['payment_method'] == 'Captured')){
            $xml .= '<ccCaptureService run="true" />
                        <ccAuthService run="true">
                            <enableAVS>N</enableAVS>
                            <commerceIndicator>unscheduled</commerceIndicator>
                        </ccAuthService>';
        }else{
            $xml .= '
                        <ccAuthService run="true">
                            <enableAVS>N</enableAVS>
                            <commerceIndicator>unscheduled</commerceIndicator>
                        </ccAuthService>';
        }

        $xml .= '<purchaseTotals>
                            <taxTotal>'.$tax.'</taxTotal>
                            <chargeAmount>'.$params['amount'].'</chargeAmount>
                            <currency>'.$params['currency'].'</currency>
                        </purchaseTotals>
                        <card>
                            <accountNumber>'.$vault['card_token'].'</accountNumber>
                            <cardType>token</cardType>
                            <nameOnCard>'.$vault['name_on_card'].'</nameOnCard>
                            <expirationMonth>'.$vault['exp_month'].'</expirationMonth>
                            <expirationYear>'.$vault['exp_year'].'</expirationYear>
                        </card>
                        <clientMetadata>
                            <sellingSystemName>'.RentMy::storeDomain().'</sellingSystemName>
                            <sellingSystemVersion>2.1</sellingSystemVersion>
                            <sellingMiddlewareName>RentMy</sellingMiddlewareName>
                            <sellingMiddlewareVersion>1.0</sellingMiddlewareVersion>
                        </clientMetadata>
                        <items>
                            ' .$items. '
                        </items>
                        <pos>
                             <entryMode>keyed</entryMode>
                             <cardPresent>N</cardPresent>
                        </pos>
                    </request>
                </Submit>
            </soap:Body>
        </soap:Envelope>';

        $response = $this->sendRequest('freeway', $xml);
        if ("ACCEPT" != $response['decision']) {
            return ['success' => false, 'data' => $response, 'message' => 'Payment failed'];
        } else {

            if (!empty($params['paymentKey']['freedom_pay_session_key']))
                $token = $params['paymentKey']['freedom_pay_session_key'];
            else
                $token = $this->getToken([
                    "purchaseTotals" => [
                        "chargeAmount" => $params['amount'],
                        "currency" => $params['currency'] ?? 'USD',
                    ]
                ]);
//            $this->acknowledgeRequest($posSyncId, $token);

            return [
                'success' => true,
                'transaction_id' => $response['requestID'],
                'data' => $response
            ];
        }
    }


    /**
     * @param $data
     * @return array|string
     * @throws \Exception
     */
    public function capture($data)
    {
        $merchantReferenceCode = 'RNTM-' . RentMy::$store->id . '-' . time();
        $invoiceNumber = rand(0, 399);
        RentMy::addModel(['Carts', 'Payments', 'Orders']);
        $payment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $data['transaction_id']])->first();
        $order = RentMy::$Model['Orders']->find()->where(['id' => $payment['order_id']])->first();
        $options = json_decode($order['options'], true);
        $cart = RentMy::$Model['Carts']->find()->select(['id'])->where(['order_id' => $payment['order_id']])->first();
        if (!empty($cart))
            $invoiceNumber = (string)$cart->id;

        RentMy::addModel(['CartItems']);
        $cartItems = RentMy::$Model['CartItems']->find()->contain(['Products'])->where(['cart_id' => $invoiceNumber])->toArray();

        $items = '';
        $taxIncludedFlag = $options['taxIncludedFlag'] ?? 'N';
        foreach ($cartItems as $item) {
            $items .= '<item>
                        <productCode>' . $item['product']['id'] . '</productCode>
                        <productName>' . $item['product']['name'] . '</productName>
                        <productDescription>' . $item['product']['name'] . '</productDescription>
                        <unitPrice>' . $item['price'] . '</unitPrice>
                        <quantity>' . $item['quantity'] . '</quantity>
                        <totalAmount>' . $item['sub_total'] . '</totalAmount>';
            if (!empty($item['sales_tax']))
                $items .= '<taxAmount>' . $item['sales_tax'] . '</taxAmount>';

            $items .= ' <saleCode>S</saleCode>
                        <taxIncludedFlag>' . $taxIncludedFlag . '</taxIncludedFlag>
                    </item>';
        }

        $nameOnTheCard = $order->first_name . ' ' . $order->last_name;
        $customerId = empty($order->customer_id) ? $invoiceNumber : $order->customer_id;
        $taxTotal = !empty($data['tax'])?$data['tax']:0;
        $xml = '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
                <soap:Body>
                    <Submit xmlns="http://freeway.freedompay.com/">
                        <request>
                            <storeId>' . $this->storeId . '</storeId>
                            <terminalId>' . $this->terminalId . '</terminalId>
                            <esKey>' . $this->esKey . '</esKey>
                            <orderRequestID>' . $data['transaction_id'] . '</orderRequestID>
                            <ccCaptureService run="true"></ccCaptureService>
                            <invoiceHeader>
                                <invoiceNumber>' . $invoiceNumber . '</invoiceNumber>
                            </invoiceHeader>
                            <purchaseTotals>
                                <taxTotal>' . $taxTotal . '</taxTotal>
                                <chargeAmount>' . $data['amount'] . '</chargeAmount>
                            </purchaseTotals>
                            <merchantReferenceCode>' . $merchantReferenceCode . '</merchantReferenceCode>
                            <clientMetadata>
                                <sellingSystemName>' . RentMy::storeDomain() . '</sellingSystemName>
                                <sellingSystemVersion>2.1</sellingSystemVersion>
                                <sellingMiddlewareName>RentMy</sellingMiddlewareName>
                                <sellingMiddlewareVersion>1.0</sellingMiddlewareVersion>
                            </clientMetadata>
                            <card>
                                <nameOnCard>' . $nameOnTheCard . '</nameOnCard>
                            </card>
                             <items>' . $items . '</items>

                        </request>
                    </Submit>
                </soap:Body>
            </soap:Envelope>';


        $response = $this->sendRequest('freeway', $xml);

        if ("ACCEPT" != $response['decision']) {
            return ['success' => false, 'data' => $response, 'message' => 'Payment failed'];
        } else {
            return [
                'success' => true,
                'transaction_id' => $response['requestID'],
                'data' => $response
            ];
        }
    }

    /**
     * @param $data
     * @return array|string
     * @throws \Exception
     */
    public function void($data)
    {
        $merchantReferenceCode = 'RNTM-' . RentMy::$store->id . '-' . time();
        $invoiceNumber = rand(0, 399);
        RentMy::addModel(['Carts', 'Payments', 'Orders']);
        $payment = RentMy::$Model['Payments']->find()->where(['transaction_id' => $data['transaction_id']])->first();
        $order = RentMy::$Model['Orders']->find()->where(['id' => $payment['order_id']])->first();
        $options = json_decode($order['options'], true);
        $cart = RentMy::$Model['Carts']->find()->select(['id'])->where(['order_id' => $payment['order_id']])->first();
        if (!empty($cart))
            $invoiceNumber = (string)$cart->id;

        RentMy::addModel(['CartItems']);
        $cartItems = RentMy::$Model['CartItems']->find()->contain(['Products'])->where(['cart_id' => $cart['id']])->toArray();

        $items = '';
        $taxIncludedFlag = $options['taxIncludedFlag'] ?? 'N';
        foreach ($cartItems as $item) {
            $items .= '<item>
                        <productCode>' . $item['product']['id'] . '</productCode>
                        <productName>' . $item['product']['name'] . '</productName>
                        <productDescription>' . $item['product']['name'] . '</productDescription>
                        <unitPrice>' . $item['price'] . '</unitPrice>
                        <quantity>' . $item['quantity'] . '</quantity>
                        <totalAmount>' . $item['sub_total'] . '</totalAmount>';
            if (!empty($item['sales_tax']))
                $items .= '<taxAmount>' . $item['sales_tax'] . '</taxAmount>';

            $items .= ' <saleCode>S</saleCode>
                        <taxIncludedFlag>' . $taxIncludedFlag . '</taxIncludedFlag>
                    </item>';
        }

        $nameOnTheCard = $order->first_name . ' ' . $order->last_name;
        $customerId = empty($order->customer_id) ? $invoiceNumber : $order->customer_id;
        $xml = '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
                <soap:Body>
                    <Submit xmlns="http://freeway.freedompay.com/">
                        <request>
                            <storeId>' . $this->storeId . '</storeId>
                            <terminalId>' . $this->terminalId . '</terminalId>
                            <esKey>' . $this->esKey . '</esKey>
                            <orderRequestID>' . $data['transaction_id'] . '</orderRequestID>
                            <voidService run="true"></voidService>
                            <invoiceHeader>
                                <invoiceNumber>' . $invoiceNumber . '</invoiceNumber>
                            </invoiceHeader>
                            <merchantReferenceCode>' . $merchantReferenceCode . '</merchantReferenceCode>
                              <clientMetadata>
                                <sellingSystemName>' . RentMy::storeDomain() . '</sellingSystemName>
                                <sellingSystemVersion>2.1</sellingSystemVersion>
                                <sellingMiddlewareName>RentMy</sellingMiddlewareName>
                                <sellingMiddlewareVersion>1.0</sellingMiddlewareVersion>
                            </clientMetadata>
                            <card>
                                <nameOnCard>' . $nameOnTheCard . '</nameOnCard>
                            </card>
                             <items>' . $items . '</items>
                        </request>
                    </Submit>
                </soap:Body>
            </soap:Envelope>';

        $response = $this->sendRequest('freeway', $xml);
        if ("ACCEPT" != $response['decision']) {
            return ['success' => false, 'data' => $response, 'message' => 'Payment failed'];
        } else {
            return [
                'success' => true,
                'transaction_id' => $response['requestID'],
                'data' => $response
            ];
        }
    }

    /**
     * @param $data
     * @return array|string
     * @throws \Exception
     */
    public function refund($data)
    {
        $merchantReferenceCode = 'RNTM-' . RentMy::$store->id . '-' . time();
        $invoiceNumber = rand(0, 399);
        RentMy::addModel(['Payments', 'Carts', 'Orders']);
        $cart = RentMy::$Model['Carts']->find()->select(['id'])->where(['order_id' => $data['order_id']])->first();
        if (!empty($cart))
            $invoiceNumber = $cart->id;

        $order = RentMy::$Model['Orders']->find()->where(['id' => $data['order_id']])->first();
        $options = json_decode($order['options'], true);
        $payment = RentMy::$Model['Payments']->find()->where(['id' => $data['payment_id']])->first();
        if (!empty($payment))
            $data['transaction_id'] = $payment['transaction_id'];

        RentMy::addModel(['CartItems']);
        $cartItems = RentMy::$Model['CartItems']->find()->contain(['Products'])->where(['cart_id' => $invoiceNumber])->toArray();

        $items = '';
        $taxIncludedFlag = $options['taxIncludedFlag'] ?? 'N';
        foreach ($cartItems as $item) {
            $items .= '<item>
                        <productCode>' . $item['product']['id'] . '</productCode>
                        <productName>' . $item['product']['name'] . '</productName>
                        <productDescription>' . $item['product']['name'] . '</productDescription>
                        <unitPrice>' . $item['price'] . '</unitPrice>
                        <quantity>' . $item['quantity'] . '</quantity>
                        <totalAmount>' . $item['sub_total'] . '</totalAmount>';
            if (!empty($item['sales_tax']))
                $items .= '<taxAmount>' . $item['sales_tax'] . '</taxAmount>';

            $items .= ' <saleCode>S</saleCode>
                         <taxIncludedFlag>' . $taxIncludedFlag . '</taxIncludedFlag>
                    </item>';
        }
        $customerId = empty($order->customer_id) ? $invoiceNumber : $order->customer_id;
        $nameOnTheCard = $order->first_name . ' ' . $order->last_name;
        $xml = '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
                <soap:Body>
                    <Submit xmlns="http://freeway.freedompay.com/">
                        <request>
                            <storeId>' . $this->storeId . '</storeId>
                            <terminalId>' . $this->terminalId . '</terminalId>
                            <esKey>' . $this->esKey . '</esKey>
                            <orderRequestID>' . $data['transaction_id'] . '</orderRequestID>
                            <ccCreditService run="true"></ccCreditService>
                            <invoiceHeader>
                                <invoiceNumber>' . $invoiceNumber . '</invoiceNumber>
                            </invoiceHeader>
                            <purchaseTotals>
                                <chargeAmount>' . $data['amount'] . '</chargeAmount>
                            </purchaseTotals>
                            <merchantReferenceCode>' . $merchantReferenceCode . '</merchantReferenceCode>
                              <clientMetadata>
                                <sellingSystemName>' . RentMy::storeDomain() . '</sellingSystemName>
                                <sellingSystemVersion>2.1</sellingSystemVersion>
                                <sellingMiddlewareName>RentMy</sellingMiddlewareName>
                                <sellingMiddlewareVersion>1.0</sellingMiddlewareVersion>
                            </clientMetadata>
                            <card>
                                <nameOnCard>' . $nameOnTheCard . '</nameOnCard>
                            </card>
                             <items>' . $items . '</items>
                        </request>
                    </Submit>
                </soap:Body>
            </soap:Envelope>';

        $response = $this->sendRequest('freeway', $xml);

        if ("ACCEPT" != $response['decision']) {
            return ['success' => false, 'data' => $response, 'message' => 'Payment failed'];
        } else {
            return [
                'success' => true,
                'transaction_id' => $response['requestID'],
                'data' => $response
            ];
        }
    }


    /**
     * @return void
     * @throws \Exception
     */
    public function acknowledgeRequest($posSyncId, $token)
    {

        RentMy::logToGenius([
            "event" => "acknowledgeRequest",
            "status" => 'success',
            "description" => 'request',
            "value" => '',
            "custom_content" => json_encode([
                'apiUrl' => 'api/v1.5/payments/acknowledge',
                'PosSyncAttemptNum' => $this->getPosSyncAttemptNum(),
                'PosSyncId' => $posSyncId,
            ])
        ]);

        $response = $this->sendRequest(
            'api/v1.5/payments/acknowledge',
            [
                'PosSyncAttemptNum' => $this->getPosSyncAttemptNum(),
                'PosSyncId' => $posSyncId,
            ],
            ['Content-Type: application/json', "Authorization: Bearer $token"]
        );

        RentMy::logToGenius([
            "event" => "acknowledgeRequest",
            "status" => 'success',
            "description" => 'response',
            "value" => '',
            "custom_content" => json_encode($response)
        ]);

    }

    /**
     * @return void
     * @throws \Exception
     */
    public function reverseRequest($posSyncId, $token)
    {

        RentMy::logToGenius([
            "event" => "reverseRequest",
            "status" => 'success',
            "description" => 'request',
            "value" => '',
            "custom_content" => json_encode([
                'apiUrl' => 'api/v1.5/payments/reverse',
                'PosSyncAttemptNum' => $this->getPosSyncAttemptNum(),
                'PosSyncId' => $posSyncId,
            ])
        ]);

        $response = $this->sendRequest(
            'api/v1.5/payments/reverse',
            [
                'PosSyncAttemptNum' => $this->getPosSyncAttemptNum(),
                'PosSyncId' => $posSyncId,
            ],
            ['Content-Type: application/json', "Authorization: Bearer $token"]
        );

        RentMy::logToGenius([
            "event" => "reverseRequest",
            "status" => 'success',
            "description" => 'response',
            "value" => '',
            "custom_content" => json_encode($response)
        ]);


    }

    /**
     * @param $path
     * @param $data
     * @param $headers
     * @return array
     * @throws \SoapFault
     */
    private function sendRequest($path, $data, $headers = [])
    {
        if ($path == 'freeway')
            return $this->sendFreewayRequest($data, $headers);

        $this->keepLogs(true, $data);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $this->baseURL . '/' . $path);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_TIMEOUT, 1000);

        $res = curl_exec($curl);
        if (curl_errno($curl))
            $error_msg = curl_error($curl);

        curl_close($curl);

        RentMy::saveLogDB('payment', $data, $res, ['store_id'=>RentMy::$store->id, 'location'=>RentMy::$token->location, 'payment_gateway_name'=>'FreedomPay', 'sub_action'=>$data['type']??'']);

        if (isset($error_msg)) {
            $this->keepLogs(false, $error_msg);
            return ['error' => $error_msg, 'success' => false];
        }
        $this->keepLogs(true, $res);
        //RentMy::saveLogDB(['request' => $data, 'response' => $res]);
        return ['result' => $res, 'success' => true];
    }

    /**
     * @param array $data
     * @param string $header
     * @return array
     * @throws \SoapFault
     */
    private function sendFreewayRequest($data)
    {
        $curl = curl_init();
        $this->keepLogs(true, $data);
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->baseURLFreeway,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: text/xml',
                'Accept: application/json'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $this->keepLogs(false, $response);
        $xmlArray = Xml::toArray(Xml::build($response));

        RentMy::saveLogDB('payment', $data, $xmlArray, ['store_id'=>RentMy::$store->id, 'location'=>RentMy::$token->location, 'payment_gateway_name'=>'FreedomPay', 'sub_action'=>$data['type']??'']);

        return $xmlArray['Envelope']['soap:Body']['SubmitResponse']['SubmitResult'];
    }

    /**
     * @param bool $forRequest
     * @param $data
     */
    private function keepLogs(bool $forRequest, $data)
    {


        $log = 'store ID: ' . RentMy::$store->id . ' -- ' . self::class;
        if ($forRequest)
            $log .= ' request ' . !empty(@$data['type']) ? ' - ' . $data['type'] : '';
        else
            $log .= ' response';

        if (!is_string($data))
            $data = json_encode($data);

        $log .= "($data)";

        RentMy::saveLogFile('alert', $log, ['payment']);
    }

}
