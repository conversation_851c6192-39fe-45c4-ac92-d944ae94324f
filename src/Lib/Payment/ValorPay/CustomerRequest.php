<?php

namespace App\Lib\Payment\ValorPay;

use App\Lib\RentMy\RentMy;
use Cake\Http\Client;
use Cake\Utility\Hash;

class CustomerRequest
{
    protected $client;
    protected $appKey;
    protected $appId;
    const BASE_URI_SANDBOX = "https://demo.valorpaytech.com";
    const BASE_URI_LIVE = "https://online.valorpaytech.com";
    protected $baseUri;

    public function __construct($config)
    {
        $this->client = new Client();
        if (isset($config['demo']) && ($config['demo'] == 1 || $config['demo'] == 'true' || $config['demo'])) {
            $this->baseUri = self::BASE_URI_SANDBOX;
        }else{
            $this->baseUri = self::BASE_URI_LIVE;
        }
        $this->appKey = $config['appKey'];
        $this->appId = $config['appId'];
    }

    public function post(string $endpoint, array $data)
    {
        $url = $this->baseUri . $endpoint;
        $this->keepLogs(true, $data);
        $response = $this->client->post($url, json_encode($data), [
            'headers' => [
                'Content-Type' => 'application/json',
                'Valor-App-ID'    => $this->appId,
                'Valor-App-Key'    => $this->appKey
            ]
        ]);
        $response = json_decode((string)$response->getBody(), true);
        $this->keepLogs(false,$response);
        return $response;
    }

    public function get(string $endpoint, array $data = [])
    {
        $this->keepLogs(true, $endpoint);
        $url = $this->baseUri . $endpoint;
        $response = $this->client->get($url, $data, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Valor-App-ID'    => $this->appId,
                'Valor-App-Key'    => $this->appKey
            ]
        ]);
        $response = json_decode((string)$response->getBody(), true);
        $this->keepLogs(false, $response);
        return  $response;

    }

    private function keepLogs($forRequest, $data, $logInfo = '', $alert = 'alert')
    {
        $log = 'store ID: ' . RentMy::$store->id . ' -- ' . self::class;

        if (!empty($logInfo)) {
            $log .= ' -- ' . $logInfo;
        }
        if ($forRequest)
            $log .= ' request ' . (isset($data['txn_type']) ? ' - ' . $data['txn_type'] : '');
        else
            $log .= ' response';

        if (!is_string($data))
            $data = json_encode($data);

        $log .= "($data)";
        RentMy::saveLogFile($alert, $log, ['payment']);
    }

}
