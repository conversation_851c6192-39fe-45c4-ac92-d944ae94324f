<?php

namespace App\Lib\Payment\ValorPay;

use App\Lib\RentMy\RentMy;
use Cake\Http\Client as HttpClient;
use Cake\Log\Log;

class Client
{
    protected $baseUrl;

    public function __construct(string $baseUrl)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    private function keepLogs($forRequest, $data, $logInfo = '', $alert = 'alert')
    {
        $log = 'store ID: ' . RentMy::$store->id . ' -- ' . self::class;

        if (!empty($logInfo)) {
            $log .= ' -- ' . $logInfo;
        }
        if ($forRequest)
            $log .= ' request ' . (isset($data['txn_type']) ? ' - ' . $data['txn_type'] : '');
        else
            $log .= ' response';

        if (!is_string($data))
            $data = json_encode($data);

        $log .= "($data)";
        RentMy::saveLogFile($alert, $log, ['payment']);

        RentMy::logToGenius([
            "account" => 'rentmy_api',
            "event" => "payment valorpay",
            "status" => $alert,
            "description" => $log,
            "value" => '',
            "custom_content" => json_encode([
                'data' => $data,
                'log_info' => $logInfo,
                'alert' => $alert,
            ])
        ]);
    }

    public function post(array $payload, $queryParams = [], $logInfo = '')
    {
        $this->keepLogs(true, $payload, $logInfo);
        $http = new HttpClient();

        // Build the full URL with query params
        $url = $this->baseUrl;
        if (!empty($queryParams)) {
            $url .= (strpos($url, '?') === false ? '?' : '&') . http_build_query($queryParams);
        }

        $response = $http->post(
            $url,
            json_encode($payload), // JSON-encode the body
            [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'timeout' => 180
            ]
        );

        if ($response->isOk()) {
            $body = $response->getJson();
            $this->keepLogs(false, $body, $logInfo);
            if (!empty($body['error_no']) && $body['error_no'] === 'S00') {
                if( isset($body['response']['STATE']) && $body['response']['STATE'] < 0) {
                    RentMy::logToGenius([
                        "account" => 'rentmy_api',
                        "event" => "payment valorpay",
                        "status" => 'fail',
                        "description" => $body['response']['ERROR_MSG'] ?? 'Terminal error',
                        "value" => '',
                        "custom_content" => json_encode([
                            'body' => $body
                        ])
                    ]);
                    return ['success' => false, 'message' => $body['response']['ERROR_MSG'] ?? 'Terminal error', 'data' => $body];
                }
                RentMy::logToGenius([
                    "account" => 'rentmy_api',
                    "event" => "payment valorpay",
                    "status" => 'success',
                    "description" => $body['msg'] ?? '',
                    "value" => '',
                    "custom_content" => json_encode([
                        'body' => $body
                    ])
                ]);
                return ['success' => true, 'data' => $body];
            }

            if( isset($body['msg']) && ($body['msg'] == 'Service not allowed' || $body['msg'] == 'New Account Information') ) {
                $body['msg'] = 'Payment Failed.  Check card details.';
            }

            RentMy::logToGenius([
                "account" => 'rentmy_api',
                "event" => "payment valorpay",
                "status" => 'fail',
                "description" => $body['msg'] ?? 'Unknown error',
                "value" => '',
                "custom_content" => json_encode([
                    'body' => $body
                ])
            ]);

            return ['success' => false, 'message' => $body['msg'] ?? 'Unknown error'];
        }
        $this->keepLogs(false, 'ValorPay API Error: ' . $response->getStringBody(), $logInfo);

        RentMy::logToGenius([
            "account" => 'rentmy_api',
            "event" => "payment valorpay",
            "status" => 'fail',
            "description" => 'API request failed',
            "value" => '',
            "custom_content" => json_encode([
                'data' => $response->getJson()
            ])
        ]);


        return [
            'success' => false,
            'message' => 'API request failed',
            'response' => $response->getStringBody(),
            'data' => $response->getJson()
        ];
    }
}
