<?php
namespace App\Middleware;

use App\Lib\RentMy\RentMy;
use Cake\Datasource\ConnectionManager;
use Cake\Log\Log;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Message\ResponseInterface;
use Closure;

class DatabaseSelectorMiddleware
{
    protected $dbMap;

    public function __construct()
    {
        $this->dbMap = include CONFIG . 'db_map.php';
    }

    public function __invoke(ServerRequestInterface $request, ResponseInterface $response, $next)
    {
        try {
            $method = strtoupper($request->getMethod()); // e.g., GET, POST
            $params = $request->getAttribute('params');
            $path = $params['_matchedRoute']; // e.g., /api/order/status

//            Log::warning("actual name '". $selectedConnection);
            $configured = ConnectionManager::configured();

            $selectedConnection = 'default'; // fallback by default

            // Try full method+path match
            $methodKey = "@{$method}:{$path}";
            if (isset($this->dbMap[$methodKey])) {
                $selectedConnection = $this->dbMap[$methodKey];
            }
            // Try path-only fallback
            elseif (isset($this->dbMap[$path])) {
                $selectedConnection = $this->dbMap[$path];
            }

            // Validate connection exists
            if (!in_array($selectedConnection, $configured, true)) {
                Log::warning("[DB SELECTOR] Undefined connection '{$selectedConnection}' for path '{$path}'. Falling back to 'default'.");
                $selectedConnection = 'default';
            }


            // Alias the selected connection to 'default'
            ConnectionManager::alias($selectedConnection, 'default');
            $conn = ConnectionManager::get('default');

            // Optional: pass selected DB name to request attributes
            $request = $request->withAttribute('selected_db', $selectedConnection);

            return $next($request, $response);

        } catch (\Throwable $e) {
            Log::error('DatabaseSelectorMiddleware error: ' . $e->getMessage());
            ConnectionManager::alias('default', 'default');
            return $next($request, $response);
        }
    }
}
