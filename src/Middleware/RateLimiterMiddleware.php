<?php
namespace App\Middleware;

use App\Lib\RentMy\RentMy;
use App\Services\RateLimiterService;
use Cake\Http\Response;
use Cake\Http\ServerRequest;
use Cake\Core\Configure;
use Cake\Network\Exception\ForbiddenException;
use Firebase\JWT\JWT;
use net\authorize\util\Log;
use RestApi\Routing\Exception\InvalidTokenFormatException;
use Throwable;

class RateLimiterMiddleware
{
    /**
     * @throws \Exception
     */
    public function __invoke(ServerRequest $request, Response $response, callable $next)
    {
        try {
            $ip = $request->clientIp();
            $userAgent = $request->getHeaderLine('User-Agent');

            $method = strtoupper($request->getMethod());
            $params = $request->getAttribute('params');
            $path = $params['_matchedRoute'];
            $key = "{$method}:{$path}";

            $rateLimiterConfig = Configure::read('RateLimiter');
            $defaultRule = $rateLimiterConfig['default'] ?? [];
            $rules = $rateLimiterConfig['rules'] ?? [];

            // Only apply rate limiting if the current API is listed
            if (array_key_exists($key, $rules)) {
                $specificRule = $rules[$key] ?? [];
                $finalRule = array_merge($defaultRule, $specificRule);

                $rateLimiter = new RateLimiterService(
                    $ip,
                    $userAgent,
                    $key,
                    $method,
                    $finalRule['limit'],
                    $finalRule['window'],
                    $finalRule['block_duration']
                );
                $rateLimiter->check();
            }

            return $next($request, $response);
        }catch (\Exception $e) {
            return $response->withStatus(429)->withStringBody(json_encode([
                'message' => $e->getMessage(),
                'type' => 'rate_limit',
                'retry_after' => "30 minutes",
            ]));
        }
    }

}
