(function (d) {
  window.DOMAIN = 'http://rentmy-api.localhost/html-template-msgr/';
  window.RENTMY_GLOBAL = {
    store_id: 346,
    locationId: 386,
    store_name: "mountain-side-gear-rental",
    access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAxOS0wNy0xMFQxOTo0OTowOCswMDowMCIsInN0b3JlX2lkIjozNDYsInN0b3JlX3VpZCI6bnVsbCwic3RvcmVfbmFtZSI6bnVsbCwic291cmNlIjoib25saW5lIiwiaXNfb25saW5lIjoxLCJsb2NhdGlvbiI6Mzg2fQ.XkHvvt-SrPlP_m3mfCYJSUBojyegcNx4Z9C3DmzNsPk",
    hide_checkoutPage_createAccount_checkbox: true,
    afterOrder: {
      paymentSuccessUrl: 'order-complete.html',
      paymentCancelUrl: 'catalog.html',
      forIframe_topMode: true,
    },
    emDateTimePicker: {
      detailsPage_useRangePicker_for_endDate: true,
    },
    page: {
      products_list: "catalog.html",
      product_details: "product-details.html?uid={uuid}",
      package_details: "package-details.html?uid={uuid}",
      cart: "cart.html",
      checkout: "checkout.html",
      login: "login.html",
      logout: "login.html",
      registration: "register.html",
      reset_password: "customer-reset-password.html",
      customer_profile: "customer-profile.html",
      customer_change_password: "customer-change-password.html",
      customer_change_avatar: "customer-change-avatar.html",
      customer_order_history: "customer-order-history.html",
      order_details: "customer-order-details.html",
    },
    detailsPage_priceLimitShowFirst: 3,
  }
  window.addEventListener('message', function (e) {
    if (e.data?.RENTMY_GLOBAL) e.source.postMessage({ outside: true, RENTMY_GLOBAL }, e.origin);
    if (e.data.action === 'goRentMyPaymentPage') { window.open(e.data.url, '_self') }
  });
})(document);