:root {
    --rentmy-bg: var(--bs-body-bg);
    --rentmy-primary-color: var(--cnvs-themecolor);
    --rentmy-primary-color-hover: var(--cnvs-themecolor);
    --rentmy-border: rgba(var(--cnvs-contrast-rgb), .1);
    --rentmy-sof-greay: var(--cnvs-contrast-100);
    /* --rentmy-cart-widget-color: var(--rentmy-primary-color); */
}

#InpageCartWidgetLauncher {
    bottom: 100px !important;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher .Summary {
    width: 250px !important;
}

.FullfillmentTabList ul li a.TabActive .fullfilment-btn, .PaymentTabList ul li a.TabActive .PaymentBtn {
    background-color: var(--rentmy-primary-color);
}

.FullfillmentTabList ul li a.TabActive h5, .PaymentTabList ul li a.TabActive h5 {
    color: var(--rentmy-primary-color);
}

.RentMyBackCartBtn {
    background-color: var(--cnvs-heading-color);
}

.OrderReviewWrapper {
    border: 3px solid var(--rentmy-primary-color);
}

.BillingCheckoutSubTitle {
    background-color: var(--rentmy-sof-greay);
}

.DateRangeTextAndIcon span {
    font-size: 14px;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher .Summary {
    font-size: 11px;
}
.RmRentalOption {
    margin-bottom: 20px;
}
.RmRentalOption h6 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}
.RmRentalOption ul {
    width: 300px;
    margin: 0;
    padding: 0;
    margin-top: -10px;
}
.RmRentalOption ul li {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 32px;
    border: 1px solid #ccc;
    padding-right: 15px;
    cursor: pointer;
    margin-right: 10px;
    margin-top: 10px;
    border-radius: 5px;
    font-weight: 400;
    color: #495057;
    font-size: 15px;
    overflow: hidden;
}
.RmRentalOption ul li.RmDaterangeActive {
    border: 1px solid #aaa;
    font-weight: 500;
}
.RmRentalOptionDaytime {
    display: flex;
    align-items: center;
    background-color: #f2f3f8;
    width: 85px;
    height: 100%;
    padding-left: 15px;
    border-right: 1px solid #ccc;
}
.RmRentalOption ul li.RmDaterangeActive .RmRentalOptionDaytime {
    border-right: 1px solid #aaa;
}
.content-wrap {
    padding-top: 25px !important;
}
.admin-cart .search-close-icon {
    top: 8px !important;
}
.item-search-dropdown.active ul.dropdown {
    padding: 0 10px !important;
}