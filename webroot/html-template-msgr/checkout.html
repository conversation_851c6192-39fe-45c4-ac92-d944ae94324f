<!DOCTYPE html>
<html dir="ltr" lang="en-US">
<head>

	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<meta http-equiv="x-ua-compatible" content="IE=edge">
	<meta name="author" content="SemiColonWeb">
	<meta name="description" content="Get Rentmy to build powerful websites easily with the Highly Customizable &amp; Best Selling Bootstrap Template, today.">

	<!-- Fav Icon -->
	<link rel="icon" type="image/x-icon" href="/images/favicon.ico">
	
	<!-- Font Imports -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:ital@0;1&display=swap" rel="stylesheet">

	<!-- Core Style -->
	<link rel="stylesheet" href="css/style.css">

	<!-- Font Icons -->
	<link rel="stylesheet" href="css/font-icons.css">

	<!-- Custom CSS -->
	<link rel="stylesheet" href="css/overwrite.css">
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Document Title
	============================================= -->
	<title>Checkout | Rentmy</title>
	<script src="js/config.js"></script>
	<script src="js/rentmy.js" defer></script>
</head>

<body class="stretched">

	<!-- Document Wrapper
	============================================= -->
	<div id="wrapper">

				<!-- Header
		============================================= -->
		<header id="header">
			<div id="header-wrap">
				<div class="container">
					<div class="header-row">

						<!-- Logo
						============================================= -->
						<div id="logo">
							<a href="index.html">
								<img class="logo-default" srcset="images/logo.webp, images/<EMAIL> 2x" src="images/logo.webp" alt="Rentmy Logo">
								<img class="logo-dark" srcset="images/logo.webp, images/<EMAIL> 2x" src="images/logo.webp" alt="Rentmy Logo">
							</a>
						</div><!-- #logo end -->

						<div class="header-misc">

							<!-- Top Search
							============================================= -->
							<div id="top-search" class="header-misc-icon">
								<a href="#" id="top-search-trigger"><i class="uil uil-search"></i><i class="bi-x-lg"></i></a>
							</div><!-- #top-search end -->

						</div>

						<div class="primary-menu-trigger">
							<button class="cnvs-hamburger" type="button" title="Open Mobile Menu">
								<span class="cnvs-hamburger-box"><span class="cnvs-hamburger-inner"></span></span>
							</button>
						</div>

						<!-- Primary Navigation
						============================================= -->
						<nav class="primary-menu">

							<ul class="menu-container">
								<li class="menu-item"><a class="menu-link" href="index.html"><div>Home</div></a></li>
								<li class="menu-item"><a class="menu-link" href="about.html"><div>About</div></a></li>
								<li class="menu-item"><a class="menu-link" href="catalog.html"><div>Catalog</div></a></li>
								<li class="menu-item"><a class="menu-link" href="contact.html"><div>Contact</div></a></li>
								<li class="menu-item RentMy-login-page"><a class="menu-link" href="login.html"><div>Login</div></a></li>
								<li class="menu-item RentMy-login-page"><a class="menu-link" href="register.html"><div>Register</div></a></li>
								<li class="menu-item RentMy-client-portal-page"><a class="menu-link" href="customer-profile.html"><div>Profile</div></a></li>
								<li class="menu-item rentmy_logout_btn hide_if_not_logged_in"><a class="menu-link"><div>Logout</div></a></li>
							</ul>

						</nav><!-- #primary-menu end -->

						<form class="top-search-form" action="search.html" method="get">
							<input type="text" name="q" class="form-control" value="" placeholder="Type &amp; Hit Enter.." autocomplete="off">
						</form>

					</div>
				</div>
			</div>
			<div class="header-wrap-clone"></div>
		</header><!-- #header end -->

		<!-- Page Title
		============================================= -->
		<section class="page-title bg-transparent">
			<div class="container">
				<div class="page-title-row">

					<div class="page-title-content">
						<h1>Checkout</h1>
					</div>

					<nav aria-label="breadcrumb">
						<ol class="breadcrumb">
							<li class="breadcrumb-item"><a href="index.html">Home</a></li>
							<li class="breadcrumb-item"><a href="catalog.html">Catalog</a></li>
							<li class="breadcrumb-item active" aria-current="page">Checkout</li>
						</ol>
					</nav>

				</div>
			</div>
		</section><!-- .page-title end -->

		<!-- Content
		============================================= -->
		<section id="content">
			<div class="content-wrap">
				<div class="container">

					<div id="RentMyCheckoutWrapper" class="RentMyCheckoutWrapper RentMyWrapper">
						<div class="RentMyRow">
							<div class="CheckoutLeftSide">
								<div class="ReturningCustomerTitle" SignInPopupArea>
									<h5> Welcome to <span StoreName>store</span> <a SignIn>(Sign in)</a></h5>
								</div>
								<div class="BillingDetailsLeftside">
									<div class="BillingDetailsLeftSideInner" BillingBorder>
										<span class="BillingCheckoutTitle">Billing Details</span>
										<form>
											<div class="RentMyRow" BillingGeneralInfo>
												<div class="RentMyInputGroup RentMyHalfwidth" FirstNameDiv>
													<label> First Name</label>
													<input type="text" class="RentMyInputField" FirstName />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" LastNameDiv>
													<label>Last Name</label>
													<input type="text" class="RentMyInputField" LastName />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" MobileDiv>
													<label>Mobile Number</label>
													<input type="text" class="RentMyInputField" Mobile />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" EmailDiv>
													<label>Email Name </label>
													<input type="email" class="RentMyInputField" Email />
												</div>
												<div class="RentMyInputGroup RentMyFullwidth" CompanyDiv>
													<label>Company Name(Optional)</label>
													<input type="text" class="RentMyInputField" Company />
												</div>
			
												<div class="RentMyRow billing-details-checkbox" AllSavedBililingAddress>
													<div class="RentMyInputGroup RentMyFullwidth">
														<label class="RentMyRadio" SingleAddress>
															This is a save address
															<input type="radio" name="select_address" value="rent">
															<span></span>
														</label>
														<!-- Create New -->
														<label class="RentMyRadio" CreateAddress>
															Create New
															<input type="radio" name="select_address" value="rent">
															<span></span>
														</label>
													</div>
												</div>
			
												<div class="RentMyInputGroup RentMyFullwidth">
													<label>Country</label>
													<select class="RentMyInputField" Country>
														<option value=""> Country Name </option>
													</select>
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth">
													<label>Address Line 1</label>
													<input type="text" class="RentMyInputField" placeholder="Enter a location"
														AddressLine1 />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth">
													<label>Address Line 2</label>
													<input type="text" class="RentMyInputField" AddressLine2 />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth">
													<label>City</label>
													<input type="text" class="RentMyInputField" City />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth">
													<label>State</label>
													<input type="text" class="RentMyInputField" State />
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth">
													<label> Zipcode</label>
													<input type="text" class="RentMyInputField" Zipcode />
												</div>
											</div>
			
											<div class="RentMyRow" BillingAdditionalInfo>
												<div class="RentMyFullwidth">
													<div class="BillingCheckoutSubTitle">
														<h5 Title>
															<i class="fa fa-plus"></i>
															Additional Information
														</h5>
													</div>
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" SpecialComments>
													<label>Special Instructions/Comments</label>
													<input type="text" class="RentMyInputField">
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" SpecialRequest>
													<label>Special Request</label>
													<input type="text" class="RentMyInputField">
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" DrivingLicence>
													<label>Driving Licence</label>
													<input type="text" class="RentMyInputField">
												</div>
			
											</div>
											<div class="RentMyRow" BillingCustomCheckoutInfo>
												<div class="RentMyFullwidth">
													<div class="BillingCheckoutSubTitle">
														<h5>
															<i class="fa fa-plus"></i>
															Custom checkout information
														</h5>
													</div>
												</div>
												<div class="RentMyInputGroup RentMyHalfwidth" CustomField></div>
											</div>
										</form>
									</div>
									<div class="BillingDetailsLeftSideInner FullfillmentArea" FullfillmentArea FullfillmentBorder>
										<h2 class="BillingCheckoutTitle" Title>Fulfillment</h2>
										<div class="FullfillmentTabArea">
											<div class="FullfillmentTabList">
												<ul>
													<li PickupTab></li>
													<li ShippingTab></li>
													<li DeliveryTab></li>
												</ul>
											</div>
											<div class="FullfillmentTabContent">
			
												<div class="FullfillmentTabBody FullfillmentPickup" PickupLocations>
													<div class="PickupLocationList" Location></div>
												</div>
			
												<div class="FullfillmentTabBody FullfillmentShippingAndDelivery" ShipAndDelivery>
													<div class="RentMyRow" SameAsAboveArea>
														<div class="RentMyInputGroup RentMyFullwidth">
															<label class="RentMyCheckbox">
																<input type="checkbox" SameAsAbove>
																Same Address as Above
																<span></span>
															</label>
														</div>
													</div>
			
													<div class="FullfillmentTabBody FullfillmentPickup" ShippingAddressList>
														<div class="PickupLocationList" Address>
															<label class="RentMyRadio">
																<input type="radio" name="shipping_address" value="rent">
																Default location (5627 Covehaven Dr, Dallas, TX, US)
																<span></span>
															</label>
														</div>
														<div class="PickupLocationList" CreateNew>
															<label class="RentMyRadio">
																<input type="radio" name="shipping_address" value="rent">
																Create New
																<span></span>
															</label>
														</div>
													</div>
			
													<form class="mt-4">
														<div class="RentMyRow">
															<div class="RentMyInputGroup RentMyHalfwidth" FirstNameDiv>
																<label> First Name</label>
																<input type="text" class="RentMyInputField" FirstName />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth" LastNameDiv>
																<label>Last Name</label>
																<input type="text" class="RentMyInputField" LastName />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth" MobileDiv>
																<label>Mobile Number</label>
																<input type="text" class="RentMyInputField" Mobile />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth" EmailDiv>
																<label>Email Name </label>
																<input type="email" class="RentMyInputField" Email />
															</div>
															<div class="RentMyInputGroup RentMyFullwidth" CompanyDiv>
																<label>Company Name(Optional)</label>
																<input type="text" class="RentMyInputField" Company />
															</div>
															<div class="RentMyInputGroup RentMyFullwidth">
																<label>Country</label>
																<select class="RentMyInputField" Country>
																	<option value=""> Country Name </option>
																</select>
															</div>
			
															<div class="RentMyInputGroup RentMyHalfwidth">
																<label>Address Line 1</label>
																<input type="text" class="RentMyInputField"
																	placeholder="Enter a location" AddressLine1 />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth">
																<label>Address Line 2</label>
																<input type="text" class="RentMyInputField" AddressLine2 />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth">
																<label>City</label>
																<input type="text" class="RentMyInputField" City />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth">
																<label>State</label>
																<input type="text" class="RentMyInputField" State />
															</div>
															<div class="RentMyInputGroup RentMyHalfwidth">
																<label> Zipcode</label>
																<input type="text" class="RentMyInputField" Zipcode />
															</div>
														</div>
													</form>
			
													<div class="ShippingMethodArea" ShippingMethods>
														<h5 Title>Select Shipping Method</h5>
														<div AllMethods>
															<div class="PickupLocationList" Method>
																<label class="RentMyRadio">
																	<input type="radio" name="shipping_methods" value="rent">
																	Flat Rate
																	<b>$33.99</b>
																	<span></span>
																</label>
															</div>
														</div>
													</div>
			
													<div class="ShippingCostArea" DeliveryCosts>
														<h5 Title>Delivery Cost</h5>
														<div class="PickupLocationList" Cost>
															<label class="RentMyRadio">
																<input type="radio" name="delivery_cost" value="rent">
																Zone A
																<b>$33.99</b>
																<span></span>
															</label>
														</div>
													</div>
			
													<div class="DeliveryAddressMsg" DeliveryOutsideAreaMsg>
														Your address is outside of our delivery area. Please contact us to make
														other arrangements.
													</div>
													<div class="DeliveryAddressErrorMsg" DeliveryAddressErrorMsg>
														Delivery is not possible for your address
													</div>
			
													<div class="RentMyRow mt-3">
														<div class="RentMyButtonGroup RentMyFullwidth RentMyGetShippingArea">
															<button type="button" class="RentMyBtn RentMyGetShippingBtn"
																GetShippingMethodsBtn>Get shipping method <i
																	class="fa fa-arrow-right"></i></button>
														</div>
														<div class="RentMyButtonGroup RentMyFullwidth RentMyGetShippingArea">
															<!-- <button type="button" class="RentMyBtn RentMyGetShippingBtn"
																GetDeliveryCostBtn>Get delivery cost <i
																	class="fa fa-arrow-right"></i></button> -->
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="CheckoutRightSide" OrderSummary>
								<div class="OrderReviewWrapper" Contents>
									<span class="OrderReviewTitle">Your Order Summary</span>
									<div class="OrderReviewWrapperBody">
										<div class="OrderSummery">
											<div class="CheckoutDatetimeShow" CheckoutDatetime>
												12/21/2023 - 12/21/2023
											</div>
											<div class="CheckoutOrderList" CartItems>
												<div class="CheckoutOrderItem" Item>
													<div class="OrderItemImg">
														<img src="" alt="order itemimg" class="img-fluid" />
													</div>
													<div class="OrderItemContent">
														<div class="OrderName" ProductName>AA</div>
														<div class="OrderQuantity" ProductQuantity>( qty : 1 )</div>
														<div class="OrderOtherInfo">
															<p class="qty" ProductVaraint>test: 1</p>
															<p class="OrderItemPrice" ProductPrice>
																Price: €0.00
															</p>
														</div>
													</div>
												</div>
											</div>
										</div>
										<table class="RentMyTable OrderSummaryTable">
											<tfoot>
												<tr class="cart-subtotal">
													<th>Subtotal</th>
													<td Subtotal>
														$0.00
													</td>
												</tr>
												<tr class="cart-subtotal">
													<th>Optional Services</th>
													<td OptionalService>
														$0.00
													</td>
												</tr>
												<tr class="cart-subtotal">
													<th>Deposit Amount</th>
													<td DepositAmount>
														$0.00
													</td>
												</tr>
												<tr class="cart-subtotal">
													<th>Tax</th>
													<td TaxAmount>
														$0.00
													</td>
												</tr>
												<tr class="cart-subtotal">
													<th lbl_shipping>Shipping Charge</th>
													<td ShippingCharge>
														$0.00
													</td>
												</tr>
												<tr class="cart-subtotal">
													<th LblDeliveryTax>Delivery Tax</th>
													<td DeliveryTax>
														$0.00
													</td>
												</tr>
												<tr class="order-total">
													<th>Total</th>
													<td>
														<strong TotalAmount>
															$0.00
														</strong>
													</td>
												</tr>
											</tfoot>
										</table>
			
										<div class="RentMyOptionalService" AdditionalCharges>
											<div class="RentMyRow">
												<div class="RentMyFullwidth">
													<div class="RentMyAdditionalChargeTitle">Optional Services</div>
												</div>
												<!-- Start -->
												<div class="RentMyFullwidth" SingleCharge>
													<div class="RentMyRow">
														<div class="RentMyCheckboxInline">
															<label class="RentMyCheckbox" Title>
																<input type="checkbox">
																give donate
																<span></span>
															</label>
														</div>
														<div class="RentMyOptionalServiceContent" FeeAmountsAndShowInputAmout>
															<div class="RentMyBtnToolbar">
																<div class="RentMyBtnGroup me-0">
																	<button type="button" class="RentMyGroupBtn"
																		FeeAmounts>10%</button>
																	<button type="button"
																		class="RentMyGroupBtn RentMyInputAmountBtn"
																		ShowInputAmount>Input Amount</button>
																</div>
																<select class="RentMyInputField mt-0" SelectOptions>
																	<option>op, op2</option>
																</select>
															</div>
															<div class="RentMyInputAmountArea" InputAmountArea>
																<div class="RentMyInputGroup">
																	<input class="RentMyInputField" type="text" InputAmount />
																	<div class="RentMyInputGroupAppend">
																		<button type="button"
																			class="RentMyGroupBtn RentMyOptionalOkBtn" OkButton><i
																				class="fa fa-check"></i></button>
																		<button type="button"
																			class="RentMyGroupBtn RentMyOptionalCancelBtn"
																			CancelButton><i class="fa fa-times"></i></button>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
												<!-- End -->
											</div>
										</div>
			
										<div class="checkout-checkbox">
											<div class="CreateCustomerCheckbox" IsCustomerAccountDiv>
												<label class="RentMyCheckbox">
													<input type="checkbox" IsCustomerAccount>
													Create an account to make ordering faster in the future
													<span></span>
												</label>
											</div>
											<div class="TermsConditionsCheckbox" TermsConditions>
												<label class="RentMyCheckbox">
													<input type="checkbox">
													I have read and agree with the <a style="cursor: pointer;" ShowPopup>terms &amp;
														conditions</a>
													<span></span>
												</label>
											</div>
										</div>
			
										<div class="SignatureContainer" SingnatureArea>
											<div class="SignaturePad">
												<div class="SignaturePadBody">
													<canvas></canvas>
												</div>
											</div>
											<div class="SignatureFooter">
												<p>Signature</p>
												<div class="UndoClear">
													<a Clear>Clear</a>
													<a Undo>Undo</a>
												</div>
											</div>
										</div>
			
										<div class="CheckoutBackcartPlaceorder">
											<a class="RentMyBtn RentMyBackCartBtn" href="#" BackToCartBtn><i
													class="fa fa-backward"></i>
												&nbsp;Back to Cart</a>
											<button type="button" class="RentMyBtn RentMyPlaceOrder" PlaceOrderBtn successMessage=""
												errorMessage="">Place
												Order</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section><!-- #content end -->

		<!-- Footer
		============================================= -->
		<footer id="footer" class="dark">
			<div class="container">

				<!-- Footer Widgets
				============================================= -->
				<div class="footer-widgets-wrap">

					<div class="row col-mb-50">
						<div class="col-lg-8">

							<div class="row col-mb-50">
								<div class="col-md-4">

									<div class="widget">

										<img src="images/logo.webp" alt="Image" class="footer-logo">

										<p>We believe in <strong>Simple</strong>, <strong>Creative</strong> &amp; <strong>Flexible</strong> Design Standards.</p>

										<div style="background: url('images/world-map.png') no-repeat center center; background-size: 100%;">
											<address>
												<strong>Headquarters:</strong><br>
												795 Folsom Ave, Suite 600<br>
												San Francisco, CA 94107<br>
											</address>
											<abbr title="Phone Number"><strong>Phone:</strong></abbr> (1) 8547 632521<br>
											<abbr title="Fax"><strong>Fax:</strong></abbr> (1) 11 4752 1433<br>
											<abbr title="Email Address"><strong>Email:</strong></abbr> <EMAIL>
										</div>

									</div>

								</div>

								<div class="col-md-4">

									<div class="widget widget_links">

										<h4>Blogroll</h4>

										<ul>
											<li><a href="https://codex.wordpress.org/">Documentation</a></li>
											<li><a href="https://wordpress.org/support/forum/requests-and-feedback">Feedback</a></li>
											<li><a href="https://wordpress.org/extend/plugins/">Plugins</a></li>
											<li><a href="https://wordpress.org/support/">Support Forums</a></li>
											<li><a href="https://wordpress.org/extend/themes/">Themes</a></li>
											<li><a href="https://wordpress.org/news/">Rentmy Blog</a></li>
											<li><a href="https://planet.wordpress.org/">Rentmy Planet</a></li>
										</ul>

									</div>

								</div>

								<div class="col-md-4">

									<div class="widget">
										<h4>Recent Posts</h4>

										<div class="posts-sm row col-mb-30" id="post-list-footer">
											<div class="entry col-12">
												<div class="grid-inner row">
													<div class="col">
														<div class="entry-title">
															<h4><a href="#">Lorem ipsum dolor sit amet, consectetur</a></h4>
														</div>
														<div class="entry-meta">
															<ul>
																<li>10th July 2021</li>
															</ul>
														</div>
													</div>
												</div>
											</div>

											<div class="entry col-12">
												<div class="grid-inner row">
													<div class="col">
														<div class="entry-title">
															<h4><a href="#">Elit Assumenda vel amet dolorum quasi</a></h4>
														</div>
														<div class="entry-meta">
															<ul>
																<li>10th July 2021</li>
															</ul>
														</div>
													</div>
												</div>
											</div>

											<div class="entry col-12">
												<div class="grid-inner row">
													<div class="col">
														<div class="entry-title">
															<h4><a href="#">Debitis nihil placeat, illum est nisi</a></h4>
														</div>
														<div class="entry-meta">
															<ul>
																<li>10th July 2021</li>
															</ul>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>

								</div>
							</div>

						</div>

						<div class="col-lg-4">

							<div class="row col-mb-50">
								<div class="col-md-4 col-lg-12">
									<div class="widget">

										<div class="row col-mb-30">
											<div class="col-lg-6">
												<div class="counter counter-small"><span data-from="50" data-to="15065421" data-refresh-interval="80" data-speed="3000" data-comma="true"></span></div>
												<h5 class="mb-0">Total Downloads</h5>
											</div>

											<div class="col-lg-6">
												<div class="counter counter-small"><span data-from="100" data-to="18465" data-refresh-interval="50" data-speed="2000" data-comma="true"></span></div>
												<h5 class="mb-0">Clients</h5>
											</div>
										</div>

									</div>
								</div>

								<div class="col-md-5 col-lg-12">
									<div class="widget subscribe-widget">
										<h5><strong>Subscribe</strong> to Our Newsletter to get Important News, Amazing Offers &amp; Inside Scoops:</h5>
										<div class="widget-subscribe-form-result"></div>
										<form id="widget-subscribe-form" action="include/subscribe.php" method="post" class="mb-0">
											<div class="input-group mx-auto">
												<div class="input-group-text"><i class="bi-envelope-plus"></i></div>
												<input type="email" id="widget-subscribe-form-email" name="widget-subscribe-form-email" class="form-control required email" placeholder="Enter your Email">
												<button class="btn btn-success" type="submit">Subscribe</button>
											</div>
										</form>
									</div>
								</div>

								<div class="col-md-3 col-lg-12">
									<div class="widget">

										<div class="row col-mb-30">
											<div class="col-6 col-md-12 col-lg-6 d-flex align-items-center">
												<a href="#" class="social-icon text-white border-transparent bg-facebook me-2 mb-0 float-none">
													<i class="fa-brands fa-facebook-f"></i>
													<i class="fa-brands fa-facebook-f"></i>
												</a>
												<a href="#" class="ms-1"><small class="d-block"><strong>Like Us</strong><br>on Facebook</small></a>
											</div>
											<div class="col-6 col-md-12 col-lg-6 d-flex align-items-center">
												<a href="#" class="social-icon text-white border-transparent bg-rss me-2 mb-0 float-none">
													<i class="fa-solid fa-rss"></i>
													<i class="fa-solid fa-rss"></i>
												</a>
												<a href="#" class="ms-1"><small class="d-block"><strong>Subscribe</strong><br>to RSS Feeds</small></a>
											</div>
										</div>

									</div>
								</div>

							</div>

						</div>
					</div>

				</div><!-- .footer-widgets-wrap end -->

			</div>

			<!-- Copyrights
			============================================= -->
			<div id="copyrights">
				<div class="container">

					<div class="row col-mb-30">

						<div class="col-md-6 text-center text-md-start">
							Copyrights &copy; 2023 All Rights Reserved by Rentmy Inc.<br>
							<div class="copyright-links"><a href="#">Terms of Use</a> / <a href="#">Privacy Policy</a></div>
						</div>

						<div class="col-md-6 text-center text-md-end">
							<div class="d-flex justify-content-center justify-content-md-end mb-2">
								<a href="#" class="social-icon border-transparent si-small h-bg-facebook">
									<i class="fa-brands fa-facebook-f"></i>
									<i class="fa-brands fa-facebook-f"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small h-bg-x-twitter">
									<i class="fa-brands fa-x-twitter"></i>
									<i class="fa-brands fa-x-twitter"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small h-bg-google">
									<i class="fa-brands fa-google"></i>
									<i class="fa-brands fa-google"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small h-bg-pinterest">
									<i class="fa-brands fa-pinterest-p"></i>
									<i class="fa-brands fa-pinterest-p"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small h-bg-vimeo">
									<i class="fa-brands fa-vimeo-v"></i>
									<i class="fa-brands fa-vimeo-v"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small h-bg-github">
									<i class="fa-brands fa-github"></i>
									<i class="fa-brands fa-github"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small h-bg-yahoo">
									<i class="fa-brands fa-yahoo"></i>
									<i class="fa-brands fa-yahoo"></i>
								</a>

								<a href="#" class="social-icon border-transparent si-small me-0 h-bg-linkedin">
									<i class="fa-brands fa-linkedin"></i>
									<i class="fa-brands fa-linkedin"></i>
								</a>
							</div>

							<i class="bi-envelope"></i> <EMAIL> <span class="middot">&middot;</span> <i class="fa-solid fa-phone"></i> *****-6541-6369 <span class="middot">&middot;</span> <i class="bi-skype"></i> RentmyOnSkype
						</div>

					</div>

				</div>
			</div><!-- #copyrights end -->
		</footer><!-- #footer end -->

	</div><!-- #wrapper end -->

	<!-- Go To Top
	============================================= -->
	<div id="gotoTop" class="uil uil-angle-up"></div>
<!-- JavaScripts
	============================================= -->
	<script src="js/plugins.min.js"></script>
	<script src="js/functions.bundle.js"></script>
</body>
</html>