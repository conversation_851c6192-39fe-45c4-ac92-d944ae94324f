<html>
<head>
    <title>FreedomPay Test</title>
    <style>
        .wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            max-width: 800px;
            margin: 100px auto;
        }
        #freedompay-container {
            display: block;
            width: 100%;
            height: 100vh;
        }
        #freedompay-container iframe {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <h1>FreedomPay Test</h1>
        <p>This is a test page for FreedomPay payment gateway.</p>
        <button onclick="initFreedomPay()">Card</button>
        <br/>
        <button onclick="initFreedomPay('google')">Googlepay</button>
        <br/>
        <button onclick="initFreedomPay('apple')">Applepay</button>
        <br/>
        <div id="freedompay-container"></div>
    </div>
    <script>
    const myHeaders = new Headers();
    myHeaders.append("accept", "application/json, text/plain, */*");
    myHeaders.append("authorization", "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAxOS0wOS0yNlQyMzoxMjo0NCswMDowMCIsInN0b3JlX2lkIjo0MjQsInN0b3JlX3VpZCI6bnVsbCwic3RvcmVfbmFtZSI6bnVsbCwic291cmNlIjoib25saW5lIiwiaXNfb25saW5lIjoxLCJsb2NhdGlvbiI6NDc0fQ.10x0uf_OySLYMoTMB5P0RJxMIrBJtvhJ7iFc2xv1URw");
    myHeaders.append("content-type", "application/json");
    myHeaders.append("location", "474");

    const raw = JSON.stringify({
      "id": 11281,
      "name": "FreedomPay",
      "config": {
        "label": "FreedomPay",
        "es_key": "WQWFRQJKDM7KM7J8HQPT38KVPW96JKR4",
        "store_id": "1520443977",
        "terminal_id": "2522786975",
        "is_live": "false"
      },
      "status": 0,
      "is_online": true,
      "type": "redirect",
      "is_admin": true,
      "online_type": "card",
      "store_id": 424,
      "location": 474,
      "test": true
    });

    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow"
    };

    let baseUrl = "http://rentmy-api.localhost/api/gateway/freedom-pay/init";

    function initFreedomPay(method) {

        if (method === 'google') {
            url = `${baseUrl}/google`;
        } else if (method === 'apple') {
            url = `${baseUrl}/apple`;
        } else {
            url = `${baseUrl}`;
        }

      fetch(url, requestOptions)
        .then((response) => response.text())
        .then((result) => {
console.log('result', result);
          try {
            const parsedResult = JSON.parse(result);
console.log('parsedResult', parsedResult);
            const container = document.getElementById('freedompay-container');
console.log('container', container);
console.log('parsedResult?.result?.data?.iframe', parsedResult?.result?.data?.iframe);
            if (container != undefined && parsedResult?.result?.data?.iframe) {
              container.innerHTML = parsedResult?.result?.data?.iframe;
            }
          } catch (e) {
            console.error("Failed to parse result or append iframe:", e, result);
          }
        })
        .catch((error) => console.error(error));
    }
    </script>
</body>
</html>
